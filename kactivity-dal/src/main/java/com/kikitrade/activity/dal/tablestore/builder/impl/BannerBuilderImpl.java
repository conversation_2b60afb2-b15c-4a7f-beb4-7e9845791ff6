package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.tablestore.builder.BannerBuilder;
import com.kikitrade.activity.dal.tablestore.model.Banner;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/1 15:03
 */
@Component
@Slf4j
public class BannerBuilderImpl extends WideColumnStoreBuilder<Banner> implements BannerBuilder {

    @Resource
    private SeqClient seqClient;

    SeqRule seqRule = new SeqRule("IDX_BANNER", 10, "1", null);

    @PostConstruct
    public void init() {
        super.init(Banner.class);
    }

    @Override
    public boolean insert(Banner banner) {
        return super.putRow(banner, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean update(Banner banner) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(banner, condition);
    }

    @Override
    public List<Banner> getByLocal(String saasId, String local, String channel) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("saas_id", saasId))
                .must(QueryBuilders.term("local", local))
                .must(QueryBuilders.term("channel", channel));

        Sort sort = new Sort(Arrays.asList(new FieldSort("sort", SortOrder.ASC)));
        PageResult result = pageSearch(boolQuery.build(), sort, 0, 100, Banner.IDX_BANNER_LOCAL);
        return result.getRows();
    }

    @Override
    public String nextId() {
        return seqClient.next(seqRule);
    }
}
