package com.kikitrade.activity.service.remote;

import com.kikitrade.activity.api.RemoteTaskService;
import com.kikitrade.activity.api.model.response.SocialAuthUrlResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import jakarta.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2025/7/23 10:30
 * @description: RemoteTaskService获取社媒授权URL测试
 */
@SpringBootTest
@TestPropertySource(locations = "classpath:application-junit.properties")
public class RemoteTaskServiceAuthUrlsTest {

    @Resource
    private RemoteTaskService remoteTaskService;

    @Test
    public void testGetTasksAuthUrls() {
        // 测试获取社媒平台授权URL列表
        String saasId = "test-saas";
        
        List<SocialAuthUrlResponse> authUrls = remoteTaskService.getTasksAuthUrls(saasId);
        
        // 验证返回结果
        assertNotNull(authUrls);
        assertFalse(authUrls.isEmpty());
        
        System.out.println("=== 社媒平台授权URL列表 ===");
        for (SocialAuthUrlResponse authUrl : authUrls) {
            System.out.println("平台: " + authUrl.getPlatform());
            System.out.println("授权URL: " + authUrl.getAuthUrl());
            System.out.println("是否启用: " + authUrl.getEnabled());
            System.out.println("排序: " + authUrl.getOrder());
            System.out.println("---");
            
            // 验证基本字段
            assertNotNull(authUrl.getPlatform());
            assertNotNull(authUrl.getEnabled());
            assertNotNull(authUrl.getOrder());
        }
        
        // 验证包含所有预期的平台
        List<String> platforms = authUrls.stream()
            .map(SocialAuthUrlResponse::getPlatform)
            .toList();
        
        assertTrue(platforms.contains("twitter"));
        assertTrue(platforms.contains("discord"));
        assertTrue(platforms.contains("google"));
        assertTrue(platforms.contains("facebook"));
        assertTrue(platforms.contains("line"));
        assertTrue(platforms.contains("email"));
        assertTrue(platforms.contains("telegram"));
        assertTrue(platforms.contains("wallet"));
        
        System.out.println("测试通过！共返回 " + authUrls.size() + " 个平台的授权URL");
    }

    @Test
    public void testGetTasksAuthUrlsWithEmptySaasId() {
        // 测试空的saasId
        List<SocialAuthUrlResponse> authUrls = remoteTaskService.getTasksAuthUrls("");
        
        assertNotNull(authUrls);
        assertTrue(authUrls.isEmpty());
        
        System.out.println("空saasId测试通过");
    }

    @Test
    public void testGetTasksAuthUrlsWithNullSaasId() {
        // 测试null的saasId
        List<SocialAuthUrlResponse> authUrls = remoteTaskService.getTasksAuthUrls(null);
        
        assertNotNull(authUrls);
        assertTrue(authUrls.isEmpty());
        
        System.out.println("null saasId测试通过");
    }

    @Test
    public void testAuthUrlsOrder() {
        // 测试授权URL的排序
        String saasId = "test-saas";
        
        List<SocialAuthUrlResponse> authUrls = remoteTaskService.getTasksAuthUrls(saasId);
        
        // 验证排序是否正确
        for (int i = 0; i < authUrls.size() - 1; i++) {
            assertTrue(authUrls.get(i).getOrder() <= authUrls.get(i + 1).getOrder(),
                "授权URL列表应该按order字段排序");
        }
        
        System.out.println("排序测试通过");
    }

    @Test
    public void testSpecificPlatformAuthUrl() {
        // 测试特定平台的授权URL格式
        String saasId = "test-saas";
        
        List<SocialAuthUrlResponse> authUrls = remoteTaskService.getTasksAuthUrls(saasId);
        
        // 查找Twitter平台
        SocialAuthUrlResponse twitterAuth = authUrls.stream()
            .filter(auth -> "twitter".equals(auth.getPlatform()))
            .findFirst()
            .orElse(null);
        
        if (twitterAuth != null) {
            assertTrue(twitterAuth.getEnabled());
            assertEquals(1, twitterAuth.getOrder());
            System.out.println("Twitter授权URL: " + twitterAuth.getAuthUrl());
        }
        
        // 查找Email平台（不需要OAuth URL）
        SocialAuthUrlResponse emailAuth = authUrls.stream()
            .filter(auth -> "email".equals(auth.getPlatform()))
            .findFirst()
            .orElse(null);
        
        if (emailAuth != null) {
            assertTrue(emailAuth.getEnabled());
            assertEquals("", emailAuth.getAuthUrl()); // Email不需要OAuth URL
            System.out.println("Email平台配置正确");
        }
        
        System.out.println("特定平台测试通过");
    }
}
