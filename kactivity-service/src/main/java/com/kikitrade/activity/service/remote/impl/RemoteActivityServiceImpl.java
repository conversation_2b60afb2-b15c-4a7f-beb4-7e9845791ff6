package com.kikitrade.activity.service.remote.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.api.RemoteActivityService;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.api.model.ActivityDTO;
import com.kikitrade.activity.api.model.ActivityRequest;
import com.kikitrade.activity.api.model.ActivityResponse;
import com.kikitrade.activity.api.model.RewardsRequest;
import com.kikitrade.activity.api.model.activity.ActivityInfoVO;
import com.kikitrade.activity.api.model.activity.RewardVO;
import com.kikitrade.activity.api.model.request.InviteRewardsRequest;
import com.kikitrade.activity.api.model.request.InviteRewardsSumRequest;
import com.kikitrade.activity.api.model.request.InviteRewardsTotalRequest;
import com.kikitrade.activity.api.model.request.PrecisionRecordRequest;
import com.kikitrade.activity.api.model.response.InviteRewardsResponse;
import com.kikitrade.activity.api.model.response.InviteRewardsSumResponse;
import com.kikitrade.activity.api.model.response.InviteRewardsTotalResponse;
import com.kikitrade.activity.api.model.response.PrecisionRecordResponse;
import com.kikitrade.activity.dal.mysql.model.ActivityMaterial;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.luck.api.RemoteLuckFortuneReceiveService;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceive;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneRelease;
import com.kikitrade.activity.luck.service.business.ReceiveService;
import com.kikitrade.activity.luck.service.business.ReleaseService;
import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.*;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.service.config.KactivityTextConfig;
import com.kikitrade.activity.service.reward.ActivityRewardService;
import com.kikitrade.activity.service.view.ActivityInfoBuilder;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class RemoteActivityServiceImpl implements RemoteActivityService {

    @Resource
    private ActivityService activityService;
    @Resource
    private ActivityRewardService activityRewardService;
    @Resource
    private KolExtraRewardService kolExtraRewardService;
    @Resource
    private ActivityMaterialService activityMaterialService;
    @Resource
    private List<ActivityInfoBuilder> buildActivityInfoServiceList;
    @Resource
    private ThreadPoolTaskExecutor activityPoolTaskExecutor;
    @Resource
    private ActivityJoinItemService activityJoinItemService;
    @Resource
    private PrecisionTrackService precisionTrackService;

    @Override
    public ActivityResponse getActivity(Integer type, String locale) {
        ActivityDTO activity = activityService.findActivity(type, locale);
        return ActivityResponse.builder().success(true).obj(activity).build();
    }

    public ActivityResponse convertResult(JsonResult result) {
        ActivityResponse ActivityResponse = null;
        if (result != null) {
            ActivityResponse = new ActivityResponse();
            ActivityResponse.setCode(result.getCode());
            ActivityResponse.setMsg(result.getMsg());
            ActivityResponse.setObj(result.getObj());
            ActivityResponse.setSuccess(result.getSuccess());
        }
        log.info("ActivityResponse return :{}", JSONObject.toJSONString(ActivityResponse));
        return ActivityResponse;
    }

    @Override
    public ActivityResponse reloadKolExtraRewardConfig() {
        try {
            if (kolExtraRewardService.reload()) {
                return ActivityResponse.builder().success(true).code(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).msg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage()).build();
            } else {
                return ActivityResponse.builder().success(false).code(ActivityExceptionType.KOL_EXTRA_REWARD_CONFIG_RELOAD_FAILED.getCode()).msg(ActivityExceptionType.KOL_EXTRA_REWARD_CONFIG_RELOAD_FAILED.getMessage()).build();
            }
        } catch (Exception e) {
            log.error("reloadKolExtraRewardConfig process failed.", e);
            return ActivityResponse.builder().success(false).code(ActivityExceptionType.UNKNOWN_REASON.getCode()).msg(ActivityExceptionType.UNKNOWN_REASON.getMessage()).build();
        }
    }

    @Override
    public Long rewardTotalCount(String customerId, String currency, ActivityType type) {
        return activityRewardService.totalRewardCount(customerId, currency, type);
    }

    @Override
    public BigDecimal rewardTotalAmount(String customerId, String currency, ActivityType type) {
        return activityRewardService.totalReward(customerId, currency, type);
    }

    @Override
    public PageResult rewards(String customerId, ActivityType type, int offset, int limit) {
        return activityRewardService.rewards(customerId, type, offset, limit);
    }

    @Override
    public Result<ActivityInfoVO> getActivityInfo(ActivityRequest activityRequest) {
        try{
            Optional<Map<String, ActivityMaterial>> materialMapOptional = activityMaterialService.findByActivityIdFromCache(activityRequest.getActivityId());
            if(!materialMapOptional.isPresent()){
                return Result.of(Result.ResultCode.ACTIVITY_OFFLINE, null);
            }
            Map<String, ActivityMaterial> materialMap = materialMapOptional.get();
            ActivityInfoVO activityInfo = new ActivityInfoVO();
            activityInfo.setActivityId(activityRequest.getActivityId());
            activityInfo.setView(new HashMap<>());
            List<CompletableFuture> futureList = new ArrayList<>();
            for(ActivityInfoBuilder buildService : buildActivityInfoServiceList){
                futureList.add(CompletableFuture.runAsync(() -> buildService.build(activityRequest, activityInfo, materialMap)
                        , activityPoolTaskExecutor));
            }
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            return Result.of(Result.ResultCode.SUCCESS, null, activityInfo);
        }catch (Exception ex){
            log.error("remote getActivityInfo error", ex);
            return Result.of(Result.ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 邀请活动奖励
     *
     * @param customerId
     * @param offset
     * @param limit
     * @return
     */
    @Override
    public PageResult inviteRewards(String saasId, String customerId, int offset, int limit) {
        PageResult pageResult = activityRewardService.inviteRewards(customerId, offset, limit);
        PageResult result = new PageResult();
        if(pageResult != null){
            if(CollectionUtils.isNotEmpty(pageResult.getRows())){
                List<ActivityCustomReward> rows = (List<ActivityCustomReward>)pageResult.getRows();
                List<RewardVO> rewardVOList = rows.stream().map(item ->
                        RewardVO.builder()
                        .nickName(item.getNickName())
                        .userName(item.getUserName())
                        .amount(item.getAmount())
                        .currency(item.getCurrency())
                        .activityName(item.getActivityName())
                        .rewardTime(item.getRewardTime())
                        .build()).collect(Collectors.toList());
                result.setRows(rewardVOList);
            }
            result.setRecordsTotal(activityRewardService.inviteRewardCount(customerId));
        }
        return result;
    }

    @Override
    public BigDecimal inviteRewardSum(String saasId, String customerId) {
        return activityRewardService.inviteRewardSum(customerId);
    }

    @Override
    public BigDecimal inviteRewardSumAfterTime(String saasId, String customerId) {
        return activityRewardService.inviteRewardSumAfterTime(customerId);
    }

    /**
     * 奖励列表
     *
     * @param rewardsRequest
     * @param offset
     * @param limit
     * @return
     */
    @Override
    public Page<RewardVO> rewards(RewardsRequest rewardsRequest, String saasId, int offset, int limit) {
        Page<RewardVO> result = new Page();
        if(StringUtils.isBlank(rewardsRequest.getBusinessType())){
            rewardsRequest.setBusinessType("reward");
        }
        long total = activityRewardService.countRewards(rewardsRequest);
        if(total <= 0){
            return result;
        }
        PageResult pageResult = activityRewardService.rewards(rewardsRequest, offset, limit);

        if(pageResult != null){
            if(CollectionUtils.isNotEmpty(pageResult.getRows())){
                List<ActivityCustomReward> rows = (List<ActivityCustomReward>)pageResult.getRows();
                List<RewardVO> rewardVOList = rows.stream().map(item ->
                        RewardVO.builder()
                                .nickName(item.getNickName())
                                .userName(item.getUserName())
                                .amount(item.getAmount())
                                .currency(item.getCurrency())
                                .activityName(item.getActivityName())
                                .rewardTime(item.getRewardTime())
                                .businessType(item.getBusinessType())
                                .extendParam(item.getExtendParamMap())
                                .build()).collect(Collectors.toList());
                result.setRows(rewardVOList);
            }
            result.setTotalCount(total);
        }
        return result;
    }

    /**
     * 参加活动
     *
     * @param username
     * @param businessCode
     * @param businessType
     * @return
     */
    @Override
    public Boolean joinActivity(String username, String businessCode, String businessType) {
        return activityJoinItemService.insert(username, businessCode, businessType);
    }

    @Override
    public InviteRewardsTotalResponse inviteRewardsTotal(InviteRewardsTotalRequest request) {
        try {
            InviteRewardsTotalResponse response = activityRewardService.inviteRewardsTotal(request);
            log.info("inviteRewardsTotal start, request={}, response={}", JSON.toJSONString(request), JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.error("inviteRewardsTotal exception, request={}", request, e);
            return InviteRewardsTotalResponse.builder().code(ActivityResponseCode.SYSTEM_ERROR).build();
        }
    }

    @Override
    public InviteRewardsResponse inviteRewards(InviteRewardsRequest request) {
        try {
            InviteRewardsResponse response = activityRewardService.inviteRewards(request);
            log.info("inviteRewardsTotal start, request={}, response={}", JSON.toJSONString(request), JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.error("inviteRewards exception, request={}", request, e);
            return InviteRewardsResponse.builder().code(ActivityResponseCode.SYSTEM_ERROR).build();
        }
    }

    @Override
    public InviteRewardsSumResponse inviteRewardsSumByType(InviteRewardsSumRequest request) {
        try {
            InviteRewardsSumResponse response = activityRewardService.inviteRewardsSumByType(request);
            log.info("inviteRewardsTotal start, request={}, response={}", JSON.toJSONString(request), JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.error("inviteRewardsSumByType exception, request={}", request, e);
            return InviteRewardsSumResponse.builder().code(ActivityResponseCode.SYSTEM_ERROR).build();
        }
    }

    @Override
    public PrecisionRecordResponse savePrecisionRecord(PrecisionRecordRequest request) {
        boolean res = false;
        PrecisionRecordResponse response = new PrecisionRecordResponse();
        try {
            res = precisionTrackService.savePrecisionTrack(request);
        } catch (Exception e) {
            response.setReason(e.getMessage());
        }
        response.setSuccess(res);
        return response;
    }
}
