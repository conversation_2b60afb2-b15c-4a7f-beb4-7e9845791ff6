package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteSocialServiceDubboMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.ksocial.api.model.dto.CustomerKolDTO;
import com.kikitrade.ksocial.api.service.RemoteSocialService;
import com.kikitrade.ksocial.common.constants.CustomerConstants;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import static org.mockito.ArgumentMatchers.anyString;

@Component
public class RemoteSocialServiceDubboMockImpl implements RemoteSocialServiceDubboMock {

    @Resource
    RemoteSocialService remoteSocialService;

    @Override
    public void getSocialKol() {

        Mockito.doAnswer(c->{

            String sassId = c.getArgument(0);
            String customerId = c.getArgument(1);

            CustomerKolDTO customerKolDTO = new CustomerKolDTO();
            customerKolDTO.setSaasId(sassId);
            customerKolDTO.setCustomerId(customerId);
            customerKolDTO.setRole(BusinessOP.kolRole);

            return customerKolDTO;
        }).when(remoteSocialService).getSocialKol(anyString(), anyString());

    }
}
