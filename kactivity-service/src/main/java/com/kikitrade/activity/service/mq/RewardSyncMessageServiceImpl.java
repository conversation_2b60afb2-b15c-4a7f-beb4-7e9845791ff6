package com.kikitrade.activity.service.mq;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.framework.ons.OnsProducer;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
public class RewardSyncMessageServiceImpl implements RewardSyncMessageService<ActivityCustomReward> {

    @Resource
    private OnsProducer onsProducer;
    @Resource
    private TopicConfig topicConfig;

    @Override
    public void send(ActivityCustomReward msg) {
        onsProducer.send(topicConfig.getTopicReward(), JSON.toJSONString(msg));
    }
}
