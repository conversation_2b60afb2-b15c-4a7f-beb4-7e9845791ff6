package com.kikitrade.activity.service.task.filter;

import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.TaskFilter;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component("activityCustomerTaskStatusFilter")
@Order(3)
public class ActivityCustomerTaskStatusFilter implements TaskFilter {

    @Resource
    private RedisService redisService;
    @Resource
    private ActivityTaskService activityTaskService;
    @Resource
    private TaskConfigService taskConfigService;

    @Override
    public void filter(ActivityEventMessage activityEventMessage, TaskConfigDTO config) throws ActivityException {
        /*String obj = redisService.get(RedisKeyConst.TASK_STATUS_KEY.getKey(String.format("%s:%s:%s", config.getTaskId(), activityEventMessage.getCustomerId(), taskConfigService.getCurrencyCycle(config))));
        if(obj != null){
            throw new ActivityException(ActivityResponseCode.TASK_STATUS_COMPLETED);
        }*/
    }

    @Override
    public FilterScope getScope() {
        return FilterScope.COMMON;
    }
}
