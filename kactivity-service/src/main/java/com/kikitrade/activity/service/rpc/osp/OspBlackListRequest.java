package com.kikitrade.activity.service.rpc.osp;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.service.common.config.OspProperties;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

import static com.kikitrade.activity.service.reward.impl.RewardBadgeTccServiceImpl.generateSignature;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/5 17:18
 */
@Data
@Slf4j
public class OspBlackListRequest {

    private static final String APP_KEY = "app_key";

    private OspProperties ospProperties;

    private String cid;
    private String saasId;

    public OspBlackListRequest() {
    }

    public OspBlackListRequest(OspProperties ospProperties) {
        this.ospProperties = ospProperties;
    }

    public OspBlackListRequest build(String saasId, String cid) {
        this.saasId = saasId;
        this.cid = cid;
        return this;
    }

    public Boolean execute() {
        try {
            return checkOspBlackList(this.cid, this.saasId);
        } catch (Exception e) {
            log.error("[OspBlackListRequest] execute exception, cid:{}, error:", cid, e);
            return Boolean.FALSE;
        }
    }

    private Boolean checkOspBlackList(String cid, String saasId) {
        try {
            TreeMap<String, Object> param = new TreeMap<>();
            param.put("scene", 12);
            param.put("customer_id", cid);
            param.put("signature", generateSignature(param, SaasConfigLoader.getConfig(saasId).getAppKey()));
            String query = URLUtil.buildQuery(param, Charset.defaultCharset());
            Headers.Builder headerBuilder = new Headers.Builder()
                    .add("Content-Type", "application/json")
                    .add("saas_id", saasId)
                    .add(APP_KEY, "quests");
            Request request = new Request.Builder()
                    .url(String.format("%s?%s", SaasConfigLoader.getConfig(saasId).getApiHost() + "/v2/customer/verify", query))
                    .headers(headerBuilder.build())
                    .build();
            log.info("[checkOspBlackList] request = {}, param = {}", request, JSON.toJSONString(param));
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                log.info("[checkOspBlackList] ospVerify responseBody:{}", responseBody);
                // {"success":true,"code":"0","msgKey":"success","obj":{"success":true,"code":null,"message":""}}
                if (StringUtils.isNotBlank(responseBody)) {
                    JSONObject resultObj = JSON.parseObject(responseBody);
                    if (Objects.nonNull(resultObj) && resultObj.getBoolean("success") && "0".equals(resultObj.getString("code")) ) {
                        return resultObj.getJSONObject("obj").getBoolean("success");
                    }
                }
            }
        } catch (Exception e) {
            log.error("[checkOspBlackList] ospVerify Exception:{}, error:", this.cid, e);
        }
        return true;
    }
}
