package com.kikitrade.activity.dal.mysql.model;

import com.kikitrade.activity.dal.IdCustomer;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 *
 */
@Getter
@Setter
@Table(name = "luck_fortune_rule")
public class LuckFortuneRule extends BaseModel {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @KeySql(genId = IdCustomer.class)
    private String id;

    /**
     * KYC等级
     */
    @Column(name = "kyc_level")
    private String kycLevel;

    /**
     * 用户类型 kyc_level + user_type 唯一
     */
    @Column(name = "user_type")
    private String userType;

    /**
     * 发放红包最小金额
     */
    @Column(name = "release_min")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private BigDecimal releaseMin;

    /**
     * 发放红包最大金额
     */
    @Column(name = "release_max")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private BigDecimal releaseMax;

    /**
     * 单个红包最大数量
     */
    @Column(name = "release_num_max")
    private Integer releaseNumMax;

    /**
     * 24小时发放累积限额
     */
    @Column(name = "release_account_days")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private BigDecimal releaseAccountDays;

    /**
     * 30天发放累积限额
     */
    @Column(name = "release_account_months")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private BigDecimal releaseAccountMonths;

    /**
     * 单个红包领取最小金额
     */
    @Column(name = "receive_min")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private BigDecimal receiveMin;

    /**
     * 单个红包领取最大金额
     */
    @Column(name = "receive_max")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private BigDecimal receiveMax;

    /**
     * 24小时领取累积限额
     */
    @Column(name = "receive_account_days")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private BigDecimal receiveAccountDays;

    /**
     * 30天领取累积限额
     */
    @Column(name = "receive_account_months")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private BigDecimal receiveAccountMonths;

    /**
     * 24小时领取累积数量限制
     */
    @Column(name = "receive_num_days")
    private Integer receiveNumDays;

    /**
     * 30天领取累积数量限制
     */
    @Column(name = "receive_num_months")
    private Integer receiveNumMonths;

    @Column(name = "status")
    private Integer status;
}
