package com.kikitrade.activity.service.reward.impl;

import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.service.common.config.EarnallianceProperties;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import com.kikitrade.kcustomer.api.model.BindSocialRequest;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("rewardEarnallianceCustomerService")
@Slf4j
public class RewardEarnallianceCustomerServiceImpl extends AbstractRewardTccService implements RewardTccService {

    @Resource
    private KactivityModuleProperties kactivityModuleProperties;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;

    @Override
    public void tryReward(RewardRequest request) throws Exception {
        //通过customerBind判断是否存在绑定关系，如果不存在,则发送绑定请求，然后在发送游戏事件
        CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(request.getSaasId(), request.getCustomerId());
        if(customerBindDTO != null && customerBindDTO.getAbstractAddress() == null){
            BindSocialRequest bindRequests = new BindSocialRequest();
            bindRequests.setSocialId(request.getAddress());
            bindRequests.setUid(request.getCustomerId());
            bindRequests.setSaasId(request.getSaasId());
            bindRequests.setSocialPlatform("address");
            remoteCustomerBindService.bindSocial(bindRequests);
            sendCustomerEvent(request);
        }
    }

    private void sendCustomerEvent(RewardRequest request){
        try {
            EarnallianceProperties properties = EarnallianceUtil.getProperties(kactivityModuleProperties.getEarnalliance());
            Map<String, Object> map = new HashMap<>();
            map.put("gameId", properties.getGameId());
            map.put("identifiers", List.of(Map.of("userId",request.getCustomerId(),"walletAddress", request.getAddress())));

            long now = new Date().getTime();
            String signature = EarnallianceUtil.getSignature(properties.getClientId(), properties.getClientSecret(), now, map);

            Headers.Builder headerBuilder = new Headers.Builder()
                    .add("x-client-id", properties.getClientId())
                    .add("x-timestamp", String.valueOf(now))
                    .add("x-signature", signature);

            HttpUrl.Builder urlBuilder = HttpUrl.parse(properties.getUrl() + "custom-events").newBuilder();

            Request httpRequest = new Request.Builder()
                    .url(urlBuilder.build())
                    .headers(headerBuilder.build())
                    .post(RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(map)))
                    .build();
            log.info("sendCustomerEvent request:{}", httpRequest);
            Response response = HttpPoolUtil.getHttpClient().newCall(httpRequest).execute();
            log.info("sendCustomerEvent response:{}", response.body().string());
        }catch (Exception ex){
            log.error("sendCustomerEvent error", ex);
        }
    }
}
