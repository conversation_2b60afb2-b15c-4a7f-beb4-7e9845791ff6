package com.kikitrade.activity.service.rpc.twitter;

import com.google.gson.reflect.TypeToken;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.twitter.clientlib.TwitterCredentialsOAuth2;
import com.twitter.clientlib.api.TwitterApi;
import com.twitter.clientlib.model.User;
import com.twitter.clientlib.model.UsersFollowingCreateRequest;
import com.twitter.clientlib.model.UsersFollowingCreateResponseData;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 19:52
 */
@Data
@Slf4j
public class FollowingRequest extends AccessToken {
    private TwitterProperties twitterProperties;
    private String twitterTargetUser;

    public FollowingRequest(){}

    public FollowingRequest(TwitterProperties twitterProperties, AccessToken accessToken){
        super(accessToken);
        this.twitterProperties = twitterProperties;
    }

    /**
     * 返回被关注人
     * @return
     */
    public String execute() throws ActivityException{
        try{
            UsersFollowingCreateRequest request = new UsersFollowingCreateRequest();
            request.setTargetUserId(this.twitterTargetUser);

            Response response = getApi(this.accessToken, this.refreshToken, this.twitterProperties)
                    .users()
                    .findUserByUsername(twitterTargetUser)
                    .userFields(Set.of("connection_status"))
                    .buildCall(null)
                    .execute();
            if(!response.isSuccessful() && response.code() == 88){
                log.warn("[twitter] verifyFollowX error:{}", response);
                throw new ActivityException(ActivityResponseCode.ACTIVITY_HOT);
            }
            if(response.isSuccessful() && response.code() != 204){
                Type localVarReturnType = (new TypeToken<UserResponse>() {
                }).getType();
                UserResponse user = com.twitter.clientlib.JSON.deserialize(response.body().string(), localVarReturnType);
                log.info("[twitter] verifyFollowX response:{}", user);
                if(user != null && user.getData() != null && user.getData().getConnectionStatus() != null
                        && user.getData().getConnectionStatus().contains("following")){
                    return user.getData().getId();
                }
            }
            return null;
        }catch (ActivityException ax){
            throw new ActivityException(ActivityResponseCode.ACTIVITY_HOT);
        }catch (Exception ex){
            log.error("[twitter] verifyFollowX error:{},{}", twitterTargetUser, this.userId, ex);
            return null;
        }
    }

    public FollowingRequest buildFollowingRequest(String twitterTargetUser){
        this.twitterTargetUser = twitterTargetUser;
        return this;
    }
}
