package com.kikitrade.activity.service.facade.domain;

import lombok.Data;

import java.io.Serializable;

@Data
public class LotteryItemFacadeDTO implements Serializable {

    //奖励名称
    private String name;
    //奖励类型
    private String currency;
    //投放奖励
    private String amount;
    //投放数量
    private Integer num = 4;
    //中奖概率
    private String percent;
    //是否兜底
    private Boolean isLow;
    //剩余数量
    private Integer remainNum;
    //TOKEN or POINT
    private String awardType;
}
