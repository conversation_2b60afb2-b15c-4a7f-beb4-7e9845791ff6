package com.kikitrade.activity.service.job;

import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReceiveItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneRefundItemBuild;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReleaseItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneRefundItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.service.business.RefundTccService;
import com.kikitrade.activity.luck.service.business.ReleaseService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.BusinessMonitorConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.framework.common.model.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Component
@Slf4j
public class ActivityRefundJob implements SimpleJob {

    @Resource
    @Lazy
    private LuckFortuneReleaseItemBuilder luckFortuneReleaseItemBuilder;
    @Resource
    @Lazy
    private KactivityModuleProperties kactivityModuleProperties;
    @Resource
    @Lazy
    private ReleaseService releaseService;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("REFUND_RELEASE_MESSAGE:start:{}", kactivityModuleProperties.getLuck().getValidMinutes());

        //未领完过期
        String endTime = TimeUtil.getUtcTime(TimeUtil.addMinute(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)), -kactivityModuleProperties.getLuck().getValidMinutes()), TimeUtil.YYYYMMDDHHMMSS);
        //未领完过期
        for (int i = 0; ; i++) {
            Page<LuckFortuneReleaseItem> result = luckFortuneReleaseItemBuilder.findByExpiredTime(ActivityConstant.LuckFortuneReleaseStatus.EXPIRED.getCode(), endTime, i * 100);
            if (result == null) {
                break;
            }
            if (CollectionUtils.isEmpty(result.getRows())) {
                break;
            }
            List<LuckFortuneReleaseItem> refundReleaseList = result.getRows();
            refundReleaseList.forEach(refund -> {
                releaseService.refund(refund);
            });
            if (result.getRows().size() < 100) {
                break;
            }
        }
    }
}