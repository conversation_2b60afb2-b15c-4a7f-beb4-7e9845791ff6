package com.kikitrade.activity.util;

import lombok.Data;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class DateUtil {
    private static final DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm Z");
    public static final DateFormat YYYYMMDDHHMMSS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public static final DateFormat YYYYMMDDHHMM = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    public static final DateFormat YYYYMMDD_000000 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
    public static final DateFormat YYYYMMDD_235959 = new SimpleDateFormat("yyyyMMdd235959");
    public static final DateFormat YYYYMMDDHH_5959 = new SimpleDateFormat("yyyyMMddHH5959");
    public static final DateFormat YYYY_MM_DD = new SimpleDateFormat("yyyy-MM-dd");
    public static final DateFormat YYYYMM = new SimpleDateFormat("yyyy-MM");
    public static final DateFormat YYYYMMDDHH_0000 = new SimpleDateFormat("yyyyMMddHH0000");
    public static final DateFormat YYYYMMDD = new SimpleDateFormat("yyyyMMdd");
    public static final DateFormat YYYYMMDDHH = new SimpleDateFormat("yyyyMMddHH");
    public static final DateFormat YYYYMMDDHHmmss = new SimpleDateFormat("yyyyMMddHHmmss");
    public static final DateFormat DD = new SimpleDateFormat("dd");

    public static Date parseUnitTime(long unitTime){
        return new Date(unitTime);
    }


    /**
     * 将yyyy-MM-dd HH:mm:ss转化为Date
     * @param data
     * @return
     */
    public static Date parseYYYYMMDDHHMMSS(String data){

        try {
            return YYYYMMDDHHMMSS.parse(data);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getCurrentUtcTime(String formatter) {
        return getCurrentUtcTime(new SimpleDateFormat(formatter));
    }
    /**
     * 获取当前UTC时间
     * @param formatter
     * @return
     */
    public static String getCurrentUtcTime(DateFormat formatter) {
        Date l_datetime = new Date();
        TimeZone l_timezone = TimeZone.getTimeZone("GMT-0");
        formatter.setTimeZone(l_timezone);
        return formatter.format(l_datetime);
    }



    public static String getToday(){
        Date now = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        return format.format(now);
    }

    public static String getNow(){
        Date now = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmssSSSS");
        return format.format(now);
    }

    public static String dateToStamp(String s) throws ParseException {
        String res;
        //设置时间模版
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = simpleDateFormat.parse(s);
        long ts = date.getTime();
        res = String.valueOf(ts);
        return res;
    }

    public static Date string2Date(String s) throws ParseException {
        SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd");
        return sdf.parse(s);
    }

    public static void main(String[] args) throws ParseException {
        Date date1 = parseYYYYMMDDHHMMSS("2022-11-11 09:00:00");
        System.out.println(date1);
        Date date2 = parseYYYYMMDDHHMMSS("2022-11-12 09:00:00");
        System.out.println(date2);

        System.out.println(date1.after(date2));
    }


}
