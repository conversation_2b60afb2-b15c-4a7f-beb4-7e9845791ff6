package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.mock.ActivityTaskItemBuilderMock;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.util.BeanUtils;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.framework.ots.TransactionCallback;
import org.mockito.Mockito;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.kikitrade.activity.op.BusinessOP.activityTaskItemList;
import static org.mockito.ArgumentMatchers.*;

@Component
public class ActivityTaskItemBuilderMockImpl implements ActivityTaskItemBuilderMock {

    @Resource
    ActivityTaskItemBuilder activityTaskItemBuilder;

    @Override
    public void findByCustomer() {
        Mockito.doAnswer(c -> {
            String customerId = c.getArgument(0);
            String cycle = c.getArgument(1);
            String taskId = c.getArgument(2);

            Optional<ActivityTaskItem> first = activityTaskItemList.stream().filter(
                    item -> customerId.equals(item.getCustomerId()) &&
                            cycle.equals(item.getCycle()) &&
                            taskId.equals(item.getTaskId())).findFirst();

            if (first.isPresent()) {
                ActivityTaskItem activityTaskItem = first.get();
                return BeanUtils.toBean(activityTaskItem);
            }
            return null;
        }).when(activityTaskItemBuilder).findByCustomer(anyString(), anyString(), anyString());

        Mockito.doAnswer(c -> {
            String customerId = c.getArgument(0);
            String taskId = c.getArgument(1);

            Optional<ActivityTaskItem> first = activityTaskItemList.stream().filter(
                    item -> customerId.equals(item.getCustomerId()) &&
                            taskId.equals(item.getTaskId())).findFirst();

            if (first.isPresent()) {
                ActivityTaskItem activityTaskItem = first.get();
                return BeanUtils.toBean(activityTaskItem);
            }
            return null;
        }).when(activityTaskItemBuilder).findByCustomer(anyString(), anyString());

        Mockito.doAnswer(c -> {
            String customerId = c.getArgument(0);
            String cycle = c.getArgument(1);
            List<String> taskIds = c.getArgument(2);

            List<ActivityTaskItem> collect = activityTaskItemList.stream().filter(
                    item -> taskIds.contains(item.getTaskId()) &&
                            customerId.equals(item.getCustomerId()) &&
                            cycle.equals(item.getCycle())
            ).collect(Collectors.toList());

            return collect;
        }).when(activityTaskItemBuilder).findByCustomer(anyString(), anyString(), anyList());
    }

    @Override
    public void findByCustomerFromIndex() {
        Mockito.doAnswer(c -> {
            String customerId = c.getArgument(0);
            String cycle = c.getArgument(1);
            ActivityTaskConfig config = c.getArgument(2);

            if (!ActivityTaskConstant.TaskCycleEnum.once.name().equals(config.getCycle())) {
                return activityTaskItemBuilder.findByCustomer(customerId, cycle, config.getId());
            }

            return activityTaskItemList.stream()
                    .filter(item -> customerId.equals(item.getCustomerId()) && config.getId().equals(item.getTaskId()))
                    .sorted(Comparator.comparing(ActivityTaskItem::getCycle))
                    .findFirst().orElse(null);
        }).when(activityTaskItemBuilder).findByCustomerFromIndex(anyString(), anyString(), any(ActivityTaskConfig.class));
    }

    @Override
    public void insert() {
        Mockito.doAnswer(c -> {
            ActivityTaskItem activityTaskItem = c.getArgument(0);
            activityTaskItem.setCreated(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
            activityTaskItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));

            String taskId = activityTaskItem.getTaskId();
            String cycle = activityTaskItem.getCycle();
            String customerId = activityTaskItem.getCustomerId();
            String targetId = activityTaskItem.getTargetId();

            Optional<ActivityTaskItem> first = activityTaskItemList.stream().filter(item ->
                    taskId.equals(item.getTaskId()) &&
                            cycle.equals(item.getCycle()) &&
                            customerId.equals(item.getCustomerId()) &&
                            targetId.equals(item.getTargetId())
            ).findFirst();

            if (first.isPresent()) {
                return false;
            }
            activityTaskItemList.add(activityTaskItem);
            return true;

        }).when(activityTaskItemBuilder).insert(any(ActivityTaskItem.class));

    }

    @Override
    public void updateProgress() {
        Mockito.doAnswer(c -> {
            ActivityTaskItem activityTaskItem = c.getArgument(0);

            String taskId = activityTaskItem.getTaskId();
            String cycle = activityTaskItem.getCycle();
            String customerId = activityTaskItem.getCustomerId();

            Optional<ActivityTaskItem> first = activityTaskItemList.stream().filter(item ->
                    taskId.equals(item.getTaskId()) &&
                            cycle.equals(item.getCycle()) &&
                            customerId.equals(item.getCustomerId())).findFirst();

            if (first.isPresent()) {
                ActivityTaskItem dbItem = first.get();
                if (dbItem.getProgress() <= activityTaskItem.getProgress()) {
                    dbItem.setProgress(activityTaskItem.getProgress());
                    dbItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
                    return true;
                }
            }
            return false;
        }).when(activityTaskItemBuilder).updateProgress(any(ActivityTaskItem.class));
    }

    @Override
    public void update() {
        Mockito.doAnswer(c -> {
            ActivityTaskItem activityTaskItem = c.getArgument(0);

            String taskId = activityTaskItem.getTaskId();
            String cycle = activityTaskItem.getCycle();
            String customerId = activityTaskItem.getCustomerId();
            String targetId = activityTaskItem.getTargetId();

            activityTaskItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));

            Optional<ActivityTaskItem> first = activityTaskItemList.stream().filter(item ->
                    taskId.equals(item.getTaskId()) &&
                            cycle.equals(item.getCycle()) &&
                            customerId.equals(item.getCustomerId()) &&
                            targetId.equals(item.getTargetId())).findFirst();

            if (first.isPresent()) {
                ActivityTaskItem dbItem = first.get();
                dbItem.setProgress(activityTaskItem.getProgress());
                dbItem.setModified(activityTaskItem.getModified());
//                org.springframework.beans.BeanUtils.copyProperties(activityTaskItem, dbItem);
                return true;
            }
            return false;
        }).when(activityTaskItemBuilder).update(any(ActivityTaskItem.class));
    }

    @Override
    public void execute() {
        Mockito.doAnswer(c -> {
            ActivityTaskItem partition = c.getArgument(0);
            TransactionCallback<ActivityTaskItem> action = c.getArgument(1);
            boolean b = action.doInTransaction();
            return b;
        }).when(activityTaskItemBuilder).execute(any(), any());
    }


    @Override
    public void incrementProgress() {
        Mockito.doAnswer(c -> {
            ActivityTaskItem taskItem = c.getArgument(0);
            Integer inc = c.getArgument(1);
            String customerId = taskItem.getCustomerId();
            String taskId = taskItem.getTaskId();
            String cycle = taskItem.getCycle();
            String targetId = taskItem.getTargetId();

            ActivityTaskItem activityTaskItemDB = activityTaskItemList.stream().filter(item ->
                    taskId.equals(item.getTaskId()) &&
                            cycle.equals(item.getCycle()) &&
                            customerId.equals(item.getCustomerId()) &&
                            targetId.equals(item.getTargetId())).findFirst().orElse(null);

            if (Objects.nonNull(activityTaskItemDB)) {
                activityTaskItemDB.setProgress(activityTaskItemDB.getProgress() + (inc != null ? inc : 1));
                return activityTaskItemDB.getProgress().longValue();
            }
            return -1L;
        }).when(activityTaskItemBuilder).incrementProgress(any(ActivityTaskItem.class), any());

    }

    @Override
    public void findDetailByCustomer() {
        Mockito.doAnswer(c -> {
            String customerId = c.getArgument(0);
            String cycle = c.getArgument(1);
            String taskId = c.getArgument(2);
            String targetId = c.getArgument(3);

            ActivityTaskItem activityTaskItemDB = activityTaskItemList.stream()
                    .filter(item -> taskId.equals(item.getTaskId()) &&
                            cycle.equals(item.getCycle()) &&
                            customerId.equals(item.getCustomerId()) &&
                            targetId.equals(item.getTargetId())).findFirst().orElse(null);

            return BeanUtil.copyProperties(activityTaskItemDB, ActivityTaskItem::new);
        }).when(activityTaskItemBuilder).findDetailByCustomer(anyString(), anyString(), anyString(), anyString());
    }

    @Override
    public void updateStatus() {
        Mockito.doAnswer(c -> {
            ActivityTaskItem activityTaskItem = c.getArgument(0);
            activityTaskItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));

            String customerId = activityTaskItem.getCustomerId();
            String taskId = activityTaskItem.getTaskId();
            String cycle = activityTaskItem.getCycle();
            String targetId = activityTaskItem.getTargetId();

            ActivityTaskItem activityTaskItemDB = activityTaskItemList.stream().filter(item ->
                    taskId.equals(item.getTaskId()) &&
                            cycle.equals(item.getCycle()) &&
                            customerId.equals(item.getCustomerId()) &&
                            targetId.equals(item.getTargetId())).findFirst().orElse(null);

            if (Objects.nonNull(activityTaskItemDB)) {
                activityTaskItemDB.setModified(activityTaskItem.getModified());
                activityTaskItemDB.setCompleteTime(activityTaskItem.getCompleteTime());
                activityTaskItemDB.setStatus(activityTaskItem.getStatus());
                return true;
            }
            return false;
        }).when(activityTaskItemBuilder).updateStatus(any(ActivityTaskItem.class));
    }

    @Override
    public void findByTarget() {
        Mockito.doAnswer(c -> {
            String platform = c.getArgument(0);
            String socialCustomerId = c.getArgument(1);
            String cycle = c.getArgument(2);
            String taskId = c.getArgument(3);

            ActivityTaskItem activityTaskItemDB = activityTaskItemList.stream().filter(item ->
                    taskId.equals(item.getTaskId()) &&
                            cycle.equals(item.getCycle()) &&
                            socialCustomerId.equals(item.getTargetId())).findFirst().orElse(null);

            return BeanUtil.copyProperties(activityTaskItemDB, ActivityTaskItem::new);
        }).when(activityTaskItemBuilder).findByTarget(anyString(), anyString(), anyString(), anyString());
    }

    @Override
    public void findByIdList() {
        Mockito.doAnswer(c -> {
            String customerId = c.getArgument(0);
            String taskId = c.getArgument(1);
            String startTime = c.getArgument(2);
            String endTime = c.getArgument(3);
            int limit = c.getArgument(4);
            List<ActivityTaskItem> result = activityTaskItemList.stream()
                    .filter(item ->
                            customerId.equals(item.getCustomerId()) &&
                                    taskId.equals(item.getTaskId()) &&
                                    "-1".equals(item.getTargetId()) &&
                                    item.getCycle().compareTo(startTime) >= 0 &&
                                    item.getCycle().compareTo(endTime) <= 0)
                    .limit(limit)
                    .collect(Collectors.toList());
            return Page.with(0, limit, result.size(), result);
        }).when(activityTaskItemBuilder).findByIdList(anyString(), anyString(), anyString(), anyString(), anyInt());
    }
}
