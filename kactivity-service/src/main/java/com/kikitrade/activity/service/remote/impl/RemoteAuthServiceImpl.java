package com.kikitrade.activity.service.remote.impl;

import com.kikitrade.activity.api.RemoteAuthService;
import com.kikitrade.activity.api.model.AuthRequest;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.auth.AuthService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/11 11:47
 */
@DubboService
@Slf4j
public class RemoteAuthServiceImpl implements RemoteAuthService {

    @Resource
    private AuthService authService;

    @Override
    @Deprecated
    public Token auth(String saasId, String platform, String code, String redirectUri, String customerId) throws ActivityException {
        return authService.auth(saasId, platform, code, redirectUri, customerId, null, null, null);
    }

    @Override
    public Token auth(AuthRequest authRequest) throws ActivityException {
        return authService.auth(authRequest.getSaasId(), authRequest.getPlatform(), authRequest.getCode(), authRequest.getRedirectUri(), authRequest.getCustomerId(), authRequest.getTwitterId(), authRequest.getSocialId(), authRequest.getSocialName());
    }

    @Override
    public Boolean sendVerifyCode(String saasId, String customerId, String customerName, String channel, String receiver, String sceneCode) throws ActivityException {
        return authService.sendVerifyCode(saasId, customerId, customerName, channel, receiver, sceneCode);
    }
}
