package com.kikitrade.activity.util;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @version Created in 2023/4/27 15:40
 */
public class HexUtils {
    public static String bigInterToHex(BigInteger bigInteger) {
        return "0x" + bigInterToHexNoPrefix(bigInteger);
    }

    public static String bigInterToHexNoPrefix(BigInteger bigInteger) {
        return bigInteger.toString(16);
    }

    public static BigInteger hexToBigInteger(String hex) {
        if (hex.startsWith("0x")) {
            return new BigInteger(hex.substring(2), 16);
        }
        return new BigInteger(hex, 16);
    }

    public static Long hexToLong(String hex) {
        if (hex.startsWith("0x")) {
            return Long.parseLong(hex.substring(2), 16);
        }
        return Long.parseLong(hex, 16);
    }

    public static String concatHex(String ...hex){
        StringBuilder res = new StringBuilder("0x");
        for (String s : hex) {
            res.append(s.replace("0x",""));
        }
        return res.toString();
    }

    public static String removeLeadingZerosFromHex(String hex) {
        return hex.replaceFirst("0x0+(?!$)", "0x");
    }
}
