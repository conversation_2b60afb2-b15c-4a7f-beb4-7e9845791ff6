package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteAssetOperateServiceDubboMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.asset.api.RemoteAssetOperateService;
import com.kikitrade.asset.model.exception.AssetException;
import com.kikitrade.asset.model.request.AssetTransferInRequest;
import com.kikitrade.asset.model.request.AssetTransferOutRequest;
import com.kikitrade.asset.model.response.AssetOperateResponse;
import com.kikitrade.asset.model.response.AssetResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Slf4j
@Component
public class RemoteAssetOperateServiceDubboMockImpl implements RemoteAssetOperateServiceDubboMock {
    @Resource
    RemoteAssetOperateService remoteAssetOperateService;


    @Override
    public void transferOut() throws AssetException {
        Mockito.doAnswer(c -> {
            BusinessOP.assetTransferOutRequest = c.getArgument(0);
            return null;
        }).when(remoteAssetOperateService).transferOut(Mockito.any(AssetTransferOutRequest.class));
    }

    @Override
    public void transferIn() throws AssetException {
        Mockito.doAnswer(c -> {
            AssetTransferInRequest assetTransferInRequest = c.getArgument(0);
            BusinessOP.assetTransferInRequestList.add(assetTransferInRequest);
            return AssetOperateResponse.builder().code(AssetResponseCode.SUCCESS).build();
        }).when(remoteAssetOperateService).transferIn(Mockito.any(AssetTransferInRequest.class));


    }

    @Override
    public void transferInError() throws AssetException {
        Mockito.doAnswer(c -> {
            throw new RuntimeException("remoteAssetOperateService.transferIn.error");
        }).when(remoteAssetOperateService).transferIn(Mockito.any(AssetTransferInRequest.class));
    }
}
