package com.kikitrade.activity.service.config;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/28 15:53
 */
@Data
public class SaasConfig implements Serializable {

    /**
     * 是否允许负资产
     */
    private Boolean allowNegativeAsset = Boolean.FALSE;
    /**
     * 是否需要进行黑名单校验
     */
    private Boolean checkBlackList = Boolean.FALSE;

    /**
     * 接入方 api 域名
     */
    private String apiHost;
    private String ospAppKey;
    private String appKey;
    private String ospAppId;
    /**
     * 是否分成积分
     */
    private Boolean dividePoint = false;
    private Double dividePointPercent;
    private String twitterStrategy;
    private String discordStrategy;
    private String googleStrategy;
    private String facebookStrategy;
    private String lineStrategy;
    private String emailStrategy;
    private String tgStrategy;
    private String walletStrategy;
    private String iconSuffix = "webp";
    private Integer twitterAccountCreateDays = 0;
    private String ospNftCallback;
    /**
     * deek成就系统域名，用来发放徽章
     */
    private String anchorHost;
    private String clientId;
    private String chainId;

    private Boolean saveBindInfo=false;
}
