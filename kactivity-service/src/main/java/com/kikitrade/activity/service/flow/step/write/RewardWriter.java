package com.kikitrade.activity.service.flow.step.write;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.model.RewardRequest;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.BusinessMonitorConstant;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.config.RateLimiterConfig;
import com.kikitrade.activity.service.reward.RewardService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.remoting.TimeoutException;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-16 11:34
 */
@Component
@StepScope
@Slf4j
public class RewardWriter implements ItemWriter<ActivityCustomReward> {

    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private RateLimiterConfig rateLimiterConfig;
    @Resource
    private RewardService rewardService;

    @Override
    public void write(Chunk<? extends ActivityCustomReward> chunk) throws Exception {
        List<? extends ActivityCustomReward> items = chunk.getItems();
        items.stream().filter(Objects::nonNull).forEach(reward -> {
            rateLimiterConfig.acquire("rewardService");
            //通过dubbo接口调用
            RewardRequest rewardRequest = RewardRequest.builder()
                    .rewardId(reward.getBusinessId())
                    .customerId(reward.getCustomerId())
                    .currency(reward.getCurrency())
                    .amount(StringUtils.isBlank(reward.getAmount()) ? null : new BigDecimal(reward.getAmount()))
                    .type(reward.getRewardType())
                    .build();
            ActivityCustomReward customReward = activityCustomRewardStoreBuilder.findByPrimaryId(reward.getBatchId(), reward.getCustomerId(), reward.getSeq());
            reward.setActivityName(customReward.getActivityName());
            reward.setNickName(customReward.getNickName());
            try{
                reward(rewardRequest, reward);
            }catch (Exception ex){
                log.error("发奖失败:{}",rewardRequest, ex);
            }
        });
    }

    private void reward(RewardRequest rewardRequest, ActivityCustomReward activityCustomReward){
        log.info("remoteRewardService request:{}", JSON.toJSONString(rewardRequest));
        try{
            if(rewardRequest.getAmount() == null || BigDecimal.ZERO.compareTo(rewardRequest.getAmount()) >= 0){
                activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.AWARD_FAILED.name());
                activityCustomReward.setMessage("reward amount must be greater than zero");
                activityCustomRewardStoreBuilder.updateStatus(activityCustomReward);
                return;
            }
            //调用发奖接口
            com.kikitrade.activity.service.reward.model.RewardRequest request = com.kikitrade.activity.service.reward.model.RewardRequest.builder()
                    .rewardId(rewardRequest.getRewardId())
                    .customerId(rewardRequest.getCustomerId())
                    .currency(rewardRequest.getCurrency())
                    .amount(rewardRequest.getAmount())
                    .type(rewardRequest.getType())
                    .nickName(activityCustomReward.getNickName())
                    .side(StringUtils.isNotBlank(activityCustomReward.getSide()) ? ActivityConstant.SideEnum.valueOf(activityCustomReward.getSide()) : null)
                    .activityName(activityCustomReward.getActivityName())
                    .businessType(activityCustomReward.getBusinessType())
                    .build();

            ActivityResponse response = rewardService.reward(request);
            activityCustomReward.setStatus(response.getCode() == ActivityResponseCode.SUCCESS ? ActivityConstant.RewardStatusEnum.AWARD_SUCCESS.name()
                    : ActivityConstant.RewardStatusEnum.AWARD_FAILED.name());
            if(response.getCode() != ActivityResponseCode.SUCCESS){
                log.info("remoteRewardService response:{}",response);
                activityCustomReward.setMessage(response.getMsg());
            }
            activityCustomReward.setRewardTime(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            activityCustomRewardStoreBuilder.updateStatus(activityCustomReward);
        }catch (RpcException | TimeoutException e){
            log.error("remoteRewardService rpc exception，rewardId:{}", rewardRequest.getRewardId(), e);
            activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.NOT_AWARD.name());
            activityCustomRewardStoreBuilder.updateStatus(activityCustomReward);
        }
        catch (Exception ex){
            log.error("remoteRewardService error，rewardId:{}", rewardRequest.getRewardId(), ex);
            activityCustomReward.setMessage(ex.getMessage());
            activityCustomReward.setStatus(ActivityConstant.RewardStatusEnum.AWARD_FAILED.name());
            activityCustomRewardStoreBuilder.updateStatus(activityCustomReward);
        }
    }
}
