package com.kikitrade.activity.dal.mysql.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.kikitrade.activity.model.FiatDepositActivityConfig;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/7/11 17:07
 */
@Getter
@Setter
@Table(name = "activity_rule_map")
public class ActivityRuleMap extends BaseModel implements Comparable<ActivityRuleMap> {


    //activity id
    @Column(name = "activity_id", nullable = false)
    private Integer activity_id;

    //rule id
    @Column(name = "rule_id", nullable = false)
    private Integer rule_id;

    //rule_name for redis use
    @Column(name = "rule_name", nullable = false)
    private String rule_name;

    //规则优先级，哪些规则优先执行
    @Column(name = "priority", nullable = false)
    private Integer priority;

    //0-失效 1-生效
    @Column(name = "status", nullable = false)
    private Integer status;

    //rule param json
    @Column(name = "params")
    private String params;

    @Transient
    private JSONObject paramsJson;

    @Transient
    private List<FiatDepositActivityConfig> configList;

    /**
     * 把params解析成JSONObject格式，并返回
     * @return
     */
    public JSONObject fetchParamsJson() {
        if (paramsJson == null && StringUtils.isNotBlank(params)) {
            try {
                paramsJson = JSONObject.parseObject(params);
            } catch (Exception e) {
                paramsJson = new JSONObject();
                e.printStackTrace();
            }
        }
        return paramsJson == null ? new JSONObject() : paramsJson;
    }

    /**
     * 解析出paramsObj中的configList字段，并返回
     * @return
     */
    public List<FiatDepositActivityConfig> fetchConfigList() {
        if (configList == null) {
            fetchParamsJson();
            try {
                if (paramsJson != null && paramsJson.containsKey("configList")) {
                    configList = paramsJson.getObject("configList", new TypeReference<List<FiatDepositActivityConfig>>() {
                    });
                } else {
                    configList = new ArrayList<>();
                }
            } catch (Exception e) {
                e.printStackTrace();
                configList = new ArrayList<>();
            }
        }
        return configList;
    }

    @Override
    public int compareTo(final ActivityRuleMap rule) {
        if (getPriority() < rule.getPriority()) {
            return -1;
        } else if (getPriority() > rule.getPriority()) {
            return 1;
        } else {
            return getRule_id().compareTo(rule.getRule_id());
        }
    }
}
