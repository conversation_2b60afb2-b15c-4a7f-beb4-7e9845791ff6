package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityRules;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 */
public interface ActivityRulesDao extends BaseDao<ActivityRules, String> {


    int insert(ActivityRules aa);

    int update(ActivityRules aa);

    int deleteById(Integer id);

    int updateStatus(@Param("id") Integer id, @Param("status") Integer status);

    ActivityRules findById(Integer id);

    List<ActivityRules> findAll();

}
