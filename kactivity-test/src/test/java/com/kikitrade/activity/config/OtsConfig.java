package com.kikitrade.activity.config;//package com.kikitrade.activity.config;
//
//import com.alicloud.openservices.tablestore.SyncClient;
//import com.kikitrade.framework.ots.WideColumnSchemaManager;
//import com.kikitrade.framework.ots.WideColumnSchemaManagerImpl;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
////@ConditionalOnProperty(prefix = "local.ots", name = "endpoint")
//public class OtsConfig {
//    @Bean
//    public SyncClient syncClient() {
//        final String endPoint = "https://vb8pfb2aws.cn-hangzhou.ots.aliyuncs.com";
//        final String accessKeyId = "LTAI5tAsbc131DvzeyuFYH2j";
//        // final String accessKeySecret = "******************************";
//        final String accessKeySecret = "******************************";
//        final String instanceName = "vb8pfb2aws";
//        return new SyncClient(endPoint, accessKeyId, accessKeySecret, instanceName);
//    }
//
//    @Bean(name = "wideColumnSchemaManager")
//    public WideColumnSchemaManager wideColumnSchemaManager(SyncClient syncClient) {
//        final WideColumnSchemaManagerImpl wideColumnSchemaManager = new WideColumnSchemaManagerImpl(syncClient, true);
//        return wideColumnSchemaManager;
//    }
//
//
//}
