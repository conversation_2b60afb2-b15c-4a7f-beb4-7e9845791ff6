package com.kikitrade.activity.service.common.strategy.discord;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.config.SaasConfig;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4 17:10
 */
@Component
@Slf4j
public class DiscordCheckTrexStrategyService implements DiscordSaasStrategyService {

    @Resource
    private RemoteCustomerBindService remoteCustomerBindService;

    @Override
    public String strategy() {
        return SaasStrategyConstant.DiscordStrategyEnum.CHECK_TREX.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        CustomerBindDTO customerBindDTO =
            remoteCustomerBindService.findByUid(request.getSaasId(), request.getCustomerId());
        String passportId = getPassportId(request.getSaasId(), request.getAuthId());
        if (passportId != null && !passportId.equals(customerBindDTO.getCid())) {
            log.info("discord check trex auth repeat:{}", request);
            throw new ActivityException(ActivityResponseCode.AUTH_REPEAT, ActivityResponseCode.AUTH_REPEAT.getKey());
        }
    }

    private String getPassportId(String saasId, String socialUserId) {
        try {
            SaasConfig config = SaasConfigLoader.getConfig(saasId);
            HttpUrl.Builder urlBuilder =
                HttpUrl.parse(config.getApiHost() + "/v1/s2s/customers/queryBySocialInfo").newBuilder();
            // 添加查询参数
            urlBuilder.addQueryParameter("socialUserId", socialUserId).addQueryParameter("platform", OpenSocialPlatformEnum.discord.name());

            Headers.Builder headerBuilder = new Headers.Builder().add("app_key", config.getAppKey());
            Request request = new Request.Builder().url(urlBuilder.build()).headers(headerBuilder.build()).build();
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                log.info("[DiscordCheckTrexStrategyService] getPassportId response:{}", jsonObject);
                return Optional.ofNullable(jsonObject.getJSONObject("obj")).map(o -> o.getString("passportId"))
                    .orElse((null));
            }
            log.info("[DiscordCheckTrexStrategyService] getPassportId failed. socialUserId:{}, response:{}", socialUserId, response);
            return null;
        } catch (Exception e) {
            log.error("[DiscordCheckTrexStrategyService] getPassportId Exception:{}, error:", socialUserId, e);
            return null;
        }
    }
}
