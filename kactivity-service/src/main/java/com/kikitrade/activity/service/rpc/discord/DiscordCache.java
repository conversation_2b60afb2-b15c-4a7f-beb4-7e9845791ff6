package com.kikitrade.activity.service.rpc.discord;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import com.alibaba.fastjson.JSON;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/4 18:32
 */
public class DiscordCache {

    private static Cache<String, String> cache = CacheUtil.newLRUCache(50);

    public static void put(String key, String value){
        cache.put(key, value);
    }

    public static String get(String key){
        return cache.get(key);
    }

    public static <T> T get(String key, Class<T> tClass){
        String s = cache.get(key);
        return s != null ? JSON.parseObject(s, tClass) : null;
    }

    public static <T> List<T> getList(String key, Class<T> tClass){
        String s = cache.get(key);
        return s != null ? JSON.parseArray(s, tClass) : null;
    }
}
