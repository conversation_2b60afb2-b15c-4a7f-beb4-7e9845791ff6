package com.kikitrade.activity.dal.tablestore.builder;

import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.dal.tablestore.model.ActivityUserTotalData;
import com.kikitrade.framework.ots.RangeResult;

import java.util.Date;
import java.util.List;


public interface ActivityUserTotalDataBuilder {

    RangeResult<ActivityUserTotalData> list(Date batchNo, Integer activityId, String currency, ActivityConstant.RecordStatus status, PrimaryKey nextToken, int limit);

    boolean batchUpdate(List<ActivityUserTotalData> records);

    ActivityUserTotalData getRow(ActivityUserTotalData data);

    boolean putRow(ActivityUserTotalData data);

    boolean updateRow(ActivityUserTotalData data);

    boolean deleteRow(ActivityUserTotalData data);
}
