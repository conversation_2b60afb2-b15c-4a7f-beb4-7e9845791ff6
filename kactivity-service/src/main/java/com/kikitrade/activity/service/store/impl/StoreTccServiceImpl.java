package com.kikitrade.activity.service.store.impl;

import com.alibaba.fastjson.JSON;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryStoreLogDao;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryStoreLog;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityLotteryDetailConfigService;
import com.kikitrade.activity.service.model.StoreInfo;
import com.kikitrade.activity.service.store.StoreService;
import com.kikitrade.activity.service.store.StoreTccService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 库存服务
 */
@Service
@Slf4j
public class StoreTccServiceImpl implements StoreTccService {

    @Resource
    private RedisService redisService;
    @Resource
    private ActivityLotteryDetailConfigService activityLotteryDetailConfigService;
    @Resource
    private ActivityLotteryStoreLogDao activityLotteryStoreLogDao;
    @Resource
    private StoreService storeService;

    /**
     * 扣减库存
     * @param itemId 抽奖记录id
     * @param storeInfo
     * @return
     */
    @Override
    @Compensation(cancelMethod = "cancelDecrease", confirmMethod = "confirmDecrease")
    public ActivityConstant.StoreStatusEnum tryDecrease(String itemId, StoreInfo storeInfo) {
        log.info("storeInfo：{}", JSON.toJSONString(storeInfo));
        //库存小于0，返回兜底奖品，不需要扣减库存
        if(storeInfo == null){
            return ActivityConstant.StoreStatusEnum.LIMITLESS;
        }
        if(storeInfo.getNum() <= 0){
            return ActivityConstant.StoreStatusEnum.NOT_ENOUGH;
        }
        boolean success = storeService.decreaseStore(storeInfo.getSkuId(), storeInfo.getStoreNo(), itemId);
        return success ? ActivityConstant.StoreStatusEnum.ENOUGH : ActivityConstant.StoreStatusEnum.NOT_ENOUGH;
    }

    public void cancelDecrease(String itemId, StoreInfo storeInfo) {
        ActivityLotteryStoreLog log = activityLotteryStoreLogDao.selectByPrimaryKey(itemId);
        //本事务失败，log == null， 其他事务失败，status == 0
        if(storeInfo == null || log == null){
            return;
        }
        if(log.getStatus() == 0){
            storeService.increaseStore(storeInfo.getSkuId(), storeInfo.getStoreNo(), 1);
            log.setStatus(-1);
            activityLotteryStoreLogDao.updateByPrimaryKey(log);
        }
    }

    public void confirmDecrease(String itemId, StoreInfo storeInfo) {
        ActivityLotteryStoreLog log = activityLotteryStoreLogDao.selectByPrimaryKey(itemId);
        //本事务失败，log == null， 其他事务失败，status == 0
        if(log != null && log.getStatus() == 0){
            log.setStatus(1);
            activityLotteryStoreLogDao.updateByPrimaryKey(log);
        }
    }
}
