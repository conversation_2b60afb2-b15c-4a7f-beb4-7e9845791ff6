package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 投放资源追逐记录
 */
@Data
@Table(name = "precision_track")
public class PrecisionTrack implements Serializable {

    @PartitionKey(name = "resource_id")
    private String resourceId; // 资源标识

    @PartitionKey(name = "cid")
    private String cid; // 第三方用户标识

    @Column(name = "resource_author_id")
    private String resourceAuthorId; // 资源用户id

    @Column(name = "resource_author_name")
    private String resourceAuthorName; // 资源用户name

    @Column(name = "operate_type")
    private String operateType; // 用户行为

    @Column(name = "metrics", type = Column.Type.DOUBLE)
    private double metrics; // 用户打分分数

    @Column(name = "time", type = Column.Type.INTEGER)
    private long time; // 记录时间

    @Column(name = "platform")
    private String platform;    //平台

    @Column(name = "saas_id")
    private String saasId; //saasId
}
