package com.kikitrade.activity.advice;

import com.kikitrade.activity.op.BusinessOP;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Component
@Aspect
@Slf4j
public class ActivityLotteryPoolConfigDaoAdvice {

    @Pointcut("execution(* com.kikitrade.activity.dal.mysql.dao.ActivityLotteryPoolConfigDao.decreaseStoreNum(..))")
    public void decreaseStoreNumPt() {
    }

    @Around("decreaseStoreNumPt()")
    public Object getRandom(ProceedingJoinPoint pjp) throws Throwable {

        if("activityLotteryPoolConfigDao.decreaseStoreNum.ERR".equals(BusinessOP.pointBlast)){
            log.info("-----定点爆破:activityLotteryPoolConfigDao.decreaseStoreNum");
            throw new RuntimeException("-----activityLotteryPoolConfigDao.decreaseStoreNum.ERR");
        }

        Object ret = pjp.proceed();
        //返回抽奖号
        return ret;
    }








}
