package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteBlockBusinessServiceMock;
import com.kikitrade.kcustomer.api.model.block.SecurityVerifyRequest;
import com.kikitrade.kcustomer.api.model.block.SecurityVerifyResponse;
import com.kikitrade.kcustomer.api.service.RemoteBlockBusinessService;
import jakarta.annotation.Resource;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import static org.mockito.ArgumentMatchers.any;

@Component
public class RemoteBlockBusinessServiceMockImpl implements RemoteBlockBusinessServiceMock {
    @Resource
    RemoteBlockBusinessService remoteBlockBusinessService;

    @Override
    public void verify() {

        Mockito.doAnswer(c -> {
            SecurityVerifyRequest securityVerifyRequest = c.getArgument(0);
            return SecurityVerifyResponse.builder().success(true).build();
        }).when(remoteBlockBusinessService).verify(any());

    }
}
