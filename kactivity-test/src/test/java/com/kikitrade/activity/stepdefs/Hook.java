package com.kikitrade.activity.stepdefs;

import com.kikitrade.accounting.api.*;
import com.kikitrade.accounting.api.domain.activity.RemoteAccountingActivityService;

import com.kikitrade.activity.dal.mysql.dao.*;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.mock.*;
import com.kikitrade.activity.mock.impl.ActivityEntityDaoMock;
//import com.kikitrade.activity.mock.impl.RewardDivideListenerMockImpl;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.reward.impl.CustomerServiceImpl;
import com.kikitrade.asset.api.RemoteAssetOperateService;

import com.kikitrade.kcustomer.api.service.*;
import com.kikitrade.ksocial.api.service.RemoteSocialService;

import com.kikitrade.trade.api.RemoteTradingService;

import io.cucumber.java.After;
import io.cucumber.java.Before;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import jakarta.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Slf4j
public class Hook {

    @Resource
    RemoteCustomerServiceDubboMock remoteCustomerServiceDubboMock;
    @Resource
    RemoteSocialService remoteSocialService;
    @Resource
    ActivityLotteryItemBuilderMock activityLotteryItemBuilderMock;
    @Resource
    RemoteMemberServiceDubboMock remoteMemberServiceDubboMock;
    @Resource
    SeqGeneraterServiceMock seqGeneraterServiceMock;
    @Resource
    RemoteProductAccountService remoteProductAccountService;
    @Resource
    RemoteProductAccountServiceDubboMock remoteProductAccountServiceDubboMock;
    @Resource
    ActivityCustomRewardStoreBuilderMock activityCustomRewardStoreBuilderMock;
    @Resource
    RemoteAccountingOperateService remoteAccountingOperateService;
    @Resource
    RemoteAccountingOperateServiceDubboMock remoteAccountingOperateServiceDubboMock;
    @Resource
    RemoteCustomerExtraService remoteCustomerExtraService;
    @Resource
    RemoteCustomerExtraServiceDubboMock remoteCustomerExtraServiceDubboMock;
    //@Resource
    RemoteNotificationService remoteNotificationService;
    //@Resource
    RemoteNotificationServiceDubboMock remoteNotificationServiceDubboMock;
    @Resource
    BusinessOP businessOP;
    @Resource
    RemoteAssetOperateService remoteAssetOperateService;
    @Resource
    RemoteAssetOperateServiceDubboMock remoteAssetOperateServiceDubboMock;
    @Resource
    RewardSyncMessageServiceMock rewardSyncMessageServiceMock;
    @Resource
    ActivityLotteryConfigTestDao activityLotteryConfigTestDao;
    @Resource
    ActivityLotteryDetailConfigTestDao activityLotteryDetailConfigTestDao;
    @Resource
    ActivityLotteryPoolConfigTestDao activityLotteryPoolConfigTestDao;
    @Resource
    ActivityLotteryStoreLogTestDao activityLotteryStoreLogTestDao;
    @Resource
    ClientExchangeServiceMock clientExchangeServiceMock;
    @Resource
    CurrencyClientMock currencyClientMock;
    @Resource
    RemoteSocialServiceDubboMock remoteSocialServiceDubboMock;
    @Resource
    RedisService redisService;
    @Resource
    LuckFortuneRuleTestDao luckFortuneRuleTestDao;
    @Resource
    TickerClientMock tickerClientMock;
    @Resource
    RemoteTradingService remoteTradingService;
    @Resource
    RemoteTradingServiceDubboMock remoteTradingServiceDubboMock;
    @Resource
    LuckFortuneReleaseItemBuilderMock luckFortuneReleaseItemBuilderMock;
    @Resource
    LuckFortuneReceiveItemBuilderMock luckFortuneReceiveItemBuilderMock;
    @Resource
    RemoteAccountingActivityService remoteAccountingActivityService;
    @Resource
    RemoteAccountingActivityServiceDubboMock remoteAccountingActivityServiceDubboMock;
    @Resource
    RemoteCustomerInviteServiceDubboMock remoteCustomerInviteServiceDubboMock;
    @Resource
    LuckFortuneRefundItemBuildMock luckFortuneRefundItemBuildMock;
    @Resource
    OnsProducerMock onsProducerMock;
    @Resource
    ActivityTaskItemBuilderMock activityTaskItemBuilderMock;
    //    @Resource
//    ActivityTaskDetailBuilderMock activityTaskDetailBuilderMock;
    @Resource
    ActivityTaskConfigTestDao activityTaskConfigTestDao;
    @Resource
    ActivityBatchBuilderMock activityBatchBuilderMock;
    @Resource
    ActivityEntityDaoMock activityEntityDaoMock;
    @Resource
    RewardCsvServiceImplMock rewardCsvServiceImplMock;
    @Resource
    OdpsMock odpsMock;
    @Resource
    OdpsReaderMock odpsReaderMock;
    @Resource
    RemoteBlockBusinessServiceDubboMock remoteBlockBusinessServiceDubboMock;
    @Resource
    RemoteQuotaConfigServiceDubboMock remoteQuotaConfigServiceDubboMock;
    @Resource
    RemoteQuotaServiceDubboMock remoteQuotaServiceDubboMock;
    @Resource
    TaskConfigBuilderMock taskConfigBuilderMock;
    @Resource
    RemoteCustomerBindServiceMock remoteCustomerBindServiceMock;
    @Resource
    RewardBadgeTccServiceImplMock rewardBadgeTccServiceImplMock;
    @Resource
    CustomerQuestionSetsBuilderMock customerQuestionSetsBuilderMock;
//    @Resource
//    RewardDivideListenerMockImpl rewardDivideListenerMock;
//    @Resource
//    RewardSetsTccServiceImplMock rewardSetsTccServiceImplMock;

    @Resource
    ActivityTaskStatisBuilderMock activityTaskStatisBuilderMock;
    @Resource
    ThreePlatformFilterMock threePlatformFilterMock;

    /**
     * scenario 前运行
     */
    @Before
    public void before() throws Exception {

        //#######################设置mock###############################
        activityLotteryItemBuilderMock.countByCustomer();
        activityLotteryItemBuilderMock.insert();
        activityLotteryItemBuilderMock.findById();
        activityLotteryItemBuilderMock.update();

        remoteMemberServiceDubboMock.membership();
        remoteMemberServiceDubboMock.questsLaddersByLevel();

        seqGeneraterServiceMock.next();

        remoteProductAccountServiceDubboMock.freeze();
        remoteProductAccountServiceDubboMock.getProductAccountConfig();
        remoteProductAccountServiceDubboMock.createConfig();
        remoteProductAccountServiceDubboMock.updateProductAccountConfig();

        remoteQuotaConfigServiceDubboMock.upsert();
        remoteQuotaServiceDubboMock.freeze();
        remoteQuotaServiceDubboMock.unfreezeDeduct();
        remoteQuotaServiceDubboMock.unfreeze();
        remoteQuotaServiceDubboMock.deduct();
        remoteQuotaServiceDubboMock.check();


        //customerMiscBuilderMock.getNickName();

        activityCustomRewardStoreBuilderMock.insert();
        activityCustomRewardStoreBuilderMock.findByPrimaryId();
        activityCustomRewardStoreBuilderMock.updateStatus();
        activityCustomRewardStoreBuilderMock.findByBatchIdAndStatus();
        activityCustomRewardStoreBuilderMock.existByBatchIdAndStatus();

        remoteCustomerServiceDubboMock.getById();

        remoteAccountingOperateServiceDubboMock.transfer();
        remoteAccountingOperateServiceDubboMock.freeze();
        remoteAccountingOperateServiceDubboMock.unfreeze();

        remoteCustomerExtraServiceDubboMock.getById();

        //remoteNotificationServiceDubboMock.send();

        remoteAssetOperateServiceDubboMock.transferOut();
        remoteAssetOperateServiceDubboMock.transferIn();

        rewardSyncMessageServiceMock.send();

        clientExchangeServiceMock.exchangeByPrecision();

        currencyClientMock.get();

        remoteSocialServiceDubboMock.getSocialKol();

        tickerClientMock.get();

        remoteTradingServiceDubboMock.transferBalance();

        luckFortuneReleaseItemBuilderMock.insert();
        luckFortuneReleaseItemBuilderMock.updateStatusToDrawing();
        luckFortuneReleaseItemBuilderMock.updateStatusToInvalid();
        luckFortuneReleaseItemBuilderMock.findById();
        luckFortuneReleaseItemBuilderMock.findByExpiredTime();
        luckFortuneReleaseItemBuilderMock.update();

        luckFortuneReceiveItemBuilderMock.findByCustomerIdAndReleaseId();
        luckFortuneReceiveItemBuilderMock.insert();
        luckFortuneReceiveItemBuilderMock.findById();
        luckFortuneReceiveItemBuilderMock.update();
        luckFortuneReceiveItemBuilderMock.findByReleaseId();

        remoteAccountingActivityServiceDubboMock.transfer();

        remoteCustomerInviteServiceDubboMock.bindRelation();

        luckFortuneRefundItemBuildMock.findObject();
        luckFortuneRefundItemBuildMock.insert();
        luckFortuneRefundItemBuildMock.update();

        onsProducerMock.send();

        activityTaskItemBuilderMock.findByCustomer();
        activityTaskItemBuilderMock.findByCustomerFromIndex();
        activityTaskItemBuilderMock.insert();
        activityTaskItemBuilderMock.updateProgress();
        activityTaskItemBuilderMock.update();
        activityTaskItemBuilderMock.execute();
        activityTaskItemBuilderMock.incrementProgress();
        activityTaskItemBuilderMock.findDetailByCustomer();
        activityTaskItemBuilderMock.updateStatus();
        activityTaskItemBuilderMock.findByTarget();
        activityTaskItemBuilderMock.findByIdList();


//        activityTaskDetailBuilderMock.insert();
//        activityTaskDetailBuilderMock.findById();
//        activityTaskDetailBuilderMock.update();
//        activityTaskDetailBuilderMock.delete();

        activityBatchBuilderMock.putRowWithoutCondition();
        activityBatchBuilderMock.insert();
        activityBatchBuilderMock.queryById();
        activityBatchBuilderMock.rangeQueryOne();
        activityBatchBuilderMock.update();
        activityBatchBuilderMock.updateRowWithoutCondition();

        activityEntityDaoMock.selectByPrimaryKey();

        rewardCsvServiceImplMock.write();

        odpsMock.getDefaultProject();

        odpsReaderMock.open();
        odpsReaderMock.close();
        odpsReaderMock.read();

        remoteBlockBusinessServiceDubboMock.verify();

        taskConfigBuilderMock.getTaskById();
        taskConfigBuilderMock.getTaskByCode();
        taskConfigBuilderMock.getTaskByGroupId();
        taskConfigBuilderMock.findTaskBySaasId();

        remoteCustomerBindServiceMock.findByUid();
        remoteCustomerBindServiceMock.findById();

        rewardBadgeTccServiceImplMock.tryReward();

        customerQuestionSetsBuilderMock.findByUser();
        customerQuestionSetsBuilderMock.incrementAvailableSets();

        activityTaskStatisBuilderMock.findDetail();
        activityTaskStatisBuilderMock.insert();
        activityTaskStatisBuilderMock.incrementProgress();

        threePlatformFilterMock.verify();

//        rewardDivideListenerMock.getInviters();
//        rewardDivideListenerMock.consume();

//        httpPoolUtilMock.getHttpClient();

//        rewardSetsTccServiceImplMock.tryReward();

        //#######################初始化容器###############################
        businessOP.initContainer();
        System.out.println("-----容器已清理完成");

        //#######################初始化redis缓存###############################
        clearRedis();
        log.info("-----redis clear finish");

        initRedis();
        log.info("-----redis init finish");

    }

    private void initRedis() {

    }

    private void clearRedis() {
        Set<String> keys = redisService.keys("*");
        List<String> list = new ArrayList<>(keys);
        redisService.del(list);
    }

    @After
    public void tearDown() {
        activityLotteryConfigTestDao.truncate();
        activityLotteryDetailConfigTestDao.truncate();
        activityLotteryPoolConfigTestDao.truncate();
        activityLotteryStoreLogTestDao.truncate();
        activityTaskConfigTestDao.truncate();
        System.out.println("-----数据库表数据已清理完成");
    }

}
