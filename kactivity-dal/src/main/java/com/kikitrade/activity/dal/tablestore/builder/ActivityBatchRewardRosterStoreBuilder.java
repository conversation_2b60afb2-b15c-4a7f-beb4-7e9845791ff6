package com.kikitrade.activity.dal.tablestore.builder;

import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.RangeResult;

import java.util.List;

public interface ActivityBatchRewardRosterStoreBuilder {

    boolean batchInsert(List<ActivityBatchRewardRoster> activityBatchRewardRosters);

    PageResult findByBatchIdAndStatus(String batchId, String status, int offset, int limit);

    boolean updateStatus(ActivityBatchRewardRoster activityBatchRewardRoster);

    boolean existByBatchIdAndStatus(String batchId, String status);

    RangeResult<ActivityBatchRewardRoster> findForPage(String batchId, String status, int limit, PrimaryKey primaryKey);

    long count(String batchId);

    ActivityBatchRewardRoster findById(String batchId, String customerId, String seq);
}
