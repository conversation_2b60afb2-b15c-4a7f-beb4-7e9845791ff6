package com.kikitrade.activity.service.remote.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.RemoteLotteryService;
import com.kikitrade.activity.api.model.LotteryConfigDTO;
import com.kikitrade.activity.api.model.LotteryResponse;
import com.kikitrade.activity.api.model.request.LotteryWinnersRequest;
import com.kikitrade.activity.api.model.response.LotteryCountResponse;
import com.kikitrade.activity.api.model.response.LotteryWinnersResponse;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityLotteryItemBuilder;
import com.kikitrade.activity.service.business.LotteryConfigService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.lottery.LotteryCommonService;
import com.kikitrade.activity.service.lottery.impl.LotteryServiceRoute;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.quota.api.RemoteQuotaService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class RemoteLotteryServiceImpl implements RemoteLotteryService {

    @Resource
    private LotteryServiceRoute lotteryServiceRoute;
    @Resource
    private KactivityProperties kactivityProperties;
    @DubboReference
    private RemoteMemberService remoteMemberService;
    @Resource
    private CustomerService customerService;
    @Resource
    private SeqGeneraterService seqGeneraterService;
    @Resource
    private ActivityLotteryItemBuilder activityLotteryItemBuilder;
    @Resource
    private RedisService redisService;
    @DubboReference
    private RemoteQuotaService remoteQuotaService;
    @Resource
    private LotteryCommonService lotteryCommonService;
    @Resource
    private LotteryConfigService lotteryConfigService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 抽奖
     * @param saasId
     * @param code
     * @param customerId
     * @return
     */
    @Override
    public LotteryResponse lottery(String customerId, String code, String saasId) throws Exception {
        // 检查用户是否已经抽奖次数达到上限了，如果已经达到上限，则直接返回，不进入下面的tcc事务
        return lotteryCommonService.lottery(customerId, code, saasId);
    }

    @Override
    public LotteryConfigDTO lotteryInfo(String code, String saasId) {
        return lotteryConfigService.findByCode(code);
    }

    @Override
    public Long lotteryCount(String customerId, String code, String saasId) {
        return lotteryCommonService.lotteryCount(customerId, code, saasId);
    }

    /**
     * 最近抽奖次数 & 历史抽奖次数
     *
     * @param customerId
     * @param code
     * @param saasId
     * @return
     */
    @Override
    public LotteryCountResponse lotteryCountDetail(String customerId, String code, String saasId) {

        Long latestCycleCount = lotteryCommonService.lotteryCount(customerId, code, saasId);
        Long historyCount = lotteryCommonService.lotteryCountCumulateAll(customerId, code, saasId);
        log.info("lotteryCountDetail customerId:{}, latestCycleCount:{}, historyCount:{}", customerId, latestCycleCount, historyCount);
        return LotteryCountResponse.builder()
                .historyCount(historyCount)
                .latestCycleCount(latestCycleCount).build();
    }

    /**
     * 记录最近的中奖记录
     * @param lotteryId
     */
    private void recordLottery(String nickName, String currency, BigDecimal amount, String lotteryId){
        String key = RedisKeyConst.LOTTERY_WIN_ITEM.getKey(lotteryId);
        String value = String.format("%s:%s:%s", nickName, currency, amount.stripTrailingZeros().toPlainString());
        //如果相同中奖记录，移除较早的
        redisService.lRemove(key, 0, value);
        //保存该中奖记录
        redisService.lpush(key, value);
        //判断队列长度是否超过50，超过移出
        Long len = redisService.lSize(key);
        if(len > 50){
            redisService.lpop(key);
        }
    }

    @Override
    public LotteryWinnersResponse listLotteryWinners(String saasId, String lotteryCode) {
        log.info("listLotteryWinners saasId:{}, lotteryCode:{}", saasId, lotteryCode);
        String redisKey = RedisKeyConst.LOTTERY_WINNER_KEY.getKey(saasId + ":" + lotteryCode);

        // 获取所有期数的中奖者信息
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(redisKey);

        List<LotteryWinnersResponse.LotteryWinner> lotteryWinners = new java.util.ArrayList<>();

        if (!entries.isEmpty()) {
            lotteryWinners = entries.entrySet().stream().map(entry -> {
                    String poolCode = entry.getKey().toString();
                    String winnersJson = entry.getValue().toString();
                    List<LotteryWinnersRequest.Winner> winners =
                        JSON.parseArray(winnersJson, LotteryWinnersRequest.Winner.class);
                    // 按照order字段进行正序排序
                    if (winners != null) {
                        winners.sort(Comparator.comparing(LotteryWinnersRequest.Winner::getOrder));
                    }
                    return LotteryWinnersResponse.LotteryWinner.builder().poolCode(poolCode)
                        .poolWinners(winners != null ? winners : new java.util.ArrayList<>()).build();
                }).sorted(Comparator.comparing(LotteryWinnersResponse.LotteryWinner::getPoolCode)) // 按照poolCode正序排序
                .collect(Collectors.toList());
        }
        log.info("listLotteryWinners lotteryWinners:{}", JSON.toJSONString(lotteryWinners));

        return LotteryWinnersResponse.builder()
            .lotteryWinners(lotteryWinners)
            .build();
    }

    /**
     * 保存中奖者信息，由后台管理端调用，目前只支持单个奖池的单个中奖者信息保存
     * @param request
     * @return
     */
    @Override
    public boolean saveLotteryWinners(LotteryWinnersRequest request) {
        try {
            log.info("saveLotteryWinners request:{}", JSON.toJSONString(request));
            String redisKey = RedisKeyConst.LOTTERY_WINNER_KEY.getKey(request.getSaasId() + ":" + request.getLotteryCode());

            // 将中奖者信息保存到Redis中，使用Hash结构
            // key = ACTIVITY:LOTTERY:WINNER:saasId:lotteryCode
            // field = poolCode (例如week1)
            // value = JSON序列化的winners列表
            // 首先从缓存中获取现有的中奖名单
            Object existingWinnersObj = redisTemplate.opsForHash().get(redisKey, request.getPoolCode());
            List<LotteryWinnersRequest.Winner> existingWinners = new java.util.ArrayList<>();
            if (existingWinnersObj != null) {
                existingWinners = JSON.parseArray(existingWinnersObj.toString(), LotteryWinnersRequest.Winner.class);
            }

            // 合并新的获奖者信息，address相同的则更新对应的order
            List<LotteryWinnersRequest.Winner> mergedWinners = new java.util.ArrayList<>(existingWinners);
            for (LotteryWinnersRequest.Winner newWinner : request.getWinners()) {
                boolean found = false;
                for (int i = 0; i < mergedWinners.size(); i++) {
                    if (mergedWinners.get(i).getAddress().equals(newWinner.getAddress())) {
                        mergedWinners.set(i, newWinner); // 更新相同address的winner信息
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    mergedWinners.add(newWinner); // 添加新的winner
                }
            }

            String winnersJson = JSON.toJSONString(mergedWinners);
            redisTemplate.opsForHash().put(redisKey, request.getPoolCode(), winnersJson);

            return true;
        } catch (Exception e) {
            log.error("保存中奖者信息失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteLotteryWinners(LotteryWinnersRequest request) {
        try {
            log.info("deleteLotteryWinners request:{}", JSON.toJSONString(request));
            String redisKey = RedisKeyConst.LOTTERY_WINNER_KEY.getKey(request.getSaasId() + ":" + request.getLotteryCode());

            // 从Redis中获取当前奖池的中奖者信息
            Object existingWinnersObj = redisTemplate.opsForHash().get(redisKey, request.getPoolCode());
            if (existingWinnersObj == null) {
                log.warn("没有找到要删除的中奖者信息，poolCode:{}", request.getPoolCode());
                return false;
            }

            List<LotteryWinnersRequest.Winner> existingWinners =
                JSON.parseArray(existingWinnersObj.toString(), LotteryWinnersRequest.Winner.class);

            // 获取需要删除的地址列表
            List<String> addressesToDelete = request.getWinners().stream()
                .map(LotteryWinnersRequest.Winner::getAddress)
                .toList();

            // 过滤掉需要删除的中奖者
            List<LotteryWinnersRequest.Winner> updatedWinners = existingWinners.stream()
                .filter(winner -> !addressesToDelete.contains(winner.getAddress()))
                .toList();

            // 如果没有剩余中奖者，则删除整个键
            if (updatedWinners.isEmpty()) {
                redisTemplate.opsForHash().delete(redisKey, request.getPoolCode());
            } else {
                // 否则更新中奖者列表
                String winnersJson = JSON.toJSONString(updatedWinners);
                redisTemplate.opsForHash().put(redisKey, request.getPoolCode(), winnersJson);
            }

            log.info("成功删除中奖者信息，删除前:{}, 删除后:{}", existingWinners.size(), updatedWinners.size());
            return true;
        } catch (Exception e) {
            log.error("删除中奖者信息失败", e);
            return false;
        }
    }
}
