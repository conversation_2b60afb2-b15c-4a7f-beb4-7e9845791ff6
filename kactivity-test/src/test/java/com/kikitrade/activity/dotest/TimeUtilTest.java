package com.kikitrade.activity.dotest;

import com.kikitrade.activity.model.util.TimeUtil;
import lombok.SneakyThrows;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;

public class TimeUtilTest {


    public static void main(String[] args) {
        LocalDateTime startTime = LocalDateTime.of(2023, 6, 6, 0, 0, 1);
        LocalDateTime endTime = LocalDateTime.of(2023, 6, 7, 0, 0, 0);

        List<Long> timestamps = TimestampGenerator.generateTimestamps(startTime, endTime);
//        for (Long timestamp : timestamps) {
//            System.out.println(timestamp*1000);
//        }
        int len = 10;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch latch = new CountDownLatch(len);
        final CountDownLatch downLatch = new CountDownLatch(1);

        for (int i = 0; i < len; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        latch.countDown();
                        downLatch.await();
                        System.out.println(System.currentTimeMillis());
                        //String parse = TimeUtil.getDataStr(TimeUtil.parseUnittime(time1), TimeUtil.YYYYMMDDHHMMSS);
                        for (Long timestamp : timestamps) {
                            try {
                                TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS);
                            } catch (Exception e) {
                                System.out.println(timestamp);
                                e.printStackTrace();
                                break;
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    endLatch.countDown();
                }
            }).start();
        }

        try {
            latch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        downLatch.countDown();
        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
        try {
            endLatch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

}
