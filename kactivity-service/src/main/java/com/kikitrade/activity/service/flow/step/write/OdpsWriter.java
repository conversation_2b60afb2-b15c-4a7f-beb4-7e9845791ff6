package com.kikitrade.activity.service.flow.step.write;

import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-05 14:25
 */
@Component
@StepScope
public class OdpsWriter implements ItemWriter<ActivityBatchRewardRoster>{

    @Resource
    private ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;

    /**
     * Process the supplied data element. Will not be called with any null items
     * in normal operation.
     *
     * @param items items to be written
     * @throws Exception if there are errors. The framework will catch the
     *                   exception and convert or rethrow it as appropriate.
     */
    @Override
    public void write(Chunk<? extends ActivityBatchRewardRoster> chunk) throws Exception {
        if(CollectionUtils.isNotEmpty(chunk.getItems())){
            activityBatchRewardRosterStoreBuilder.batchInsert(new ArrayList<>(chunk.getItems()));
        }
    }
}
