package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.kikitrade.activity.dal.tablestore.builder.PrecisionTrackBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrecisionTrack;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;

@Service
public class PrecisionTrackBuilderImpl extends WideColumnStoreBuilder<PrecisionTrack> implements PrecisionTrackBuilder {

    @Override
    public String getTableName() {
        return "precision_track";
    }

    @PostConstruct
    public void init() {
        init(PrecisionTrack.class);
    }

    @Override
    public boolean insert(PrecisionTrack precisionTrack) {
        return super.putRow(precisionTrack, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }
}
