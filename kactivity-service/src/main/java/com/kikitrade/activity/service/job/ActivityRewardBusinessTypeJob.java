package com.kikitrade.activity.service.job;

import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.framework.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class ActivityRewardBusinessTypeJob implements SimpleJob {

    @Resource
    @Lazy
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;

    @Override
    public void execute(ShardingContext shardingContext) {
        for(int i = 0; i < 100; i++){
            PageResult page = activityCustomRewardStoreBuilder.findByBusinessType(i * 100, 100);
            if(page != null && CollectionUtils.isNotEmpty(page.getRows())){
                List<ActivityCustomReward> rows = (List<ActivityCustomReward>)page.getRows();
                for(ActivityCustomReward row : rows){
                    if(StringUtils.isNotBlank(row.getBusinessType())){
                        continue;
                    }
                    if(row.getSeq().contains(":")){
                        row.setBusinessType(ActivityConstant.RewardBusinessType.task.name());
                    }else{
                        row.setBusinessType(ActivityConstant.RewardBusinessType.reward.name());
                    }
                    activityCustomRewardStoreBuilder.update(row);
                }
            }
        }
    }
}
