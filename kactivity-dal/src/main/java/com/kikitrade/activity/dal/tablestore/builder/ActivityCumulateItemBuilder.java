package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.ActivityCumulateItem;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/22 20:02
 */
public interface ActivityCumulateItemBuilder {

    String getTableName();

    boolean insert(ActivityCumulateItem activityCumulateItem);

    boolean update(ActivityCumulateItem activityCumulateItem);
}
