package com.kikitrade.activity.service.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryDetailConfigDao;
import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryPoolConfigDao;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryPoolConfig;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.service.business.ActivityLotteryDetailConfigService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Component
@Slf4j
public class KactivityLotteryConfig {

    @Resource
    private RedisService redisService;
    @Resource
    private ActivityLotteryDetailConfigService activityLotteryDetailConfigService;
    @Resource
    private ActivityLotteryDetailConfigDao activityLotteryDetailConfigDao;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private ActivityLotteryPoolConfigDao activityLotteryPoolConfigDao;

    public void load(String content){
        if(StringUtils.isBlank(content)){
            return;
        }
        Map<String, JSONObject> template = JSON.parseObject(content, Map.class);
        try{
            List<String> idList = new ArrayList<>();
            List<String> lotteryIdList = new ArrayList<>();
            if(MapUtils.isNotEmpty(template)){
                log.info("kactivityLotteryConfig:{}", template);
                //key = L0_1
                for(Map.Entry<String, JSONObject> key : template.entrySet()){
                    LotteryTemplate lottery = key.getValue().toJavaObject(LotteryTemplate.class);

                    String id = String.format("assign_%s_%s" ,lottery.getType(), key.getKey());
                    String lotteryId = String.format("assign_%s", lottery.getType());
                    ActivityLotteryDetailConfig detailConfig = activityLotteryDetailConfigService.findItemById(id);
                    idList.add(id);
                    lotteryIdList.add(lotteryId);

                    ActivityLotteryDetailConfig config = buildConfig(id, lotteryId, lottery);

                    if(detailConfig == null){
                        if(config.getNum() > 0){
                            activityLotteryDetailConfigDao.insert(config);
                            ActivityLotteryPoolConfig poolConfig = new ActivityLotteryPoolConfig();
                            poolConfig.setLotteryId(lotteryId);
                            poolConfig.setDrawId(id);
                            poolConfig.setPoolNo("1");
                            poolConfig.setRemainNum(config.getNum());
                            poolConfig.setSaasId(kactivityProperties.getSaasId());
                            poolConfig.setCreated(new Date());
                            poolConfig.setModified(new Date());
                            activityLotteryPoolConfigDao.insert(poolConfig);
                        }
                    }else{
                        int add = config.getNum() - detailConfig.getNum();
                        if(add >= 0){
                            activityLotteryDetailConfigDao.increaseStoreNum(id, add);
                            config.setRemainNum(null);
                            activityLotteryDetailConfigDao.updateByPrimaryKeySelective(config);
                            activityLotteryPoolConfigDao.increaseStoreNum(id, "1", add);
                        }else{
                            log.error("该记录未更新，变更大小小于0,{}", JSON.toJSONString(lottery));
                        }
                    }
                }
                for(String lotteryId : lotteryIdList){
                    List<ActivityLotteryDetailConfig> assignConfigs = activityLotteryDetailConfigService.findItemByLotteryId(lotteryId);
                    for(ActivityLotteryDetailConfig config : assignConfigs){
                        if(!idList.contains(config.getId())){
                            activityLotteryDetailConfigDao.deleteByPrimaryKey(config.getId());
                            ActivityLotteryPoolConfig poolConfig = new ActivityLotteryPoolConfig();
                            poolConfig.setDrawId(config.getId());
                            log.info("pool delete:{}", JSON.toJSONString(poolConfig));
                            activityLotteryPoolConfigDao.delete(poolConfig);
                        }
                    }
                }
            }else{
                List<ActivityLotteryDetailConfig> assignConfigs = activityLotteryDetailConfigService.findItemByLotteryId("assign_slot");
                for(ActivityLotteryDetailConfig config : assignConfigs){
                    activityLotteryDetailConfigDao.deleteByPrimaryKey(config.getId());
                    ActivityLotteryPoolConfig poolConfig = new ActivityLotteryPoolConfig();
                    poolConfig.setDrawId(config.getId());
                    log.info("pool delete:{}", JSON.toJSONString(poolConfig));
                    activityLotteryPoolConfigDao.delete(poolConfig);
                }
            }
        }catch (Exception ex){
            log.error("kactivity-sql parse error", ex);
        }
    }

    private ActivityLotteryDetailConfig buildConfig(String id, String lotteryId, LotteryTemplate lottery){
        ActivityLotteryDetailConfig config = new ActivityLotteryDetailConfig();
        config.setId(id);
        config.setLotteryId(lotteryId);
        config.setNum(lottery.getNum());
        config.setIsLow(true);
        config.setRemainNum(lottery.getNum());
        config.setAmount(new BigDecimal(lottery.getAwardAmount()));
        config.setCurrency(lottery.getAward().toUpperCase());
        config.setName(id);
        config.setCreated(new Date());
        config.setModified(new Date());
        config.setAwardType(lottery.getAwardType());
        config.setSaasId(kactivityProperties.getSaasId());
        return config;
    }
}
