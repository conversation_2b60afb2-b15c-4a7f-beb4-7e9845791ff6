<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityContentsDao">

    <resultMap id="BaseResultMap" type="com.kikitrade.activity.dal.mysql.model.ActivityContents">
        <result column="activity_id" property="activityId" javaType="java.lang.Integer" jdbcType="SMALLINT"/>
        <result column="locale" property="locale" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="name" property="name" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="url" property="url" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="content" property="content" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="saasId" property="saasId" javaType="java.lang.String" jdbcType="VARCHAR"/>
    </resultMap>


    <!-- 增加 -->
    <insert id="insert" parameterType="com.kikitrade.activity.dal.mysql.model.ActivityContents">
        INSERT INTO activity_contents VALUES (
            #{activityId},
            #{locale},
            #{name},
            #{url},
            #{content},
            #{saasId}
        )
    </insert>

    <!-- 修改 -->
    <update id="update" parameterType="com.kikitrade.activity.dal.mysql.model.ActivityContents">
        update activity_contents ac
        <set>
            ac.name=#{name},
            <if test="url!=null">
                ac.url=#{url},
            </if>
            <if test="content!=null">
                ac.content=#{content},
            </if>
        </set>
        where ac.activity_id = #{activityId} and locale= #{locale}
    </update>

    <delete id="deleteById">
        DELETE FROM activity_contents
        WHERE activity_id = #{activityId}
    </delete>


    <delete id="deleteByPara">
        DELETE FROM activity_contents
        WHERE activity_id = #{activityId} and locale= #{locale}
    </delete>

    <select id="findByPara" resultMap="BaseResultMap">
        SELECT *
        FROM activity_contents
        WHERE activity_id = #{activityId} and locale= #{locale}
    </select>


    <select id="findById" resultMap="BaseResultMap">
        SELECT *
        FROM activity_contents
        WHERE 1=1
        <if test="activityId!=null">
            and activity_id = #{activityId}
        </if>
        and saasId=#{saasId}
        order by activity_id desc
    </select>

</mapper>
