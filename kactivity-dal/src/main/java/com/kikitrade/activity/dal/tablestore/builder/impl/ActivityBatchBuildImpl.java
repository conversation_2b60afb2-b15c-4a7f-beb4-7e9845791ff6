package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.dao.ActivityEntityDao;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.param.ActivityBatchParam;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.*;

import static com.kikitrade.activity.dal.tablestore.model.ActivityBatch.*;

@Service
@Slf4j
public class ActivityBatchBuildImpl extends WideColumnStoreBuilder<ActivityBatch> implements ActivityBatchBuilder {

    @Resource
    private SeqGeneraterService seqGeneraterService;
    @Resource
    private ActivityEntityDao activityEntityDao;

    @Override
    public String getTableName(){
        return "activity_batch";
    }

    @PostConstruct
    public void init() {
        init(ActivityBatch.class);
    }

    @Override
    public boolean insert(ActivityBatch activityBatch) {
        if(StringUtils.isBlank(activityBatch.getBatchId())){
            activityBatch.setBatchId(seqGeneraterService.next(CustomerSeqRuleBuilder.instance(getTableName())));
        }
        return putRow(activityBatch);
    }

    @Override
    public boolean update(ActivityBatch activityBatch) {
        activityBatch.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        return updateRow(activityBatch);
    }

    @Override
    public PageResult queryForList(ActivityBatchParam activityBatchParam, int offset, int limit) {

        BoolQuery.Builder builder = QueryBuilders.bool();
        if(StringUtils.isNotBlank(activityBatchParam.getBatchId())){
            builder.must(QueryBuilders.term("batch_id", activityBatchParam.getBatchId()));
            offset = 0;
        }
        if(StringUtils.isNotBlank(activityBatchParam.getBatchName())){
            builder.must(QueryBuilders.term("name", activityBatchParam.getBatchName()));
            offset = 0;
        }
        if(StringUtils.isNotBlank(activityBatchParam.getRewardStatus()) && !"ALL".equals(activityBatchParam.getRewardStatus())){
            builder.must(QueryBuilders.term("reward_status", activityBatchParam.getRewardStatus()));
            offset = 0;
        }
        if(StringUtils.isNotBlank(activityBatchParam.getActivityId())){
            builder.must(QueryBuilders.term("activity_id", activityBatchParam.getActivityId()));
            offset = 0;
        }
        builder.must(QueryBuilders.term("status", Boolean.TRUE));

        Sort sort = new Sort(Arrays.asList(new FieldSort("batch_id", SortOrder.DESC)));

        return pageSearch(builder.build(), sort, offset, limit, IDX_MUL_ACTIVITY_BATCH_NAME);
    }

    @Override
    public ActivityBatch queryById(String id) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.fromString(id), PrimaryKeyValue.fromString(id)));
        queryList.add(new RangeQueryParameter("activity_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        ActivityBatch activityBatch = rangeQueryOne(IDX_ACTIVITY_BATCH_BY_ID, queryList);
        if(activityBatch != null && BooleanUtils.isTrue(activityBatch.getStatus())){
            if(StringUtils.isBlank(activityBatch.getActivityType())){
                ActivityEntity activity = activityEntityDao.selectByPrimaryKey(activityBatch.getActivityId());
                activityBatch.setActivityType(activity.getType());
            }
            return activityBatch;
        }
        return null;
    }

    @Override
    public int countByStatus(String activityId, List<String> statusList) {
        int count = 0;
        for(String status : statusList){
            List<RangeQueryParameter> query = new ArrayList<>();
            query.add(new RangeQueryParameter("reward_status", PrimaryKeyValue.fromString(status), PrimaryKeyValue.fromString(status)));
            query.add(new RangeQueryParameter("activity_id", PrimaryKeyValue.fromString(activityId), PrimaryKeyValue.fromString(activityId)));
            query.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
            count += rangeQuery(IDX_ACTIVITY_BATCH_BY_STATUS,query).size();
        }
        return count;
    }

    @Override
    public List<ActivityBatch> queryListByStatus(String status){
        List<RangeQueryParameter> query = new ArrayList<>();
        query.add(new RangeQueryParameter("reward_status", PrimaryKeyValue.fromString(status), PrimaryKeyValue.fromString(status)));
        query.add(new RangeQueryParameter("activity_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        query.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQuery(IDX_ACTIVITY_BATCH_BY_STATUS,query);
    }

    @Override
    public int countByActivityId(String activityId){
        List<RangeQueryParameter> query = new ArrayList<>();
        query.add(new RangeQueryParameter("activity_id", PrimaryKeyValue.fromString(activityId), PrimaryKeyValue.fromString(activityId)));
        query.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        List<ActivityBatch> batches = rangeQuery(query, 10);
        return batches == null ? 0 : batches.size();
    }

    @Override
    public boolean deleteById(ActivityBatch activityBatch){
        log.info("{}",activityBatch);
        if(activityBatch.getActivityId() == null || activityBatch.getBatchId() == null){
            return false;
        }
        return deleteRow(activityBatch);
    }

    @Override
    public ActivityBatch queryLastByActivityId(String activityId) {
        List<RangeQueryParameter> query = new ArrayList<>();
        query.add(new RangeQueryParameter("activity_id", PrimaryKeyValue.fromString(activityId), PrimaryKeyValue.fromString(activityId)));
        query.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.INF_MAX, PrimaryKeyValue.INF_MIN));
        List<ActivityBatch> batches = rangeQuery(query, Direction.BACKWARD ,1);
        return CollectionUtils.isNotEmpty(batches) ? batches.get(0) : null;
    }
}
