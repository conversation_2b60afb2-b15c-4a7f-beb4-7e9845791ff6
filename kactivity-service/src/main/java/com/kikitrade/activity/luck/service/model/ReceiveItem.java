package com.kikitrade.activity.luck.service.model;

import lombok.*;

import java.math.BigDecimal;

/**
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceiveItem {

    public ReceiveItem(String releaseId, String id, String amount, String currency, BigDecimal cost){
        this.releaseId = releaseId;
        this.id = id;
        this.amount = amount;
        this.currency = currency;
        this.cost = cost;
    }

    /**
     * 待领取的红包id
     */
    private String id;

    /**
     * 发放的红包id
     */
    private String releaseId;

    /**
     * 红包金额
     */
    private String amount;

    /**
     * 币种
     */
    private String currency;

    /**
     * usd价值
     */
    private BigDecimal cost;

    /**
     * 领取人
     */
    private String receiveCode;

    /**
     * 领取时间
     */
    private String receiveTime;

    /**
     * 过期时间
     */
    private String expiredTime;

    private String cover;

    private String greeting;

    private String avatar;

    private String releaseNickName;

    private String assignType;

    private String businessId;
}
