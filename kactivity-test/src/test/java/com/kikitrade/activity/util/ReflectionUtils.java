package com.kikitrade.activity.util;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class ReflectionUtils {

    /**
     * 向私有属性赋值
     * @param classPath
     * @param fieldName
     * @param beanName
     * @param value
     * @throws ClassNotFoundException
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    @Deprecated
    public static void setField(String classPath, String fieldName, Object beanName, Object value) throws ClassNotFoundException, NoSuchFieldException, IllegalAccessException {
        Class<?> catClass = Class.forName(classPath);
        Field catMap = catClass.getDeclaredField(fieldName);
        catMap.setAccessible(true);
        catMap.set(beanName, value);
    }

    /**
     * 向私有属性赋值
     * @param clazz
     * @param fieldName
     * @param bean
     * @param value
     */
    @Deprecated
    public static void setField(Class clazz, String fieldName, Object bean, Object value){
        Field field = org.springframework.util.ReflectionUtils.findField(clazz, fieldName);
        field.setAccessible(true);
        org.springframework.util.ReflectionUtils.setField(field, bean, value);
    }

    public static void setField(String fieldName, Object bean, Object value){
        //Field field = org.springframework.util.ReflectionUtils.findField(clazz, fieldName);
        Field field = EntityUtils.getField(bean, fieldName);
        assert field != null;
        field.setAccessible(true);
        org.springframework.util.ReflectionUtils.setField(field, bean, value);
    }

    /**
     * 通过反射调用私有方法
     * @param clazz
     * @param methodName
     * @param bean
     * @param param
     * @throws ClassNotFoundException
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    public static Object invokeMethod(Class<?> clazz, String methodName, Object bean, Class<?>[] paramType, Object... param) throws ClassNotFoundException, NoSuchFieldException, IllegalAccessException, InvocationTargetException {
        Method method = org.springframework.util.ReflectionUtils.findMethod(clazz, methodName, paramType);
        assert method != null;
        method.setAccessible(true);
        //System.out.println(method.toGenericString());
        return method.invoke(bean, param);

    }

    /**
     * 获取私有属性
     * @param fieldName
     * @param bean
     * @return
     * @throws IllegalAccessException
     */
    public static Object getField(String fieldName, Object bean) throws IllegalAccessException {
        //Field field = org.springframework.util.ReflectionUtils.findField(clazz, fieldName);
        Field field = EntityUtils.getField(bean, fieldName);
        assert field != null;
        field.setAccessible(true);
        return field.get(bean);
    }

}