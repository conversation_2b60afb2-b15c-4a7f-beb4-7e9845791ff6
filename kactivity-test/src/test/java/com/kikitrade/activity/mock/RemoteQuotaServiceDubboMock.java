package com.kikitrade.activity.mock;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

public interface RemoteQuotaServiceDubboMock {
    void freeze();
    void unfreezeDeduct();
    void unfreeze();
    void deduct();
    void check() throws ExecutionException, InterruptedException, TimeoutException;
    void checkError() throws ExecutionException,InterruptedException,TimeoutException;
}
