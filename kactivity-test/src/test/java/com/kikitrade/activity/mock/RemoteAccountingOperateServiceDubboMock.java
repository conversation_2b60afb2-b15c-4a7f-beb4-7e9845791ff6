package com.kikitrade.activity.mock;

import com.kikitrade.accounting.api.model.account.AccountException;

public interface RemoteAccountingOperateServiceDubboMock {
    void transfer() throws AccountException;

    void transferErr() throws AccountException;

    void freeze() throws AccountException;

    void freezeError() throws AccountException;

    void unfreeze() throws AccountException;

    void unfreezeError() throws AccountException;

}
