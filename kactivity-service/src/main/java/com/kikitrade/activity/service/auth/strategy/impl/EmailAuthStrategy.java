package com.kikitrade.activity.service.auth.strategy.impl;

import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.auth.strategy.AuthContext;
import com.kikitrade.activity.service.auth.strategy.AuthResult;
import com.kikitrade.activity.service.auth.strategy.PlatformAuthStrategy;
import com.kikitrade.activity.service.model.OpenAuthRequest;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Email认证策略实现
 * Email认证不使用OAuth2流程，而是基于验证码验证
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
public class EmailAuthStrategy implements PlatformAuthStrategy {

    private final RedisService redisService;

    public EmailAuthStrategy(RedisService redisService) {
        this.redisService = redisService;
    }

    @Override
    public String getPlatformName() {
        return OpenSocialPlatformEnum.email.name();
    }

    @Override
    public AuthResult authenticate(AuthContext context) throws ActivityException {
        log.info("[Email] 开始邮箱认证: customerId={}, inputCode={}",
            context.getCustomerId(), context.getCode());

        try {
            // 从redis中获取验证码和邮箱信息
            String redisKey = RedisKeyConst.ACTIVITY_EMAIL_VERIFY_CODE.getKey(context.getCustomerId());
            Map<String, Object> verifyData = redisService.hGetAllMap(redisKey);

            if (verifyData == null || verifyData.isEmpty()) {
                log.warn("[Email] 验证码不存在或已过期: customerId={}", context.getCustomerId());
                throw new ActivityException(ActivityResponseCode.AUTH_CODE_INVALID);
            }

            String cachedCode = String.valueOf(verifyData.get("code"));
            String cachedEmail = String.valueOf(verifyData.get("email"));

            if (StringUtils.isBlank(cachedCode) || StringUtils.isBlank(cachedEmail)) {
                log.warn("[Email] 验证码数据不完整: customerId={}", context.getCustomerId());
                throw new ActivityException(ActivityResponseCode.AUTH_CODE_INVALID);
            }

            if (!context.getCode().equals(cachedCode)) {
                log.warn("[Email] 验证码不匹配: customerId={}, inputCode={}, cachedCode={}",
                    context.getCustomerId(), context.getCode(), cachedCode);
                throw new ActivityException(ActivityResponseCode.AUTH_CODE_INVALID);
            }

            // 验证码正确，删除redis中的验证码
            redisService.del(redisKey);

            // 构建社交用户信息，使用从缓存中获取的邮箱地址
            SocialUserInfo socialUserInfo = SocialUserInfo.builder()
                    .userId(cachedEmail) // 使用缓存的邮箱作为用户ID
                    .userName(cachedEmail) // 使用缓存的邮箱作为用户名
                    .email(cachedEmail)
                    .build();

            // Email认证不需要token
            Token token = new Token();
            token.setSocialCustomerId(socialUserInfo.getUserId());
            token.setUserName(socialUserInfo.getUserName());

            log.info("[Email] 邮箱认证成功: customerId={}, email={}",
                context.getCustomerId(), cachedEmail);

            return AuthResult.success(token, socialUserInfo);

        } catch (ActivityException e) {
            log.error("[Email] 认证失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("[Email] 认证过程中发生异常", e);
            throw new ActivityException(ActivityResponseCode.AUTH_CODE_INVALID);
        }
    }

    @Override
    public OpenAuthRequest buildAuthRequest(String saasId, String code, String redirectUri) {
        // Email认证不需要OAuth2请求
        return null;
    }

    @Override
    public OpenAuthRequest buildRefreshRequest(String saasId, String refreshToken) {
        // Email认证不需要刷新令牌
        return null;
    }

    @Override
    public SocialUserInfo getCurrentUser(String saasId, String accessToken, String refreshToken) {
        // Email认证不需要获取用户信息
        return null;
    }

    @Override
    public boolean supportsRefreshToken() {
        return false;
    }
}
