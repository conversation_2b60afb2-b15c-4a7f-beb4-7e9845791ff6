package com.kikitrade.activity.service.engine;

import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.mysql.model.ActivityRuleMap;
import com.kikitrade.activity.model.ActivityMessage;
import com.kikitrade.activity.service.engine.rule.base.BaseRule;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngine;
import org.jeasy.rules.core.DefaultRulesEngine;

import java.util.Collections;
import java.util.List;
import java.util.Map;


@Setter
@Getter
@Slf4j
public class GlobalEventRuleEngine {
    private static RulesEngine ruleEngine = new DefaultRulesEngine();
    private List<BaseRule> ruleList;

    private Rules initProcessingRules(List<ActivityRuleMap> activityRuleMaps) {
        Rules currentRules = new Rules();
        //规则列表按规则优先级排序
        Collections.sort(activityRuleMaps);
        for (ActivityRuleMap ruleMap : activityRuleMaps) {
            ruleList.stream()
                    .filter(f -> f.getClass().getSimpleName().equalsIgnoreCase(ruleMap.getRule_name()))
                    .forEach(f -> currentRules.register(f));
        }
        return currentRules;
    }


    public Map<Rule, Boolean> check(Activity activity, ActivityMessage activityMassage) {
        Facts facts = new Facts();
        facts.put("ruleConfig", activity.getRuleConfig());
        facts.put("request", activityMassage);
        Map<Rule, Boolean> result = ruleEngine.check(initProcessingRules(activity.getRuleConfig()), facts);
        log.info("easyruleEngine check result List [{}]", result);
        return result;
    }


}
