package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-12 18:50
 */
@Data
@Table(name = "activity_join_item")
public class ActivityJoinItem implements Serializable {

    @PartitionKey(name = "user_name")
    private String userName;

    @PartitionKey(name = "activity_code", value = 1)
    private String activityCode;

    @Column(name = "business_code", isDefined = true)
    private String businessCode;

    @Column(name = "business_type", isDefined = true)
    private String businessType;

    @Column(name = "status", isDefined = true)
    private String status;

    @Column(name = "created", isDefined = true, type = Column.Type.INTEGER)
    private Date created;

    @Column(name = "modified", isDefined = true, type = Column.Type.INTEGER)
    private Date modified;
}
