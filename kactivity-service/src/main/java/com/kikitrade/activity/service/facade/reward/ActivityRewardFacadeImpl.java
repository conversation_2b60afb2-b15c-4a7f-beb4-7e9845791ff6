package com.kikitrade.activity.service.facade.reward;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.facade.reward.ActivityRewardFacadeGrpc;
import com.kikitrade.activity.facade.reward.ManualRewardRequest;
import com.kikitrade.activity.facade.reward.ManualRewardResponse;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import com.kikitrade.activity.model.constant.BusinessMonitorConstant;

/**
 * ActivityRewardFacade Implementation
 *
 * <AUTHOR>
 * @create 2021/10/26 9:06 上午
 * @modify
 */
@Slf4j
@Component
public class ActivityRewardFacadeImpl extends ActivityRewardFacadeGrpc.ActivityRewardFacadeImplBase {

    @Resource
    private RewardService rewardService;

    @Override
    public void manualReward(ManualRewardRequest request, StreamObserver<ManualRewardResponse> responseObserver) {

        RewardRequest rewardRequest;
        try {
            String businessId = request.getRewardId() + String.format("%02d", request.getType().getNumber()) + request.getCustomerId();

            rewardRequest = RewardRequest.builder()
                    .customerId(request.getCustomerId().trim())
                    .currency(request.getCurrency().trim())
                    .amount(new BigDecimal(request.getAmount().getValue()))
                    .rewardId(businessId)
                    .type(request.getType().name())
                    .build();

            ActivityResponse result = rewardService.reward(rewardRequest);
            log.info("manualReward result {} {} request={}, response={}", result.getCode().isSuccess(),
                    request.getCustomerId(), JSON.toJSONString(rewardRequest),
                    JSON.toJSONString(result));

            responseObserver.onNext(ManualRewardResponse.newBuilder()
                    .setSuccess(result.getCode().isSuccess())
                    .setMessage(result.getMsg() == null ? "" : result.getMsg())
                    .build());
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("manualReward result fail {} request={}", request.getCustomerId(),
                    JSON.toJSONString(request), e);
            responseObserver.onError(e);
        }
    }
}
