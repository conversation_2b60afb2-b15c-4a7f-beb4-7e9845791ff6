package com.kikitrade.activity.service.view.impl;

import com.kikitrade.activity.api.model.ActivityRequest;
import com.kikitrade.activity.api.model.activity.ActivityInfoVO;
import com.kikitrade.activity.dal.mysql.model.ActivityMaterial;
import com.kikitrade.activity.service.config.KactivityMaterialTemplateConfig;
import com.kikitrade.activity.service.view.ActivityInfoBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;
import java.util.Map;

@DubboService
public class ActivityBaseInfoBuilder implements ActivityInfoBuilder {

    @Override
    public void doBuild(ActivityRequest activityRequest, ActivityInfoVO activityInfoVO, Map<String, ActivityMaterial> material, List<KactivityMaterialTemplateConfig.MaterialTemplate> templateList) {
        Map<String,String> activityInfoMap = activityInfoVO.getView();
        for(KactivityMaterialTemplateConfig.MaterialTemplate template : templateList){
            activityInfoMap.put(StringUtils.isNotBlank(template.getAlias()) ? template.getAlias()
                    : template.getCode(), material.get(template.getCode()) == null ? (StringUtils.isBlank(template.getDefaultValue()) ? null : template.getDefaultValue()) : material.get(template.getCode()).getValue());
        }
    }
}
