package com.kikitrade.activity.service.flow.job;

public interface BatchConstantName {

    interface JobBeanName{
        String MANUAL_ROSTER = "manualRosterJob";//手动导入数据
        String MANUAL_REWARD = "manualRewardJob";//手动发奖
        String SEMI_AUTO_MATIC_REWARD = "semiAutoMaticRewardJob";//半自动发奖
        String AUTO_MATIC_REWARD = "autoMaticRewardJob";//自动发奖
    }

    interface StepName{
        String ODPS = "odpsStep";//拉取odps数据
        String ROSTER = "rosterStep";//数据导入到roster
        String REWARD = "rewardStep";//发奖
    }
}
