package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.tablestore.builder.ActivityLotteryItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem;
import com.kikitrade.activity.mock.ActivityLotteryItemBuilderMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.framework.common.util.BeanUtil;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;

@Component
public class ActivityLotteryItemBuilderMockImpl implements ActivityLotteryItemBuilderMock {
    @Resource
    ActivityLotteryItemBuilder activityLotteryItemBuilder;

    @Override
    public void countByCustomer() {
        Mockito.doAnswer(c -> {
            return Long.valueOf(BusinessOP.count);
        }).when(activityLotteryItemBuilder).countByCustomer(anyString());
    }

    @Override
    public void insert() {
        Mockito.doAnswer(c -> {
            ActivityLotteryItem item = c.getArgument(0);

            Map<String, ActivityLotteryItem> activityLotteryItemMap = BusinessOP.activityLotteryItemMap;
            activityLotteryItemMap.put(item.getId(), item);

            return true;
        }).when(activityLotteryItemBuilder).insert(Mockito.any(ActivityLotteryItem.class));
    }

    @Override
    public void findById() {
        Mockito.doAnswer(c -> {
            String id = c.getArgument(1);

            Map<String, ActivityLotteryItem> activityLotteryItemMap = BusinessOP.activityLotteryItemMap;

            if (activityLotteryItemMap.containsKey(id)) {
                ActivityLotteryItem dbitem = activityLotteryItemMap.get(id);
                ActivityLotteryItem item = new ActivityLotteryItem();
                BeanUtil.copyProperties(dbitem, item);
                return item;
            }
            return null;

        }).when(activityLotteryItemBuilder).findById(anyString(), anyString());
    }

    @Override
    public void update() {
        Mockito.doAnswer(c -> {
            ActivityLotteryItem item = c.getArgument(0);

            String id = item.getId();

            Map<String, ActivityLotteryItem> activityLotteryItemMap = BusinessOP.activityLotteryItemMap;

            if (activityLotteryItemMap.containsKey(id)) {
                activityLotteryItemMap.put(id, item);
                return true;
            }

            return false;


        }).when(activityLotteryItemBuilder).update(Mockito.any(ActivityLotteryItem.class));

    }

    @Override
    public void insertErr() {
        Mockito.doAnswer(c -> {
            throw new RuntimeException("-----ActivityLotteryItemBuilder.insert.ERR");
        }).when(activityLotteryItemBuilder).insert(Mockito.any(ActivityLotteryItem.class));

    }

    @Override
    public void updateErr() {
        Mockito.doAnswer(c -> {
            throw new RuntimeException("-----ActivityLotteryItemBuilder.update.ERR");
        }).when(activityLotteryItemBuilder).update(Mockito.any(ActivityLotteryItem.class));
    }
}
