package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.CustomerQuestionSets;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/29 15:26
 */
public interface CustomerQuestionSetsBuilder {

    boolean insert(CustomerQuestionSets customerQuestionSets);

    Long incrementAvailableSets(CustomerQuestionSets customerQuestionSets, int inc);

    Long incrementUsedSets(CustomerQuestionSets customerQuestionSets);

    boolean update(CustomerQuestionSets customerQuestionSets);

    CustomerQuestionSets findByUser(String saasId, String customerId);

}
