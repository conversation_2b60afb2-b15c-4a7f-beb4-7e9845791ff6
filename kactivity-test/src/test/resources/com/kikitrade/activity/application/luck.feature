# language: zh-CN
功能: luck

  # 背景中的步骤，该feature用例前运行
  背景:
    假如redis缓存数据已经初始化完成
    并且seq的规则更改为next2


  场景大纲: 发红包-成功
    当luck-用户发红包<luck>
    那么luck-校验账务帐冻结<accountFreezeRequest>
    并且luck-校验业务帐冻结<productAccountParam>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-校验redis-LUCK_FORTUNE_RELEASE_STATUS_KEY<status>
    并且luck-校验redis-LUCK_FORTUNE_RELEASE_INFO_KEY<info>
    并且luck-校验redis-LUCK_FORTUNE_RELEASE_NOT_DRAW_KEY<not_draw>
    例子:
      | luck                                                                                                                                                                                                                                                                                        | accountFreezeRequest                                                                                                                                                                                        | productAccountParam                                                                                                       | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | status | info                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | not_draw                                                                                                                  |
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | {"amount":2,"businessId":"202211021943150509","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","freezeAccount":true,"saasId":"kiki","strictCheck":true} | {"amount":2,"bizId":"202211021943150509","customerId":"**************","productLimitCode":"AIRDROP_RELEASE_LIMIT_L0_KOL"} | {"amount":"2","assignType":"GENERAL","avatar":"","cost":2.********,"cover":"","created":"2022-11-02 12:38:53","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2.********,\"currency\":\"USD\",\"id\":\"202211022038530810\",\"releaseId\":\"202211022038390264\"}]","expiredTime":"2022-11-03 04:38:53","greeting":"","id":"202211022038390264","nickName":"TestNickName","num":1,"releaseCode":"9Md6fouj3OfTMBn6AuU6","releaseTime":"2022-11-02 12:38:53","status":1,"validTime":1} | 1      | {"amount":"2","assignType":"GENERAL","cost":2,"cover":"","created":"2023-03-08 09:18:42","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2.********,\"currency\":\"USD\",\"id\":\"296902300887393390\",\"releaseId\":\"208956923678131778\"}]","expiredTime":"2023-03-09 01:18:42","greeting":"","id":"208956923678131778","modified":"2023-03-08 09:18:42","num":1,"releaseCode":"BJxCkI1kZXuBF8ZGdpQa","releaseTime":"2023-03-08 09:18:42","status":0,"validTime":1} | ["{\"amount\":\"2\",\"cost\":2,\"currency\":\"USD\",\"id\":\"291962883658231027\",\"releaseId\":\"221436816120817536\"}"] |


  场景大纲: 发红包-失败，没有发红包权限 L0 MEDIA
    假如luck-用户的KolRole为<kolRole>
    当luck-异常-用户发红包<luck>
    那么luck-校验exceptions为<exceptions>
    例子:
      | kolRole | luck                                                                                                                                                                                                                                                                                        | exceptions |
      | MEDIA   | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | null       |


  场景大纲: 发红包-失败，红包的数量无效
    当luck-异常-用户发红包<luck>
    那么luck-校验exceptions为<exceptions>
    例子:
      | luck                                                                                                                                                                                                                                                                                         | exceptions               |
      # 红包数量2>1
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"2","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""}  | LUCK_FORTUNE_NUM_INVALID |
      # 红包数量0
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"0","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""}  | LUCK_FORTUNE_NUM_INVALID |
      # 红包数量-1
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"-1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_NUM_INVALID |

  场景大纲: 发红包-失败，红包的金额无效
    当luck-异常-用户发红包<luck>
    那么luck-校验exceptions为<exceptions>
    例子:
      | luck                                                                                                                                                                                                                                                                                               | exceptions                  |
      # 拼手气 num:1 * amount:0
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"FORTUNE","num":"1","amount":"0","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""}        | LUCK_FORTUNE_AMOUNT_INVALID |
      # 拼手气 1 * -1
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"FORTUNE","num":"1","amount":"-1","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""}       | LUCK_FORTUNE_AMOUNT_INVALID |
      # 拼手气 1 * 2.13 > 最大2.12345
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"FORTUNE","num":"1","amount":"2.13","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""}     | LUCK_FORTUNE_AMOUNT_INVALID |
      # 拼手气 1 * 0.000009 < receive_min:0.00001
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"FORTUNE","num":"1","amount":"0.000009","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_AMOUNT_INVALID |
      # 拼手气 1 * 4 < receive_max:2
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"FORTUNE","num":"1","amount":"4","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""}        | LUCK_FORTUNE_AMOUNT_INVALID |
      # 普通 1 * -1
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"-1","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""}       | LUCK_FORTUNE_AMOUNT_INVALID |
      # 普通 1 * 2.01 > receive_max:2
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"-1","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""}       | LUCK_FORTUNE_AMOUNT_INVALID |

  场景大纲: 发红包-失败，用户金额不足
    假如luck-用户可用余额为<available>
    当luck-异常-用户发红包<luck>
    那么luck-校验exceptions为<exceptions>
    例子:
      | available | luck                                                                                                                                                                                                                                                                                        | exceptions                    |
      # 1 * 1 > 0.1
      | 0.1       | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"FORTUNE","num":"1","amount":"1","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | CUSTOMER_INSUFFICIENT_BALANCE |

  场景大纲: 发红包-失败，冻结账户异常
    假如luck-kaccounting异常，无法冻结账务
    当luck-异常-用户发红包<luck>
    那么luck-校验exceptions为<exceptions>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    例子:
      | luck                                                                                                                                                                                                                                                                                        | exceptions                                  | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"FORTUNE","num":"1","amount":"1","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | remoteAccountingOperateService.freeze error | {"amount":"1","assignType":"FORTUNE","avatar":"","cost":1.********,"cover":"","created":"2022-11-03 07:50:50","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"1\",\"cost\":1.********,\"currency\":\"USD\",\"id\":\"202211031550500555\",\"releaseId\":\"202211031550500316\"}]","expiredTime":"2022-11-03 23:50:50","greeting":"","id":"202211031550500316","nickName":"TestNickName","num":1,"releaseCode":"C0H1H76OyImRE0Ovszke","releaseTime":"2022-11-03 07:50:50","status":-1,"validTime":1} |


  场景大纲: 发红包-失败，冻结产品帐异常,账务帐需要解冻
    假如luck-kaccounting异常，无法冻结产品帐
    当luck-异常-用户发红包<luck>
    那么luck-校验exceptions为<exceptions>
    并且luck-校验账务帐冻结<accountFreezeRequest>
    并且luck-校验业务帐冻结<productAccountParam>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    例子:
      | luck                                                                                                                                                                                                                                                                                        | exceptions                       | accountFreezeRequest                                                                                                                                                                                        | productAccountParam | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"FORTUNE","num":"1","amount":"1","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_RELEASE_LIMIT_ERROR | {"amount":1,"businessId":"232691401748181653","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","freezeAccount":true,"saasId":"kiki","strictCheck":true} |                     | {"amount":"1","assignType":"FORTUNE","avatar":"","cost":1.********,"cover":"","created":"2022-11-03 07:50:50","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"1\",\"cost\":1.********,\"currency\":\"USD\",\"id\":\"202211031550500555\",\"releaseId\":\"202211031550500316\"}]","expiredTime":"2022-11-03 23:50:50","greeting":"","id":"202211031550500316","nickName":"TestNickName","num":1,"releaseCode":"C0H1H76OyImRE0Ovszke","releaseTime":"2022-11-03 07:50:50","status":-1,"validTime":1} |


#  场景大纲: 发红包-并发，同时发多个红包
#    当luck-并发-用户发红包<luck>
#    那么luck-并发-校验账务帐冻结
#    并且luck-并发-校验业务帐冻结
#    并且luck-并发-校验luckFortuneReleaseItem
#    例子:
#      | luck                                                                                                                                                                                                                                                                                        |
#      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"FORTUNE","num":"1","amount":"1","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} |

  场景大纲: 领红包-成功
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    那么luck-校验redis-LUCK_FORTUNE_RECEIVE_DETAIL_KEY<detail>
    那么luck-校验redis-LUCK_FORTUNE_RELEASE_DREW_KEY<drew>
    那么luck-校验redis-LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY<not_active>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | detail                                                                                                                                                                                                     | drew                                                                                                                                                                                                                                         | not_active                                                                                                                                                                                                                                                                                     |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | {"receiveCode":"**************","currency":"USD","cost":2,"amount":"2","id":"217490496494341319","releaseId":"207895418695927702","expiredTime":"2022-11-06 23:38:57","receiveTime":"2022-11-04 07:38:57"} | ["{\"receiveCode\":\"**************\",\"currency\":\"USD\",\"cost\":2,\"amount\":\"2\",\"id\":\"228731347847126162\",\"releaseId\":\"254166972810150652\",\"expiredTime\":\"2022-11-07 00:31:39\",\"receiveTime\":\"2022-11-04 08:31:39\"}"] | [{"amount":"2","assignType":"GENERAL","businessId":"252501393052909325","cost":2,"cover":"","currency":"USD","expiredTime":"2023-03-11 01:18:45","greeting":"","id":"205311184617095369","receiveCode":"**************","receiveTime":"2023-03-08 09:18:45","releaseId":"282802017031453759"}] |

  场景大纲: 领红包-失败-releaseId为空
    当luck-用户发红包<luck>
    并且luck-配置红包releaseId为<releaseId>
    并且luck-异常-用户<customerId>抢红包
    并且luck-校验exceptions为<exceptions>
    例子:
      | releaseId | customerId     | luck                                                                                                                                                                                                                                                                                        | exceptions              |
      |           | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_PARAM_NULL |

  场景大纲: 领红包-失败-红包过期了
    当luck-用户发红包<luck>
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-异常-用户<customerId>抢红包
    并且luck-校验exceptions为<exceptions>
    例子:
      | expireTime          | customerId     | luck                                                                                                                                                                                                                                                                                        | exceptions           |
      | 2022-11-03 09:00:00 | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_EXPIRED |

  场景大纲: 领红包-失败-红包已经被抢过了
    当luck-用户发红包<luck>
    并且luck-配置红包为已抢状态
    并且luck-异常-用户<customerId>抢红包
    并且luck-校验exceptions为<exceptions>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | exceptions                        |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_RECEIVE_REPEAT_ERROR |

  场景大纲: 领红包-并发-多个人同时抢一个红包
    当luck-用户发红包<luck>
    并且luck-并发-多个用户同时抢红包
    并且luck-校验exceptions为<exceptions>
    例子:
      | luck                                                                                                                                                                                                                                                                                        | exceptions               |
      | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_EMPTY_ERROR |

  场景大纲: 开启红包-成功，领取人发放人不同
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-用户开启红包
    那么luck-校验accountActivityRequestList账务帐操作<accountActivityRequestList>
    并且luck-校验productAccountParamList业务帐冻结<productAccountParamList>
    并且luck-校验luckFortuneReceiveItemMap<luckFortuneReceiveItemMap>
    并且luck-校验redis-LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY<not_active>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | accountActivityRequestList                                                                                                                                                                                                                                                                        | productAccountParamList                                                                                                                                                                                                                                                                                               | luckFortuneReceiveItemMap                                                                                                                                                                                                                                                                                                                                                                                                                           | not_active |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2,"businessId":"245295342211559941","businessType":"ACTIVITY_AIRDROP","currency":"USD","fromCategory":"NORMAL","fromCustomerId":"**************","originalBusinessId":"293857380569940201","saasId":"kiki","strictCheck":false,"toCategory":"NORMAL","toCustomerId":"**************"}] | [{"amount":2.********,"bizId":"293857380569940201","customerId":"**************","productLimitCode":"AIRDROP_RELEASE_LIMIT_L0_KOL"},{"amount":2.0,"bizId":"245295342211559941","customerId":"**************","productLimitCode":"AIRDROP_RECEIVE_LIMIT_L0_KOL","productLimitCodes":["AIRDROP_RECEIVE_LIMIT_L0_KOL"]}] | {"amount":"2","assignType":"GENERAL","businessId":"245295342211559941","avatar":"","cost":2.0,"cover":"","currency":"USD","customerId":"**************","expiredTime":"2022-11-10 01:39:50","greeting":"","id":"273919363972698694","nickName":"TestNickName","receiveCode":"**************","receiveTime":"2022-11-07 09:39:50","releaseCustomerId":"**************","releaseId":"293857380569940201","releaseNickName":"TestNickName","status":5} | []         |

  场景大纲: 开启红包-成功，领取于发放人相同
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-用户开启红包
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验productAccountParamList业务帐冻结<productAccountParamList>
    并且luck-校验luckFortuneReceiveItemMap<luckFortuneReceiveItemMap>
    并且luck-校验redis-LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY<not_active>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | accountUnfreezeRequestList                                                                                                                                                                                                    | productAccountParamList                                                                                                                                                                                                                                                                                                      | luckFortuneReceiveItemMap                                                                                                                                                                                                                                                                                                                                                                                                                           | not_active |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2,"businessId":"214628462853668973","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"269289036293328290","retro":false,"saasId":"kiki"}] | [{"amount":2.********,"bizId":"269289036293328290","customerId":"**************","productLimitCode":"AIRDROP_RELEASE_LIMIT_L0_KOL"},{"amount":2.********,"bizId":"214628462853668973","customerId":"**************","productLimitCode":"AIRDROP_RECEIVE_LIMIT_L0_KOL","productLimitCodes":["AIRDROP_RECEIVE_LIMIT_L0_KOL"]}] | {"amount":"2","assignType":"GENERAL","businessId":"214628462853668973","avatar":"","cost":2.0,"cover":"","currency":"USD","customerId":"**************","expiredTime":"2022-11-10 02:09:26","greeting":"","id":"295331480968552352","nickName":"TestNickName","receiveCode":"**************","receiveTime":"2022-11-07 10:09:26","releaseCustomerId":"**************","releaseId":"269289036293328290","releaseNickName":"TestNickName","status":5} | []         |

  场景大纲: 开启红包-失败，红包过期
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-配置红包过期时间<expiredTime>
    并且luck-异常-用户开启红包
    并且luck-校验exceptions为<exceptions>
    例子:
      | expiredTime         | customerId     | luck                                                                                                                                                                                                                                                                                        | exceptions           |
      | 2022-11-07 09:00:00 | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_EXPIRED |

  场景大纲: 开启红包-失败，红包无效
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-配置红包无效
    并且luck-异常-用户开启红包
    并且luck-校验exceptions为<exceptions>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | exceptions           |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_EXPIRED |

  场景大纲: 开启红包-失败，红包已经被领取过
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-配置红包已被领取
    并且luck-异常-用户开启红包
    并且luck-校验exceptions为<exceptions>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | exceptions                        |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_RECEIVE_REPEAT_ERROR |

  场景大纲: 开启红包-失败，冻结产品帐异常
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-kaccounting异常，无法冻结产品帐
    并且luck-异常-用户开启红包
    并且luck-校验luckFortuneReceiveItemMap<luckFortuneReceiveItemMap>
    并且luck-校验exceptions为<exceptions>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | luckFortuneReceiveItemMap                                                                                                                                                                                                                                                                                                                                                                                                                           | exceptions                                  |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | {"amount":"2","assignType":"GENERAL","businessId":"217627289133424985","avatar":"","cost":2.0,"cover":"","currency":"USD","customerId":"**************","expiredTime":"2022-11-10 03:06:30","greeting":"","id":"201096127787404547","nickName":"TestNickName","receiveCode":"**************","receiveTime":"2022-11-07 11:06:30","releaseCustomerId":"**************","releaseId":"212497746316909880","releaseNickName":"TestNickName","status":3} | LUCK_FORTUNE_RECEIVE_LIMIT_AMOUNT_DAY_ERROR |


  场景大纲: 开启红包-失败，领取于发放人相同，解冻账务失败
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-kaccounting异常，无法解冻账务
    并且luck-异常-用户开启红包
    并且luck-校验luckFortuneReceiveItemMap<luckFortuneReceiveItemMap>
    并且luck-校验exceptions为<exceptions>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | luckFortuneReceiveItemMap                                                                                                                                                                                                                                                                                                                                                                                                                           | exceptions                                    |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | {"amount":"2","assignType":"GENERAL","businessId":"210668373519090648","avatar":"","cost":2.0,"cover":"","currency":"USD","customerId":"**************","expiredTime":"2022-11-12 03:04:29","greeting":"","id":"293577788206715738","nickName":"TestNickName","receiveCode":"**************","receiveTime":"2022-11-09 11:04:29","releaseCustomerId":"**************","releaseId":"274556889971326577","releaseNickName":"TestNickName","status":3} | remoteAccountingOperateService.unfreeze error |


  场景大纲: 开启红包-失败，领取于发放人不同，转账失败
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-kaccounting异常，无法转账
    并且luck-异常-用户开启红包
    并且luck-校验productAccountParamList业务帐冻结<productAccountParamList>
    并且luck-校验luckFortuneReceiveItemMap<luckFortuneReceiveItemMap>
    并且luck-校验exceptions为<exceptions>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | productAccountParamList                                                                                                                                                                                                                                                                                               | luckFortuneReceiveItemMap                                                                                                                                                                                                                                                                                                                                                                                                                           | exceptions                                     |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2.********,"bizId":"252760276883871634","customerId":"**************","productLimitCode":"AIRDROP_RELEASE_LIMIT_L0_KOL"},{"amount":2.0,"bizId":"284016205309167015","customerId":"**************","productLimitCode":"AIRDROP_RECEIVE_LIMIT_L0_KOL","productLimitCodes":["AIRDROP_RECEIVE_LIMIT_L0_KOL"]}] | {"amount":"2","assignType":"GENERAL","businessId":"285248205548058528","avatar":"","cost":2.0,"cover":"","currency":"USD","customerId":"**************","expiredTime":"2022-11-12 23:17:47","greeting":"","id":"257784985633366722","nickName":"TestNickName","receiveCode":"**************","receiveTime":"2022-11-10 07:17:47","releaseCustomerId":"**************","releaseId":"201515298126517738","releaseNickName":"TestNickName","status":3} | remoteAccountingActivityService transfer error |


  场景大纲: 开启红包-失败，领取于发放人不同，第一次失败，操作第二次
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-kaccounting异常，无法转账
    并且luck-异常-用户开启红包
    并且luck-kaccounting恢复正常，可以转账
    并且luck-用户开启红包
    那么luck-校验accountActivityRequestList账务帐操作<accountActivityRequestList>
    并且luck-校验productAccountParamList业务帐冻结<productAccountParamList>
    并且luck-校验luckFortuneReceiveItemMap<luckFortuneReceiveItemMap>
    并且luck-校验exceptions为<exceptions>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | accountActivityRequestList                                                                                                                                                                                                                                                                        | productAccountParamList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | luckFortuneReceiveItemMap                                                                                                                                                                                                                                                                                                                                                                                                                           | exceptions                                     |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2,"businessId":"272730926196054453","businessType":"ACTIVITY_AIRDROP","currency":"USD","fromCategory":"NORMAL","fromCustomerId":"**************","originalBusinessId":"225602277500246943","saasId":"kiki","strictCheck":false,"toCategory":"NORMAL","toCustomerId":"**************"}] | [{"amount":2.********,"bizId":"212898306409648909","customerId":"**************","productLimitCode":"AIRDROP_RELEASE_LIMIT_L0_KOL"},{"amount":2.0,"bizId":"256524654878093943","customerId":"**************","productLimitCode":"AIRDROP_RECEIVE_LIMIT_L0_KOL","productLimitCodes":["AIRDROP_RECEIVE_LIMIT_L0_KOL"]},{"amount":2.0,"bizId":"256524654878093943","customerId":"**************","productLimitCode":"AIRDROP_RECEIVE_LIMIT_L0_KOL","productLimitCodes":["AIRDROP_RECEIVE_LIMIT_L0_KOL"]}] | {"amount":"2","assignType":"GENERAL","businessId":"288625156771725157","avatar":"","cost":2.0,"cover":"","currency":"USD","customerId":"**************","expiredTime":"2022-11-12 23:34:19","greeting":"","id":"264126002465220168","nickName":"TestNickName","receiveCode":"**************","receiveTime":"2022-11-10 07:34:19","releaseCustomerId":"**************","releaseId":"253568656736541987","releaseNickName":"TestNickName","status":5} | remoteAccountingActivityService transfer error |

  场景大纲: 开启红包-失败，领取于发放人不同，第一次成功，操作第二次
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-用户开启红包
    并且luck-异常-用户开启红包
    那么luck-校验accountActivityRequestList账务帐操作<accountActivityRequestList>
    并且luck-校验productAccountParamList业务帐冻结<productAccountParamList>
    并且luck-校验luckFortuneReceiveItemMap<luckFortuneReceiveItemMap>
    并且luck-校验exceptions为<exceptions>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                        | accountActivityRequestList                                                                                                                                                                                                                                                                        | productAccountParamList                                                                                                                                                                                                                                                                                               | luckFortuneReceiveItemMap                                                                                                                                                                                                                                                                                                                                                                                                                           | exceptions           |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2,"businessId":"233682597318310508","businessType":"ACTIVITY_AIRDROP","currency":"USD","fromCategory":"NORMAL","fromCustomerId":"**************","originalBusinessId":"216451244887724039","saasId":"kiki","strictCheck":false,"toCategory":"NORMAL","toCustomerId":"**************"}] | [{"amount":2.********,"bizId":"216451244887724039","customerId":"**************","productLimitCode":"AIRDROP_RELEASE_LIMIT_L0_KOL"},{"amount":2.0,"bizId":"233682597318310508","customerId":"**************","productLimitCode":"AIRDROP_RECEIVE_LIMIT_L0_KOL","productLimitCodes":["AIRDROP_RECEIVE_LIMIT_L0_KOL"]}] | {"amount":"2","assignType":"GENERAL","businessId":"233682597318310508","avatar":"","cost":2.0,"cover":"","currency":"USD","customerId":"**************","expiredTime":"2022-11-10 23:21:28","greeting":"","id":"217306218525957554","nickName":"TestNickName","receiveCode":"**************","receiveTime":"2022-11-08 07:21:04","releaseCustomerId":"**************","releaseId":"216451244887724039","releaseNickName":"TestNickName","status":5} | LUCK_FORTUNE_EXPIRED |


#  场景大纲: 开启红包-并发，两个线程同时打开一个红包
#    当luck-用户发红包<luck>
#    并且luck-用户<customerId>抢红包
#    并且luck-并发-用户开启红包
#    并且luck-校验productAccountParamList业务帐冻结<productAccountParamList>
#    那么luck-校验accountActivityRequestList账务帐操作<accountActivityRequestList>
#    并且luck-校验luckFortuneReceiveItemMap<luckFortuneReceiveItemMap>
#    并且luck-校验exceptions为<exceptions>
#    例子:
#      | customerId     | luck                                                                                                                                                                                                                                                                                        | productAccountParamList                                                                                                                                                                                                                                                                                               | accountActivityRequestList                                                                                                                                                                                                                                                                        | luckFortuneReceiveItemMap                                                                                                                                                                                                                                                                                                                                                                                                                           | exceptions                        |
#      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2.********,"bizId":"254414015721360941","customerId":"**************","productLimitCode":"AIRDROP_RELEASE_LIMIT_L0_KOL"},{"amount":2.0,"bizId":"291901668130756840","customerId":"**************","productLimitCode":"AIRDROP_RECEIVE_LIMIT_L0_KOL","productLimitCodes":["AIRDROP_RECEIVE_LIMIT_L0_KOL"]}] | [{"amount":2,"businessId":"291901668130756840","businessType":"ACTIVITY_AIRDROP","currency":"USD","fromCategory":"NORMAL","fromCustomerId":"**************","originalBusinessId":"254414015721360941","saasId":"kiki","strictCheck":false,"toCategory":"NORMAL","toCustomerId":"**************"}] | {"amount":"2","assignType":"GENERAL","businessId":"291901668130756840","avatar":"","cost":2.0,"cover":"","currency":"USD","customerId":"**************","expiredTime":"2022-11-10 22:32:54","greeting":"","id":"208062541457614105","nickName":"TestNickName","receiveCode":"**************","receiveTime":"2022-11-08 06:32:53","releaseCustomerId":"**************","releaseId":"254414015721360941","releaseNickName":"TestNickName","status":5} | LUCK_FORTUNE_RECEIVE_REPEAT_ERROR |

  场景大纲: 红包未领完，退款-成功
    当luck-用户发红包<luck>
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-配置数据库红包过期时间<expireTime>
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-系统执行未领红包退款任务
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>
    例子:
      | expireTime          | luck                                                                                                                                                                                                                                                                                        | accountUnfreezeRequestList                                                                                                                                                                                                    | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | luckFortuneRefundItem                                                                                                                                                                                                                                                        |
      | 2022-11-03 09:00:00 | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2,"businessId":"211833215226205678","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"282866967942052410","retro":false,"saasId":"kiki"}] | {"amount":"2","assignType":"GENERAL","avatar":"","cost":2.********,"cover":"","created":"2022-11-09 03:18:23","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2,\"currency\":\"USD\",\"id\":\"249261055556712030\",\"releaseId\":\"282866967942052410\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"282866967942052410","nickName":"TestNickName","num":1,"receiveList":"[]","receiveNum":0,"refundAmount":"2","releaseCode":"gYAK9Y9RrnIWvTdjXkbu","releaseTime":"2022-11-09 03:18:23","status":9,"validTime":1} | [{"amount":"2","businessId":"263925947592410141","created":"2022-11-09 06:59:58","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"263925947592410141","refundId":"284007977402098779","refundTime":"2022-11-09 06:59:50","status":9}] |


  场景大纲: 红包未领完，退款-失败，luckFortuneReleaseItemBuilder.update异常
    当luck-用户发红包<luck>
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-配置数据库红包过期时间<expireTime>
    并且luck-配置luckFortuneReleaseItemBuilder.update异常
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-系统执行未领红包退款任务
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>
    例子:
      | expireTime          | luck                                                                                                                                                                                                                                                                                        | accountUnfreezeRequestList | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | luckFortuneRefundItem                                                                                                                                                                                                                                                        |
      | 2022-11-03 09:00:00 | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | []                         | {"amount":"2","assignType":"GENERAL","avatar":"","cost":2.********,"cover":"","created":"2022-11-09 11:04:30","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2,\"currency\":\"USD\",\"id\":\"275534000672294155\",\"releaseId\":\"282475441581558024\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"282475441581558024","nickName":"TestNickName","num":1,"receiveList":"[]","receiveNum":0,"releaseCode":"UuGZtz13VEAXAzaros0q","releaseTime":"2022-11-09 11:04:30","status":7,"validTime":1} | [{"amount":"2","businessId":"232369572905249765","created":"2022-11-10 07:34:20","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"232369572905249765","refundId":"205160973556073048","refundTime":"2022-11-10 07:34:20","status":8}] |


  场景大纲: 红包未领完，退款-失败，remoteAccountingOperateService.unfreeze异常
    当luck-用户发红包<luck>
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-配置数据库红包过期时间<expireTime>
    并且luck-配置remoteAccountingOperateService.unfreeze异常
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-系统执行未领红包退款任务
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>
    例子:
      | expireTime          | luck                                                                                                                                                                                                                                                                                        | accountUnfreezeRequestList | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | luckFortuneRefundItem                                                                                                                                                                                                                                                        |
      | 2022-11-03 09:00:00 | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | []                         | {"amount":"2","assignType":"GENERAL","cost":2,"cover":"","created":"2022-12-30 07:45:28","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2.********,\"currency\":\"USD\",\"id\":\"265359223797853286\",\"releaseId\":\"210909397933840636\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"210909397933840636","nickName":"TestNickName","num":1,"receiveList":"[]","receiveNum":0,"releaseCode":"dXwbbA7OCF.i58DokCNm","releaseTime":"2022-12-30 07:45:28","status":6,"validTime":1} | [{"amount":"2","businessId":"280912583365286034","created":"2022-11-09 11:04:30","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"280912583365286034","refundId":"268768465206846259","refundTime":"2022-11-09 11:04:30","status":8}] |


  场景大纲: 红包未领完，退款-失败，第一次失败，重复退款
    当luck-用户发红包<luck>
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-配置数据库红包过期时间<expireTime>
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-配置remoteAccountingOperateService.unfreeze异常
    并且luck-系统执行未领红包退款任务
    并且luck-配置remoteAccountingOperateService.unfreeze正常
    并且luck-系统执行未领红包退款任务
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>
    例子:
      | expireTime          | luck                                                                                                                                                                                                                                                                                        | accountUnfreezeRequestList                                                                                                                                                                  | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | luckFortuneRefundItem                                                                                                                                                                                                                                                        |
      | 2022-11-03 09:00:00 | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2,"businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"269371052337314312","retro":false,"saasId":"kiki"}] | {"amount":"2","assignType":"GENERAL","cost":2,"cover":"","created":"2022-12-29 11:59:03","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2.********,\"currency\":\"USD\",\"id\":\"268584857897218371\",\"releaseId\":\"219484110133474552\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"219484110133474552","nickName":"TestNickName","num":1,"receiveList":"[]","receiveNum":0,"refundAmount":"2","releaseCode":"0Q06J34iR4KBT6SGePc.","releaseTime":"2022-12-29 11:59:03","status":9,"validTime":1} | [{"amount":"2","businessId":"287758846892545953","created":"2022-12-30 03:28:27","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"287758846892545953","refundId":"241756584250180945","refundTime":"2022-12-30 03:28:27","status":9}] |

  场景大纲: 用户发了三个普通红包，只打开了一个,其余退款,第一次退款失败，第二次退款成功
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-用户开启红包
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-配置数据库红包过期时间<expireTime>
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-配置remoteAccountingOperateService.unfreeze异常
    并且luck-系统执行未领红包退款任务
    并且luck-配置remoteAccountingOperateService.unfreeze正常
    并且luck-系统执行未领红包退款任务
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>
    例子:
      | customerId     | expireTime          | luck                                                                                                                                                                                                                                                                                          | accountUnfreezeRequestList                                                                                                                                                                                                      | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | luckFortuneRefundItem                                                                                                                                                                                                                                                          |
      | ************** | 2022-11-03 09:00:00 | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L1","nickName":"","avatar":""},"assignType":"GENERAL","num":"3","amount":"100","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":200,"businessId":"273690492422849170","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"266084119105870411","retro":false,"saasId":"kiki"}] | {"amount":"300","assignType":"GENERAL","cost":100,"cover":"","created":"2022-12-30 06:48:57","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"100\",\"cost\":100.********,\"currency\":\"USD\",\"id\":\"235867189011613020\",\"releaseId\":\"266084119105870411\"},{\"amount\":\"100\",\"cost\":100.********,\"currency\":\"USD\",\"id\":\"299825328062112419\",\"releaseId\":\"266084119105870411\"},{\"amount\":\"100\",\"cost\":100.********,\"currency\":\"USD\",\"id\":\"256412988941074776\",\"releaseId\":\"266084119105870411\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"266084119105870411","nickName":"TestNickName","num":3,"receiveList":"[\"{\\\"receiveCode\\\":\\\"**************\\\",\\\"currency\\\":\\\"USD\\\",\\\"cost\\\":100,\\\"amount\\\":\\\"100\\\",\\\"id\\\":\\\"235867189011613020\\\",\\\"releaseId\\\":\\\"266084119105870411\\\",\\\"expiredTime\\\":\\\"2023-01-01 22:48:57\\\",\\\"receiveTime\\\":\\\"2022-12-30 06:48:57\\\"}\"]","receiveNum":1,"refundAmount":"200","releaseCode":"MOdmaIECuLjndKoPsTk2","releaseTime":"2022-12-30 06:48:57","status":9,"validTime":1} | [{"amount":"200","businessId":"266084119105870411","created":"2022-12-30 06:48:57","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"266084119105870411","refundId":"273690492422849170","refundTime":"2022-12-30 06:48:57","status":9}] |

  场景大纲: 红包未领完，退款，第一次成功，重复退款
    当luck-用户发红包<luck>
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-配置数据库红包过期时间<expireTime>
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-系统执行未领红包退款任务
    并且luck-系统执行未领红包退款任务
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>
    例子:
      | expireTime          | luck                                                                                                                                                                                                                                                                                        | accountUnfreezeRequestList                                                                                                                                                                                                    | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | luckFortuneRefundItem                                                                                                                                                                                                                                                        |
      | 2022-11-03 09:00:00 | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2,"businessId":"215185526466421206","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"248676391066686422","retro":false,"saasId":"kiki"}] | {"amount":"2","assignType":"GENERAL","avatar":"","cost":2.********,"cover":"","created":"2022-11-09 12:32:25","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2,\"currency\":\"USD\",\"id\":\"290647511555356750\",\"releaseId\":\"203452768455896486\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"203452768455896486","nickName":"TestNickName","num":1,"receiveList":"[]","receiveNum":0,"refundAmount":"2","releaseCode":"23.X96RrLM2o0QJK1MYi","releaseTime":"2022-11-09 12:32:25","status":9,"validTime":1} | [{"amount":"2","businessId":"263925947592410141","created":"2022-11-09 06:59:58","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"263925947592410141","refundId":"284007977402098779","refundTime":"2022-11-09 06:59:50","status":9}] |


  场景大纲: 红包未激活，退款-成功
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-配置数据库红包过期时间<expireTime>
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-系统执行未领红包退款任务
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>
    例子:
      | customerId     | expireTime          | luck                                                                                                                                                                                                                                                                                        | accountUnfreezeRequestList                                                                                                                                                                                                    | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | luckFortuneRefundItem                                                                                                                                                                                                                                                        |
      | ************** | 2022-11-03 09:00:00 | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2,"businessId":"275930381963651200","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"213299376314068615","retro":false,"saasId":"kiki"}] | {"amount":"2","assignType":"GENERAL","avatar":"","cost":2.********,"cover":"","created":"2022-11-09 12:32:25","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2,\"currency\":\"USD\",\"id\":\"210485865951137079\",\"releaseId\":\"245404776945136560\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"245404776945136560","nickName":"TestNickName","num":1,"receiveList":"[\"{\\\"receiveCode\\\":\\\"**************\\\",\\\"currency\\\":\\\"USD\\\",\\\"cost\\\":2,\\\"amount\\\":\\\"2\\\",\\\"id\\\":\\\"210485865951137079\\\",\\\"releaseId\\\":\\\"245404776945136560\\\",\\\"expiredTime\\\":\\\"2022-11-12 04:32:25\\\",\\\"receiveTime\\\":\\\"2022-11-09 12:32:25\\\"}\"]","receiveNum":1,"refundAmount":"2","releaseCode":"Q5M0LSshlb09KKK6Myo6","releaseTime":"2022-11-09 12:32:25","status":9,"validTime":1} | [{"amount":"2","businessId":"263925947592410141","created":"2022-11-09 06:59:58","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"263925947592410141","refundId":"284007977402098779","refundTime":"2022-11-09 06:59:50","status":9}] |


  场景大纲: 用户发了多个手气红包，只打开了一个,其余退款
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-用户开启红包
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-配置数据库红包过期时间<expireTime>
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-系统执行未领红包退款任务
    那么luck-增加忽略校验-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-增加忽略校验-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-增加忽略校验-校验luckFortuneRefundItem<luckFortuneRefundItem>
    并且luck-校验总金额
    例子:
      | customerId     | expireTime          | luck                                                                                                                                                                                                                                                                                          | accountUnfreezeRequestList                                                                                                                                                                                                              | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | luckFortuneRefundItem                                                                                                                                                                                                                                                        |
      | ************** | 2022-11-03 09:00:00 | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L1","nickName":"","avatar":""},"assignType":"FORTUNE","num":"5","amount":"100","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":84.50109238,"businessId":"242266645172778864","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"246281670395804934","retro":false,"saasId":"kiki"}] | {"amount":"100","assignType":"FORTUNE","avatar":"","cost":100.********,"cover":"","created":"2022-11-09 12:25:53","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"27.15350724\",\"cost\":27.15350724,\"currency\":\"USD\",\"id\":\"203664375331950919\",\"releaseId\":\"218517897494845745\"},{\"amount\":\"27.82026225\",\"cost\":27.82026225,\"currency\":\"USD\",\"id\":\"286878604069671630\",\"releaseId\":\"218517897494845745\"},{\"amount\":\"11.34776060\",\"cost\":11.34776060,\"currency\":\"USD\",\"id\":\"292268828857910388\",\"releaseId\":\"218517897494845745\"},{\"amount\":\"29.17934332\",\"cost\":29.17934332,\"currency\":\"USD\",\"id\":\"233407080900126319\",\"releaseId\":\"218517897494845745\"},{\"amount\":\"4.49912659\",\"cost\":4.49912659,\"currency\":\"USD\",\"id\":\"204517593949898730\",\"releaseId\":\"218517897494845745\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"218517897494845745","nickName":"TestNickName","num":5,"receiveList":"[\"{\\\"receiveCode\\\":\\\"**************\\\",\\\"currency\\\":\\\"USD\\\",\\\"cost\\\":27.15350724,\\\"amount\\\":\\\"27.15350724\\\",\\\"id\\\":\\\"203664375331950919\\\",\\\"releaseId\\\":\\\"218517897494845745\\\",\\\"expiredTime\\\":\\\"2022-11-12 04:25:53\\\",\\\"receiveTime\\\":\\\"2022-11-09 12:25:53\\\"}\"]","receiveNum":1,"refundAmount":"72.84649276","releaseCode":"GAHQaJjbtY2KAw759cjm","releaseTime":"2022-11-09 12:25:53","status":9,"validTime":1} | [{"amount":"2","businessId":"263925947592410141","created":"2022-11-09 06:59:58","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"263925947592410141","refundId":"284007977402098779","refundTime":"2022-11-09 06:59:50","status":9}] |

  场景大纲: 用户发了三个普通红包，只打开了一个,其余退款
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-用户开启红包
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-配置数据库红包过期时间<expireTime>
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-系统执行未领红包退款任务
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>
    例子:
      | customerId     | expireTime          | luck                                                                                                                                                                                                                                                                                          | accountUnfreezeRequestList                                                                                                                                                                                                      | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | luckFortuneRefundItem                                                                                                                                                                                                                                                          |
      | ************** | 2022-11-03 09:00:00 | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L1","nickName":"","avatar":""},"assignType":"GENERAL","num":"3","amount":"100","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":200,"businessId":"273690492422849170","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"266084119105870411","retro":false,"saasId":"kiki"}] | {"amount":"300","assignType":"GENERAL","cost":100,"cover":"","created":"2022-12-30 06:48:57","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"100\",\"cost\":100.********,\"currency\":\"USD\",\"id\":\"235867189011613020\",\"releaseId\":\"266084119105870411\"},{\"amount\":\"100\",\"cost\":100.********,\"currency\":\"USD\",\"id\":\"299825328062112419\",\"releaseId\":\"266084119105870411\"},{\"amount\":\"100\",\"cost\":100.********,\"currency\":\"USD\",\"id\":\"256412988941074776\",\"releaseId\":\"266084119105870411\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"266084119105870411","nickName":"TestNickName","num":3,"receiveList":"[\"{\\\"receiveCode\\\":\\\"**************\\\",\\\"currency\\\":\\\"USD\\\",\\\"cost\\\":100,\\\"amount\\\":\\\"100\\\",\\\"id\\\":\\\"235867189011613020\\\",\\\"releaseId\\\":\\\"266084119105870411\\\",\\\"expiredTime\\\":\\\"2023-01-01 22:48:57\\\",\\\"receiveTime\\\":\\\"2022-12-30 06:48:57\\\"}\"]","receiveNum":1,"refundAmount":"200","releaseCode":"MOdmaIECuLjndKoPsTk2","releaseTime":"2022-12-30 06:48:57","status":9,"validTime":1} | [{"amount":"200","businessId":"266084119105870411","created":"2022-12-30 06:48:57","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"266084119105870411","refundId":"273690492422849170","refundTime":"2022-12-30 06:48:57","status":9}] |


  场景大纲: 用户发了两个红包，一个用户只能领一次
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    并且luck-异常-用户<customerId>抢红包
    并且luck-校验exceptions为<exceptions>
    例子:
      | customerId     | luck                                                                                                                                                                                                                                                                                          | exceptions                        |
      | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L1","nickName":"","avatar":""},"assignType":"GENERAL","num":"2","amount":"100","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | LUCK_FORTUNE_RECEIVE_REPEAT_ERROR |


  场景大纲: 退款失败，定时任务将红包状态恢复到超时，重试退款
    当luck-用户发红包<luck>
    并且luck-配置未领红包过期时间为<expireTime>
    并且luck-配置数据库红包过期时间<expireTime>
    并且luck-配置remoteAccountingOperateService.unfreeze异常
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-系统执行未领红包退款任务
    并且luck-配置remoteAccountingOperateService.unfreeze正常
    并且luck-系统执行ActivitySignExpiredJob注册超时任务
    并且luck-系统执行未领红包退款任务
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>
    并且luck-校验exceptions为<exceptions>
    例子:
      | expireTime          | luck                                                                                                                                                                                                                                                                                        | accountUnfreezeRequestList                                                                                                                                                                  | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | luckFortuneRefundItem                                                                                                                                                                                                                                                        | exceptions |
      | 2022-11-03 09:00:00 | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | [{"amount":2,"businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"238221208258029722","retro":false,"saasId":"kiki"}] | {"amount":"2","assignType":"GENERAL","cost":2,"cover":"","created":"2022-12-30 07:45:30","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2.********,\"currency\":\"USD\",\"id\":\"266285886332417531\",\"releaseId\":\"235999484220041347\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"235999484220041347","nickName":"TestNickName","num":1,"receiveList":"[]","receiveNum":0,"refundAmount":"2","releaseCode":"q5GUoVFEyHsKCjUxugAy","releaseTime":"2022-12-30 07:45:30","status":9,"validTime":1} | [{"amount":"2","businessId":"264893117916709144","created":"2022-12-29 02:45:38","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"264893117916709144","refundId":"247367502170942508","refundTime":"2022-12-29 02:45:38","status":9}] | null       |


  场景大纲: 空投领取超15分钟，自动将空投退回到原队列中
    当luck-用户发红包<luck>
    并且luck-用户<customerId>抢红包
    当luck-系统接收到延迟mq消息<event>
    并且luck-校验redis-LUCK_FORTUNE_RELEASE_STATUS_KEY<status>
    并且luck-校验redis-LUCK_FORTUNE_RELEASE_INFO_KEY<info>
    并且luck-校验redis-LUCK_FORTUNE_RELEASE_NOT_DRAW_KEY<not_draw>
    那么luck-校验redis-LUCK_FORTUNE_RECEIVE_DETAIL_KEY<detail>
    那么luck-校验redis-LUCK_FORTUNE_RELEASE_DREW_KEY<drew>
    那么luck-校验redis-LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY<not_active>
    那么luck-校验redis-LUCK_FORTUNE_RECEIVE_EXPIRE_KEY<receive_expire>

    例子:
      | event  | customerId     | luck                                                                                                                                                                                                                                                                                        | status | info                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | not_draw                                                                                                                                                 | detail | drew | not_active | receive_expire                                                                                                                                                                                                                                                                                                                |
      | REVERT | ************** | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | 1      | {"amount":"2","assignType":"GENERAL","cost":2,"cover":"","created":"2023-03-08 09:27:52","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2.********,\"currency\":\"USD\",\"id\":\"266029075011002032\",\"releaseId\":\"264509569816754754\"}]","expiredTime":"2023-03-09 01:27:52","greeting":"","id":"264509569816754754","modified":"2023-03-08 09:27:52","num":1,"releaseCode":"TVrQR1AKdLEYswugbWAG","releaseTime":"2023-03-08 09:27:52","status":0,"validTime":1} | [{"amount":"2","currency":"USD","id":"208930031983601318","cost":2,"receiveCode":"","releaseId":"202738410009546863","expiredTime":"","receiveTime":""}] | null   | []   | []         | {"amount":"2","assignType":"GENERAL","businessId":"256233976983381768","cost":2,"cover":"","currency":"USD","expiredTime":"2022-12-31 18:55:18","greeting":"","id":"208930031983601318","receiveCode":"**************","receiveTime":"2022-12-29 02:55:18","releaseId":"202738410009546863","releaseNickName":"TestNickName"} |

  场景大纲: 空投超时
    当luck-用户发红包<luck>
    并且luck-配置数据库红包过期时间<expireTime>
    当luck-系统接收到延迟mq消息<event>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>

    例子:
      | expireTime          | event  | luck                                                                                                                                                                                                                                                                                        | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
      | 2022-11-03 09:00:00 | EXPIRE | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | {"amount":"2","assignType":"GENERAL","cost":2,"cover":"","created":"2022-12-29 03:22:56","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2.********,\"currency\":\"USD\",\"id\":\"224805976598977515\",\"releaseId\":\"265618051166858047\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"265618051166858047","nickName":"TestNickName","num":1,"receiveList":"[]","receiveNum":0,"releaseCode":"JwAepYmb.4A3PAbRBgmW","releaseTime":"2022-12-29 03:22:56","status":6,"validTime":1} |

  场景大纲: 空投退款
    当luck-用户发红包<luck>
    并且luck-配置数据库红包过期时间<expireTime>
    当luck-系统接收到延迟mq消息<event>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>

    例子:
      | expireTime          | event  | luck                                                                                                                                                                                                                                                                                        | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | accountUnfreezeRequestList                                                                                                                                                                                                    | luckFortuneRefundItem                                                                                                                                                                                                                                                        |
      | 2022-11-03 09:00:00 | REFUND | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | {"amount":"2","assignType":"GENERAL","cost":2,"cover":"","created":"2022-12-29 03:31:08","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2.********,\"currency\":\"USD\",\"id\":\"277055746390938364\",\"releaseId\":\"226440116613284845\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"226440116613284845","nickName":"TestNickName","num":1,"refundAmount":"2","releaseCode":"MbAN1ojSEvENAJyA91n.","releaseTime":"2022-12-29 03:31:08","status":9,"validTime":1} | [{"amount":2,"businessId":"294388100417651520","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"226440116613284845","retro":false,"saasId":"kiki"}] | [{"amount":"2","businessId":"226440116613284845","created":"2022-12-29 03:31:09","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"226440116613284845","refundId":"294388100417651520","refundTime":"2022-12-29 03:31:09","status":9}] |

  场景大纲: 空投退款-重复mq
    当luck-用户发红包<luck>
    并且luck-配置数据库红包过期时间<expireTime>
    当luck-系统接收到延迟mq消息<event>
    当luck-系统接收到延迟mq消息<event>
    并且luck-校验luckFortuneReleaseItem<luckFortuneReleaseItem>
    那么luck-校验accountUnfreezeRequestList账务帐操作<accountUnfreezeRequestList>
    并且luck-校验luckFortuneRefundItem<luckFortuneRefundItem>

    例子:
      | expireTime          | event  | luck                                                                                                                                                                                                                                                                                        | luckFortuneReleaseItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | accountUnfreezeRequestList                                                                                                                                                                                                    | luckFortuneRefundItem                                                                                                                                                                                                                                                        |
      | 2022-11-03 09:00:00 | REFUND | {"customer":{"userName":"test","id":"**************","phone":"","email":"","type":"1","kycLevel":"L0","nickName":"","avatar":""},"assignType":"GENERAL","num":"1","amount":"2","currency":"USD","cover":"","greeting":"","validTime":"1","keepDecimalForCoin":"","tokenId":"","usdCost":""} | {"amount":"2","assignType":"GENERAL","cost":2,"cover":"","created":"2022-12-29 03:31:08","currency":"USD","customerId":"**************","detail":"[{\"amount\":\"2\",\"cost\":2.********,\"currency\":\"USD\",\"id\":\"277055746390938364\",\"releaseId\":\"226440116613284845\"}]","expiredTime":"2022-11-03 09:00:00","greeting":"","id":"226440116613284845","nickName":"TestNickName","num":1,"refundAmount":"2","releaseCode":"MbAN1ojSEvENAJyA91n.","releaseTime":"2022-12-29 03:31:08","status":9,"validTime":1} | [{"amount":2,"businessId":"294388100417651520","businessType":"ACTIVITY_AIRDROP","category":"NORMAL","currency":"USD","customerId":"**************","originalBusinessId":"226440116613284845","retro":false,"saasId":"kiki"}] | [{"amount":"2","businessId":"226440116613284845","created":"2022-12-29 03:31:09","currency":"USD","customerId":"**************","message":"红包未领完，退款","originBusinessId":"226440116613284845","refundId":"294388100417651520","refundTime":"2022-12-29 03:31:09","status":9}] |


