package com.kikitrade.activity.dal.tablestore.model;

import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
@Table(name = "activity_customer_reward")
public class ActivityCustomReward implements Serializable {

    public static final String INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX = "activity_customer_reward_index";
    //二级索引，查询是否存在某个状态的记录
    public static final String INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID = "idx_activity_custom_reward_by_batch_id_v3";

    public static final String INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS = "idx_activity_custom_reward_by_status_v2";

    @PartitionKey(name = "batch_id")
    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "batch_id")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, pkColumn = "batch_id")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, pkColumn = "batch_id", pkValue = 1)
    private String batchId;

    @PartitionKey(name = "customer_id", value = 1)
    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "customer_id")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, pkColumn = "customer_id", pkValue = 1)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, pkColumn = "customer_id", pkValue = 2)
    private String customerId;

    @PartitionKey(name = "seq", value = 2)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, pkColumn = "seq", pkValue = 2)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, pkColumn = "seq", pkValue = 3)
    private String seq;

    @Column(name = "status", isDefined = true)
    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "status")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, pkColumn = "status")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "status")
    private String status;

    @Column(name = "activity_id", isDefined = true)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "activity_id")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "activity_id")
    private String activityId;

    @Column(name = "user_name", isDefined = true)
    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "user_name")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "user_name")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "user_name")
    private String userName;

    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "nick_name")
    @Column(name = "nick_name", isDefined = true)
    private String nickName;

    @Column(name = "currency", isDefined = true)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "currency")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "currency")
    private String currency;

    @Column(name = "amount", isDefined = true, type = Column.Type.STRING)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "amount")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "amount")
    private String amount;

    @Column(name = "created", isDefined = true, type = Column.Type.INTEGER)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "created")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "created")
    private Date created;

    @Column(name = "modified", isDefined = true, type = Column.Type.INTEGER)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "modified")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "modified")
    private Date modified;

    @Column(name = "phone", isDefined = true)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "phone")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "phone")
    private String phone;

    @Column(name = "email", isDefined = true)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "email")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "email")
    private String email;

    //实际发奖时间
    @Column(name = "reward_time", isDefined = true, type = Column.Type.INTEGER)
    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "reward_time")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "reward_time")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "reward_time")
    private Date rewardTime;

    //分片，执行定时任务
    @Column(name = "shard", isDefined = true)
    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "shard", fieldType = FieldType.LONG)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "shard")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "shard")
    private Integer shard;

    //失败原因
    @Column(name = "message", isDefined = true)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "message")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "message")
    private String message;

    //业务id
    @Column(name = "business_id", isDefined = true)
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "business_id")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "business_id")
    private String businessId;

    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "reward_type")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "reward_type")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "reward_type")
    @Column(name = "reward_type", isDefined = true)
    private String rewardType;

    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "side")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "side")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "side")
    @Column(name = "side", isDefined = true)
    private String side;

    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "user_type")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "user_type")
    @Column(name = "user_type", isDefined = true)
    private String userType = "Normal";

    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "scope")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "scope")
    @Column(name = "scope", isDefined = true)
    private String scope;

    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_STATUS, dfColumn = "level")
    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "level")
    @Column(name = "level", isDefined = true)
    private String level;

    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "refer_id")
    @Column(name = "refer_id", isDefined = true)
    private String referId;

    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "activity_name")
    @Column(name = "activity_name", isDefined = true)
    private String activityName;

    // 默认是USD币种
    @Column(name = "cost", isDefined = true, type = Column.Type.DOUBLE)
    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "cost", fieldType = FieldType.BOOLEAN)
    private BigDecimal cost;

    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "business_type")
    @Column(name = "business_type", isDefined = true)
    private String businessType;

    @SearchIndex(name = INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX, column = "extend_param")
    @Column(name = "extend_param", isDefined = true)
    private String extendParam;

    @Column(name = "saas_id", isDefined = true)
    private String saasId;

    public Map<String, String> getExtendParamMap(){
        if(StringUtils.isNotBlank(this.extendParam)){
            return JSON.parseObject(this.extendParam, Map.class);
        }
        return new HashMap<>();
    }

    public void addExtendParam(String key, String value){
        Map<String, String> map = this.getExtendParamMap();
        if(MapUtils.isEmpty(map)){
            map = new HashMap<>();
        }
        map.put(key, value);
        this.setExtendParam(JSON.toJSONString(map));
    }

    /**
     * 邀请人
     */
    ActivityCustomReward inviter;

    @Index(name = INDEX_ACTIVITY_CUSTOMER_REWARD_BY_BATCH_ID, dfColumn = "vip_level")
    @Column(name = "vip_level", isDefined = true)
    private String vipLevel;

    @Column(name = "address", isDefined = true)
    private String address;
}
