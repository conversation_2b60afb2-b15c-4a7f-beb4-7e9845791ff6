package com.kikitrade.activity.service.common.strategy.twitter;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.config.SaasConfig;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * © 2024 by Aspen Digital Limited.All Rights Reserved.
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/23 17:37
 */
@Component
@Slf4j
public class TwitterCheckCreateDaysStrategyService implements TwitterSaasStrategyService {
    @Resource
    private RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    private RedisService redisService;

    @Override
    public String strategy() {
        return SaasStrategyConstant.TwitterStrategyEnum.CHECK_CREATE_DAYS.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        log.info("TwitterCheckMugenStrategyService execute start, request: {}", JSON.toJSONString(request));

        // 检查X账号创建时间
        checkTwitterCreatedDays(request.getSaasId(), request.getCreatedAt());

        if (request.getAuthId() != null && request.getLoginTwitterId() != null) {
            if (request.getAuthId().equals(request.getLoginTwitterId())) {
                List<TCustomerDTO> tCustomerBySocial = remoteCustomerBindService.findTCustomerBySocial(request.getSaasId(), ActivityTaskConstant.OpenSocialEnum.twitter.name(), request.getAuthId());
                if (CollectionUtils.isNotEmpty(tCustomerBySocial)) {
                    List<TCustomerDTO> customerDTOS = tCustomerBySocial.stream().filter(social -> !request.getCustomerId().equals(social.getUid())).toList();
                    if (CollectionUtils.isNotEmpty(customerDTOS)) {
                        for(TCustomerDTO customerDTO : customerDTOS){
                            //解绑
                            remoteCustomerBindService.unbindSocial(request.getSaasId(), customerDTO.getUid(), OpenSocialPlatformEnum.twitter.name());
                            redisService.del(RedisKeyConst.ACTIVITY_AUTH_TOKEN.getKey(String.format("%s:%s:%s", request.getAuthVersion() ,customerDTO.getUid(), request.getTwitterClientId())));
                        }
                    }
                }
            } else {
                log.info("auth_login_not_same:{}", request);
                throw new com.kikitrade.activity.model.exception.ActivityException(ActivityResponseCode.AUTH_NO_SAME_LOGIN, ActivityResponseCode.AUTH_NO_SAME_LOGIN.getKey());
            }
        } else {
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(request.getSaasId(), request.getCustomerId());
            //之前授权过
            if (StringUtils.isNotBlank(customerBindDTO.getTwitterId())) {
                if (StringUtils.isNotBlank(request.getAuthId()) && !request.getAuthId().equals(customerBindDTO.getTwitterId())) {
                    throw new com.kikitrade.activity.model.exception.ActivityException(ActivityResponseCode.AUTH_NO_SAME_LAST, "@" + customerBindDTO.getTwitterName());
                }
            } else {
                List<TCustomerDTO> tCustomerBySocial = remoteCustomerBindService.findTCustomerBySocial(request.getSaasId(), ActivityTaskConstant.OpenSocialEnum.twitter.name(), request.getAuthId());
                if (CollectionUtils.isNotEmpty(tCustomerBySocial)) {
                    throw new com.kikitrade.activity.model.exception.ActivityException(ActivityResponseCode.AUTH_REPEAT, ActivityResponseCode.AUTH_REPEAT.getKey());
                }
            }
        }
    }

    private void checkTwitterCreatedDays(String saasId, String createdAt) throws ActivityException {
        log.info("checkTwitterCreatedDays start, saasId:{}, createdAt:{}", saasId, createdAt);

        SaasConfig saasConfig = SaasConfigLoader.getConfig(saasId);
        Integer days;
        if (saasConfig == null || (days = saasConfig.getTwitterAccountCreateDays()) <= 0) {
            log.warn("checkTwitterCreatedDays skip, saasConfig:{}", JSON.toJSONString(saasConfig));
            return;
        }

        // X账号创建天数必须大于等于配置的天数
        long daysMillis = TimeUnit.DAYS.toMillis(days);
        long createdMillis = Long.valueOf(createdAt) * 1000;
        long currentMillis = System.currentTimeMillis();
        if (currentMillis - createdMillis < daysMillis) {
            log.error("checkTwitterCreatedDays check did not pass, daysMillis:{}, createdMillis:{}, " +
                    "currentMillis:{}", daysMillis, createdMillis, currentMillis);
            throw new ActivityException(ActivityResponseCode.CHECK_CRATED_DAYS_FAIL, ActivityResponseCode.CHECK_CRATED_DAYS_FAIL.getKey());
        }

        log.info("checkTwitterCreatedDays check pass, daysMillis:{}, createdMillis:{}, " +
                "currentMillis:{}", daysMillis, createdMillis, currentMillis);
    }
}
