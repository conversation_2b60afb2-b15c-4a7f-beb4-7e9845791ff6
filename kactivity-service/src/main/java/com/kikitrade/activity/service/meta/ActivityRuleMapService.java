package com.kikitrade.activity.service.meta;

import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.dal.mysql.model.ActivityRuleMap;

import java.util.List;

public interface ActivityRuleMapService {

    JsonResult save(ActivityRuleMap activityRuleMap);

    JsonResult delete(Integer activityId);

    JsonResult batchInsert(List<ActivityRuleMap> activityRuleMapList);

    JsonResult update(ActivityRuleMap activityRuleMap);

    List<ActivityRuleMap> findByActivityId(Integer id);

    JsonResult findAll(Integer offset, Integer limit);

}
