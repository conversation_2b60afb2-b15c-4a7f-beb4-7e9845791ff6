package com.kikitrade.activity.service.auth.strategy;

import lombok.Builder;
import lombok.Data;

/**
 * 认证上下文
 * 包含认证过程中需要的所有参数
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@Data
@Builder
public class AuthContext {

    /**
     * SaaS ID
     */
    private String saasId;

    /**
     * 平台名称
     */
    private String platform;

    /**
     * 授权码
     */
    private String code;

    /**
     * 重定向URI
     */
    private String redirectUri;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * Twitter ID（兼容性保留）
     */
    private String twitterId;

    /**
     * 社交平台ID（用于Telegram等直接传递的场景）
     */
    private String socialId;

    /**
     * 社交平台用户名（用于Telegram等直接传递的场景）
     */
    private String socialName;
}
