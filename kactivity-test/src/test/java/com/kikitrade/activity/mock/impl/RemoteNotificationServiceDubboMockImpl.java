package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteNotificationServiceDubboMock;
import com.kikitrade.kcustomer.api.model.EmailNotificationDTO;
import com.kikitrade.kcustomer.api.model.FirebaseMessageDTO;
import com.kikitrade.kcustomer.api.service.RemoteNotificationService;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

//@Component
public class RemoteNotificationServiceDubboMockImpl implements RemoteNotificationServiceDubboMock {
    //@Resource
    RemoteNotificationService remoteNotificationService;

    @Override
    public void send() {
        Mockito.doAnswer(c -> {
            return true;
        }).when(remoteNotificationService).send(Mockito.any(EmailNotificationDTO.class));

        Mockito.doAnswer(c -> {
            return true;
        }).when(remoteNotificationService).send(Mockito.any(FirebaseMessageDTO.class));
    }
}
