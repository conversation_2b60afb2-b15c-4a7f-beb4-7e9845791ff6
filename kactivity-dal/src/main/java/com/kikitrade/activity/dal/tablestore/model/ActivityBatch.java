package com.kikitrade.activity.dal.tablestore.model;

import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Data
@Table(name = "activity_batch")
public class ActivityBatch implements Serializable {

    public static final String IDX_ACTIVITY_BATCH_BY_STATUS = "idx_activity_batch_by_status_v3";
    public static final String IDX_ACTIVITY_BATCH_BY_ID = "idx_activity_batch_by_batch_id_v2";

    public static final String IDX_MUL_ACTIVITY_BATCH_NAME = "idx_mul_activity_batch_name";

    //活动id
    @PartitionKey(name = "activity_id")
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, pkColumn = "activity_id" , pkValue = 1)
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, pkColumn = "activity_id", pkValue = 1)
    @SearchIndex(name = IDX_MUL_ACTIVITY_BATCH_NAME, column = "activity_id")
    private String activityId;

    //批次id
    @PartitionKey(name = "batch_id", value = 1)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, pkColumn = "batch_id", pkValue = 2)
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, pkColumn = "batch_id")
    @SearchIndex(name = IDX_MUL_ACTIVITY_BATCH_NAME, column = "batch_id")
    private String batchId;

    //批次名称
    @Column(name = "name", isDefined = true)
    @SearchIndex(name = IDX_MUL_ACTIVITY_BATCH_NAME, column = "name")
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "name")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "name")
    private String name;

    //发奖状态
    @Column(name = "reward_status", isDefined = true)
    @SearchIndex(name = IDX_MUL_ACTIVITY_BATCH_NAME, column = "reward_status")
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, pkColumn = "reward_status")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "reward_status")
    private String rewardStatus;

    //活动名称
    @Column(name = "activity_name", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "activity_name")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "activity_name")
    private String activityName;

    //发奖类型 token
    @Column(name = "reward_type", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "reward_type")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "reward_type")
    private String rewardType;

    //预计发奖时间
    @Column(name = "reward_time", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "reward_time")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "reward_time")
    private String rewardTime;

    //金额
    @Column(name = "amount", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "amount")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "amount")
    private String amount;

    //货币单位
    @Column(name = "currency", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "currency")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "currency")
    private String currency;

    //批次描述
    @Column(name = "remark", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "remark")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "remark")
    private String remark;

    //是否立即发奖 true:是， false:否
    @Column(name = "scheduled", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "scheduled")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "scheduled")
    private Boolean scheduled;

    //发奖时间
    @Column(name = "scheduled_time", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "scheduled_time")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "scheduled_time")
    private String scheduledTime;

    //cvs上传到oss上的地址
    @Column(name = "oss_url", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "oss_url")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "oss_url")
    private String ossUrl;

    @Column(name = "source_oss_url", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "source_oss_url")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "source_oss_url")
    private String sourceOssUrl;

    //cvs上传时间
    @Column(name = "general_time", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "general_time")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "general_time")
    private String generalTime;

    //本批次发奖总金额
    @Column(name = "prize_amount", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "prize_amount")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "prize_amount")
    private String prizeAmount;

    //本批次总发奖人数
    @Column(name = "winners", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "winners")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "winners")
    private Integer winners;

    //最后编辑人
    @Column(name = "amended", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "amended")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "amended")
    private String amended;

    //批次标识
    @Column(name = "token", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "token")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "token")
    private String token;

    //批次状态 true:有效 false:无效
    @Column(name = "status", isDefined = true, type = Column.Type.BOOLEAN)
    @SearchIndex(name = IDX_MUL_ACTIVITY_BATCH_NAME, column = "status", fieldType = FieldType.BOOLEAN)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "status")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "status")
    private Boolean status;

    @Column(name = "created", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "created")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "created")
    private String created;

    @Column(name = "modified", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "modified")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "modified")
    private String modified;

    @Column(name = "saasId", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "saasId")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "saasId")
    private String saasId;

    @Column(name = "shard_count", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "shard_count")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "shard_count")
    private Integer shardCount;

    @Column(name = "reward_config", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "reward_config")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "reward_config")
    private String rewardConfig;

    @Column(name = "activity_type", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "activity_type")
    @Index(name = IDX_ACTIVITY_BATCH_BY_ID, dfColumn = "activity_type")
    private String activityType;

    @Column(name = "instance", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "instance")
    private String instance;

    @Column(name = "conditions", isDefined = true)
    @Index(name = IDX_ACTIVITY_BATCH_BY_STATUS, dfColumn = "conditions")
    private String conditions;

    public List<Rule> getRules(){
        try{
            return JSON.parseArray(getRewardConfig(), Rule.class);
        }catch (Exception ex){
            //活动可能不是多层级发奖活动，解析出错，跳过
            return null;
        }
    }

    @Getter
    @Setter
    public static class Rule{
        //inviter  invitee
        private String level;
        private String side;
        private Double min;
        private Double max;
        //vip normal
        private String userType;
        private String awardType;
        private String awardAmount;
        private String award;
        private String vipLevel;
    }
}
