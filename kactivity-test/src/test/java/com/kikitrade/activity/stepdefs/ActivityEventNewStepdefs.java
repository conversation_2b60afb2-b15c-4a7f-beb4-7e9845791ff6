package com.kikitrade.activity.stepdefs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.accounting.api.model.account.request.AccountTransferRequest;
import com.kikitrade.activity.api.RemoteRewardService;
import com.kikitrade.activity.api.RemoteTaskService;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.api.model.response.VerifyResponse;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.model.*;
import com.kikitrade.activity.mock.CustomerQuestionSetsBuilderMock;
import com.kikitrade.activity.mock.RemoteAssetOperateServiceDubboMock;
import com.kikitrade.activity.mock.RewardDivideListenerMock;
//import com.kikitrade.activity.mock.impl.RewardDivideListenerMockImpl;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TimeFormat;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.config.SaasConfig;
import com.kikitrade.activity.service.config.SaasConfigDTO;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.job.BreadMemberRankingJob;
import com.kikitrade.activity.service.mq.*;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.activity.service.task.action.ActivityEventAction;
import com.kikitrade.activity.util.CheckUtils;
import com.kikitrade.asset.model.exception.AssetException;
import com.kikitrade.asset.model.request.AssetTransferInRequest;
import com.kikitrade.kevent.common.model.EventDTO;
import io.cucumber.java.zh_cn.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import com.kikitrade.activity.model.util.TimeUtil;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.kikitrade.activity.op.BusinessOP.*;


@Slf4j
public class ActivityEventNewStepdefs {
    @Resource
    ActivityEventListener activityEventListener;
    @Resource
    ActivityEventAction activityEventAction;
    @Resource
    RemoteAssetOperateServiceDubboMock remoteAssetOperateServiceDubboMock;
    @Resource
    RemoteTaskService remoteTaskService;
    @Resource
    RewardDivideListener rewardDivideListener;

    //    @Resource
//    RewardDivideListenerMockImpl rewardDivideListenerMock;
    @Resource
    CustomerQuestionSetsBuilderMock customerQuestionSetsBuilderMock;

    @Resource
    BreadMemberRankingJob breadMemberRankingJob;
    @Resource
    RemoteRewardService remoteRewardService;
    @Resource
    private RedisService redisService;

    @假如("task-初始化任务code配置TaskCodeConfig")
    public void task初始化任务code配置task_config() {
        String taskCodeConfig = "[{\"code\":\"connect_x\",\"mainCode\":\"connect_x\",\"inc\":1,\"desc\":\"Connect X\"},{\"code\":\"follow_x\",\"mainCode\":\"follow_x\",\"inc\":1,\"desc\":\"Follow X\"},{\"code\":\"connect_dc\",\"mainCode\":\"connect_dc\",\"inc\":1,\"desc\":\"Connect DC\"},{\"code\":\"join_dc\",\"mainCode\":\"join_dc\",\"inc\":1,\"desc\":\"JOIN DC\"},{\"code\":\"like_post_x\",\"mainCode\":\"like_post_x\",\"inc\":1,\"desc\":\"Like Post X\"},{\"code\":\"comment_post_x\",\"mainCode\":\"comment_post_x\",\"inc\":1,\"desc\":\"Comment Post X\"},{\"code\":\"retweet_post_x\",\"mainCode\":\"retweet_post_x\",\"inc\":1,\"desc\":\"Retweet Post X\"},{\"code\":\"sign_in\",\"mainCode\":\"sign_in\",\"inc\":1,\"desc\":\"sign_in\"},{\"code\":\"join_game\",\"mainCode\":\"join_game\",\"inc\":1,\"desc\":\"join_game\"},{\"code\":\"play_game\",\"mainCode\":\"play_game\",\"inc\":1,\"desc\":\"play_game\"},{\"code\":\"like\",\"mainCode\":\"like\",\"inc\":1,\"desc\":\"like\"},{\"code\":\"megaphone\",\"mainCode\":\"megaphone\",\"inc\":1,\"desc\":\"megaphone\"},{\"code\":\"post\",\"mainCode\":\"post\",\"inc\":1,\"desc\":\"post\"},{\"code\":\"liked\",\"mainCode\":\"liked\",\"inc\":1,\"desc\":\"liked\"},{\"code\":\"comment\",\"mainCode\":\"comment\",\"inc\":1,\"desc\":\"comment\"},{\"code\":\"purchase_membership\",\"mainCode\":\"purchase_membership\",\"inc\":1,\"desc\":\"purchase_membership\"},{\"code\":\"follow\",\"mainCode\":\"follow\",\"inc\":1,\"desc\":\"follow\"},{\"code\":\"join_tribe\",\"mainCode\":\"join_tribe\",\"inc\":1,\"desc\":\"join_tribe\"},{\"code\":\"subject_post_x\",\"mainCode\":\"subject_post_x\",\"inc\":1,\"desc\":\"subject_post_x\"},{\"code\":\"reply_post_x\",\"mainCode\":\"reply_post_x\",\"inc\":1,\"desc\":\"reply_post_x\"},{\"code\":\"wallet_bind\",\"mainCode\":\"wallet_bind\",\"inc\":1,\"desc\":\"wallet_bind\"},{\"code\":\"wallet_asset\",\"mainCode\":\"wallet_asset\",\"inc\":1,\"desc\":\"wallet_asset\"},{\"code\":\"wallet_deposit\",\"mainCode\":\"wallet_deposit\",\"inc\":1,\"desc\":\"wallet_deposit\"},{\"code\":\"vampire_attack\",\"mainCode\":\"vampire_attack\",\"inc\":1,\"desc\":\"vampire_attack\"},{\"code\":\"name_x\",\"mainCode\":\"name_x\",\"inc\":1,\"desc\":\"name_x\"},{\"code\":\"registration\",\"mainCode\":\"registration\",\"inc\":1,\"desc\":\"registration\"},{\"code\":\"invited_register\",\"mainCode\":\"invited_register\",\"inc\":1,\"desc\":\"invited_register\"},{\"code\":\"acquire_question\",\"mainCode\":\"acquire_question\",\"inc\":1,\"desc\":\"acquire question\"},{\"code\":\"submit_question\",\"mainCode\":\"submit_question\",\"inc\":1,\"desc\":\"submit_question\"},{\"code\":\"sets_calculate_reward\",\"mainCode\":\"sets_calculate_reward\",\"inc\":1,\"desc\":\"calculate sets\"},{\"code\":\"sets_nature_reward\",\"mainCode\":\"sets_nature_reward\",\"inc\":1,\"desc\":\"sets nature reward\"},{\"code\":\"join_tg_channel\",\"mainCode\":\"join_tg_channel\",\"inc\":1,\"desc\":\"join tg channel\"},{\"code\":\"external_reward\",\"mainCode\":\"external_reward\",\"inc\":1,\"desc\":\"external reward\"},{\"code\":\"osp_callback\",\"mainCode\":\"osp_callback\",\"inc\":1,\"desc\":\"osp callback\"},{\"code\":\"owo_asset\",\"mainCode\":\"owo_asset\",\"inc\":1,\"desc\":\"owo_asset\"},{\"code\":\"eoa_asset_gensokishionlinev2\",\"mainCode\":\"eoa_asset_gensokishionlinev2\",\"inc\":1,\"desc\":\"eoa_asset_gensokishionlinev2\"},{\"code\":\"eoa_asset_persona_eth\",\"mainCode\":\"eoa_asset_persona_eth\",\"inc\":1,\"desc\":\"eoa_asset_persona_eth\"},{\"code\":\"join_tg_channel_3\",\"mainCode\":\"join_tg_channel_3\",\"inc\":1,\"desc\":\"join_tg_channel_3\"},{\"code\":\"claim_once_harvest\",\"mainCode\":\"claim_once_harvest\",\"inc\":1,\"desc\":\"claim_once_harvest\"},{\"code\":\"play_deek_game_shake\",\"mainCode\":\"play_deek_game_shake\",\"inc\":1,\"desc\":\"play_deek_game_shake\"},{\"code\":\"join_tg_channel\",\"mainCode\":\"join_tg_channel\",\"inc\":1,\"desc\":\"join_tg_channel\"},{\"code\":\"subscribe_tg\",\"desc\":\"subscribe_tg\",\"inc\":1,\"mainCode\":\"subscribe_tg\"},{\"code\":\"invited_play_game\",\"desc\":\"invited_play_game\",\"inc\":1,\"mainCode\":\"invited_play_game\"},{\"code\":\"game_settle\",\"desc\":\"game_settle\",\"inc\":1,\"mainCode\":\"game_settle\"},{\"code\":\"check_in\",\"desc\":\"check_in\",\"inc\":1,\"mainCode\":\"check_in\"}]";
        TaskCodeConfig.load(taskCodeConfig);
    }

    @同时("task-设置任务任务白名单用户")
    public void task设置任务任务白名单用户() throws Exception {
        redisService.hSet(RedisKeyConst.TASK_WHITELIST.getMiddleKey(null), "2024071906435660016061", true);
    }

    @假如("task-初始化任务配置task_config{}")
    public void task初始化任务配置task_config(String taskConfig) throws InterruptedException {
        List<TaskConfig> configs = JSON.parseArray(taskConfig, TaskConfig.class);
        configs.forEach(c -> {
            c.setStartTime(new Date());
            //设置endTime为当前时间+1天
            c.setEndTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));
            taskConfigMap.put(c.getTaskId(), c);
        });
        log.info("configs:{}", configs);
    }

    @同时("task-已存在customer_question_sets{}")
    public void task已存在customer_question_sets(String customerQuestionSet) {
        List<CustomerQuestionSets> customerQuestionSets = JSON.parseArray(customerQuestionSet, CustomerQuestionSets.class);
        customerQuestionSetsList.addAll(customerQuestionSets);
    }

    @同时("task-已存在主任务activity_task_item{}")
    public void 已存在主任务activity_task_item(String primaryActivityTaskItem) {
        List<ActivityTaskItem> activityTaskItems = JSON.parseArray(primaryActivityTaskItem, ActivityTaskItem.class);
        activityTaskItems.forEach(item -> {
            String dataStr = TimeUtil.getDataStr(TimeUtil.addDay(new Date(), -1), TimeFormat.YYYYMMDDHHMMSS_PATTERN);
            item.setCreated(dataStr);
            item.setModified(dataStr);

            if (item.getCycle() == null) {
                dataStr = TimeUtil.getDataStr(new Date(), TimeFormat.YYYYMMDDHHMMSS_PATTERN);
                item.setCycle(TimeUtil.getDataStr(new Date(), TimeFormat.YYYYMMDD_PATTERN));
                item.setCreated(dataStr);
                item.setModified(dataStr);
            }
        });
        activityTaskItemList.addAll(activityTaskItems);
    }

    @同时("task-已存在子任务activity_task_item{}")
    public void 已存在子任务activity_task_item(String subActivityTaskItem) {
        List<ActivityTaskItem> activityTaskItems = JSON.parseArray(subActivityTaskItem, ActivityTaskItem.class);

        activityTaskItems.forEach(item -> {
            String dataStr;
            if (item.getTargetId() == null) {
                //主要
                item.setTargetId(TimeUtil.getDataStr(TimeUtil.addDay(new Date(), -1), TimeFormat.YYYYMMDD_PATTERN));

                //设置为昨日完成的子任务
                dataStr = TimeUtil.getDataStr(TimeUtil.addDay(new Date(), -1), TimeFormat.YYYYMMDDHHMMSS_PATTERN);
                item.setCompleteTime(dataStr);
                item.setCreated(dataStr);
                item.setModified(dataStr);
            }

            if (item.getCycle() == null) {
                item.setCycle(TimeUtil.getDataStr(new Date(), TimeFormat.YYYYMMDD_PATTERN));
                dataStr = TimeUtil.getDataStr(new Date(), TimeFormat.YYYYMMDDHHMMSS_PATTERN);
                item.setCreated(dataStr);
                item.setModified(dataStr);
            }
        });
        activityTaskItemList.addAll(activityTaskItems);
        log.info("activityTaskItemList:{}", activityTaskItemList);
    }

    @同时("task-初始化配置TaskCodeConfig{}")
    public void initTaskCodeConfig(String configCode) {
        TaskCodeConfig taskCodeConfig = new TaskCodeConfig();
        TaskCodeConfig.load(configCode);
    }

    @同时("task-已存在activity_task_item{}")
    public void taskExistsActivityTaskItem(String activityTaskItem) {
        ActivityTaskItem item = JSON.parseObject(activityTaskItem, ActivityTaskItem.class);
        activityTaskItemList.add(item);
    }

    @当("task-任务校验taskVerify{}")
    public void taskVerify(String verifyInfo) throws ActivityException {
        JSONObject jsonObject = JSON.parseObject(verifyInfo, JSONObject.class);
        Token accessToken = jsonObject.getObject("accessToken", Token.class);
        String taskId = jsonObject.getString("taskId");
        String saasId = jsonObject.getString("saasId");
        String ext = jsonObject.getString("ext");

        try {
            VerifyResponse response = remoteTaskService.verify(accessToken, taskId, saasId, ext);
            verifyResponseList.add(response);
        } catch (Exception e) {
            exceptions.add(e.toString());
            log.info("exceptions{}", exceptions);
        }
    }

    @当("task-手动领取奖励{}")
    public void task手动领取奖励(String receiveRewardRequest) {
        JSONObject jsonObject = JSON.parseObject(receiveRewardRequest, JSONObject.class);
        String customerId = jsonObject.getString("customerId");
        String address = jsonObject.getString("address");
        String taskId = jsonObject.getString("taskId");
        String extendAttr = jsonObject.getString("extendAttr");

        try {
            remoteRewardService.receiveReward(customerId, address, taskId, extendAttr);
        } catch (Exception e) {
            exceptions.add(e.toString());
        }
    }

    @假如("activity-event-配置remoteAssetOperateService.transferIn异常")
    public void activityEvent配置remoteAssetOperateServiceTransferIn异常() throws AssetException {
        remoteAssetOperateServiceDubboMock.transferInError();
    }

    @假如("activity-event-配置customerQuestionSetsBuilder.incrementAvailableSets异常")
    public void activityEvent配置customerQuestionSetsBuilder_incrementAvailableSets() throws AssetException {
        customerQuestionSetsBuilderMock.incrementAvailableSets_error();
    }


    @当("task-系统活动事件监听{}")
    public void task系统活动事件监听(String activityEventMessage) {
        List<ActivityEventMessage> messages = JSON.parseArray(activityEventMessage, ActivityEventMessage.class);
        messages.forEach(msg -> {
            if (Objects.isNull(msg.getBody())) {
                Map<String, Object> body = new HashMap<>();
                body.put("saasId", "monster");
                msg.setBody(body);
            }

            if (Objects.isNull(msg.getEventTime())) {
                msg.setEventTime(System.currentTimeMillis());
            }

            try {
                ActivityResponse<List<Award>> action = activityEventAction.action(msg);
                activityResponseList.add(action);
            } catch (Exception e) {
                exceptions.add(e.toString());
                log.info("exceptions{}", exceptions);
            }
        });
    }

    @假如("task-初始化配置{}")
    public void task初始化配置saasConfig(String saasConfig) throws NoSuchFieldException, IllegalAccessException {
        SaasConfig config = JSON.parseObject(saasConfig, SaasConfig.class);
        Map<String, SaasConfig> saasConfigDTO = new HashMap<>();
        saasConfigDTO.put("opensocial", config);

        Field field = SaasConfigLoader.class.getDeclaredField("saasConfig");
        field.setAccessible(true);
        field.set(null, saasConfigDTO);
    }

    @假如("task-monster初始化saasConfig配置{}")
    public void monster初始化saasConfig配置(String saasConfig) throws NoSuchFieldException, IllegalAccessException {
        SaasConfig config = JSON.parseObject(saasConfig, SaasConfig.class);
        Map<String, SaasConfig> saasConfigDTO = new HashMap<>();
        saasConfigDTO.put("monster", config);

        Field field = SaasConfigLoader.class.getDeclaredField("saasConfig");
        field.setAccessible(true);
        field.set(null, saasConfigDTO);
    }


    @同时("task-设置邀请人{}")
    public void task设置邀请人(String inviters) {
        List<RewardDivideListener.OwnerInvite> ownerInvites = JSON.parseArray(inviters, RewardDivideListener.OwnerInvite.class);
        inviterList.addAll(ownerInvites);
    }

    @同时("task-已存在activity_task_statis{}")
    public void task已存在activity_task_statis(String existActivityTaskStatis) {
        List<ActivityTaskStatis> activityTaskStatis = JSON.parseArray(existActivityTaskStatis, ActivityTaskStatis.class);
        activityTaskStatis.forEach(item -> {
            if (item.getCycle() == null) {
                item.setCycle(TimeUtil.getDataStr(new Date(), TimeFormat.YYYYMMDD_PATTERN));
            }
            activityTaskStatisList.add(item);
        });
    }

//    @当("task-milestone积分加成{}")
//    public void taskMilestone积分加成(String reward) {
//        Message message = new Message();
//        message.setBody(reward.getBytes());
//        message.setTopic("T_ACTIVITY_REWARD_DIVIDE_QUEST_BETA");
//        message.setMsgID("C0A8268700014B85612C2F0FF423B7BB");
//        rewardDivideListenerMock.rewardDivideListenerSpy.consume(message, null);
//    }

    @那么("task-校验activity_task_item{}")
    public void task校验activity_task_item(String activityTaskItem) {
        List<ActivityTaskItem> itemsEXP = JSON.parseArray(activityTaskItem, ActivityTaskItem.class);
        log.info(">>Expect--List<ActivityTaskItem>{}", JSON.toJSONString(itemsEXP));

        List<ActivityTaskItem> itemsACT = activityTaskItemList.stream().sorted(Comparator.comparing(ActivityTaskItem::getCreated)).collect(Collectors.toList());
        log.info(">>Actual--List<ActivityTaskItem>{}", JSON.toJSONString(itemsACT));

        System.out.println("比较>>" + System.currentTimeMillis());
        CheckUtils.compare(itemsEXP, itemsACT, new String[]{"cycle", "expiredTime", "completeTime", "created", "modified", "businessId", "ts", "targetId"});
    }

    @那么("task-校验activity_task_statis{}")
    public void task校验activity_task_statis(String activityTaskStatis) {
        List<ActivityTaskStatis> activityTaskStatisEXP = JSON.parseArray(activityTaskStatis, ActivityTaskStatis.class);
        log.info(">>Expect--List<ActivityTaskStatis>{}", JSON.toJSONString(activityTaskStatisEXP));

        List<ActivityTaskStatis> activityTaskStatisACT = activityTaskStatisList.stream().sorted(Comparator.comparing(ActivityTaskStatis::getProgress)).collect(Collectors.toList());
        log.info(">>Actual--List<ActivityTaskStatis>{}", JSON.toJSONString(activityTaskStatisACT));

        CheckUtils.compare(activityTaskStatisEXP, activityTaskStatisACT, new String[]{"cycle"});
    }

    @那么("task-校验activity_customer_reward{}")
    public void task校验activity_customer_reward(String activityCustomReward) {
        List<ActivityCustomReward> rewardsEXP = JSON.parseArray(activityCustomReward, ActivityCustomReward.class);
        log.info(">>Expect--List<ActivityCustomReward>{}", JSON.toJSONString(rewardsEXP));

        List<ActivityCustomReward> rewardsACT = activityCustomRewardList.stream().sorted(Comparator.comparing(ActivityCustomReward::getSeq)).collect(Collectors.toList());
        log.info(">>Actual--List<ActivityCustomReward>{}", JSON.toJSONString(rewardsACT));

        CheckUtils.compare(rewardsEXP, rewardsACT, new String[]{"businessId", "seq", "batchId","receiveEndTime"});
    }

    @那么("task-校验assetTransferInRequest{}")
    public void task校验assetTransferInRequest(String assetTransferInRequest) {
        List<AssetTransferInRequest> requestsEXP = JSON.parseArray(assetTransferInRequest, AssetTransferInRequest.class);
        log.info(">>Expect--List<AssetTransferInRequest>{}", JSON.toJSONString(requestsEXP));

        List<AssetTransferInRequest> requestsACT = assetTransferInRequestList;
        log.info(">>Actual--List<AssetTransferInRequest>{}", JSON.toJSONString(requestsACT));
        CheckUtils.compare(requestsEXP, requestsACT, new String[]{"businessId"});
    }

    @那么("task-校验customer_question_sets{}")
    public void task校验customer_question_sets(String customerQuestionSets) {
        List<CustomerQuestionSets> customerQuestionSetsEXP = JSON.parseArray(customerQuestionSets, CustomerQuestionSets.class);
        log.info(">>Expect--List<CustomerQuestionSets>{}", JSON.toJSONString(customerQuestionSetsEXP));

        log.info(">>Actual--List<CustomerQuestionSets>{}", JSON.toJSONString(customerQuestionSetsList));
        CheckUtils.compare(customerQuestionSetsEXP, customerQuestionSetsList);

    }

    @那么("task-校验accountTransferInRequest{}")
    public void task校验accountTransferInRequest(String accountTransferInRequest) {
        List<AccountTransferRequest> requestsEXP = JSON.parseArray(accountTransferInRequest, AccountTransferRequest.class);
        log.info(">>Expect--List<AccountTransferRequest>{}", JSON.toJSONString(requestsEXP));

        List<AccountTransferRequest> requestsACT = accountTransferRequestList;
        log.info(">>Actual--List<AccountTransferRequest>{}", JSON.toJSONString(requestsACT));
        CheckUtils.compare(requestsEXP, requestsACT, new String[]{"businessId"});
    }

    @那么("task-校验RewardRequest{}")
    public void task校验RewardRequest(String rewardRequest) {
        JSONObject requestACT = (JSONObject) JSON.toJSON(rewardRequestList.get(0));
        log.info(">>Actual--{}", JSON.toJSONString(requestACT));

        JSONObject requestEXP = JSON.parseObject(rewardRequest, JSONObject.class);
        log.info(">>Expect--{}", requestEXP);

        CheckUtils.compare(requestEXP, requestACT, new String[]{"receiveEndTime", "rewardId"});
    }

//    @那么("task-校验ActivityResponse{}")
//    public void task校验ActivityResponse(String activityResponse) {
//        log.info(">>Expect--ActivityResponse{}", JSON.toJSONString(activityResponse));
//
//        log.info(">>Actual--ActivityResponse{}", JSON.toJSONString(activityResponseList.get(0)));
//        CheckUtils.compare(activityResponse, JSON.toJSONString(activityResponseList.get(0)));
//    }

    @那么("task-校验ActivityResponse{}")
    public void task校验ActivityResponse(String activityResponse) {
        log.info(">>Expect--ActivityResponse{}", JSON.toJSONString(activityResponse));

        log.info(">>Actual--ActivityResponse{}", JSON.toJSONString(activityResponseList));
        CheckUtils.compare(activityResponse, JSON.toJSONString(activityResponseList));
    }

    @那么("task-校验VerifyResponse{}")
    public void task校验VerifyResponse(String verifyResponse) {
        log.info(">>Expect--VerifyResponse{}", JSON.toJSONString(verifyResponse));

        log.info(">>Actual--VerifyResponse{}", JSON.toJSONString(verifyResponseList));
        CheckUtils.compare(verifyResponse, JSON.toJSONString(verifyResponseList));
    }

    @那么("task-校验积分分成消息{}")
    public void task校验积分分成消息(String rewardDivideMessage) {
        log.info(">>Expect--rewardDivideMessage{}", JSON.toJSONString(rewardDivideMessage));

        List<String> rewardDivideMessageList = mqMessageMap.values().stream().toList();
        log.info(">>Actual--ActivityResponse{}", JSON.toJSONString(rewardDivideMessageList));

        CheckUtils.compare(rewardDivideMessage, JSON.toJSONString(activityResponseList));

    }

    @当("task-排行榜发徽章{}")
    public void task排行榜发徽章(String message) {
        breadMemberRankingJob.ex();
    }
}