package com.kikitrade.activity.service.rpc.facebook;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.service.common.config.FacebookProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.json.JSONObject;

import java.time.OffsetDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/20 12:05
 * @description:
 */
@Data
@Slf4j
public class CurrentUserRequest extends AccessToken {

    public CurrentUserRequest() {
    }

    public CurrentUserRequest(FacebookProperties facebookProperties, String accessToken) {
        this.accessToken = accessToken;
    }

    public SocialUserInfo execute() {
        SocialUserInfo cache = FacebookCache.get(this.accessToken, SocialUserInfo.class);
        if (cache != null) {
            return cache;
        }
        String url = "https://graph.facebook.com/v23.0/me" + "?fields=id,name&access_token=" + accessToken;
        Request request = new Request.Builder()
            .url(url)
            .build();

        try {
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("[facebook] currentUser:{}", jsonObject);

            String id = jsonObject.getString("id");
            String name = jsonObject.getString("name");

            SocialUserInfo userInfo = SocialUserInfo.builder()
                .userId(id)
                .userName(name)
                .createdAt(OffsetDateTime.now().toEpochSecond())
                .build();
            FacebookCache.put(this.accessToken, JSON.toJSONString(userInfo));
            return userInfo;
        } catch (Exception e) {
            log.error("[facebook] currentUser error:{}", accessToken, e);
            return null;
        }
    }
}
