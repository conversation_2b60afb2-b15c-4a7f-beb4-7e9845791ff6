<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityCustomerDiscountDao">


    <resultMap id="BaseResultMap" type="com.kikitrade.activity.dal.mysql.model.ActivityCustomerDiscount">
        <result column="discount_date" property="discountDate" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="investment_product_id" property="investmentProductId" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="customer_id" property="customerId" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="buy_total_amt" property="buyTotalAmt" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result column="discount_rate" property="discountRate" javaType="java.lang.Double" jdbcType="DOUBLE"/>
        <result column="activity_id" property="activityId" javaType="java.lang.Integer" jdbcType="BIGINT"/>
        <result column="activity_type" property="activityType" javaType="java.lang.Integer" jdbcType="BIGINT"/>
        <result column="discount_currency" property="discountCurrency" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result column="discount_amt" property="discountAmt" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result column="status" property="status" javaType="java.lang.Integer" jdbcType="SMALLINT"/>
    </resultMap>


    <select id="findAll" resultMap="BaseResultMap">
       SELECT *
        FROM activity_customer_discount
        WHERE discount_date=#{discountDate} order by customer_id
        LIMIT #{offset}, #{limit}
    </select>


    <update id="updateStatus">
        UPDATE activity_customer_discount
        SET  status = #{status}
        WHERE discount_date= #{discountDate} and customer_id= #{customerId}
         and investment_product_id = #{investmentProductId} and status=0
    </update>

    <select id="countByStatus" resultType="java.lang.Long">
       SELECT COUNT(*)
        FROM activity_customer_discount
        WHERE discount_date= #{discountDate} and status = #{status}
    </select>

</mapper>
