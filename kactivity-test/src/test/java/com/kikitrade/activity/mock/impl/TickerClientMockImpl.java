package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.TickerClientMock;
import com.kikitrade.market.client.TickerClient;
import com.kikitrade.market.common.model.TickerDTO;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.anyString;

@Component
public class TickerClientMockImpl implements TickerClientMock {
    @Resource
    TickerClient tickerClient;

    @Override
    public void get() {
        Mockito.doAnswer(c->{
            TickerDTO tickerDTO = new TickerDTO();
            BigDecimal price = null;
            String symbol = c.getArgument(0);
            if(symbol.equalsIgnoreCase("usd_usd")){
                price = new BigDecimal("1");
            }
            tickerDTO.setSymbol(symbol);
            tickerDTO.setPrice(price);
            return tickerDTO;
        }).when(tickerClient).get(anyString());
    }
}
