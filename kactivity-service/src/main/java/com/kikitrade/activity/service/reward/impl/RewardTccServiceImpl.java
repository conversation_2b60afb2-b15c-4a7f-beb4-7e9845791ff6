package com.kikitrade.activity.service.reward.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSONObject;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.accounting.api.RemoteAccountingOperateService;
import com.kikitrade.accounting.api.model.InternalAccount;
import com.kikitrade.accounting.api.model.account.common.Category;
import com.kikitrade.accounting.api.model.account.common.TransferType;
import com.kikitrade.accounting.api.model.account.request.AccountTransferRequest;
import com.kikitrade.accounting.api.model.account.response.AccountTransferResponse;
import com.kikitrade.activity.model.EmailTemplateType;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.business.NoticeService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.common.config.RateLimiterConfig;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.kcustomer.api.constants.FirebaseTemplateType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;

/**
 * Reward Tcc Service Implementation
 * <p>
 * -- TODO 产品账限制
 *
 * <AUTHOR>
 * @create 2022/3/9 11:14 上午
 * @modify
 */
@Slf4j
@Service("rewardTccService")
public class RewardTccServiceImpl extends AbstractRewardTccService implements RewardTccService {

    @DubboReference
    public RemoteAccountingOperateService remoteAccountingOperateService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    public NoticeService noticeService;
    @Resource
    private RateLimiterConfig rateLimiterConfig;

    @Override
    @Compensation(confirmMethod = "confirmReward")
    public void tryReward(RewardRequest request) throws Exception {
        if(request.getCurrency() == null){
            throw new ActivityException(ActivityResponseCode.ACCOUNTING_ERROR, "reward currency can not be empty");
        }
        AccountTransferRequest transferRequest = AccountTransferRequest.builder()
                .businessId(request.getRewardId())
                .type(TransferType.REWARD)
                .fromCustomerId(InternalAccount.MARKETING.id())
                .fromCategory(Category.NORMAL)
                .toCustomerId(request.getCustomerId())
                .toCategory(Category.NORMAL)
                .currency(request.getCurrency())
                .amount(request.getAmount())
                .strictCheck(true)
                .fee(BigDecimal.ZERO)
                .saasId(kactivityProperties.getSaasId())
                .build();
        rateLimiterConfig.acquire("rewardToken");

        AccountTransferResponse response = remoteAccountingOperateService.transfer(transferRequest);
        if (!response.getCode().isSuccess()) {
            throw new ActivityException(ActivityResponseCode.ACCOUNTING_ERROR, "reward accounting process fail");
        }
    }

    public void confirmReward(RewardRequest request) throws Exception {
        if("lottery".equals(request.getBusinessType())){
            return;
        }
        log.info("RewardTccService confirmReward begin  ...{}", JSONObject.toJSONString(request));
        String saasId = kactivityProperties.getSaasId();
        String customerId = request.getCustomerId();
        if(StringUtils.isNotBlank(request.getActivityName())){
            if(request.getSide() != null && ActivityConstant.SideEnum.INVITER == request.getSide()){
                noticeService.notice(saasId, customerId, EmailTemplateType.MANUAL_REWARD, request, ActivityConstant.FirebaseTemplateCode.activity_invite_reward);
            }else{
                noticeService.notice(saasId, customerId, EmailTemplateType.MANUAL_REWARD, request, ActivityConstant.FirebaseTemplateCode.activity_reward);
            }
        }else{
            noticeService.notice(saasId, customerId, EmailTemplateType.MANUAL_REWARD, request, ActivityConstant.FirebaseTemplateCode.manual_reward);
        }
        log.info("RewardTccService confirmReward end  ...{}", customerId);
    }
}
