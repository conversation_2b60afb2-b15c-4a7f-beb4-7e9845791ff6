package com.kikitrade.activity.service.common.strategy.google;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;

/**
 * <AUTHOR>
 * @date 2025/7/22 18:10
 * @description:
 */
public interface GoogleSaasStrategyService {
    String strategy();
    void execute(OpenStrategyAuthRequest request) throws ActivityException;
}
