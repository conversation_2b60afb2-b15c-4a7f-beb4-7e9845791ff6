package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityRewardInfo;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityRewardInfoDao extends BaseDao<ActivityRewardInfo, String> {


    List<ActivityRewardInfo> findByActivityId(@Param("activity_id") Integer activity_id);

}
