package com.kikitrade.activity.service.common.strategy;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4 16:25
 */
@Data
@Builder
public class OpenStrategyAuthRequest implements Serializable {

    private String authId;
    private String authHandleName;
    private String authEmail;
    private String loginTwitterId;
    private String customerId;
    private String saasId;
    private String authVersion;
    private String twitterClientId;
    private String createdAt;
}
