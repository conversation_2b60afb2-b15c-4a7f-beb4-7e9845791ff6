package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteCustomerExtraServiceDubboMock;
import com.kikitrade.kcustomer.api.model.CustomerExtraDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerExtraService;
import org.mockito.Mockito;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Map;

@Component
public class RemoteCustomerExtraServiceDubboMockImpl implements RemoteCustomerExtraServiceDubboMock {
    @Resource
    RemoteCustomerExtraService remoteCustomerExtraService;
    @Override
    public void getById() {
        Mockito.doAnswer(c->{
            String saasId = c.getArgument(0);
            String customerId = c.getArgument(1);
            boolean isSimple = c.getArgument(2);

            CustomerExtraDTO customerExtraDTO = new CustomerExtraDTO();
            customerExtraDTO.setId(customerId);

            return customerExtraDTO;

        }).when(remoteCustomerExtraService).getById(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean());
    }
}
