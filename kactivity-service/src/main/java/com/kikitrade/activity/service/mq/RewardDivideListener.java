package com.kikitrade.activity.service.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.service.config.SaasConfig;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.framework.ons.OnsMessageListener;
import com.kikitrade.framework.ons.OnsProperties;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/3 14:24
 */
@Component
@Slf4j
public class RewardDivideListener implements OnsMessageListener {

    @Resource
    private TopicConfig topicConfig;
    @Resource
    private OnsProperties onsProperties;
    @Resource
    private RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;

    @Override
    public String topic() {
        return topicConfig.getTopicRewardDivide();
    }

    @Override
    public boolean traffic() {
        return onsProperties != null && onsProperties.isEnableTraffic();
    }

    /**
     * 消费消息接口，由应用来实现<br>
     * 网络抖动等不稳定的情形可能会带来消息重复，对重复消息敏感的业务可对消息做幂等处理
     *
     * @param message 消息
     * @param context 消费上下文
     * @return 消费结果，如果应用抛出异常或者返回Null等价于返回Action.ReconsumeLater
     * @see <a href="https://help.aliyun.com/document_detail/44397.html">如何做到消费幂等</a>
     */
    @Override
    public Action consume(Message message, ConsumeContext context) {
        String body = new String(message.getBody());
        log.info("reward divide:{},{},{}", message.getTopic(), message.getMsgID(), body);
        LauncherParameter parameter = JSON.parseObject(body, LauncherParameter.class);
        ActivityCustomReward customReward = parameter.getActivityCustomReward();
        String sourceAmount = customReward.getAmount();
        SaasConfig saasConfig = SaasConfigLoader.getConfig(customReward.getSaasId());
        //查询ownerId
        CustomerBindDTO bindDTO = remoteCustomerBindService.findByUid(customReward.getSaasId(), customReward.getCustomerId());
        //查询邀请人
        List<String> invitersList = new ArrayList<>();
        if(customReward.getSaasId().equals("trex")){
            List<CustomerInvite> invitersForTrex = getInvitersForTrex(saasConfig, bindDTO.getCid(), bindDTO.getSaasId());
            if(CollectionUtils.isNotEmpty(invitersForTrex)){
                invitersList = invitersForTrex.stream().map(CustomerInvite::getInviter).collect(Collectors.toList());
            }
        }else{
            List<OwnerInvite> inviters = getInviters(saasConfig, bindDTO.getCid(), bindDTO.getSaasId());
            if(CollectionUtils.isNotEmpty(inviters)){
                invitersList = inviters.stream().map(OwnerInvite::getOwner_id).collect(Collectors.toList());
            }
        }
        if(CollectionUtils.isNotEmpty(invitersList)){
            try {
                int i = 0;
                for(String inviter : invitersList){
                    i++;
                    CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(customReward.getSaasId(), inviter);
                    if(customerBindDTO != null){
                        customReward.setCustomerId(customerBindDTO.getUid());
                        customReward.setBusinessType(AssetBusinessType.INVITE_DIVIDE.getCodeDesc());
                        customReward.setBusinessId(customReward.getBusinessId() + i);
                        customReward.setAmount(new BigDecimal(sourceAmount).multiply(BigDecimal.valueOf(saasConfig.getDividePointPercent())).setScale(2, RoundingMode.HALF_DOWN).toPlainString());
                        customReward.setSeq(customReward.getSeq() + i);
                        parameter.setActivityCustomReward(customReward);
                        activityRealTimeRewardTccService.reward(parameter);
                    }
                }
            } catch (Exception e) {
                log.error("reward divide point error:{}", parameter, e);
                return Action.ReconsumeLater;
            }
        }
        return Action.CommitMessage;
    }

    private List<CustomerInvite> getInvitersForTrex(SaasConfig saasConfig, String ownerId, String saasId){

        try {
            SaasConfig config = SaasConfigLoader.getConfig(saasId);
            HttpUrl.Builder urlBuilder = HttpUrl.parse( saasConfig.getApiHost()+ "/s2s/customers/invites/me?cid="+ownerId).newBuilder();
            Headers.Builder headerBuilder = new Headers.Builder();
            headerBuilder.add("app_key", config.getOspAppId());

            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .headers(headerBuilder.build())
                    .build();
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(responseBody);
                log.info("[RewardDivide] getInviters response:{}", jsonObject);
                JSONArray data = Optional.ofNullable(jsonObject.getJSONArray("data"))
                        .orElse(null);
                if(data != null){
                    return data.toJavaList(CustomerInvite.class);
                }
            }
            return null;
        } catch (Exception e) {
            log.error("[OspProfileRequest] getProfileByEoaAddress Exception:{}, error:", ownerId, e);
            return null;
        }
    }

    private List<OwnerInvite> getInviters(SaasConfig saasConfig, String ownerId, String saasId){

        try {
            SaasConfig config = SaasConfigLoader.getConfig(saasId);
            HttpUrl.Builder urlBuilder = HttpUrl.parse( saasConfig.getApiHost()+ "/v2/s2s/owners/"+ownerId+"/invites").newBuilder();
            Headers.Builder headerBuilder = new Headers.Builder();
            headerBuilder.add("os-app-id", config.getOspAppId());
            headerBuilder.add("os-Api-Key", config.getOspAppKey());

            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .headers(headerBuilder.build())
                    .build();
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(responseBody);
                log.info("[RewardDivide] getInviters response:{}", jsonObject);
                JSONArray data = Optional.ofNullable(jsonObject.getJSONArray("data"))
                        .orElse(null);
                if(data != null){
                    return data.toJavaList(OwnerInvite.class);
                }
            }
            return null;
        } catch (Exception e) {
            log.error("[OspProfileRequest] getProfileByEoaAddress Exception:{}, error:", ownerId, e);
            return null;
        }
    }

    @Data
    public static class OwnerInvite implements Serializable {
        private String owner_id;
    }

    @Data
    public static class CustomerInvite implements Serializable {
        private String inviter;
    }
}
