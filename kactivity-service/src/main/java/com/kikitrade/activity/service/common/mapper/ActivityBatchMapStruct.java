package com.kikitrade.activity.service.common.mapper;

import com.kikitrade.activity.facade.award.ActivityBatch;

import java.util.List;

public interface ActivityBatchMapStruct {

    ActivityBatch serviceToRpc(com.kikitrade.activity.dal.tablestore.model.ActivityBatch activityBatch);

    List<ActivityBatch> serviceToRpcList(List<com.kikitrade.activity.dal.tablestore.model.ActivityBatch> activityBatchList);
}
