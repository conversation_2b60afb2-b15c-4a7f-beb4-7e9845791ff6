package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.ActivityMaterial;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ActivityMaterialDao extends BaseDao<ActivityMaterial, String> {

    @Insert("<script>" +
            "insert into activity_material(`id`,`activity_id`,`code`,`value`,`status`,`created`,`modified`,`saasId`) values " +
            " <foreach collection='activityMaterialList' item='item' index='index' separator=','>" +
            "            <trim prefix='(' suffix=')' suffixOverrides=','>" +
            "                    #{item.id,jdbcType=VARCHAR}," +
            "                    #{item.activityId,jdbcType=VARCHAR}," +
            "                    #{item.code,jdbcType=VARCHAR}," +
            "                    #{item.value,jdbcType=VARCHAR}," +
            "                    #{item.status,jdbcType=TINYINT}," +
            "                    #{item.created,jdbcType=TIMESTAMP}," +
            "                    #{item.modified,jdbcType=TIMESTAMP}," +
            "                    #{item.saasId,jdbcType=VARCHAR}," +
            "            </trim>" +
            "        </foreach>" +
            "</script>")
    void batchInsert(@Param("activityMaterialList") List<ActivityMaterial> activityMaterialList);

    /**
     * 逻辑删除物料
     * @param activityMaterial
     * @return
     */
    @Update("update activity_material set status = 1, modified = now() where activity_id = #{activityMaterial.activityId} and status = 0")
    int delete(@Param("activityMaterial") ActivityMaterial activityMaterial);
}
