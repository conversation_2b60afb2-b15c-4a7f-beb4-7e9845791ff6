package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.tablestore.builder.CustomerRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.CustomerReward;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Arrays;

import static com.kikitrade.activity.dal.tablestore.model.CustomerReward.INDEX_CUSTOMER_REWARD_DEFAULT_INDEX;


@Slf4j
@Component
public class CustomerRewardStoreBuilderImpl extends WideColumnStoreBuilder<CustomerReward> implements CustomerRewardStoreBuilder {

    @PostConstruct
    public void init() {
        init(CustomerReward.class);
    }

    @Override
    public boolean insertIfNotExist(CustomerReward reward) {
        return putRow(reward, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean update(CustomerReward reward) {
        return updateRow(reward);
    }

    @Override
    public PageResult findByCustomerId(String customerId, int offset, int limit) {
        return pageSearch(QueryBuilders.term("customer_id", customerId).build(),
                null, offset, limit, INDEX_CUSTOMER_REWARD_DEFAULT_INDEX);
    }

    @Override
    public long countByCustomer(String customerId) {
        String aggName = "count_agg_customer_reward_by_customer";
        SearchQuery query = SearchQuery.newBuilder().query(QueryBuilders.term("customer_id", customerId))
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(query, INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, aggName);
    }

    @Override
    public long countByCustomerAndType(String customerId, Integer type) {

        String aggName = "count_agg_customer_reward_by_customer";

        BoolQuery.Builder boolQuery = QueryBuilders.bool().must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("type", type));

        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(searchQuery, INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, aggName);
    }

    @Override
    public long countByCustomerTypeAndCurrency(String customerId, String currency, Integer type) {

        String aggName = "count_agg_customer_reward_by_customer";

        BoolQuery.Builder boolQuery = QueryBuilders.bool().must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("currency", currency))
                .must(QueryBuilders.term("type", type));

        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(searchQuery, INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, aggName);
    }


    @Override
    public long countByInviteeAndType(String invitee, Integer type) {
        String aggName = "count_agg_customer_reward_by_invitee";

        BoolQuery.Builder boolQuery = QueryBuilders.bool().must(QueryBuilders.term("invitee", invitee)).must(QueryBuilders.term("type", type));

        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(searchQuery, INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, aggName);
    }

    @Override
    public long countByCustomerAndInvitee(String customerId, String invitee, Integer type) {
        String aggName = "count_agg_customer_reward_by_invitee";

        BoolQuery.Builder boolQuery = QueryBuilders.bool().must(QueryBuilders.term("customer_id", customerId)).must(QueryBuilders.term("invitee", invitee)).must(QueryBuilders.term("type", type));
        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(searchQuery, INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, aggName);
    }


    @Override
    public BigDecimal sumByCustomer(String customerId) {
        String aggName = "sum_agg_customer_reward_by_customer";
        SearchQuery query = SearchQuery.newBuilder().query(QueryBuilders.term("customer_id", customerId))
                .addAggregation(AggregationBuilders.sum(aggName, "amount")).build();

        return BigDecimal.valueOf(searchSum(query, INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, aggName));
    }

    @Override
    public BigDecimal sumByCustomerAndType(String customerId, Integer type) {
        String aggName = "sum_agg_customer_reward_by_customer_type";

        BoolQuery.Builder boolQuery = QueryBuilders.bool().must(QueryBuilders.term("customer_id", customerId)).must(QueryBuilders.term("type", type));

        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.sum(aggName, "amount")).build();

        return BigDecimal.valueOf(searchSum(searchQuery, INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, aggName));
    }


    @Override
    public BigDecimal sumByCustomerCurrencyAndType(String customerId, String currency, Integer type) {
        String aggName = "sum_agg_customer_reward_by_customer_type";

        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("currency", currency))
                .must(QueryBuilders.term("type", type));

        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.sum(aggName, "amount")).build();

        return BigDecimal.valueOf(searchSum(searchQuery, INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, aggName));
    }


    @Override
    public PageResult findByCustomerIdAndType(String customerId, Integer activityType, int offset, int limit) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("type", activityType))
                .must(QueryBuilders.bool().filter(QueryBuilders.range("amount")
                .greaterThan(0.0)));

        Sort sort = new Sort(Arrays.asList(new FieldSort("reward_time", SortOrder.DESC)));
        return pageSearch(boolQuery.build(),
                sort, offset, limit, INDEX_CUSTOMER_REWARD_DEFAULT_INDEX);
    }
}
