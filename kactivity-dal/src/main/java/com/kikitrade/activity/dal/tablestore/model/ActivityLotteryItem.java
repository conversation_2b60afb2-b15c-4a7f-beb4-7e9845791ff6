package com.kikitrade.activity.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Table(name = "activity_lottery_item")
public class ActivityLotteryItem implements Serializable {

    public static final String SEARCH_ACTIVITY_LOTTERY = "SEARCH_ACTIVITY_LOTTERY";

    @PartitionKey(name = "id")
    private String id;

    @SearchIndex(name = SEARCH_ACTIVITY_LOTTERY, column = "customer_id")
    @Column(name = "customer_id", isDefined = true)
    private String customerId;

    @SearchIndex(name = SEARCH_ACTIVITY_LOTTERY, column = "pool_id")
    @Column(name = "pool_id", isDefined = true)
    private String poolId;

    @SearchIndex(name = SEARCH_ACTIVITY_LOTTERY, column = "code")
    @Column(name = "code", isDefined = true)
    private String code;

    @SearchIndex(name = SEARCH_ACTIVITY_LOTTERY, column = "drew_time", fieldType = FieldType.LONG)
    @Column(name = "drew_time", isDefined = true, type = Column.Type.INTEGER)
    private Long drewTime;

    @Column(name = "reward_name")
    private String rewardName;

    @Column(name = "amount", type = Column.Type.DOUBLE)
    private BigDecimal amount;

    @Column(name = "currency")
    private String currency;

    @Column(name = "status")
    @SearchIndex(name = SEARCH_ACTIVITY_LOTTERY, column = "status")
    private String status;

    @Column(name = "use_amount", type = Column.Type.DOUBLE)
    private BigDecimal useAmount;

    @Column(name = "created", type = Column.Type.INTEGER)
    private Long created;

    @Column(name = "saas_id")
    private String saasId;

    @Column(name = "business_id")
    private String businessId;

    @Column(name = "is_cumulate", isDefined = true, type = Column.Type.BOOLEAN)
    @SearchIndex(name = SEARCH_ACTIVITY_LOTTERY, column = "is_cumulate")
    private boolean isCumulate;
}
