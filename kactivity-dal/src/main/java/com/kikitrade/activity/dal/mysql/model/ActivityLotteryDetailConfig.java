package com.kikitrade.activity.dal.mysql.model;

import com.kikitrade.activity.dal.IdCustomer;
import lombok.Getter;
import lombok.Setter;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@Table(name = "activity_lottery_detail_config")
public class ActivityLotteryDetailConfig extends BaseModel {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @KeySql(genId = IdCustomer.class)
    private String id;

    @Column(name = "lottery_id")
    private String lotteryId;

    @Column(name = "name")
    private String name;

    @Column(name = "currency")
    private String currency;

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "num")
    private Integer num;

    @Column(name = "percent")
    private BigDecimal percent;

    @Column(name = "is_low")
    private Boolean isLow;

    @Column(name = "remain_num")
    private Integer remainNum;

    @Column(name = "award_type")
    private String awardType;

    @Column(name = "status")
    private Integer status;

    private List<String> nodes;
    private String rewardLevel;
}
