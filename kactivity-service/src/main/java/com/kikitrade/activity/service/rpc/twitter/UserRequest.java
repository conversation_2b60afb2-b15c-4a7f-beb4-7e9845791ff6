package com.kikitrade.activity.service.rpc.twitter;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.twitter.clientlib.model.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/12 14:25
 */
@Data
@Slf4j
public class UserRequest extends AccessToken {

    private TwitterProperties twitterProperties;
    private String tweetId;
    private boolean cache = true;

    public UserRequest(){}

    public UserRequest(TwitterProperties twitterProperties){
        this.twitterProperties = twitterProperties;
    }

    public UserRequest build(String userId){
        this.tweetId = userId;
        return this;
    }

    public User execute(){
        try {
            User user = TwitterCache.IdToken.get(this.tweetId, User.class);
            if(user != null){
                return user;
            }
            User data = getApi(this.twitterProperties).users().findUserById(this.tweetId).execute().getData();
            if(data != null){
                TwitterCache.IdToken.put(this.tweetId, JSON.toJSONString(data));
            }
            log.info("[twitter] findUserById:{}", data);
            return data;
        }catch (Exception ex){
            log.error("[twitter] findUserById error {}", this.tweetId, ex);
            return null;
        }
    }
}
