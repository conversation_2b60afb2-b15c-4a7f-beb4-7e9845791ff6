package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/22 19:46
 */
@Data
@Table(name = "activity_cumulate_item")
public class ActivityCumulateItem implements Serializable {
    public static final String SEARCH_ACTIVITY_CUMULATE = "SEARCH_ACTIVITY_CUMULATE";

    @PartitionKey(name = "id")
    private String id;

    @Column(name = "saas_id")
    private String saasId;

    @SearchIndex(name = SEARCH_ACTIVITY_CUMULATE, column = "business_id")
    @Column(name = "business_id")
    private String businessId;

    @SearchIndex(name = SEARCH_ACTIVITY_CUMULATE, column = "customer_id")
    @Column(name = "customer_id", isDefined = true)
    private String customerId;

    @SearchIndex(name = SEARCH_ACTIVITY_CUMULATE, column = "code")
    @Column(name = "code", isDefined = true)
    private String code;

    @Column(name = "status")
    @SearchIndex(name = SEARCH_ACTIVITY_CUMULATE, column = "status")
    private String status;

    @Column(name = "cumulate_total", isDefined = true)
    private String cumulateTotal;

    @Column(name = "created", type = Column.Type.INTEGER)
    private Long created;

    @Column(name = "reward_time", type = Column.Type.INTEGER)
    private Long rewardTime;

    @Column(name = "reward_type")
    private String rewardCurrency;

    @Column(name = "reward_amount", type = Column.Type.DOUBLE)
    private BigDecimal rewardAmount;
}
