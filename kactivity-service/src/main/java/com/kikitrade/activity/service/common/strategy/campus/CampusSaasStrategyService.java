package com.kikitrade.activity.service.common.strategy.campus;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4 17:47
 */
public interface CampusSaasStrategyService {
    String strategy();
    void execute(OpenStrategyAuthRequest request) throws ActivityException;
}
