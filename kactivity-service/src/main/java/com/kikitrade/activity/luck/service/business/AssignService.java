package com.kikitrade.activity.luck.service.business;

import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReleaseDTO;
import com.kikitrade.activity.luck.service.model.ReceiveItem;
import java.util.List;

/**
 *
 */
public interface AssignService {

    /**
     * 构建红包
     * @param luckFortuneReleaseDTO
     * @return
     */
    List<ReceiveItem> buildLuckFortuneReceive(LuckFortuneRule rule, LuckFortuneReleaseDTO luckFortuneReleaseDTO, String id);
}
