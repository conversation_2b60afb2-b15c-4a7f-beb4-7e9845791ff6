package com.kikitrade.activity.service.common.config;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
*   <AUTHOR>
*   @date 2025/4/29 17:25
*   @description:
*/
@Data
public class LineProperties implements Serializable {
    private Map<String, String> clientId;
    private Map<String, String> clientSecret;
    private Map<String, String> pcRedirectUri;
    private Map<String, String> authScore;
    private Map<String, String> authVersion;
}
