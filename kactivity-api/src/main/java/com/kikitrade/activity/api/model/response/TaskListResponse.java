package com.kikitrade.activity.api.model.response;

import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/21 17:48
 */
@Data
public class TaskListResponse implements Serializable {

    private String taskId;
    private String groupId;
    private String title;
    private String code;
    private String desc;
    /**
     * 任务图片
     */
    private Map<String, String> image;
    private String labelName;
    private String labelColor;
    private List<Award> rewards;
    private String domain;
    private Integer status;
    private Long completeTime;
    /**
     * 任务结束时间
     */
    private Long endTime;
    private Integer limit;
    private Integer progress;
    private ActivityTaskConstant.ProgressTypeEnum progressType;
    private Integer order;
    /**
     * 奖励频率
     */
    private Integer rewardFrequency;
    /**
     * 任务详情页
     */
    private String url;
    /**
     * 任务授权页
     */
    private String connectUrl;
    private Integer btn;
    private List<TaskVO> subTasks;
    private Boolean showProgress;
    private Map<String, String> link;
    private BigDecimal showReward;
    private String position; //任务所属位置
    private String cycle;    // 这里存cycle计算出来的本档期的时间
    private ActivityTaskConstant.TaskCycleEnum cycleEnum;   // 这里存cycle
    private String icon;
    private String postTaskId;
    private String postTaskCode;
    private Integer postTaskStatus;
    private String postTaskDesc;
    private Integer postTaskReward;
    private Integer todayTaskStatus; //一次性累计任务展示当天任务完成状态
}
