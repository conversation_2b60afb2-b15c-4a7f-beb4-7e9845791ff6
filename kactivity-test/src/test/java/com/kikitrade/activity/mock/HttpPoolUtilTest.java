package com.kikitrade.activity.mock;

import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import okhttp3.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HttpPoolUtilTest {

    @Mock
    private OkHttpClient httpClient;

    @Mock
    private Call call;

    @Test
    public void testGetHttpClient() throws Exception {
        // 创建请求对象
        Request request = new Request.Builder().url("http://example.com").build();

        // 模拟call.execute()返回期望的响应
        Response expectedResponse = new Response.Builder()
                .request(request)
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .body(ResponseBody.create("application/json", MediaType.parse("{\"key\":\"value\"}")))
                .build();

        // 配置mock行为
        when(httpClient.newCall(request)).thenReturn(call);
        when(call.execute()).thenReturn(expectedResponse);

        // 使用HttpPoolUtil的mock版本
        OkHttpClient mockHttpClient = HttpPoolUtil.getHttpClient();
        Call mockCall = mockHttpClient.newCall(request);
        Response actualResponse = mockCall.execute();

        // 验证返回的响应是否符合预期
//        assertEquals(expectedResponse.code(), actualResponse.code());
//        assertEquals(expectedResponse.body().string(), actualResponse.body().string());
    }
}