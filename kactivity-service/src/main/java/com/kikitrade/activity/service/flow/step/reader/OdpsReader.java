package com.kikitrade.activity.service.flow.step.reader;

import com.alibaba.fastjson.JSON;
import com.aliyun.odps.Odps;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.tunnel.TableTunnel;
import com.aliyun.odps.tunnel.TunnelException;
import com.aliyun.odps.tunnel.io.TunnelRecordReader;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.flow.job.BatchConstantName;
import com.kikitrade.frameworks.odps.OdpsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.*;
import org.springframework.batch.item.support.AbstractItemStreamItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-05 14:04
 */
@Component
@StepScope
@Slf4j
public class OdpsReader extends AbstractItemStreamItemReader<Record> {

    @Resource
    private Odps odps;
    @Resource
    private ActivityBatchStatusStoreBuilder activityBatchStatusStoreBuilder;

    @Value("#{jobParameters['batchId']}")
    private String batchId;

    TunnelRecordReader recordReader;

    long total;

    @Override
    public Record read() throws Exception {
        if(recordReader == null){
            return null;
        }
        return recordReader.read();
    }

    @Override
    public void open(ExecutionContext executionContext) {
        try {
            TableTunnel tunnel = new TableTunnel(odps);
            TableTunnel.DownloadSession downloadSession = tunnel.createDownloadSession(odps.getDefaultProject(), String.format("%s%s", "auto_rewad_template_", batchId));
            total = downloadSession.getRecordCount();
            activityBatchStatusStoreBuilder.updateStatusAndNum(batchId, ActivityConstant.BatchRewardStatusEnum.NOT_IMPORTED.name(), (int)total);
            recordReader = downloadSession.openRecordReader(0, total);
        } catch (TunnelException e) {
            log.error("TableTunnel.DownloadSession.createDownloadSession exception", e);
        } catch (IOException ex){
            log.error("TableTunnel.DownloadSession.openRecordReader exception", ex);
        }
    }

    @Override
    public void close() {
        if(recordReader != null){
            try {
                recordReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
