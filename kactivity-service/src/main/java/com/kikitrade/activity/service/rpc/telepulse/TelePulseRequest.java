package com.kikitrade.activity.service.rpc.telepulse;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.service.common.config.TelePulseProperties;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/5 17:18
 */
@Data
@Slf4j
public class TelePulseRequest {

    private static final String INTA_DATA = "init-data";

    private TelePulseProperties telePulseProperties;

    private String cid;
    private String initData;
    private Map<String, Object> params;

    public TelePulseRequest() {
    }

    public TelePulseRequest(TelePulseProperties telePulseProperties) {
        this.telePulseProperties = telePulseProperties;
    }

    public TelePulseRequest build(String cid, String initData, Map<String, Object> params) {
        this.cid = cid;
        this.initData = initData;
        this.params = params;
        return this;
    }

    public Boolean verifyBoostTGChannel() {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("verify", VerifyTypeEnum.boost);
            param.put("chat_name", this.params.get("tgChannel"));
            String query = URLUtil.buildQuery(param, Charset.defaultCharset());
            Headers.Builder headerBuilder = new Headers.Builder()
                    .add("Content-Type", "application/json")
                    .add(INTA_DATA, this.initData);
            Request request = new Request.Builder()
                    .url(String.format("%s?%s",telePulseProperties.getDomain() + "/teleVerify", query))
                    .headers(headerBuilder.build())
                    .build();
            log.info("[verifyBoostTGChannel] request = {}, param = {}", request, JSON.toJSONString(param));
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                log.info("[verifyBoostTGChannel] responseBody:{}", responseBody);
                if (StringUtils.isNotBlank(responseBody)) {
                    JSONObject jsonObject = JSON.parseObject(responseBody);
                    if (Objects.nonNull(jsonObject) && jsonObject.containsKey("data")) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        return Objects.nonNull(data) && data.containsKey("is_boost") && data.getBoolean("is_boost");
                    }
                }
            }
        } catch (Exception e) {
            log.error("[verifyBoostTGChannel] Exception:{}, error:", this.cid, e);
        }
        return false;
    }

    public Boolean verifyTGPremium() {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("verify", VerifyTypeEnum.premium);
            String query = URLUtil.buildQuery(param, Charset.defaultCharset());
            Headers.Builder headerBuilder = new Headers.Builder()
                    .add("Content-Type", "application/json")
                    .add(INTA_DATA, this.initData);
            Request request = new Request.Builder()
                    .url(String.format("%s?%s",telePulseProperties.getDomain() + "/teleVerify", query))
                    .headers(headerBuilder.build())
                    .build();
            log.info("[verifyTGPremium] request = {}, param = {}", request, JSON.toJSONString(param));
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                log.info("[verifyTGPremium] responseBody:{}", responseBody);
                if (StringUtils.isNotBlank(responseBody)) {
                    JSONObject jsonObject = JSON.parseObject(responseBody);
                    if (Objects.nonNull(jsonObject) && jsonObject.containsKey("data")) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        return Objects.nonNull(data) && data.containsKey("is_premium") && data.getBoolean("is_premium");
                    }
                }
            }
        } catch (Exception e) {
            log.error("[verifyTGPremium] Exception:{}, error:", this.cid, e);
        }
        return false;
    }
}

enum VerifyTypeEnum {
    boost,
    premium
}