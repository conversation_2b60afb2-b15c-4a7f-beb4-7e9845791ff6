package com.kikitrade.activity.service.common.strategy.discord;

import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4 19:05
 */
@Component
public class DiscordRejectStrategyService implements DiscordSaasStrategyService{

    @Resource
    private RemoteCustomerBindService remoteCustomerBindService;

    @Override
    public String strategy() {
        return SaasStrategyConstant.DiscordStrategyEnum.REJECT.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(request.getSaasId(), request.getCustomerId());
        //之前授权过
        if(customerBindDTO.getDiscordId() != null){
            if(request.getAuthId() != null && !request.getAuthId().equals(customerBindDTO.getDiscordId())){
                throw new ActivityException(ActivityResponseCode.AUTH_REPEAT, ActivityResponseCode.AUTH_REPEAT.getKey());//no same enum todo
            }
        }else{
            List<TCustomerDTO> tCustomerBySocial = remoteCustomerBindService.findTCustomerBySocial(request.getSaasId(), ActivityTaskConstant.OpenSocialEnum.discord.name(), request.getAuthId());
            if(CollectionUtils.isNotEmpty(tCustomerBySocial)){
                throw new ActivityException(ActivityResponseCode.AUTH_REPEAT, ActivityResponseCode.AUTH_REPEAT.getKey());
            }
        }
    }
}
