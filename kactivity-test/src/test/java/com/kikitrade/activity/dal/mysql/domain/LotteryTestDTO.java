package com.kikitrade.activity.dal.mysql.domain;

import com.kikitrade.activity.facade.award.LotteryItem;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LotteryTestDTO {
    private String id;
    private String valid;
    private String remark;
    private String status;
    private String vipLevel;
    private String amount;
    private String timesLimit;
    private String rewardLimit;
    private List<Item> item;
}
