package com.kikitrade.activity.dal.mysql.model;

import com.kikitrade.activity.dal.IdCustomer;
import lombok.Getter;
import lombok.Setter;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Getter
@Setter
@Table(name = "activity_lottery_config")
public class ActivityLotteryConfig extends BaseModel {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @KeySql(genId = IdCustomer.class)
    private String id;

    /**
     * 奖池有效期
     * yyyy-MM-dd hh:mm:ss
     */
    @Column(name = "valid")
    private String valid;

    /**
     * 奖池描述
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 奖池状态
     * 1：active 0：draft
     */
    @Column(name = "status")
    private Integer status;

    /**
     * vip等级，多个，分割
     */
    @Column(name = "vip_level")
    private String vipLevel;

    /**
     * 抽奖消耗的数
     */
    @Column(name = "amount")
    private BigDecimal amount;

    /**
     * 抽奖消耗的币种
     */
    @Column(name = "currency")
    private String currency;

    /**
     * 抽奖次数限制
     */
    @Column(name = "times_limit")
    private Integer timesLimit;

    /**
     * 获奖金额限制
     */
    @Column(name = "reward_limit")
    private BigDecimal rewardLimit;

    /**
     * 抽象形式，box/slot
     */
    @Column(name = "type")
    private String type;
}
