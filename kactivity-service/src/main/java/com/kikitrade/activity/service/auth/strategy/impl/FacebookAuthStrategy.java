package com.kikitrade.activity.service.auth.strategy.impl;

import com.kikitrade.activity.service.auth.strategy.AbstractOAuth2AuthStrategy;
import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.model.OpenAuthRequest;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import com.kikitrade.activity.service.rpc.ThreePlatformApi;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Component;

import java.util.Base64;

/**
 * Facebook认证策略实现
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
public class FacebookAuthStrategy extends AbstractOAuth2AuthStrategy {

    private static final String FACEBOOK_AUTH_URL = "https://graph.facebook.com/v23.0/oauth/access_token";

    private final ThreePlatformProperties threePlatformProperties;
    private final ThreePlatformApi threePlatformApi;

    public FacebookAuthStrategy(OkHttpClient okHttpClient, 
                               ThreePlatformProperties threePlatformProperties,
                               ThreePlatformApi threePlatformApi) {
        super(okHttpClient);
        this.threePlatformProperties = threePlatformProperties;
        this.threePlatformApi = threePlatformApi;
    }

    @Override
    public String getPlatformName() {
        return OpenSocialPlatformEnum.facebook.name();
    }

    @Override
    public OpenAuthRequest buildAuthRequest(String saasId, String code, String redirectUri) {
        try {
            return OpenAuthRequest.builder()
                    .url(FACEBOOK_AUTH_URL)
                    .query(String.format("code=%s&grant_type=authorization_code&redirect_uri=%s", 
                        code, redirectUri))
                    .clientId(threePlatformProperties.getFacebook().getClientId().get(saasId))
                    .basic(Base64.getEncoder().encodeToString(
                        String.format("%s:%s",
                            threePlatformProperties.getFacebook().getClientId().get(saasId),
                            threePlatformProperties.getFacebook().getClientSecret().get(saasId)).getBytes()))
                    .build();
        } catch (Exception e) {
            log.error("[Facebook] 构建认证请求失败", e);
            return null;
        }
    }

    @Override
    public OpenAuthRequest buildRefreshRequest(String saasId, String refreshToken) {
        // Facebook通常不需要刷新令牌，或者有不同的实现方式
        log.warn("[Facebook] 暂不支持刷新令牌");
        return null;
    }

    @Override
    public SocialUserInfo getCurrentUser(String saasId, String accessToken, String refreshToken) {
        try {
            AccessToken build = AccessToken.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .saasId(saasId)
                    .build();
            return threePlatformApi.facebook().getCurrentUserRequest(build).execute();
        } catch (Exception e) {
            log.error("[Facebook] 获取用户信息失败", e);
            return null;
        }
    }

    @Override
    public boolean supportsRefreshToken() {
        return false;
    }
}
