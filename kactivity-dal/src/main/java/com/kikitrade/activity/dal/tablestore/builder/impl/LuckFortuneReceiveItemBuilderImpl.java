package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.query.TermsQuery;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReceiveItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem.*;
import static com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem.SEARCH_RELEASE_BY_ID;

@Service
@Slf4j
public class LuckFortuneReceiveItemBuilderImpl extends WideColumnStoreBuilder<LuckFortuneReceiveItem> implements LuckFortuneReceiveItemBuilder {

    @Resource
    private SeqGeneraterService seqGeneraterService;

    @Override
    public String getTableName() {
        return "luck_fortune_receive_item";
    }

    @PostConstruct
    public void init() {
        init(LuckFortuneReceiveItem.class);
    }

    /**
     * 插入一个领取的红包
     *
     * @param luckFortuneReceiveItem
     * @return
     */
    @Override
    public boolean insert(LuckFortuneReceiveItem luckFortuneReceiveItem) {
        return super.putRow(luckFortuneReceiveItem, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    /**
     * 更新发放的红包
     *
     * @param luckFortuneReceiveItem
     * @return
     */
    @Override
    public boolean update(LuckFortuneReceiveItem luckFortuneReceiveItem) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(luckFortuneReceiveItem, condition);
    }

    /**
     * 根据红包id，查询红包
     *
     * @param id
     * @return
     */
    @Override
    public LuckFortuneReceiveItem findById(String id) {
        List<RangeQueryParameter> query = new ArrayList<>();
        query.add(new RangeQueryParameter("id", PrimaryKeyValue.fromString(id), PrimaryKeyValue.fromString(id)));
        query.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        query.add(new RangeQueryParameter("release_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQueryOne(IDX_RECEIVE_BY_ID,query);
    }

    /**
     * 红包领取记录
     * @param releaseId
     * @return
     */
    @Override
    public List<LuckFortuneReceiveItem> findByReleaseId(String releaseId) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.term("release_id", releaseId));
        Sort sort = new Sort(Arrays.asList(new FieldSort("id", SortOrder.DESC)));
        List<LuckFortuneReceiveItem> list = new ArrayList<>();
        int i = 0;
        while (true){
            Page page = pageSearchQuery(builder.build(), sort, i++ * 100, 100, SEARCH_RECEIVE_BY_ID);
            if(page == null || CollectionUtils.isEmpty(page.getRows())){
                break;
            }
            list.addAll(page.getRows());
            if(page.getRows().size() == page.getTotalCount()){
                break;
            }
        }
        return list;
    }

    /**
     * 查询在某个红包中的领取记录
     * @param customerId
     * @param releaseId
     * @return
     */
    @Override
    public LuckFortuneReceiveItem findByCustomerIdAndReleaseId(String customerId, String releaseId) {
        LuckFortuneReceiveItem param = new LuckFortuneReceiveItem();
        param.setCustomerId(customerId);
        param.setReleaseId(releaseId);
        return getRow(param);
    }

    /**
     * 查询在某个红包中的领取记录
     *
     * @param receiveCode
     * @param releaseId
     * @return
     */
    @Override
    public LuckFortuneReceiveItem findByReceiveCodeIdAndReleaseId(String receiveCode, String releaseId) {
        List<RangeQueryParameter> query = new ArrayList<>();
        query.add(new RangeQueryParameter("receive_code", PrimaryKeyValue.fromString(receiveCode), PrimaryKeyValue.fromString(receiveCode)));
        query.add(new RangeQueryParameter("release_id", PrimaryKeyValue.fromString(releaseId), PrimaryKeyValue.fromString(releaseId)));
        query.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQueryOne(IDX_RECEIVE_BY_RECEIVE_CODE, query);
    }

    /**
     * 查询在某个红包中的领取记录
     *
     * @param receiveCode
     * @return
     */
    @Override
    public List<LuckFortuneReceiveItem> findByReceiveCode(String receiveCode) {
        List<RangeQueryParameter> query = new ArrayList<>();
        query.add(new RangeQueryParameter("receive_code", PrimaryKeyValue.fromString(receiveCode), PrimaryKeyValue.fromString(receiveCode)));
        query.add(new RangeQueryParameter("release_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        query.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQuery(IDX_RECEIVE_BY_RECEIVE_CODE, query);
    }

    /**
     * 最近领取的n条记录
     *
     * @param customerId
     * @param limit
     * @return
     */
    @Override
    public Page<LuckFortuneReceiveItem> findByCustomerId(String customerId, String type, String startTime, String endTime, int offset, int limit) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.term("customer_id", customerId));
        if(StringUtils.isNotBlank(type)){
            builder.must(QueryBuilders.term("assign_type", type));
        }
        if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
            builder.must(QueryBuilders.range("receive_time").greaterThanOrEqual(startTime).lessThanOrEqual(endTime));
        }else if(StringUtils.isNotBlank(startTime)){
            builder.must(QueryBuilders.range("receive_time").greaterThanOrEqual(startTime));
        }else if(StringUtils.isNotBlank(endTime)){
            builder.must(QueryBuilders.range("receive_time").greaterThanOrEqual(endTime));
        }
        builder.must(QueryBuilders.terms("status")
                .addTerm(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_SUCCESS.getCode()));

        Sort sort = new Sort(Arrays.asList(new FieldSort("receive_time", SortOrder.DESC)));
        long total = countByCustomerId(customerId, type, startTime, endTime);
        Page page = pageSearchQuery(builder.build(), sort, offset, limit, SEARCH_RECEIVE_BY_ID);
        if(page != null){
            page.setTotalCount(total);
        }
        return page;
    }

    private long countByCustomerId(String customerId, String type, String startTime, String endTime) {
        String aggName = "count_agg_receive_by_customer_id_v1";
        BoolQuery.Builder filter = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", customerId));
        if(StringUtils.isNotBlank(type)){
            filter.must(QueryBuilders.term("assign_type", type));
        }
        if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
            filter.must(QueryBuilders.range("receive_time").greaterThanOrEqual(startTime).lessThanOrEqual(endTime));
        }else if(StringUtils.isNotBlank(startTime)){
            filter.must(QueryBuilders.range("receive_time").greaterThanOrEqual(startTime));
        }else if(StringUtils.isNotBlank(endTime)){
            filter.must(QueryBuilders.range("receive_time").greaterThanOrEqual(endTime));
        }
        filter.must(QueryBuilders.terms("status")
                .addTerm(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_SUCCESS.getCode()));
        SearchQuery.Builder builder = SearchQuery.newBuilder().query(filter);
        builder.addAggregation(AggregationBuilders.count(aggName, "customer_id"));
        return searchCount(builder.build(), SEARCH_RECEIVE_BY_ID, aggName);
    }

    @Override
    public List<LuckFortuneReceiveItem> findByExpiredTime(Integer status, String expiredTime) {
        List<RangeQueryParameter> query = new ArrayList<>();

        query.add(new RangeQueryParameter("status", PrimaryKeyValue.fromLong(status),PrimaryKeyValue.fromLong(status)));
        query.add(new RangeQueryParameter("expired_time", PrimaryKeyValue.INF_MIN, expiredTime == null ? PrimaryKeyValue.INF_MAX : PrimaryKeyValue.fromString(expiredTime)));
        query.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        query.add(new RangeQueryParameter("release_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));

        return rangeQuery(IDX_RECEIVE_BY_EXPIRED_DATE, query);
    }

    /**
     * 发放的红包总数
     *
     * @param customerId
     * @return
     */
    @Override
    public long countReceive(String customerId) {
        String aggName = "count_agg_receive_by_customer_id";
        SearchQuery query = SearchQuery.newBuilder().query(QueryBuilders.bool()
                .filter(QueryBuilders.term("customer_id", customerId))
                .filter(QueryBuilders.terms("status")
                        .addTerm(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_SUCCESS.getCode())))
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(query, SEARCH_RECEIVE_BY_ID, aggName);
    }

    /**
     * 历史发放的空投总价值
     *
     * @param customerId
     * @return
     */
    @Override
    public BigDecimal sumAmountReceive(String customerId) {
        String aggName = "sum_agg_receive_by_customer";
        SearchQuery searchQuery = SearchQuery.newBuilder().query(QueryBuilders.bool()
                .filter(QueryBuilders.term("customer_id", customerId))
                .filter(QueryBuilders.terms("status")
                        .addTerm(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_SUCCESS.getCode())))
                .addAggregation(AggregationBuilders.sum(aggName, "cost")).build();

        return BigDecimal.valueOf(searchSum(searchQuery, SEARCH_RECEIVE_BY_ID, aggName));
    }

    /**
     * 根据状态查询最近领取的红包
     *
     * @param customerId
     * @param startTime
     * @param statusList
     * @return
     */
    @Override
    public List<LuckFortuneReceiveItem> findByCustomerIdAndStatus(String customerId, String startTime, List<Long> statusList) {
        log.info("customerId:{}, startTime:{}, statusList:{}", customerId, startTime, statusList);
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.term("customer_id", customerId));
        if(StringUtils.isNotBlank(startTime)){
            builder.must(QueryBuilders.range("receive_time").greaterThanOrEqual(startTime));
        }
        if(CollectionUtils.isNotEmpty(statusList)){
            TermsQuery.Builder statusBuild = QueryBuilders.terms("status");
            for(Long status : statusList){
                statusBuild.addTerm(status);
            }
            builder.must(statusBuild);
        }
        Sort sort = new Sort(Arrays.asList(new FieldSort("status", SortOrder.ASC), new FieldSort("receive_time", SortOrder.DESC)));
        return search(builder.build(), sort, 0, 100, SEARCH_RECEIVE_BY_ID);
    }
}
