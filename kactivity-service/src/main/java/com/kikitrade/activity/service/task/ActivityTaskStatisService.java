package com.kikitrade.activity.service.task;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.mq.ActivityEventMessage;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/24 11:06
 */
public interface ActivityTaskStatisService {

    /**
     * 计数
     * @param config
     * @param activityEventMessage
     * @param eventCode
     * @throws Exception
     */
    Long incrementProgress(TaskConfigDTO config, ActivityEventMessage activityEventMessage, TaskCodeConfig eventCode) throws Exception;
}
