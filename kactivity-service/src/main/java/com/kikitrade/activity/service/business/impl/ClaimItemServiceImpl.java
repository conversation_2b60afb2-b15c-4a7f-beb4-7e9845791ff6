package com.kikitrade.activity.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.api.model.response.ClaimResponse;
import com.kikitrade.activity.dal.tablestore.builder.ClaimItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.ClaimItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ClaimItemService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.model.balance.BalanceDataVO;
import com.kikitrade.activity.service.model.balance.BalanceVO;
import com.kikitrade.activity.service.model.balance.NFTVO;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.kikitrade.activity.api.exception.ActivityExceptionType.CLAIM_REPEAT;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/9 14:17
 */
@Service
@Slf4j
public class ClaimItemServiceImpl implements ClaimItemService {

    @Resource
    private ClaimItemBuilder claimItemBuilder;
    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private ThreePlatformProperties threePlatformProperties;

    @Override
    public Boolean isContinue(String businessType) {
        return "nft".equals(businessType);
    }

    @Override
    public Boolean allowClaim(String businessType, String customerId, String address, List<String> addresses) {
        boolean exist = existClaim(businessType, customerId);
        if(exist){
            throw new com.kikitrade.activity.api.exception.ActivityException(CLAIM_REPEAT);
        }
        return CollectionUtils.isNotEmpty(getNfts(addresses));
    }

    @Override
    public ClaimResponse claim(String businessType, String address, String customerId, String code, String saasId, List<String> addresses) throws ActivityException {
        boolean exist = existClaim(businessType, customerId);
        if(exist){
            throw new com.kikitrade.activity.api.exception.ActivityException(CLAIM_REPEAT);
        }
        ClaimResponse response = new ClaimResponse();
        List<String> nfts = getNfts(addresses);
        if(CollectionUtils.isEmpty(nfts)){
            log.info("nft not match :{}", address);
            response.setSuccess(false);
            return response;
        }
        long count = claimItemBuilder.countByIds(businessType, nfts, OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        if(nfts.size() == count){
            log.info("nft all claim:{}", address);
            response.setSuccess(false);
            return response;
        }
        List<ClaimItem> claimItems = new ArrayList<>();
        boolean success = false;
        for(String nft : nfts){
            ClaimItem claimItem = new ClaimItem();
            claimItem.setBusinessType(businessType);
            claimItem.setCode(nft);
            claimItem.setCreated(OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            claimItem.setTs(System.currentTimeMillis());
            claimItem.setCustomerId(customerId);
            claimItem.setSaasId(saasId);
            Map<String,String> map = new HashMap<>();
            map.put("amount", String.valueOf(kactivityProperties.getNftRewardPoint()));
            map.put("currency", ActivityConstant.AwardTypeEnum.POINT.name());
            claimItem.setAward(JSON.toJSONString(map));
            claimItems.add(claimItem);
            Boolean insert = claimItemBuilder.insert(claimItem);
            success = success || insert;
        }
        if(success){
            reward(claimItems.get(0), new BigDecimal(kactivityProperties.getNftRewardPoint()), ActivityConstant.AwardTypeEnum.POINT.name());
        }
        response.setSuccess(true);
        return response;
    }

    private boolean existClaim(String businessType, String customerId) {
        return claimItemBuilder.exist(businessType, customerId, OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }

    private List<String> getNfts(List<String> addresses) {
        List<String> nfts = new ArrayList<>();
        for(String address : addresses){
            List<String> nft = getNfts(address);
            if(nft != null && !nft.isEmpty()){
                nfts.addAll(nft);
            }
        }
        return nfts;
    }

    private List<String> getNfts(String address){
        HttpUrl.Builder urlBuilder = HttpUrl.parse("https://"+kactivityProperties.getNftChain()+".g.alchemy.com/nft/v3/"+kactivityProperties.getNftAppToken()+"/getNFTsForOwner").newBuilder();
        urlBuilder.addQueryParameter("owner", address);
        urlBuilder.addQueryParameter("contractAddresses[]",  threePlatformProperties.getMugen().getOdysseyPassNFTAddress());
        urlBuilder.addQueryParameter("withMetadata",  "false");
        Headers.Builder headerBuilder = new Headers.Builder()
                .add("accept", "application/json");
        Request request = new Request.Builder()
                .url(urlBuilder.build())
                .headers(headerBuilder.build())
                .build();
        try{
            OkHttpClient httpClient = HttpPoolUtil.getNftHttpClient();
            Response response = httpClient.newCall(request).execute();
            log.info("ClaimItemServiceImpl getNfts address{}, response:{}", address, JSON.toJSONString(response));

            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                log.info("ClaimItemServiceImpl getNfts address:{}, responseBody:{}", address, responseBody);
                JSONObject jsonObject = JSON.parseObject(responseBody);
                return Optional.ofNullable(jsonObject.getJSONArray("ownedNfts"))
                        .map(NFTs -> NFTs.stream().filter(Objects::nonNull)
                                .map(nft -> ((JSONObject) nft).getString("tokenId")).collect(Collectors.toList()))
                        .orElse((null));
            }
            log.error("getNfts failed. address:{}, response code:{}", address, response.code());
        }catch (Exception ex){
            log.error("getNfts error:{}", address ,ex);
        }
        return null;
    }

    private void reward(ClaimItem claimItem, BigDecimal amount, String currency) {
        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setSaasId(claimItem.getSaasId());
        reward.setBatchId(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD));
        reward.setCustomerId(claimItem.getCustomerId());
        reward.setBusinessId(TimeUtil.getDataStr(TimeUtil.parseUnittime(claimItem.getTs()), TimeUtil.YYYYMMDDHHmmss) + claimItem.getCustomerId().substring(claimItem.getCustomerId().length() - 4));
        reward.setSeq("claim_nft:" + reward.getBusinessId());
        //计算奖励金额
        reward.setAmount(amount.toPlainString());
        reward.setRewardType(currency);
        reward.setCurrency(currency);
        reward.setBusinessType(AssetBusinessType.REWARD.getCodeDesc());
        reward.addExtendParam("desc", "nft claim");
        LauncherParameter launcherParameter = new LauncherParameter();
        launcherParameter.setActivityCustomReward(reward);
        launcherParameter.setProvideType(ActivityTaskConstant.ProvideType.auto);
        try {
            log.info("[task] eventAction send reward: {}", launcherParameter);
            activityRealTimeRewardTccService.reward(launcherParameter);
        } catch (Exception e) {
            log.error("reward error:{}", launcherParameter, e);
        }
    }
}
