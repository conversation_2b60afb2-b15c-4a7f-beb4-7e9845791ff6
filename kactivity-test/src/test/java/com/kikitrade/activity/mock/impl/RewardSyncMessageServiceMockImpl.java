package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.mock.RewardSyncMessageServiceMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.mq.RewardSyncMessageService;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class RewardSyncMessageServiceMockImpl implements RewardSyncMessageServiceMock {
    @Resource
    RewardSyncMessageService<ActivityCustomReward> rewardSyncMessageService;

    @Override
    public void send() {
        Mockito.doAnswer(c -> {
            BusinessOP.reward = c.getArgument(0);
            return null;
        }).when(rewardSyncMessageService).send(Mockito.any(ActivityCustomReward.class));
    }
}
