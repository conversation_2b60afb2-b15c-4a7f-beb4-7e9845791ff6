package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityCustomerCurrentInterest;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityCustomerCurrentInterestDao extends BaseDao<ActivityCustomerCurrentInterest, String> {

    List<ActivityCustomerCurrentInterest> findAll(@Param("transDate") String transDate, @Param("offset") Integer offset, @Param("limit") Integer limit);

    int updateStatus(@Param("transDate") String transDate, @Param("customerId") String customerId, @Param("currency") String currency, @Param("status") Integer status);

    long countByStatus(@Param("transDate") String transDate, @Param("status") int status);
}
