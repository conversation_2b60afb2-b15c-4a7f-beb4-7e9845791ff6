package com.kikitrade.activity.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * created by jacky. 2020/2/12 5:20 PM
 */

@Getter
@Setter
@Table(name = "activity_biz_results")
public class ActivityBizResult {

    private final String ACTIVITY_BIZ_RESULTS_INDEX_FIND_STATUS = "activity_biz_results_index_find_status";

    @PartitionKey(value = 2, name = "biz_id")
    @Column(name = "biz_id", isDefined = true)
    private String bizId;

    @PartitionKey(value = 1, name = "activity_id")
    @SearchIndex(name = {ACTIVITY_BIZ_RESULTS_INDEX_FIND_STATUS}, column = "activity_id", fieldType = FieldType.LONG)
    @Column(name = "activity_id", isDefined = true)
    private Integer activityId;
    /**
     * {@link ActivityType}
     */
    @Column(name = "activity_type", isDefined = true)
    private Integer activityType;
    @PartitionKey(value = 0, name = "customer_id")
    @SearchIndex(name = {ACTIVITY_BIZ_RESULTS_INDEX_FIND_STATUS}, column = "customer_id")
    @Column(name = "customer_id")
    private String customerId;
    //业务自定义
    @SearchIndex(name = {ACTIVITY_BIZ_RESULTS_INDEX_FIND_STATUS}, column = "status")
    @Column(name = "status", isDefined = true)
    private String status;
    @Column(name = "currency", isDefined = true)
    private String currency;
    @Column(name = "obj_json")
    private String objJson;
    @Column(name = "remark")
    private String remark;

    @SearchIndex(name = {ACTIVITY_BIZ_RESULTS_INDEX_FIND_STATUS}, column = "created", fieldType = FieldType.LONG)
    @Column(name = "created", isDefined = true)
    private Date created;
    @SearchIndex(name = {ACTIVITY_BIZ_RESULTS_INDEX_FIND_STATUS}, column = "saas_id")
    @Column(name = "saas_id", isDefined = true)
    private String saasId;


//    public ActivityBizResult fromActivityMessage(ActivityMessage request) {
//        this.setBizId(request.getBusinessId());
//        this.setActivityId(request.getActivityId());
//        this.setCustomerId(request.getCustomerId());
//        this.setStatus("1");
//        this.setCreated(new Date());
//        this.setSaasId(request.getSaasId());
//        return this;
//    }


}
