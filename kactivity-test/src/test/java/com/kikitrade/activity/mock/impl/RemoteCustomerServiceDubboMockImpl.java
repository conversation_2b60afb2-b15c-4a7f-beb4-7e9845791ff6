package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteCustomerServiceDubboMock;
import com.kikitrade.activity.service.reward.impl.CustomerServiceImpl;
import com.kikitrade.activity.util.ReflectionUtils;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.kcustomer.common.constants.CustomerConstants;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

import java.util.Date;

import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;

@Component
public class RemoteCustomerServiceDubboMockImpl implements RemoteCustomerServiceDubboMock {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    CustomerServiceImpl customerService;
    @Resource(name = "remoteCustomerServiceStub")
    RemoteCustomerService remoteCustomerService;

    @PostConstruct
    public void init() throws NoSuchFieldException, ClassNotFoundException, IllegalAccessException {
        RemoteCustomerService fromSpring= applicationContext.getBean("remoteCustomerServiceStub",RemoteCustomerService.class);
        //通过反射注入属性
        /*String classPath = "com.kikitrade.activity.service.reward.impl.CustomerServiceImpl";
        String fieldName = "remoteCustomerService";*/
        //ReflectionUtils.setField(classPath, fieldName, customerService, fromSpring);

        /*RemoteAccountingService remoteAccountingServiceStub = applicationContext.getBean("remoteAccountingServiceStub", RemoteAccountingService.class);
        Field remoteAccountingServiceField = ReflectionUtils.findField(TradingCommonServiceImpl.class, "remoteAccountingService");
        remoteAccountingServiceField.setAccessible(Boolean.TRUE);
        ReflectionUtils.setField(remoteAccountingServiceField, tradingCommonService, remoteAccountingServiceStub);*/
        ReflectionUtils.setField(CustomerServiceImpl.class, "remoteCustomerService", customerService, fromSpring);
    }



    @Override
    public void getById() {
        Mockito.doAnswer(c->{
            String cus = c.getArgument(1);
            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setId(cus);
            customerDTO.setStatus(CustomerConstants.Status.ACTIVATED.getCode());
            customerDTO.setCreateTime(new Date());
            return customerDTO;
        }).when(remoteCustomerService).getById(anyString(), anyString(), anyBoolean());

        Mockito.doAnswer(c->{
            String cus = c.getArgument(1);
            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setId(cus);
            customerDTO.setStatus(CustomerConstants.Status.ACTIVATED.getCode());
            customerDTO.setCreateTime(new Date());
            return customerDTO;
        }).when(remoteCustomerService).getById(anyString(), anyString());
    }
}
