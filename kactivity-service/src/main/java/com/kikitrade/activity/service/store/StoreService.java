package com.kikitrade.activity.service.store;

import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.activity.service.model.StoreInfo;

public interface StoreService {

    /**
     * 查询库存所在仓库
     * @param now
     * @param drew
     * @return
     */
    StoreInfo getStore(long now, ActivityLotteryDetailConfig drew);

    /**
     * 删除库存
     * @param skuId
     * @param storeNo
     * @param itemId
     * @return
     */
    boolean decreaseStore(String skuId, String storeNo, String itemId);

    /**
     * 增加库存
     * @param skuId
     * @param storeNo
     * @param num
     * @return
     */
    boolean increaseStore(String skuId, String storeNo, Integer num);
}
