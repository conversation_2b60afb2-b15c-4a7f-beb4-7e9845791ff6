package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.kikitrade.activity.dal.tablestore.builder.PrecisionMetricsBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrecisionMetrics;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;

@Service
public class PrecisionMetricsBuilderImpl extends WideColumnStoreBuilder<PrecisionMetrics> implements PrecisionMetricsBuilder {

    @Override
    public String getTableName() {
        return "precision_metrics";
    }

    @PostConstruct
    public void init() {
        init(PrecisionMetrics.class);
    }

    @Override
    public boolean insert(PrecisionMetrics precisionMetrics) {
        return super.putRow(precisionMetrics);
    }
}
