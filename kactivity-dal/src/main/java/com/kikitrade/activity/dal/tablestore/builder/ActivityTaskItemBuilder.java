package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.ots.TransactionCallback;

import java.util.List;

public interface ActivityTaskItemBuilder {

    String getTableName();

    /**
     * 插入一个任务项
     *
     * @param activityTaskItem
     * @return
     */
    boolean insert(ActivityTaskItem activityTaskItem);

    /**
     * 主键判断是否存在
     *
     * @return
     */
    boolean exist(String customerId, String taskId, String cycle, String targetId);

    /**
     * 更新任务项，主要进度，完成事件
     *
     * @param activityTaskItem
     * @return
     */
    boolean update(ActivityTaskItem activityTaskItem);

    boolean updateStatus(ActivityTaskItem activityTaskItem);

    boolean deleteDetail(String customerId, String taskId, String cycle, String targetId);

    boolean delete(List<ActivityTaskItem> list);

    /**
     * 更新任务项，主要进度，完成事件
     *
     * @param activityTaskItem
     * @return
     */
    boolean updateProgress(ActivityTaskItem activityTaskItem);


    /**
     * 查询我在当前周期的任务
     *
     * @param customerId 用户id
     * @param cycle      当前任务周期
     * @param taskId     config->id
     * @return
     */
    ActivityTaskItem findByCustomer(String customerId, String cycle, String taskId);

    /**
     * 查询我在当前周期的任务
     *
     * @param customerId 用户id
     * @param cycle      当前任务周期
     * @param taskId     config->id
     * @return
     */
    List<ActivityTaskItem> findByCustomer(String customerId, String cycle, List<String> taskId);

    /**
     *
     * @param customerId
     * @param taskId
     * @return
     */
    List<ActivityTaskItem> findByCustomer(String customerId, String taskId);

    /**
     * 查询某个周期内任务的详情
     * @param customerId
     * @param cycle
     * @param taskId
     * @return
     */
    List<ActivityTaskItem> findDetailByCustomer(String customerId, String cycle, String taskId);

    ActivityTaskItem findDetailByCustomer(String customerId, String cycle, String taskId, String targetId);

    /**
     * 查询我在当前周期的任务
     *
     * @param platform 社区平台twitter、discord
     * @param socialCustomerId 某个社区平台的用户id
     * @return
     */
    ActivityTaskItem findByTarget(String platform, String socialCustomerId, String cycle, String taskId);


    /**
     * 查询我在当前周期的任务
     *
     * @param customerId 用户id
     * @param cycle      当前任务周期
     * @param config     config
     * @return
     */
    ActivityTaskItem findByCustomerFromIndex(String customerId, String cycle, ActivityTaskConfig config);

    Long incrementProgress(ActivityTaskItem activityTaskItem, Integer inc);

    boolean execute(ActivityTaskItem activityTaskItem, TransactionCallback<ActivityTaskItem> action);

    TokenPage<ActivityTaskItem> findByCodeList(List<String> codeList, String startTime, String endTime, List<String> status, String nextToken, int limit);

    Page<ActivityTaskItem> findByIdList(String customerId, String taskId, String startTime, String endTime, int limit);
}