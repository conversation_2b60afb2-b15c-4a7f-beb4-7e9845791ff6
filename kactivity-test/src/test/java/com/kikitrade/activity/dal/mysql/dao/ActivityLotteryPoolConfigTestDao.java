package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.ActivityLotteryPoolConfig;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ActivityLotteryPoolConfigTestDao {

    void createTableIfNotExist();

    List<ActivityLotteryPoolConfig> selectAll();

    @Delete("truncate activity_lottery_pool_config")
    void truncate();

    void updateRemainNumByCondition(String drawId, Integer[] poolNos);

    int getSumRemainNumByCondition(String drawId, Integer[] poolNos);

    int getRemainNumByCondition(String drawId, String poolNo);

    @Select("update activity_lottery_pool_config set remain_num = #{num} where draw_id=#{drawId}" )
    void updateRemainNumByDrawId(@Param("num") int num, @Param("drawId") String drawId);

    List<ActivityLotteryPoolConfig> selectByDrawId(@Param("drawId") String drawId);




}
