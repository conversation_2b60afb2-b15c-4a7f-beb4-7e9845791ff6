
package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.annotation.Generated;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2019/03/11 14:07
 */
@Getter
@Setter
@Table(name = "activity_contents")
public class ActivityContents extends BaseModel {

    @Id
    //活动ID（主键）
    @Column(name = "activity_id", nullable = false)
    private Integer activityId;  //id

    //语言版本
    @Column(name = "locale", nullable = false)
    private String locale;

    //活动名称
    @Column(name = "name", nullable = false)
    private String name;

    //活动链接
    @Column(name = "url")
    private String url;

    //活动详情
    @Column(name = "content")
    private String content;

}
