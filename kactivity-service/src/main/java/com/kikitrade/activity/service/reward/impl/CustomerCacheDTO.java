package com.kikitrade.activity.service.reward.impl;

import com.kikitrade.kcustomer.api.model.CustomerDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class CustomerCacheDTO {

    private String userName;
    private String id;
    private String phone;
    private String email;
    private Integer status;
    private String country;
    private Integer type;
    private String vipLevel = "vip0";
    private String kycLevel;
    private String icon;
    private Date registerTime;
    private String nickName;

    public static CustomerCacheDTO toEntity(CustomerDTO customerDTO){
        CustomerCacheDTO dto = new CustomerCacheDTO();
        dto.setId(customerDTO.getId());
        dto.setUserName(customerDTO.getUserName());
        dto.setCountry(customerDTO.getRegion());
        dto.setEmail(customerDTO.getEmail());
        dto.setPhone(customerDTO.getPhone());
        dto.setStatus(customerDTO.getStatus());
        dto.setType(customerDTO.getType());
        dto.setRegisterTime(customerDTO.getCreateTime());
        dto.setIcon(customerDTO.getAvatar());
        dto.setNickName(customerDTO.getNickName());
        return dto;
    }
}
