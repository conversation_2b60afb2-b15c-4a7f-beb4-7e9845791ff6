package com.kikitrade.activity.service.flow.step.reader;

import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-12 10:50
 */
@Component
@StepScope
@Slf4j
public class RosterReader extends AbstractTokenPageReader<ActivityBatchRewardRoster>{

    @Resource
    private ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;

    @Value("#{jobParameters['batchId']}")
    private String batchId;

    /**
     * 进行分页读取
     *
     * @return nextToken
     */
    @Override
    protected PrimaryKey doReadPage() {
        RangeResult<ActivityBatchRewardRoster> page = activityBatchRewardRosterStoreBuilder.findForPage(batchId, ActivityConstant.ImportStatusEnum.NOT_IMPORTED.name(), 100, getNextToken());
        if(page != null){
            results = page.list;
            return page.nextToken;
        }
        results = new ArrayList<>();
        return null;
    }

    @Override
    public int getPageSize() {
        return 100;
    }
}
