package com.kikitrade.activity.service.facade.goods;

import com.alibaba.fastjson.JSON;
import com.dipbit.dtm.client.domain.stock.constant.StockConstant;
import com.kikitrade.activity.dal.tablestore.builder.GoodsBuilder;
import com.kikitrade.activity.dal.tablestore.builder.impl.GoodsStockBuilder;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.activity.dal.tablestore.model.GoodsStock;
import com.kikitrade.activity.facade.goods.DubboGoodsFacadeTriple;
import com.kikitrade.activity.facade.goods.GoodsDTO;
import com.kikitrade.activity.facade.goods.GoodsFacade;
import com.kikitrade.activity.facade.goods.GoodsSaveResponse;
import com.kikitrade.activity.model.util.TimeUtil;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.config.annotation.DubboService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@DubboService(interfaceClass = GoodsFacade.class)
public class GoodsFacadeImpl extends DubboGoodsFacadeTriple.GoodsFacadeImplBase {

    @Resource
    private GoodsStockBuilder goodsStockBuilder;
    @Resource
    private GoodsBuilder goodsBuilder;

    @Override
    public void save(GoodsDTO request, StreamObserver<GoodsSaveResponse> responseObserver) {
        Goods goods = buildGoods(request);
        Goods obj = goodsBuilder.findById(goods.getGoodsId());
        if(obj != null){
            goodsBuilder.update(goods);
            if(!goods.getStock().equals(obj.getStock())){
                goodsStockBuilder.incrementStock(goods.getGoodsId(), goods.getStock() - obj.getStock());
            }
        }else{
            goodsBuilder.insert(goods);
            goodsStockBuilder.insert(buildGoodsStock(goods));
        }
        GoodsSaveResponse response = GoodsSaveResponse.newBuilder()
                .setId(goods.getGoodsId())
                .setSuccess(true)
                .setMessage("success")
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    private Goods buildGoods(GoodsDTO request){
        Goods goods = new Goods();
        if(StringUtils.isBlank(request.getId())){
            goods.setGoodsId(goodsBuilder.nextId());
        }else{
            goods.setGoodsId(request.getId());
        }
        goods.setName(request.getName());
        goods.setOutId(request.getOutId());
        goods.setDesc(request.getDesc());
        goods.setType(request.getType().name());
        goods.setBlockchain(request.getChain().name());
        goods.setPrice(new BigDecimal(request.getPrice()));
        goods.setCurrency("POINT");
        goods.setCurrencyType("POINT");
        goods.setStock(request.getStock());
        if(StringUtils.isNotBlank(request.getStartTime())){
            goods.setStartTime(TimeUtil.parse(request.getStartTime()).getTime());
        }
        if(StringUtils.isNotBlank(request.getEndTime())){
            goods.setEndTime(TimeUtil.parse(request.getEndTime()).getTime());
        }

        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("list", request.getListImage());
        imageMap.put("detail", request.getDetailImage());
        imageMap.put("share", request.getShareImage());
        goods.setImage(JSON.toJSONString(imageMap));

        goods.setLabelName(request.getLabelName());
        goods.setLabelColor(request.getLabelColor());
        goods.setStatus(request.getStatus().getNumber());

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("taskId", request.getPreQuestId());
        goods.setParam(JSON.toJSONString(paramMap));

        goods.setSaasId(request.getSaasId());
        return goods;
    }

    private GoodsStock buildGoodsStock(Goods goods){
        GoodsStock goodsStock = new GoodsStock();
        goodsStock.setSku(goods.getGoodsId());
        goodsStock.setOrderId(goods.getGoodsId());
        goodsStock.setType(StockConstant.Type.stock.name());
        goodsStock.setChannel("default");
        goodsStock.setAvailable(new BigDecimal(goods.getStock()));
        goodsStock.setSaasId(goods.getSaasId());
        goodsStock.setCreated(new Date());
        goodsStock.setModified(new Date());
        return goodsStock;
    }
}
