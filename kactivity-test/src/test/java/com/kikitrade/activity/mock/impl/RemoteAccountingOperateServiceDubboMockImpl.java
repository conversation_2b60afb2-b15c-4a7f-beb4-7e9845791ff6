package com.kikitrade.activity.mock.impl;

import com.kikitrade.accounting.api.RemoteAccountingOperateService;
import com.kikitrade.accounting.api.model.account.AccountException;
import com.kikitrade.accounting.api.model.account.request.AccountFreezeRequest;
import com.kikitrade.accounting.api.model.account.request.AccountTransferRequest;
import com.kikitrade.accounting.api.model.account.request.AccountUnfreezeRequest;
import com.kikitrade.accounting.api.model.account.response.AccountOperateResponse;
import com.kikitrade.accounting.api.model.account.response.AccountTransferResponse;
import com.kikitrade.accounting.api.model.account.response.AccountingResponseCode;
import com.kikitrade.activity.mock.RemoteAccountingOperateServiceDubboMock;
import com.kikitrade.activity.op.BusinessOP;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.util.List;

import static com.kikitrade.activity.op.BusinessOP.accountTransferRequestList;

@Component
public class RemoteAccountingOperateServiceDubboMockImpl implements RemoteAccountingOperateServiceDubboMock {
    @Resource
    RemoteAccountingOperateService remoteAccountingOperateService;

    @Override
    public void transfer() throws AccountException {
        Mockito.doAnswer(c -> {
            AccountTransferRequest transfer = c.getArgument(0);
            accountTransferRequestList.add(transfer);
            AccountTransferResponse response = AccountTransferResponse.builder().build();
            response.setCode(AccountingResponseCode.SUCCESS);
            return response;
        }).when(remoteAccountingOperateService).transfer(Mockito.any(AccountTransferRequest.class));
    }

    @Override
    public void transferErr() throws AccountException {
        Mockito.doAnswer(c -> {
            throw new RuntimeException("-----remoteAccountingOperateService.transfer.ERR");
        }).when(remoteAccountingOperateService).transfer(Mockito.any(AccountTransferRequest.class));

    }

    @Override
    public void freeze() throws AccountException {
        Mockito.doAnswer(c -> {
            AccountFreezeRequest request = c.getArgument(0);
            BusinessOP.accountFreezeRequest = c.getArgument(0);
            BusinessOP.accountFreezeRequestList.add(request);
            return AccountOperateResponse.builder()
                    .code(AccountingResponseCode.SUCCESS)
                    .build();
        }).when(remoteAccountingOperateService).freeze(Mockito.any(AccountFreezeRequest.class));
    }

    @Override
    public void freezeError() throws AccountException {
        Mockito.doAnswer(c -> {
            throw new RuntimeException("remoteAccountingOperateService.freeze error...");
        }).when(remoteAccountingOperateService).freeze(Mockito.any(AccountFreezeRequest.class));
    }

    @Override
    public void unfreeze() throws AccountException {
        Mockito.doAnswer(c -> {
            AccountUnfreezeRequest operation = c.getArgument(0);
            BusinessOP.accountUnfreezeRequestList.add(operation);
            return AccountOperateResponse.builder()
                    .code(AccountingResponseCode.SUCCESS)
                    .build();
        }).when(remoteAccountingOperateService).unfreeze(Mockito.any(AccountUnfreezeRequest.class));


    }

    @Override
    public void unfreezeError() throws AccountException {

        Mockito.doAnswer(c -> {
            throw new RuntimeException("remoteAccountingOperateService.unfreeze error...");
        }).when(remoteAccountingOperateService).unfreeze(Mockito.any(AccountUnfreezeRequest.class));

    }

}
