package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 投放统计表
 */
@Table(name = "precision_metrics")
@Data
public class PrecisionMetrics implements Serializable {

    @PartitionKey(name = "uid", value = 0)
    private String uid; // quests 用户id
    @PartitionKey(name = "cycle", value = 1)
    private String cycle; // 统计周期
    @PartitionKey(name = "type", value = 2)
    private String type; // 分成的池子
    @PartitionKey(name = "pool_id", value = 3)
    private String poolId; // 投放id

    @Column(name = "author_username")
    private String authorUsername;

    @Column(name = "metrics", type = Column.Type.DOUBLE)
    private double metrics; // 当前用户周期内所有曝光/打分/打分条数和

    @Column(name = "total_metrics", type = Column.Type.DOUBLE)
    private double totalMetrics; // 周期内所有用户曝光/打分/打分条数和

    @Column(name = "rate", type = Column.Type.DOUBLE)
    private double rate; // 获得aura的占比

    @Column(name = "saas_id")
    private String saasId;

    @Column(name = "business_id")
    private String businessId;
}
