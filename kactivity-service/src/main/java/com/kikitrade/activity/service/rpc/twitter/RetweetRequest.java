package com.kikitrade.activity.service.rpc.twitter;

import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.twitter.clientlib.TwitterCredentialsOAuth2;
import com.twitter.clientlib.api.TwitterApi;
import com.twitter.clientlib.model.Tweet;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 19:38
 */
@Data
@Slf4j
public class RetweetRequest extends AccessToken {

    private TwitterProperties twitterProperties;
    private String targetUserName;
    private String tweetId;
    private String twitterName;
    private OffsetDateTime startTime;
    private OffsetDateTime endTime;


    public RetweetRequest(){}

    public RetweetRequest(TwitterProperties twitterProperties, AccessToken accessToken){
        super(accessToken);
        this.twitterProperties = twitterProperties;
    }

    public List<Tweet> execute() {
        try{
            String query = null;
            if(twitterName != null){
                query = String.format("from:%s is:retweet retweets_of:%s", this.targetUserName, this.twitterName);
            }else if(tweetId != null){
                query = String.format("from:%s is:retweet retweets_of_tweet_id:%s", this.targetUserName, this.tweetId);
            }
            log.info("verifyRetweet request:{}", query);
            return getApi(this.accessToken, this.refreshToken, this.twitterProperties).tweets()
                    .tweetsRecentSearch(query)
                    .startTime(this.startTime)
                    .endTime(this.endTime)
                    .execute().getData();
        }catch (Exception ex){
            log.error("verifyRetweet error:{},{}", this.targetUserName, this.tweetId, ex);
            return null;
        }
    }

    public RetweetRequest buildRetweetNameAndTweet(String userName, String tweetId, String twitterName, OffsetDateTime startTime, OffsetDateTime endTime){
        this.targetUserName = userName;
        this.tweetId = tweetId;
        this.twitterName = twitterName;
        this.startTime = startTime;
        this.endTime = endTime;
        return this;
    }
}
