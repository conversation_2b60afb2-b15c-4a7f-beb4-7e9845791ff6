package com.kikitrade.activity.util;

import java.lang.reflect.Field;
import java.util.Map;

import static java.lang.System.getenv;

public class SystemUtils {
    private static Map<String, String> getFieldValue(Class<?> klass,
                                                     Object object, String name)
            throws NoSuchFieldException, IllegalAccessException {
        Field field = klass.getDeclaredField(name);
        field.setAccessible(true);
        return (Map<String, String>) field.get(object);
    }

    private static Map<String, String> getEditableMapOfVariables() {
        Class<?> classOfMap = getenv().getClass();
        try {
            return getFieldValue(classOfMap, getenv(), "m");
        } catch (IllegalAccessException e) {
            throw new RuntimeException("System Rules cannot access the field"
                    + " 'm' of the map System.getenv().", e);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException("System Rules expects System.getenv() to"
                    + " have a field 'm' but it has not.", e);
        }
    }

    private static void set(Map<String, String> variables, String name, String value) {
        if (variables != null) //theCaseInsensitiveEnvironment may be null
            if (value == null)
                variables.remove(name);
            else
                variables.put(name, value);
    }

    // 设置系统变量
    public static void set(String name, String value) {
        set(getEditableMapOfVariables(), name, value);
    }

}
