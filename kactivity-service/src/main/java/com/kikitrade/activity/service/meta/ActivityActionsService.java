package com.kikitrade.activity.service.meta;

import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.dal.mysql.model.ActivityActions;
 

public interface ActivityActionsService {

    JsonResult save(ActivityActions activityAction)  throws Exception;

    JsonResult delete(Integer id)  throws Exception;

    JsonResult insert(ActivityActions activityAction)  throws Exception;

    JsonResult update(ActivityActions activityAction)  throws Exception;

    JsonResult findById(Integer id);

    JsonResult findByType(Integer id);

    JsonResult findAll(Integer offset, Integer limit);

}
