package com.kikitrade.activity.service.common.strategy.discord;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4 19:44
 */
@Component
public class DiscordSkipStrategyService implements DiscordSaasStrategyService{

    @Override
    public String strategy() {
        return SaasStrategyConstant.DiscordStrategyEnum.SKIP.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {

    }
}
