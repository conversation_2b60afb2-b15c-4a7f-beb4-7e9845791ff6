package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteBlockBusinessServiceDubboMock;
import com.kikitrade.kcustomer.api.model.block.SecurityVerifyRequest;
import com.kikitrade.kcustomer.api.model.block.SecurityVerifyResponse;
import com.kikitrade.kcustomer.api.service.RemoteBlockBusinessService;
import jakarta.annotation.Resource;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

@Component
public class RemoteBlockBusinessServiceDubboMockImpl implements RemoteBlockBusinessServiceDubboMock {
    @Resource
    RemoteBlockBusinessService remoteBlockBusinessService;

    @Override
    public void verify() {
        Mockito.doAnswer(c -> {
            SecurityVerifyRequest securityVerifyRequest = c.getArgument(0);
            return SecurityVerifyResponse.success();
        }).when(remoteBlockBusinessService).verify(Mockito.any(SecurityVerifyRequest.class));
    }

    @Override
    public void verify_failed() {
        Mockito.doAnswer(c -> {
            SecurityVerifyRequest securityVerifyRequest = c.getArgument(0);
            return SecurityVerifyResponse.forbidden();
        }).when(remoteBlockBusinessService).verify(Mockito.any(SecurityVerifyRequest.class));
    }
}
