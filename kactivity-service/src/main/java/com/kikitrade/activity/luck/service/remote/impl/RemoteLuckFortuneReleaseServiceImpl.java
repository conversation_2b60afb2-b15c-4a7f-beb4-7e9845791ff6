package com.kikitrade.activity.luck.service.remote.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReleaseItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.service.business.*;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityJoinItemService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.kcustomer.api.service.RemoteVerifyService;
import com.kikitrade.activity.luck.api.RemoteLuckFortuneReleaseService;
import com.kikitrade.activity.luck.api.exception.LuckFortuneException;
import com.kikitrade.activity.luck.api.exception.LuckFortuneExceptionType;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneCheckDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReleaseDTO;
import com.kikitrade.activity.luck.api.model.response.AirdropRuleVO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneHistoryReleaseVO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceive;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneRelease;
import com.kikitrade.market.client.CurrencyClient;
import com.kikitrade.market.client.TickerClient;
import com.kikitrade.market.client.service.impl.ClientExchangeService;
import com.kikitrade.market.common.model.CurrencyDTO;
import com.kikitrade.market.common.model.TickerDTO;
import com.kikitrade.market.common.model.constant.CoinCodeConstant;
import com.kikitrade.trade.api.RemoteTradingService;
import com.kikitrade.trade.api.model.request.TransferBalanceRequest;
import com.kikitrade.trade.api.model.response.TransferBalanceResponse;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.math3.fraction.BigFractionFormat;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import static com.kikitrade.activity.luck.api.exception.LuckFortuneExceptionType.LUCK_FORTUNE_CURRENCY_INVALID;

/**
 *
 */
@DubboService
@Slf4j
public class RemoteLuckFortuneReleaseServiceImpl implements RemoteLuckFortuneReleaseService {

    @Resource
    private RuleService ruleService;
    @Resource
    private RedisService redisService;
    @Resource
    private TickerClient tickerClient;
    @Resource
    private ReleaseService releaseService;
    @Resource
    private ReleaseTccService releaseTccService;
    @Resource
    private ReceiveService receiveService;
    @Resource
    private CurrencyClient currencyClient;
    @Resource
    private SeqGeneraterService seqGeneraterService;
    @Resource
    private LuckFortuneReleaseItemBuilder luckFortuneReleaseItemBuilder;
    @DubboReference
    private RemoteVerifyService remoteVerifyService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private CustomerService customerService;
    @DubboReference
    private RemoteTradingService remoteTradingService;
    @Resource
    private ActivityJoinItemService activityJoinItemService;
    @Resource
    private AirDropBaseService airDropBaseService;

    private static final String toCurrency = "USD";
    /**
     * 查询规则
     *
     * @param customer
     * @return
     */
    @Override
    public Result<AirdropRuleVO> rule(ActivityCustomerDTO customer) throws Exception {
        LuckFortuneRule rule = ruleService.findObject(customer.getKycLevel(), customerService.getKolType(customer.getId()));
        if(rule == null){
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RELEASE_PERMISSION);
        }
        return Result.of(Result.ResultCode.SUCCESS,"",
                AirdropRuleVO.builder()
                        .releaseNumMax(rule.getReleaseNumMax())
                        .releaseMin(rule.getReleaseMin())
                        .releaseMax(rule.getReleaseMax())
                        .receiveMin(rule.getReceiveMin())
                        .receiveMax(rule.getReceiveMax())
                        .build());
    }

    /**
     * 发放的空投简单详情
     *
     * @param paramId
     * @param customer
     * @return
     * @throws Exception
     */
    @Override
    public Result<LuckFortuneRelease> findReleaseAirdropSimpleById(String paramId , ActivityCustomerDTO customer) throws Exception {
        log.info("findReleaseAirdropSimpleById:{}", paramId);
        String id = releaseService.decryptReleaseId(paramId);
        if(StringUtils.isBlank(id)){
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_INVALID);
        }
        String releaseItemStr = redisService.get(RedisKeyConst.LUCK_FORTUNE_RELEASE_INFO_KEY.getKey(id));
        if(StringUtils.isNotBlank(releaseItemStr)){
            LuckFortuneReleaseItem item = JSON.parseObject(releaseItemStr, LuckFortuneReleaseItem.class);
            return buildSimpleReleaseInfo(item);
        }
        return buildSimpleReleaseInfo(releaseService.findById(id));
    }

    @Override
    public Result<LuckFortuneRelease> findReleaseAirdropById(String paramId, String refer, ActivityCustomerDTO customer) throws Exception {
        String id = releaseService.decryptReleaseId(paramId);
        boolean success = releaseService.checkLuckFortuneToken(id, paramId);
        if(!success){
            //红包id与token不匹配
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_INVALID);
        }
        LuckFortuneReleaseItem item = releaseService.findById(id);
        List<LuckFortuneReceive> receiveList = receiveService.findItemByReleaseId(item, refer);
        return Result.of(Result.ResultCode.SUCCESS, "", LuckFortuneRelease.builder()
                .id(paramId)
                .assignType(item.getAssignType())
                .num(item.getNum())
                .amount(item.getAmount())
                .currency(item.getCurrency())
                .cover(item.getCover())
                .avatar(item.getAvatar())
                .greeting(item.getGreeting())
                .expiredTime(StringUtils.isNotBlank(item.getExpiredTime()) ? TimeUtil.parse(item.getExpiredTime()).getTime() : null)
                .nickName(item.getNickName())
                .status(item.getStatus())
                .receiveNum(CollectionUtils.isEmpty(receiveList) ? 0 : receiveList.size())
                .receiveList(receiveList)
                .tag(activityJoinItemService.isContainsActivityCode(paramId).getRight())
                .build());
    }

    /**
     * 发放红包
     * @param luckFortuneReleaseDTO
     * @return
     */
    @Override
    @LogRecord(success = "【{{#luckFortuneReleaseDTO.customer.id}}】success_release【{{#luckFortuneReleaseDTO.num}}】:【{{#luckFortuneReleaseDTO.currency}}】", type = "AIRDROP", subType = "RELEASE", bizNo = "{{_ret.data.id}}", successCondition = "{{#_ret.success}}"
            , fail = "【{{#luckFortuneReleaseDTO.customer.id}}】fail_release【{{#luckFortuneReleaseDTO.num}}】:【{{#luckFortuneReleaseDTO.currency}}】result：{{#_errorMsg}}")
    public Result<LuckFortuneRelease> releaseLuckFortune(LuckFortuneReleaseDTO luckFortuneReleaseDTO) throws Exception {
        log.info("luckFortuneReleaseDTO:{}", luckFortuneReleaseDTO);
        //发放的币种精度
        Integer keepDecimalForCoin = getKeepDecimalForCoin(luckFortuneReleaseDTO.getCurrency());
        //空投usd价值
        BigDecimal price = getPrice(luckFortuneReleaseDTO.getAmount(), luckFortuneReleaseDTO.getCurrency(), CoinCodeConstant.USD.getCode(), 2);

        luckFortuneReleaseDTO.setKeepDecimalForCoin(keepDecimalForCoin);
        luckFortuneReleaseDTO.setUsdCost(price);

        LuckFortuneRule rule = ruleService.findObject(luckFortuneReleaseDTO.getCustomer().getKycLevel(), customerService.getKolType(luckFortuneReleaseDTO.getCustomer().getId()));
        //校验发放的金额及数量
        check(price, rule, luckFortuneReleaseDTO);
        //钱包余额校验
        TransferBalanceRequest transferBalanceRequest = new TransferBalanceRequest();
        transferBalanceRequest.setCurrency(luckFortuneReleaseDTO.getCurrency());
        transferBalanceRequest.setCustomerId(luckFortuneReleaseDTO.getCustomer().getId());
        transferBalanceRequest.setSaasId(kactivityProperties.getSaasId());
        TransferBalanceResponse transferBalanceResponse = remoteTradingService.transferBalance(transferBalanceRequest);
        if(transferBalanceResponse == null || transferBalanceResponse.getTransferBalanceDTO() == null
            || Math.min(transferBalanceResponse.getTransferBalanceDTO().getAvailable() == null ? 0 : transferBalanceResponse.getTransferBalanceDTO().getAvailable().doubleValue(),
                transferBalanceResponse.getTransferBalanceDTO().getTransferAvailable() == null ? 0 : transferBalanceResponse.getTransferBalanceDTO().getTransferAvailable().doubleValue()) < luckFortuneReleaseDTO.getAmount().doubleValue()){
            throw new LuckFortuneException(LuckFortuneExceptionType.CUSTOMER_INSUFFICIENT_BALANCE);
        }
        String id = seqGeneraterService.next(CustomerSeqRuleBuilder.instance(luckFortuneReleaseItemBuilder.getTableName()));
        try{
            Result<LuckFortuneReleaseItem> result = releaseTccService.tryRelease(rule, luckFortuneReleaseDTO, luckFortuneReleaseDTO.getCustomer(), id);
            Result<LuckFortuneRelease> releaseResult = buildSimpleReleaseInfo(result.getData());
            return new Result(result.isSuccess(), result.getMessage(), releaseResult.getData());
        }catch (Exception ex){
            log.error("airdrop release fail, request:{}", luckFortuneReleaseDTO , ex);
            throw ex;
        }
    }

    /**
     * 历史发放的红包，时间倒叙
     * @param customer
     * @return
     */
    @Override
    public LuckFortuneHistoryReleaseVO historyReleaseLuckFortune(ActivityCustomerDTO customer, String type, Long startTime, Long endTime, int offset, int limit) {
        Page<LuckFortuneRelease> page = releaseService.historyRelease(customer.getId(), type, startTime == null || startTime == 0 ? null : TimeUtil.getUtcTime(TimeUtil.parseUnittime(startTime), TimeUtil.YYYYMMDDHHMMSS),
                endTime == null || endTime == 0  ? null : TimeUtil.getUtcTime(TimeUtil.parseUnittime(endTime), TimeUtil.YYYYMMDDHHMMSS), offset, limit);
        if(page == null || CollectionUtils.isEmpty(page.getRows())){
            return null;
        }
        long num = releaseService.countRelease(customer.getId());
        BigDecimal amount = releaseService.sumAmountRelease(customer.getId());
        return new LuckFortuneHistoryReleaseVO(offset, limit, page.getTotalCount(), num, amount.setScale(2, BigDecimal.ROUND_DOWN), page.getRows());
    }

    /**
     * 校验发放的红包数量
     * @param luckFortuneCheckDTO
     * @return
     */
    private Result<String> checkReleaseNum(LuckFortuneRule rule, LuckFortuneCheckDTO luckFortuneCheckDTO) throws Exception {
        log.info("checkReleaseNum luckFortuneCheckDTO {}", luckFortuneCheckDTO);
        if(rule == null){
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RELEASE_PERMISSION);
        }
        if(luckFortuneCheckDTO.getNum() > rule.getReleaseNumMax() || luckFortuneCheckDTO.getNum() < 1){
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_NUM_INVALID, String.format("%s~%s", 1, rule.getReleaseNumMax()));
        }
        return Result.of(Result.ResultCode.SUCCESS);
    }

    /**
     * 校验红包金额
     * @param luckFortuneCheckDTO
     * @return
     */
    private Result<String> checkReleaseAmount(LuckFortuneRule rule, LuckFortuneCheckDTO luckFortuneCheckDTO) throws Exception {
        log.info("checkReleaseAmount luckFortuneCheckDTO {}", luckFortuneCheckDTO);
        if(rule == null){
            //没有发红包权限
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RELEASE_PERMISSION);
        }

        BigDecimal usdAmount = luckFortuneCheckDTO.getUsdCost();
        if(ActivityConstant.AssignType.FORTUNE.isEqual(luckFortuneCheckDTO.getAssignType())){
            if(usdAmount.compareTo(rule.getReleaseMin()) < 0 || usdAmount.compareTo(rule.getReleaseMax()) > 0){
                log.info("fortune usdAmount range:{},{},{},{},{}",usdAmount, rule.getReleaseMin(), rule.getReleaseMax(), luckFortuneCheckDTO, rule);
                String message = String.format("%s~%s%s",
                        getPrice(rule.getReleaseMin(), CoinCodeConstant.USD.getCode(), luckFortuneCheckDTO.getCurrency(), luckFortuneCheckDTO.getKeepDecimalForCoin()),
                        getPrice(rule.getReleaseMax(), CoinCodeConstant.USD.getCode(), luckFortuneCheckDTO.getCurrency(), luckFortuneCheckDTO.getKeepDecimalForCoin()),
                        luckFortuneCheckDTO.getCurrency());
                throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_AMOUNT_INVALID, message);
            }
            if(usdAmount.divide(new BigDecimal(luckFortuneCheckDTO.getNum()), BigDecimal.ROUND_DOWN).compareTo(rule.getReceiveMin()) < 0
                    || usdAmount.divide(new BigDecimal(luckFortuneCheckDTO.getNum()), BigDecimal.ROUND_DOWN).compareTo(rule.getReceiveMax()) > 0){
                log.info("fortune usdAmount single range:{},{},{},{},{}",usdAmount, rule.getReceiveMin(), rule.getReceiveMax(), luckFortuneCheckDTO, rule);
                String message = String.format("%s~%s%s",
                        getPrice(rule.getReceiveMin(), CoinCodeConstant.USD.getCode(), luckFortuneCheckDTO.getCurrency(), luckFortuneCheckDTO.getKeepDecimalForCoin()),
                        getPrice(rule.getReceiveMax(), CoinCodeConstant.USD.getCode(), luckFortuneCheckDTO.getCurrency(), luckFortuneCheckDTO.getKeepDecimalForCoin()),
                        luckFortuneCheckDTO.getCurrency());
                throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_SINGLE_AMOUNT_INVALID, message);
            }
            return Result.of(Result.ResultCode.SUCCESS);
        }
        if(ActivityConstant.AssignType.GENERAL.isEqual(luckFortuneCheckDTO.getAssignType())){
            if(usdAmount.multiply(new BigDecimal(luckFortuneCheckDTO.getNum())).compareTo(rule.getReleaseMin()) < 0
                    || usdAmount.multiply(new BigDecimal(luckFortuneCheckDTO.getNum())).compareTo(rule.getReleaseMax()) > 0){
                log.info("general usdAmount range:{},{},{},{},{}",usdAmount, rule.getReleaseMin(), rule.getReleaseMax(), luckFortuneCheckDTO, rule);
                String message = String.format("%s~%s%s",
                        getPrice(rule.getReleaseMin(), CoinCodeConstant.USD.getCode(), luckFortuneCheckDTO.getCurrency(), luckFortuneCheckDTO.getKeepDecimalForCoin()),
                        getPrice(rule.getReleaseMax(), CoinCodeConstant.USD.getCode(), luckFortuneCheckDTO.getCurrency(), luckFortuneCheckDTO.getKeepDecimalForCoin()),
                        luckFortuneCheckDTO.getCurrency());
                throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_AMOUNT_INVALID, message);
            }
            if(usdAmount.compareTo(rule.getReceiveMin()) < 0 || usdAmount.compareTo(rule.getReceiveMax()) > 0){
                log.info("general usdAmount single range:{},{},{},{},{}",usdAmount, rule.getReceiveMin(), rule.getReceiveMax(), luckFortuneCheckDTO, rule);
                String message = String.format("%s~%s%s",
                        getPrice(rule.getReceiveMin(), CoinCodeConstant.USD.getCode(), luckFortuneCheckDTO.getCurrency(), luckFortuneCheckDTO.getKeepDecimalForCoin()),
                getPrice(rule.getReceiveMax(), CoinCodeConstant.USD.getCode(), luckFortuneCheckDTO.getCurrency(), luckFortuneCheckDTO.getKeepDecimalForCoin()),
                luckFortuneCheckDTO.getCurrency());
                throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_SINGLE_AMOUNT_INVALID, message);
            }
            return Result.of(Result.ResultCode.SUCCESS);
        }
        return Result.of(Result.ResultCode.SYSTEM_ERROR);
    }

    private LuckFortuneCheckDTO toCheckDTO(LuckFortuneReleaseDTO luckFortuneReleaseDTO){
        LuckFortuneCheckDTO checkDTO = new LuckFortuneCheckDTO();
        checkDTO.setCustomer(luckFortuneReleaseDTO.getCustomer());
        checkDTO.setAmount(luckFortuneReleaseDTO.getAmount());
        checkDTO.setNum(luckFortuneReleaseDTO.getNum());
        checkDTO.setCurrency(luckFortuneReleaseDTO.getCurrency());
        checkDTO.setAssignType(luckFortuneReleaseDTO.getAssignType());
        checkDTO.setUsdCost(luckFortuneReleaseDTO.getUsdCost());
        checkDTO.setKeepDecimalForCoin(luckFortuneReleaseDTO.getKeepDecimalForCoin());
        return checkDTO;
    }

    private  Result<LuckFortuneRelease> buildSimpleReleaseInfo(LuckFortuneReleaseItem item){
        String code = StringUtils.isNotBlank(item.getReleaseCode()) ? item.getReleaseCode() : releaseService.encryptReleaseId(item.getId());
        Pair<Boolean, String> pair = activityJoinItemService.isContainsActivityCode(code);
        return Result.of(Result.ResultCode.SUCCESS, "", LuckFortuneRelease.builder()
                .id(code)
                .assignType(item.getAssignType())
                .num(item.getNum())
                .amount(item.getAmount())
                .currency(item.getCurrency())
                .cover(item.getCover())
                .avatar(item.getAvatar())
                .greeting(item.getGreeting())
                .expiredTime(StringUtils.isNotBlank(item.getExpiredTime()) ? TimeUtil.parse(item.getExpiredTime()).getTime() : null)
                .nickName(item.getNickName())
                .tag(pair.getRight())
                .build());
    }

    private void check(BigDecimal price, LuckFortuneRule rule, LuckFortuneReleaseDTO luckFortuneReleaseDTO) throws Exception {
        log.info("usd price:{}", price);
        LuckFortuneCheckDTO checkDTO = toCheckDTO(luckFortuneReleaseDTO);
        checkReleaseNum(rule, checkDTO);
        checkReleaseAmount(rule, checkDTO);
    }

    /**
     * 返回usd价值
     * @param amount
     * @param fromCurrency
     * @return
     */
    private BigDecimal getPrice(BigDecimal amount, String fromCurrency, String toCurrency, Integer keepDecimalForCoin){
        return airDropBaseService.getPrice(amount, fromCurrency, toCurrency, keepDecimalForCoin);
//        if(fromCurrency.equals(toCurrency)){
//            return amount;
//        }
//        TickerDTO tickerDTO = tickerClient.get(String.format("%s_%s", fromCurrency, toCurrency));
//        if(tickerDTO != null){
//            return amount.multiply(tickerDTO.getPrice()).setScale(keepDecimalForCoin, BigDecimal.ROUND_DOWN);
//        }
//        return clientExchangeService.exchangeByPrecision(amount, fromCurrency, toCurrency, keepDecimalForCoin, RoundingMode.DOWN);
    }

    private Integer getKeepDecimalForCoin(String currency){
        return airDropBaseService.getKeepDecimalForCoin(currency);
//        CurrencyDTO currencyDTO = currencyClient.get(currency);
//        return currencyDTO != null ? currencyDTO.getKeepDecimalForCoin() : 8;
    }
}
