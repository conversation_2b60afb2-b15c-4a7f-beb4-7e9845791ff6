package com.kikitrade.activity.service.common.strategy.wallet;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/22 18:40
 * @description: Wallet跳过策略服务
 */
@Component
@Slf4j
public class WalletSkipStrategyService implements WalletSaasStrategyService{

    @Override
    public String strategy() {
        return SaasStrategyConstant.WalletStrategyEnum.SKIP.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        log.info("WalletSkipStrategyService execute, request: {}", request);
        // 跳过策略，不执行任何操作
    }
}
