package com.kikitrade.activity.api.model;

import java.io.Serializable;

public class PrecisionPoolDTO implements Serializable {

    private String id; // 投放id

    private String channel; // 投放渠道

    private String task; // 关联任务/活动

    private String owner; // 所属者

    private double distributionAmount; // distribution投放金额

    private double contributionAiAmount; // contribution_ai投放金额

    private double contributionHumanAmount; // contribution_human投放金额

    private double contributionDeepHumanAmount; // contribution_deep_human投放金额

    private double distributionMaxAmount; // distribution单用户最大金额

    private double contributionAiMaxAmount; // contribution_ai单用户最大金额

    private double contributionHumanMaxAmount; // contribution_human单用户最大金额

    private double contributionDeepHumanMaxAmount; // contribution_deep_human单用户最大金额

    private double precisionTrackCheckAiPoint; //保存打分记录前校验AI的分数

    private double precisionTrackCheckFollowsCount; //保存打分记录前校验粉丝数

    private double precisionTrackCheckAsset; //保存打分记录前校验用户资产
}
