package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.mysql.dao.ActivityEntityDao;
import com.kikitrade.activity.dal.mysql.dao.ActivityEntityTestDao;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import static org.mockito.ArgumentMatchers.anyString;

@Component
public class ActivityEntityDaoMockImpl implements ActivityEntityDaoMock {

    @Resource
    ActivityEntityDao activityEntityDao;

    @Resource
    ActivityEntityTestDao activityEntityTestDao;

    @Override
    public void selectByPrimaryKey() {

//        Mockito.doAnswer(c -> {
//            String key  = c.getArgument(0);
//            return activityEntityTestDao.selectByPrimaryKey(key);
//        }).when(activityEntityDao).selectByPrimaryKey(anyString());


    }
}
