//package com.kikitrade.activity.mock.impl;
//
//import com.kikitrade.activity.mock.RewardSetsTccServiceImplMock;
//import com.kikitrade.activity.op.BusinessOP;
//import com.kikitrade.activity.service.reward.impl.RewardSetsTccServiceImpl;
//import com.kikitrade.activity.service.reward.model.RewardRequest;
//import jakarta.annotation.Resource;
//import org.mockito.Mockito;
//import org.springframework.stereotype.Component;
//
//@Component
//public class RewardSetsTccServiceImplMockImpl implements RewardSetsTccServiceImplMock {
//    @Resource
//    RewardSetsTccServiceImpl rewardSetsTccServiceImpl;
//
//    @Override
//    public void tryReward() throws Exception {
//        Mockito.doAnswer(c ->{
//            RewardRequest request = c.getArgument(0);
//            BusinessOP.rewardRequestList.add(request);
//            return null;
//        }).when(rewardSetsTccServiceImpl).tryReward(Mockito.any(RewardRequest.class));
//    }
//}
