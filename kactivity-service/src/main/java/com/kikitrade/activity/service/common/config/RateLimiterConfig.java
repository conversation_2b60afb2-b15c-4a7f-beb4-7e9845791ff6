package com.kikitrade.activity.service.common.config;

import com.google.common.util.concurrent.RateLimiter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
public class RateLimiterConfig {

    @Autowired
    private KactivityProperties kactivityProperties;

    private Map<String, RateLimiter> limiterMap = new ConcurrentHashMap<>();

    public void acquire(String beanName) {
        if (limiterMap.size() == 0) {
            init();
        }
        RateLimiter rateLimiter = limiterMap.getOrDefault(beanName, limiterMap.get("default"));
        Map<String, String> limiter = kactivityProperties.getLimiter();
        double lastRateLimiter = Double.parseDouble(limiter.getOrDefault(beanName, limiter.get("default")));
        if(rateLimiter.getRate() != lastRateLimiter){
            rateLimiter.setRate(lastRateLimiter);
        }
        rateLimiter.acquire();
    }

    public void init() {
        Map<String, String> limiter = kactivityProperties.getLimiter();
        for (Map.Entry<String, String> entry : limiter.entrySet()) {
            limiterMap.put(entry.getKey(), RateLimiter.create(Double.parseDouble(entry.getValue())));
        }
    }
}
