package com.kikitrade.activity.service.common.strategy.email;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/22 18:40
 * @description: Email跳过策略服务
 */
@Component
@Slf4j
public class EmailSkipStrategyService implements EmailSaasStrategyService{

    @Override
    public String strategy() {
        return SaasStrategyConstant.EmailStrategyEnum.SKIP.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        log.info("EmailSkipStrategyService execute, request: {}", request);
        // 跳过策略，不执行任何操作
    }
}
