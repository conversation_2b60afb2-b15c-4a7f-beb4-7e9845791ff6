package com.kikitrade.activity.mock.impl;

import com.aliyun.odps.Column;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.data.ArrayRecord;
import com.aliyun.odps.data.Record;
import com.kikitrade.activity.mock.OdpsReaderMock;
import com.kikitrade.activity.service.flow.step.reader.OdpsReader;
import org.mockito.Mockito;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class OdpsReaderMockImpl implements OdpsReaderMock {
    @Resource
    OdpsReader odpsReader;


    @Override
    public void open() {

        Mockito.doAnswer(c -> {
            System.out.println("open");


            return null;
        }).when(odpsReader).open(Mockito.any(ExecutionContext.class));

    }

    @Override
    public void read() throws Exception {
        Mockito.doAnswer(c -> {
            System.out.println("read");

            Column column = new Column("test", OdpsType.TIMESTAMP);

            Column[] columns = new Column[]{column};

            Record record = new ArrayRecord(columns);

            return record;

        }).when(odpsReader).read();

/*        Mockito.doCallRealMethod().when(odpsReader).read();*/

    }

    @Override
    public void close() {
        Mockito.doAnswer(c -> {
            System.out.println("close");


            return null;
        }).when(odpsReader).close();

    }
}
