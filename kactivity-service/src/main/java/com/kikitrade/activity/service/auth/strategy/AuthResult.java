package com.kikitrade.activity.service.auth.strategy;

import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import lombok.Builder;
import lombok.Data;

/**
 * 认证结果
 * 包含认证成功后的令牌和用户信息
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@Data
@Builder
public class AuthResult {

    /**
     * 访问令牌
     */
    private Token token;

    /**
     * 社交用户信息
     */
    private SocialUserInfo socialUserInfo;

    /**
     * 是否认证成功
     */
    private boolean success;

    /**
     * 错误信息（认证失败时）
     */
    private String errorMessage;

    /**
     * 创建成功的认证结果
     * @param token 令牌
     * @param socialUserInfo 用户信息
     * @return 认证结果
     */
    public static AuthResult success(Token token, SocialUserInfo socialUserInfo) {
        return AuthResult.builder()
                .token(token)
                .socialUserInfo(socialUserInfo)
                .success(true)
                .build();
    }

    /**
     * 创建失败的认证结果
     * @param errorMessage 错误信息
     * @return 认证结果
     */
    public static AuthResult failure(String errorMessage) {
        return AuthResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
}
