package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityBatchInfo;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityBatchInfoDao extends BaseDao<ActivityBatchInfo, String> {

    int insert(ActivityBatchInfo aa);

    int update(ActivityBatchInfo aa);

    int updateStatus(@Param("tran_date") String tran_date, @Param("activity_id") Integer activity_id, @Param("batch_id") String batch_id, @Param("status") Integer status);

    ActivityBatchInfo findByPrimaryKey(@Param("tran_date") String tran_date, @Param("activity_id") Integer activity_id, @Param("batch_id") String batch_id);

    ActivityBatchInfo findByBatchId(@Param("batch_id") String batch_id);

    ActivityBatchInfo findLastBatchByActivityId(@Param("activity_id") Integer activity_id);

    List<ActivityBatchInfo> findAll(@Param("tran_date") String tran_date, @Param("activity_id") Integer activity_id, @Param("batch_id") String batch_id, @Param("offset") Integer offset, @Param("limit") Integer limit);


}
