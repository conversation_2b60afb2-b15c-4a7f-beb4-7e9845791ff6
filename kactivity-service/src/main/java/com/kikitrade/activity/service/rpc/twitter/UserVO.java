package com.kikitrade.activity.service.rpc.twitter;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/19 14:05
 */
@Data
public class UserVO {
    @SerializedName("id")
    private String id;
    @SerializedName("name")
    private String name;
    @SerializedName("username")
    private String username;
    @SerializedName("connection_status")
    private List<String> connectionStatus;
}
