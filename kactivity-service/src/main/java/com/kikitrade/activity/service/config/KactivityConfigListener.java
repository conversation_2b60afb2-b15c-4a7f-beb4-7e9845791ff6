package com.kikitrade.activity.service.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.listener.AbstractSharedListener;
import com.alibaba.nacos.api.exception.NacosException;
import com.dipbit.dtm.client.domain.stock.constant.StockConstant;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.tablestore.builder.GoodsBuilder;
import com.kikitrade.activity.dal.tablestore.builder.LotteryConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.impl.GoodsStockBuilder;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.activity.dal.tablestore.model.GoodsStock;
import com.kikitrade.activity.dal.tablestore.model.LotteryConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.NoticeService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.lottery.LotteryCommonService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.kcustomer.api.service.RemoteNotificationService;
import com.kikitrade.quota.api.RemoteQuotaConfigService;
import com.kikitrade.quota.api.model.request.QuotaConfigDTO;
import com.kikitrade.quota.api.model.request.QuotaConfigInterval;
import com.kikitrade.quota.common.constant.QuotaConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class KactivityConfigListener{

    private static final String MATERIAL_TEMPLATE_DATA_ID = "kactivity-material-template.json";
    private static final String TEXT_DATA_ID = "kactivity-text.properties";
    private static final String LOTTERY_DATA_ID = "kactivity-lottery.json";
    private static final String SQL_DATA_ID = "kactivity-sql.json";
    private static final String ACTIVITY_TASK = "kactivity-task.json";
    private static final String ACTIVITY_TASK_CODE = "kactivity-task-code.json";
    private static final String GOODS = "goods.json";

    private static final String SAAS_CONFIG = "saas-config.yaml";

    @Resource
    private PropertiesConfigService propertiesConfigService;
    @Resource
    private KactivityLotteryConfig kactivityLotteryConfig;
    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private GoodsBuilder goodsBuilder;
    @Resource
    private GoodsStockBuilder goodsStockBuilder;
    @Resource
    private LotteryCommonService lotteryCommonService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private LotteryConfigBuilder lotteryConfigBuilder;
    @Resource
    private NoticeService noticeService;

    @PostConstruct
    public void init(){
        try {
            addKactivityTaskCodeListener();
            addKactivityTaskListener();
            addLotteryConfig();
            addSaasConfigListener();

        } catch (NacosException e) {
            log.error("nacos addListener exception",e);
        }
    }

    /**
     * 监听物料模版变化
     * @throws NacosException
     */
    private void addSaasConfigListener() throws NacosException{
        String config = propertiesConfigService.getConfigService().getConfigAndSignListener(SAAS_CONFIG, "quests", 1000, new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->saasConfig change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                SaasConfigLoader.load(s2);
            }
        });
        log.info("mes->saasConfig init:{}", config);
        SaasConfigLoader.load(config);
    }

    /**
     * 监听物料模版变化
     * @throws NacosException
     */
    private void addKactivityMaterialTemplateListener() throws NacosException{
        String config = propertiesConfigService.getConfigService().getConfigAndSignListener(MATERIAL_TEMPLATE_DATA_ID, "kactivity", 1000, new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-material change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                KactivityMaterialTemplateConfig.load(s2);
            }
        });
        log.info("mes->kactivity-material init:{}", config);
        KactivityMaterialTemplateConfig.load(config);
    }

    /**
     * 监听文案变化
     * @throws NacosException
     */
    private void addKactivityTextListener() throws NacosException{
        String config = propertiesConfigService.getConfigService().getConfigAndSignListener(TEXT_DATA_ID, "kactivity", 1000, new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-text change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                KactivityTextConfig.load(s2);
            }
        });
        log.info("mes->kactivity-text init:{}", config);
        KactivityTextConfig.load(config);
    }

    /**
     * 监听发奖奖品变化
     * @throws NacosException
     */
    private void addKactivityLotteryListener() throws NacosException{
        propertiesConfigService.getConfigService().getConfigAndSignListener(LOTTERY_DATA_ID, "kactivity", 1000, new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-lottery change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                kactivityLotteryConfig.load(s2);
            }
        });
    }

    /**
     * 自动发奖sql
     * @throws NacosException
     */
    private void addKactivitySqlListener() throws NacosException{
        String config = propertiesConfigService.getConfigService().getConfigAndSignListener(SQL_DATA_ID, "kactivity", 1000, new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-sql change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                KactivitySqlConfig.load(s2);
            }
        });
        log.info("mes->kactivity-sql init, content:{}", config);
        KactivitySqlConfig.load(config);
    }

    /**
     * 任务配置
     * @throws NacosException
     */
    private void addKactivityTaskListener() throws NacosException{
        propertiesConfigService.getConfigService().addListener(ACTIVITY_TASK, "kactivity", new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-task change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                List<TaskConfigDTO> dtoList = new ArrayList<>();
                if(s2.trim().startsWith("[") && s2.trim().endsWith("]")){
                    dtoList = JSON.parseArray(s2, TaskConfigDTO.class);
                }else{
                    dtoList.add(JSON.parseObject(s2, TaskConfigDTO.class));
                }
                for(TaskConfigDTO configDTO : dtoList){
                    taskConfigService.upsert(configDTO, false);
                    if(kactivityProperties.getQuestsDingTalkUrl() != null){
                        if(configDTO.getStatus() == ActivityConstant.CommonStatus.ACTIVE){
                            noticeService.noticeDingTalk("kactivity_task" + configDTO.getTaskId() + TimeUtil.getCurrentUtcTime("yyyyMMddHH"), kactivityProperties.getQuestsDingTalkUrl(), "notice: " + configDTO.getTaskId() + ":" + configDTO.getTitle() + " online");
                        }else{
                            noticeService.noticeDingTalk("kactivity_task" + configDTO.getTaskId() + TimeUtil.getCurrentUtcTime("yyyyMMddHH"), kactivityProperties.getQuestsDingTalkUrl(), "notice: " + configDTO.getTaskId() + ":" + configDTO.getTitle() + " offline");
                        }
                    }
                }
            }
        });
    }

    /**
     * 任务code配置
     * @throws NacosException
     */
    private void addKactivityTaskCodeListener() throws NacosException{
        String config = propertiesConfigService.getConfigService().getConfigAndSignListener(ACTIVITY_TASK_CODE, "kactivity", 1000, new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->kactivity-task-code change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                TaskCodeConfig.load(s2);
            }
        });
        log.info("mes->kactivity-task-code init, content:{}", config);
        TaskCodeConfig.load(config);
    }

    private void addGoodsListener() throws NacosException{
        propertiesConfigService.getConfigService().addListener(GOODS, "kactivity", new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                log.info("mes->goods change, dataId:{}, groupId:{}, content:{}", s, s1, s2);
                List<Goods> goodses = JSON.parseArray(s2, Goods.class);
                for(Goods goods : goodses){
                    Goods obj = goodsBuilder.findById(goods.getGoodsId());
                    if(obj != null){
                        int goodsStock = goods.getStock();
                        goods.setStock(null);
                        goodsBuilder.update(goods);
                        if(goodsStock != obj.getStock()){
                            goodsStockBuilder.incrementStock(goods.getGoodsId(), goodsStock - obj.getStock());
                        }
                    }else{
                        goodsBuilder.insert(goods);
                        goodsStockBuilder.insert(buildGoodsStock(goods));
                    }
                }
            }
        });
    }

    private GoodsStock buildGoodsStock(Goods goods){
        GoodsStock goodsStock = new GoodsStock();
        goodsStock.setSku(goods.getGoodsId());
        goodsStock.setOrderId(goods.getGoodsId());
        goodsStock.setType(StockConstant.Type.stock.name());
        goodsStock.setChannel("default");
        goodsStock.setAvailable(new BigDecimal(goods.getStock()));
        goodsStock.setSaasId(goods.getSaasId());
        goodsStock.setCreated(new Date());
        goodsStock.setModified(new Date());
        return goodsStock;
    }

    public void addLotteryConfig() throws NacosException{
        String config = propertiesConfigService.getConfigService().getConfigAndSignListener(LOTTERY_DATA_ID, "kactivity", 1000, new AbstractSharedListener() {
            @Override
            public void innerReceive(String s, String s1, String s2) {
                loadLotteryConfig(s2);
            }
        });
        loadLotteryConfig(config);
    }

    private void loadLotteryConfig(String s2){
        List<LotteryConfig> configs = JSON.parseArray(s2, LotteryConfig.class);
        for(LotteryConfig config : configs){
            lotteryConfigBuilder.update(config);
        }
    }
}
