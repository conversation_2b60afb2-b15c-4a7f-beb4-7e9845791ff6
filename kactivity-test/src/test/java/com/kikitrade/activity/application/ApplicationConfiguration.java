package com.kikitrade.activity.application;

import com.wix.mysql.EmbeddedMysql;
import com.wix.mysql.config.Charset;
import com.wix.mysql.config.MysqldConfig;
import com.wix.mysql.config.SchemaConfig;
import jakarta.annotation.PreDestroy;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.TimeZone;

import static com.wix.mysql.EmbeddedMysql.anEmbeddedMysql;
import static com.wix.mysql.distribution.Version.v5_7_latest;

@Component
public class ApplicationConfiguration implements BeanPostProcessor {
    public EmbeddedMysql embeddedMysql;
    public SchemaConfig schemaConfig;

    public ApplicationConfiguration() {
        MysqldConfig mysqldConfig = MysqldConfig.aMysqldConfig(v5_7_latest).withPort(3306)
                .withUser("user", "password")
                .withTimeZone(TimeZone.getTimeZone("UTC"))
                .withCharset(Charset.UTF8)
                .build();
        schemaConfig = SchemaConfig.aSchemaConfig("x_shard_01").withCharset(Charset.UTF8).build();
        EmbeddedMysql.Builder embeddedMysqlBuilder = anEmbeddedMysql(mysqldConfig).addSchema(schemaConfig);
        embeddedMysql = embeddedMysqlBuilder.start();
        Runtime.getRuntime().addShutdownHook(new Thread(() -> embeddedMysql.dropSchema(schemaConfig)));

    }

    @PreDestroy
    public void preDestroy() throws IOException {
        this.embeddedMysql.stop();
        embeddedMysql.dropSchema(schemaConfig);
    }
}
