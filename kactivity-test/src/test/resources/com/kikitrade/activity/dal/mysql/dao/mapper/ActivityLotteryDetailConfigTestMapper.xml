<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityLotteryDetailConfigTestDao">
    <resultMap id="BaseResultMap" type="com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig">
        <result column="lottery_id" property="lotteryId"/>
        <result column="is_low" property="isLow"/>
        <result column="remain_num" property="remainNum"/>
        <result column="award_type" property="awardType"/>
    </resultMap>
    <insert id="init">
        INSERT INTO `activity_lottery_detail_config` (`id`, `lottery_id`, `name`, `amount`, `currency`, `num`,
                                                      `percent`, `is_low`, `remain_num`, `created`, `modified`,
                                                      `saasId`, `status`)
        VALUES ('2022101102122413904200', '', '一等奖', 0.10000000, 'BTC', 100, 0.05000, 0, 100, '2022-10-11 02:12:24',
                '2022-10-12 06:15:31', 'kiki', 1);
        INSERT INTO `activity_lottery_detail_config` (`id`, `lottery_id`, `name`, `amount`, `currency`, `num`,
                                                      `percent`, `is_low`, `remain_num`, `created`, `modified`,
                                                      `saasId`, `status`)
        VALUES ('2022101102122416004201', '', '二等奖', 0.09000000, 'BTC', 100, 0.10000, 0, 100, '2022-10-11 02:12:24',
                '2022-10-12 06:41:40', 'kiki', 1);
        INSERT INTO `activity_lottery_detail_config` (`id`, `lottery_id`, `name`, `amount`, `currency`, `num`,
                                                      `percent`, `is_low`, `remain_num`, `created`, `modified`,
                                                      `saasId`, `status`)
        VALUES ('2022101102122416004202', '', '三等奖', 0.08000000, 'BTC', 100, 0.15000, 0, 100, '2022-10-11 02:12:24',
                '2022-10-11 08:18:57', 'kiki', 1);
        INSERT INTO `activity_lottery_detail_config` (`id`, `lottery_id`, `name`, `amount`, `currency`, `num`,
                                                      `percent`, `is_low`, `remain_num`, `created`, `modified`,
                                                      `saasId`, `status`)
        VALUES ('2022101102122416004203', '', '四等奖', 0.07000000, 'BTC', 100, 0.20000, 0, 100, '2022-10-11 02:12:24',
                '2022-10-12 07:20:26', 'kiki', 1);
        INSERT INTO `activity_lottery_detail_config` (`id`, `lottery_id`, `name`, `amount`, `currency`, `num`,
                                                      `percent`, `is_low`, `remain_num`, `created`, `modified`,
                                                      `saasId`, `status`)
        VALUES ('2022101102122416004214', '', '幸运奖', 50.00000000, 'POINT', 99999, 0.20000, 0, 99999,
                '2022-10-11 02:12:24', '2022-10-12 06:49:52', 'kiki', 1);
        INSERT INTO `activity_lottery_detail_config` (`id`, `lottery_id`, `name`, `amount`, `currency`, `num`,
                                                      `percent`, `is_low`, `remain_num`, `created`, `modified`,
                                                      `saasId`, `status`)
        VALUES ('2022101102122416004215', '', 'thanks', 0.00000000, 'POINT', 99999, 0.30000, 0, 99999,
                '2022-10-11 02:12:24', '2022-10-12 06:49:52', 'kiki', 1);

    </insert>

    <update id="createTableIfNotExist">
        CREATE TABLE `activity_lottery_detail_config`
        (
            `id`         varbinary(64) NOT NULL,
            `lottery_id` varchar(32)    DEFAULT NULL COMMENT '奖池id',
            `name`       varchar(128)   DEFAULT NULL COMMENT '奖品名称',
            `amount`     decimal(20, 8) DEFAULT NULL COMMENT '奖品金额',
            `currency`   varchar(16)    DEFAULT NULL COMMENT '奖品币种',
            `num`        int(11) DEFAULT NULL COMMENT '发放数量',
            `percent`    decimal(10, 5) DEFAULT NULL COMMENT '中奖率',
            `is_low`     tinyint(4) DEFAULT NULL COMMENT '是否兜底',
            `remain_num` int(11) DEFAULT NULL COMMENT '剩余数量',
            `created`    datetime    NOT NULL COMMENT '创建时间',
            `modified`   datetime    NOT NULL COMMENT '修改时间',
            `saasId`     varchar(16) NOT NULL COMMENT 'saasId',
            `status`     tinyint(4) DEFAULT '1' COMMENT '奖品状态',
            `award_type` varchar(16)    DEFAULT NULL COMMENT '奖励类型',
            `reward_level` varchar(16)    DEFAULT NULL COMMENT '奖励等级',
            PRIMARY KEY (`id`),
            KEY          `idx_lottery_id` (`lottery_id`) USING BTREE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='抽奖活动奖池奖品配置'
        ;
    </update>
    <select id="selectAll" resultMap="BaseResultMap">
        select *
        from activity_lottery_detail_config;
    </select>

</mapper>

