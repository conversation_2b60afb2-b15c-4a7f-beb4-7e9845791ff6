package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * 投放池子配置表
 */
@Data
@Table(name = "precision_pool")
public class PrecisionPool implements Serializable {

    @PartitionKey(name = "id")
    private String id; // 投放id

    @Column(name = "channel")
    private String channel; // 投放渠道

    @Column(name = "task")
    private String task; // 关联任务/活动

    @Column(name = "owner")
    private String owner; // 所属者

    @Column(name = "distribution_amount" , type = Column.Type.DOUBLE)
    private double distributionAmount; // distribution投放金额

    @Column(name = "contribution_ai_amount", type = Column.Type.DOUBLE)
    private double contributionAiAmount; // contribution_ai投放金额

    @Column(name = "contribution_human_amount", type = Column.Type.DOUBLE)
    private double contributionHumanAmount; // contribution_human投放金额

    @Column(name = "contribution_deep_human_amount", type = Column.Type.DOUBLE)
    private double contributionDeepHumanAmount; // contribution_deep_human投放金额

    @Column(name = "distribution_max_amount", type = Column.Type.DOUBLE)
    private double distributionMaxAmount; // distribution单用户最大金额

    @Column(name = "contribution_ai_max_amount", type = Column.Type.DOUBLE)
    private double contributionAiMaxAmount; // contribution_ai单用户最大金额

    @Column(name = "contribution_human_max_amount", type = Column.Type.DOUBLE)
    private double contributionHumanMaxAmount; // contribution_human单用户最大金额

    @Column(name = "contribution_deep_human_max_amount", type = Column.Type.DOUBLE)
    private double contributionDeepHumanMaxAmount; // contribution_deep_human单用户最大金额

    @Column(name = "precision_track_check_ai_point", type = Column.Type.DOUBLE)
    private double precisionTrackCheckAiPoint; //保存打分记录前校验AI的分数

    @Column(name = "precision_track_check_follows_count", type = Column.Type.DOUBLE)
    private double precisionTrackCheckFollowsCount; //保存打分记录前校验粉丝数

    @Column(name = "precision_track_check_asset", type = Column.Type.DOUBLE)
    private double precisionTrackCheckAsset; //保存打分记录前校验用户资产

    @Column(name = "history")
    private String history; // 历史记录
}
