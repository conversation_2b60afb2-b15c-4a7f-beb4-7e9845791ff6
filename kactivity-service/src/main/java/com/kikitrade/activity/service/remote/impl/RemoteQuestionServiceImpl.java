package com.kikitrade.activity.service.remote.impl;

import com.kikitrade.activity.api.RemoteQuestionService;
import com.kikitrade.activity.api.model.RespondentIdentity;
import com.kikitrade.activity.api.model.request.UserAnswerRequest;
import com.kikitrade.activity.api.model.response.QuestionSetsResponse;
import com.kikitrade.activity.api.model.response.QuestionListResponse;
import com.kikitrade.activity.api.model.response.QuestionSettleResponse;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.question.QuestionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 答题服务
 */
@DubboService
@Slf4j
public class RemoteQuestionServiceImpl implements RemoteQuestionService {

    @Resource
    private QuestionService questionService;

    @Override
    public QuestionSetsResponse getQuestionSets(RespondentIdentity identity) {
        return questionService.getQuestionSets(identity);
    }

    @Override
    public QuestionListResponse acquireQuestions(RespondentIdentity identity) throws ActivityException {
        return questionService.acquireQuestions(identity);
    }

    @Override
    public QuestionSettleResponse submitQuestions(RespondentIdentity identity, UserAnswerRequest userAnswerRequest) throws ActivityException {
        return questionService.submitQuestions(identity, userAnswerRequest);
    }

}
