package com.kikitrade.activity.service.auth.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.model.OpenAuthRequest;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.time.OffsetDateTime;

/**
 * OAuth2认证策略抽象基类
 * 提供通用的OAuth2认证流程实现
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
public abstract class AbstractOAuth2AuthStrategy implements PlatformAuthStrategy {

    protected final OkHttpClient okHttpClient;

    public AbstractOAuth2AuthStrategy(OkHttpClient okHttpClient) {
        this.okHttpClient = okHttpClient;
    }

    @Override
    public AuthResult authenticate(AuthContext context) throws ActivityException {
        try {
            // 构建认证请求
            OpenAuthRequest authRequest = buildAuthRequest(context.getSaasId(), context.getCode(), context.getRedirectUri());
            if (authRequest == null) {
                log.error("[{}] 构建认证请求失败: platform={}, customerId={}", 
                    getPlatformName(), context.getPlatform(), context.getCustomerId());
                throw new ActivityException(ActivityResponseCode.AUTH_CODE_INVALID);
            }

            log.info("[{}] 开始认证: authRequest={}, customerId={}", 
                getPlatformName(), authRequest, context.getCustomerId());

            // 发送认证请求
            Token token = performOAuth2Request(authRequest);
            if (token == null) {
                throw new ActivityException(ActivityResponseCode.AUTH_CODE_INVALID);
            }

            // 获取用户信息
            SocialUserInfo socialUserInfo = getCurrentUser(context.getSaasId(), 
                token.getAccessToken(), token.getRefreshToken());
            if (socialUserInfo == null) {
                log.error("[{}] 获取用户信息失败", getPlatformName());
                return AuthResult.failure("获取用户信息失败");
            }

            // 设置创建时间
            if (socialUserInfo.getCreatedAt() == null) {
                socialUserInfo.setCreatedAt(OffsetDateTime.now().toEpochSecond());
            }

            // 设置token中的用户信息
            token.setSocialCustomerId(socialUserInfo.getUserId());
            token.setUserName(socialUserInfo.getUserName());

            return AuthResult.success(token, socialUserInfo);

        } catch (ActivityException e) {
            log.error("[{}] 认证失败: {}", getPlatformName(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("[{}] 认证过程中发生异常", getPlatformName(), e);
            throw new ActivityException(ActivityResponseCode.AUTH_CODE_INVALID);
        }
    }

    /**
     * 执行OAuth2请求
     * @param authRequest 认证请求
     * @return Token
     * @throws Exception 异常
     */
    public Token performOAuth2Request(OpenAuthRequest authRequest) throws Exception {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, authRequest.getQuery());
        Request request = new Request.Builder()
            .url(authRequest.getUrl())
            .method("POST", body)
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .addHeader("Authorization", "Basic " + authRequest.getBasic())
            .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            String responseBody = response.body().string();
            JSONObject jsonObject = JSON.parseObject(responseBody);
            
            log.info("[{}] 认证响应: {}", getPlatformName(), jsonObject);
            
            if (!response.isSuccessful() || !jsonObject.containsKey("access_token")) {
                log.error("[{}] 认证失败: response={}", getPlatformName(), jsonObject);
                return null;
            }

            Token token = new Token();
            token.setAccessToken(jsonObject.getString("access_token"));
            token.setRefreshToken(jsonObject.getString("refresh_token"));

            // 存储过期时间信息（用于后续Redis存储）
            if (jsonObject.containsKey("expires_in")) {
                long expiresIn = jsonObject.getLongValue("expires_in");
                // 这里可以将过期时间信息存储到token的扩展字段中，或者通过其他方式传递
                // 暂时不在Token中添加新字段，保持原有结构
            }

            return token;
        }
    }
}
