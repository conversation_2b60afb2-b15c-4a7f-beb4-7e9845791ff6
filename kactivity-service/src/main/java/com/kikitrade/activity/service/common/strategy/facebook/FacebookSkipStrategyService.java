package com.kikitrade.activity.service.common.strategy.facebook;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/22 18:40
 * @description: Facebook跳过策略服务
 */
@Component
@Slf4j
public class FacebookSkipStrategyService implements FacebookSaasStrategyService{

    @Override
    public String strategy() {
        return SaasStrategyConstant.FacebookStrategyEnum.SKIP.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        log.info("FacebookSkipStrategyService execute, request: {}", request);
        // 跳过策略，不执行任何操作
    }
}
