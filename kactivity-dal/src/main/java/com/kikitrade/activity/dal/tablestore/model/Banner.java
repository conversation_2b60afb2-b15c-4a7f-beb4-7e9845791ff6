package com.kikitrade.activity.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/1 14:45
 */
@Data
@Table(name = "banner")
public class Banner implements Serializable {

    public static final String IDX_BANNER_LOCAL = "idx_banner_local";

    @PartitionKey(name = "id")
    private String id;
    @Column(name = "image")
    private String image;
    @Column(name = "name")
    private String name;
    @Column(name = "desc")
    private String desc;
    @Column(name = "url")
    private String url;
    @Column(name = "source", isDefined = true)
    private String source;
    @Column(name = "source_id", isDefined = true)
    private String sourceId;
    @Column(name = "order", isDefined = true, type = Column.Type.INTEGER)
    @SearchIndex(name = IDX_BANNER_LOCAL, column = "order", fieldType = FieldType.LONG)
    private Integer order;
    @Column(name = "channel", isDefined = true)
    @SearchIndex(name = IDX_BANNER_LOCAL, column = "channel")
    private String channel;
    @Column(name = "local", isDefined = true)
    @SearchIndex(name = IDX_BANNER_LOCAL, column = "local")
    private String local;
    @Column(name = "saas_id", isDefined = true)
    @SearchIndex(name = IDX_BANNER_LOCAL, column = "saas_id")
    private String saasId;
}
