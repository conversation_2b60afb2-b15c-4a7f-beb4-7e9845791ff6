package com.kikitrade.activity.service.engine.process;

import com.alibaba.fastjson.JSONObject;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.Subscribe;
import com.kikitrade.activity.dal.mysql.model.ActivityActionMap;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.ActivityEventMassage;
import com.kikitrade.activity.service.business.ActivityBatchService;
import com.kikitrade.activity.service.business.ActivityService;
import com.kikitrade.activity.service.common.SpringUtil;
import com.kikitrade.activity.service.engine.action.base.AbstractBaseAction;
import com.kikitrade.activity.service.meta.ActivityActionMapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @ClassName ActivityAsyncEventBus
 * @Description
 * <AUTHOR>
 * @Date 2018/7/23 19:50
 * @Version 1.0
 **/
@Slf4j
@Service
public class ActivityAsyncEventBus {

    private AsyncEventBus asyncEventBus;
    private ExecutorService executorService;

    @Resource
    private ActivityBatchService activityBatchService;

    @Resource
    private ActivityActionMapService activityActionMapService;

    @Resource
    private ActivityService activityService;

    @Value("${activity.dispatch.limit:100}")
    private String limit = "100";

    @Value("${activity.dispatch.notifyurl}")
    private String notifyurl;

    public ActivityAsyncEventBus() {
        executorService = Executors.newFixedThreadPool(30, t ->
                new Thread(t, "activity-event-push-executor"));

        asyncEventBus = new AsyncEventBus(executorService, (e, context) -> {
            log.error("running error", e);
        });
        asyncEventBus.register(this);
    }


    public void pushActivityEvent(ActivityEventMassage activityEventMassage) {
        asyncEventBus.post(activityEventMassage);
    }

    @Subscribe
    @AllowConcurrentEvents
    public void postAsyncEvent(ActivityEventMassage activityEventMassage) throws Exception {

        switch (ActivityConstant.RewardType.getRewardType(activityEventMassage.getType())) {
            case JOB_DISPATCH:
                log.info("notify Activity job JOB_DISPATCH for {}", JSONObject.toJSONString(activityEventMassage));
                String params = activityEventMassage.getParams();
                JSONObject paramsObj = JSONObject.parseObject(params);
                Integer querylimit = paramsObj.getInteger("limit");
                String notifyUrl = paramsObj.getString("notifyUrl");
                Date deadline = paramsObj.getDate("deadline");
                activityBatchService.jobDispatch(activityEventMassage.getId(), ActivityConstant.ExecuteType.TIMED.getCode(), ActivityConstant.RecordStatus.RECORDED.getCode(), querylimit, deadline, notifyUrl);
                break;

            case MANUAL_REWARD:


                log.info("notify Activity manualReward job for {} limit={}, notifyUrl={}", JSONObject.toJSONString(activityEventMassage), limit, notifyurl);
                Integer queryLimit = Integer.parseInt(limit);
                Date deadline2 = new Date();
                activityBatchService.manualReward(activityEventMassage.getId(), ActivityConstant.ExecuteType.MANUAL.getCode(), ActivityConstant.RecordStatus.RECORDED.getCode(), deadline2, queryLimit, notifyurl);

                break;

            case TIMED_REWARD:
                log.info("notify Activity job TIMED_REWARD for {}", JSONObject.toJSONString(activityEventMassage));
                activityBatchService.timedReward(activityEventMassage.getParams());
                break;

            case DATA_PROCESS:
                log.info("Activity data process DATA_PROCESS for {}", JSONObject.toJSONString(activityEventMassage));

                List<ActivityActionMap> activityActionMapList = activityActionMapService.findByActivityId(activityEventMassage.getId());
                if (CollectionUtils.isNotEmpty(activityActionMapList)) {
                    ActivityActionMap activityActionMap = activityActionMapList.get(0);
                    AbstractBaseAction action = (AbstractBaseAction) SpringUtil.getBean(activityActionMap.getAction_name());
                    action.actionDataPrepare(activityEventMassage);
                }
                break;
            default:
                break;

        }

    }

}
