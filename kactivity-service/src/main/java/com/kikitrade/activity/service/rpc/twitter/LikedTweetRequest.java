package com.kikitrade.activity.service.rpc.twitter;

import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.twitter.clientlib.TwitterCredentialsOAuth2;
import com.twitter.clientlib.api.TwitterApi;
import com.twitter.clientlib.model.Tweet;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 17:55
 */
@Data
@Slf4j
public class LikedTweetRequest extends AccessToken {
    private TwitterProperties twitterProperties;

    public LikedTweetRequest(){}

    public LikedTweetRequest(TwitterProperties twitterProperties, AccessToken accessToken){
        super(accessToken);
        this.twitterProperties = twitterProperties;
    }

    public List<Tweet> execute() {
        try{
            return getApi(this.accessToken, this.refreshToken, this.twitterProperties).tweets()
                    .usersIdLikedTweets(this.userId)
                    .tweetFields(Set.of("author_id", "created_at"))
                    .execute().getData();
        }catch (Exception ex){
            log.error("verifyCommentPost error:{}",this.userId, ex);
            return null;
        }
    }

    public LikedTweetRequest buildLikedTweetRequest(String userId){
        this.userId = userId;
        return this;
    }
}
