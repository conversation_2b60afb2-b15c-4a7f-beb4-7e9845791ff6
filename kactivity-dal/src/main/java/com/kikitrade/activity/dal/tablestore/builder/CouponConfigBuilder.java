package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.CouponConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/8 17:22
 */
public interface CouponConfigBuilder {

    Boolean batchInsert(List<CouponConfig> couponConfigs);

    List<CouponConfig> batchGetByCode(List<String> codes, String saasId, String businessType);

    CouponConfig getConfigByPK(String code, String saasId, String businessType);

}
