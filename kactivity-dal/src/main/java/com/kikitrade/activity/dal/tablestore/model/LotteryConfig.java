package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/6 14:48
 */
@Data
@Table(name = "lottery_config")
public class LotteryConfig {

    public static final String SEARCH_LOTTERY_CODE = "search_lottery_code";

    @PartitionKey(name = "id")
    private String id;
    @Column(name = "type")
    private String type;
    @Column(name = "code")
    @SearchIndex(name = SEARCH_LOTTERY_CODE, column = "code")
    private String code;
    @Column(name = "amount")
    private Integer amount;
    @Column(name = "startTime", type = Column.Type.INTEGER)
    private Long startTime;
    @Column(name = "endTime", type = Column.Type.INTEGER)
    private Long endTime;
    @Column(name = "limit_times", type = Column.Type.INTEGER)
    private Integer limitTimes;
    @Column(name = "limit_unit")
    private String limitUnit;
    @Column(name = "currency")
    private String currency;
    @Column(name = "nodes")
    private String nodes;
    @Column(name = "awards")
    private String awards;
    @Column(name = "status")
    @SearchIndex(name = SEARCH_LOTTERY_CODE, column = "status")
    private String status;
    @Column(name = "saas_id")
    private String saasId;
    @Column(name = "is_cumulate", type = Column.Type.BOOLEAN)
    private Boolean isCumulate;
    @Column(name = "cumulate_info")
    private String cumulateInfo;
}
