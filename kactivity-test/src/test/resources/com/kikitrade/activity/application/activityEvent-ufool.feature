# language: zh-CN
功能: activity-event

  背景:
    假如task-初始化任务code配置TaskCodeConfig

# somon中心化

# 签到任务
# Normal用户limit每日1次,每次奖励5 EXPERIENCE
# L1用户limit每日1次,每次奖励10 EXPERIENCE


  # Example_1:  Normal用户,签到一次,一次性奖励5积分
  # Example_2:  L1用户,签到一次,一次性奖励10积分
#  场景大纲: 每日签到,积分奖励
#    假如task-初始化任务配置task_config<taskConfig>
#    当task-系统活动事件监听<eventDTO>
#    那么task-校验activity_task_item<activityTaskItem>
#    那么task-校验activity_customer_reward<activityCustomReward>
#    那么task-校验assetTransferInRequest<assetTransferInRequest>
#    那么task-校验ActivityResponse<activityResponse>
#    例子:
#      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | activityCustomReward                                                                                                                                                                                                                                                                                                                                                          | assetTransferInRequest                                                                                                                                                                          | activityResponse                                                                                              |
#      # NORMAL
#      | [{"taskId":"400009","groupId":null,"isGroup":true,"showList":true,"title":"Daily Check-in","desc":"Get [[p5]] daily by checking in","labelName":"","labelColor":"","saasId":"monster","status":"ACTIVE","startTime":1698598861000,"endTime":1735491661000,"code":"sign_in","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"POINT","amount":"10","currency":"POINT","vipLevel":"L1","index":1},{"type":"POINT","amount":"5","currency":"POINT","vipLevel":"NORMAL","index":2}]},"order":1,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"app"}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"400009"}}]                  | [{"businessId":"202403311419240192","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-03-31 06:19:24","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","expiredTime":"20240331235959","modified":"2024-03-31 06:19:24","progress":1,"status":"DONE","targetId":"-1","taskId":"400009"},{"businessId":"202403311419240192","completeTime":"2024-03-31 06:19:24","created":"2024-03-31 06:19:24","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","modified":"2024-03-31 06:19:24","status":"DONE","targetId":"","taskId":"400009"}] | [{"amount":"5","batchId":"20240331","businessId":"2024033114271900601","businessType":"sign_in","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"POINT","saasId":"monster","seq":"sign_in:2024033114271900601","status":"AWARD_SUCCESS","userType":"Normal"}]  | [{"amount":5,"businessId":"2024033114305808721","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"monster","type":"POINT"}]  | [{"code":"SUCCESS","obj":[{"amount":"5.0","currency":"POINT","index":2,"type":"POINT","vipLevel":"NORMAL"}]}] |
#      # L1
#      | [{"taskId":"400009","groupId":null,"isGroup":true,"showList":true,"title":"Daily Check-in","desc":"Get [[p5]] daily by checking in","labelName":"","labelColor":"","saasId":"monster","status":"ACTIVE","startTime":1698598861000,"endTime":1735491661000,"code":"sign_in","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"POINT","amount":"10","currency":"POINT","vipLevel":"L1","index":1},{"type":"POINT","amount":"5","currency":"POINT","vipLevel":"NORMAL","index":2}]},"order":1,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"app"}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"400009","vipLevel":"L1"}]}] | [{"businessId":"202403311424330191","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-03-31 06:24:33","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","expiredTime":"20240331235959","modified":"2024-03-31 06:24:33","progress":1,"status":"DONE","targetId":"-1","taskId":"400009"},{"businessId":"202403311424330191","completeTime":"2024-03-31 06:24:33","created":"2024-03-31 06:24:33","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","modified":"2024-03-31 06:24:33","status":"DONE","targetId":"","taskId":"400009"}] | [{"amount":"10","batchId":"20240331","businessId":"2024033114271904201","businessType":"sign_in","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"POINT","saasId":"monster","seq":"sign_in:2024033114271904201","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":10,"businessId":"2024033114305903061","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"monster","type":"POINT"}] | [{"code":"SUCCESS","obj":[{"amount":"10.0","currency":"POINT","index":1,"type":"POINT","vipLevel":"L1"}]}]    |



  # Example_1:  Normal用户,签到一次,一次性奖励5 EXPERIENCE
  # Example_2:  L1用户,签到一次,一次性奖励10 EXPERIENCE
  场景大纲: 每日签到,经验奖励
    假如task-初始化任务配置task_config<taskConfig>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验ActivityResponse<activityResponse>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                 | assetTransferInRequest                                                                                                                                                                                         | activityResponse                                                                                                        |
      # NORMAL
      | [{"taskId":"400009","groupId":null,"isGroup":true,"showList":true,"title":"Daily Check-in","desc":"Get [[p5]] daily by checking in","labelName":"","labelColor":"","saasId":"monster","status":"ACTIVE","startTime":1698598861000,"endTime":1735491661000,"code":"sign_in","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"EXPERIENCE","amount":"10","currency":"EXPERIENCE","vipLevel":"L1","index":1},{"type":"EXPERIENCE","amount":"5","currency":"EXPERIENCE","vipLevel":"NORMAL","index":2}]},"order":1,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"app"}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"400009"}}]                  | [{"businessId":"202403311419240192","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-03-31 06:19:24","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","expiredTime":"20240331235959","modified":"2024-03-31 06:19:24","progress":1,"status":"DONE","targetId":"-1","taskId":"400009"},{"businessId":"202403311419240192","completeTime":"2024-03-31 06:19:24","created":"2024-03-31 06:19:24","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","modified":"2024-03-31 06:19:24","status":"DONE","targetId":"","taskId":"400009"}] | [{"amount":"5.0","batchId":"20240331","businessId":"202409211443420816202409211","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"monster","seq":"sign_in:202409211443420816202409211:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"}]  | [{"amount":5.0,"businessId":"202409211447300077202409211","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"monster","type":"EXPERIENCE"}]  | [{"code":"SUCCESS","obj":[{"amount":"5.0","currency":"EXPERIENCE","index":2,"type":"EXPERIENCE","vipLevel":"NORMAL"}]}] |
      # L1
      | [{"taskId":"400009","groupId":null,"isGroup":true,"showList":true,"title":"Daily Check-in","desc":"Get [[p5]] daily by checking in","labelName":"","labelColor":"","saasId":"monster","status":"ACTIVE","startTime":1698598861000,"endTime":1735491661000,"code":"sign_in","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"EXPERIENCE","amount":"10","currency":"EXPERIENCE","vipLevel":"L1","index":1},{"type":"EXPERIENCE","amount":"5","currency":"EXPERIENCE","vipLevel":"NORMAL","index":2}]},"order":1,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"app"}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"400009","vipLevel":"L1"}]}] | [{"businessId":"202403311424330191","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-03-31 06:24:33","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","expiredTime":"20240331235959","modified":"2024-03-31 06:24:33","progress":1,"status":"DONE","targetId":"-1","taskId":"400009"},{"businessId":"202403311424330191","completeTime":"2024-03-31 06:24:33","created":"2024-03-31 06:24:33","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","modified":"2024-03-31 06:24:33","status":"DONE","targetId":"","taskId":"400009"}] | [{"amount":"10.0","batchId":"20240331","businessId":"202409211444330528202409211","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"monster","seq":"sign_in:202409211444330528202409211:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":10.0,"businessId":"202409211447300425202409211","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"monster","type":"EXPERIENCE"}] | [{"code":"SUCCESS","obj":[{"amount":"10.0","currency":"EXPERIENCE","index":1,"type":"EXPERIENCE","vipLevel":"L1"}]}]    |

  # Example_1:  Normal用户,签到一次,一次性奖励5 EXPERIENCE
  # Example_2:  L1用户,签到一次,一次性奖励10 EXPERIENCE
  场景大纲: 每日签到,经验奖励,tcc获取失败,数据回滚
    假如task-初始化任务配置task_config<taskConfig>
    同时activity-event-配置remoteAssetOperateService.transferIn异常
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验ActivityResponse<activityResponse>
    那么luck-校验exceptions为<exceptions>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                | assetTransferInRequest | activityResponse     | exceptions |
      # NORMAL
      | [{"taskId":"400009","groupId":null,"isGroup":true,"showList":true,"title":"Daily Check-in","desc":"Get [[p5]] daily by checking in","labelName":"","labelColor":"","saasId":"monster","status":"ACTIVE","startTime":1698598861000,"endTime":1735491661000,"code":"sign_in","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"EXPERIENCE","amount":"10","currency":"EXPERIENCE","vipLevel":"L1","index":1},{"type":"EXPERIENCE","amount":"5","currency":"EXPERIENCE","vipLevel":"NORMAL","index":2}]},"order":1,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"app"}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"400009"}}]                  | [{"businessId":"202403311419240192","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-03-31 06:19:24","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","expiredTime":"20240331235959","modified":"2024-03-31 06:19:24","progress":1,"status":"DONE","targetId":"-1","taskId":"400009"},{"businessId":"202403311419240192","completeTime":"2024-03-31 06:19:24","created":"2024-03-31 06:19:24","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","modified":"2024-03-31 06:19:24","status":"DONE","targetId":"","taskId":"400009"}] | [{"amount":"5.0","batchId":"20240331","businessId":"202409211503330551202409211","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"monster","seq":"sign_in:202409211503330551202409211:EXPERIENCE","status":"AWARD_FAILED","userType":"Normal"}]  | []                     | [{"code":"SUCCESS"}] | null       |
      # L1
      | [{"taskId":"400009","groupId":null,"isGroup":true,"showList":true,"title":"Daily Check-in","desc":"Get [[p5]] daily by checking in","labelName":"","labelColor":"","saasId":"monster","status":"ACTIVE","startTime":1698598861000,"endTime":1735491661000,"code":"sign_in","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"EXPERIENCE","amount":"10","currency":"EXPERIENCE","vipLevel":"L1","index":1},{"type":"EXPERIENCE","amount":"5","currency":"EXPERIENCE","vipLevel":"NORMAL","index":2}]},"order":1,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"app"}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"400009","vipLevel":"L1"}]}] | [{"businessId":"202403311424330191","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-03-31 06:24:33","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","expiredTime":"20240331235959","modified":"2024-03-31 06:24:33","progress":1,"status":"DONE","targetId":"-1","taskId":"400009"},{"businessId":"202403311424330191","completeTime":"2024-03-31 06:24:33","created":"2024-03-31 06:24:33","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","modified":"2024-03-31 06:24:33","status":"DONE","targetId":"","taskId":"400009"}] | [{"amount":"10.0","batchId":"20240331","businessId":"202409211503330932202409211","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"monster","seq":"sign_in:202409211503330932202409211:EXPERIENCE","status":"AWARD_FAILED","userType":"Normal"}] | []                     | [{"code":"SUCCESS"}] | null       |



  # Example_1:  Normal用户,签到一次,一次性奖励5个 question-sets
  # Example_2:  L1用户,签到一次,一次性奖励10个 question-sets
  场景大纲: 签到,获取题组数(sets)
    假如task-初始化任务配置task_config<taskConfig>
    同时task-已存在customer_question_sets<questionSet>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | questionSet                                                                                 | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                               | customerQuestionSet                                                                                                                   | activityResponse                                                                                            |
      # NORMAL
      | [{"taskId":"400009","groupId":null,"isGroup":true,"showList":true,"title":"Daily Check-in","desc":"Get [[p5]] daily by checking in","labelName":"","labelColor":"","saasId":"monster","status":"ACTIVE","startTime":1698598861000,"endTime":1735491661000,"code":"sign_in","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"SETS","amount":"10","currency":"SETS","vipLevel":"L1","index":1},{"type":"SETS","amount":"5","currency":"SETS","vipLevel":"NORMAL","index":2}]},"order":1,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"app"}] | [{"saasId":"monster","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"400009"}}]                  | [{"businessId":"202403311419240192","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-03-31 06:19:24","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","expiredTime":"20240331235959","modified":"2024-03-31 06:19:24","progress":1,"status":"DONE","targetId":"-1","taskId":"400009"},{"businessId":"202403311419240192","completeTime":"2024-03-31 06:19:24","created":"2024-03-31 06:19:24","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","modified":"2024-03-31 06:19:24","status":"DONE","targetId":"","taskId":"400009"}] | [{"amount":"5.0","batchId":"20240331","businessId":"202409211355540774202409211","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"monster","seq":"sign_in:202409211355540774202409211:SETS","status":"AWARD_SUCCESS","userType":"Normal"}]  | [{"availableSets":5,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"monster","usedSets":0}]  | [{"code":"SUCCESS","obj":[{"amount":"5.0","currency":"SETS","index":2,"type":"SETS","vipLevel":"NORMAL"}]}] |
      # L1
      | [{"taskId":"400009","groupId":null,"isGroup":true,"showList":true,"title":"Daily Check-in","desc":"Get [[p5]] daily by checking in","labelName":"","labelColor":"","saasId":"monster","status":"ACTIVE","startTime":1698598861000,"endTime":1735491661000,"code":"sign_in","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"SETS","amount":"10","currency":"SETS","vipLevel":"L1","index":1},{"type":"SETS","amount":"5","currency":"SETS","vipLevel":"NORMAL","index":2}]},"order":1,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"app"}] | [{"saasId":"monster","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"400009","vipLevel":"L1"}]}] | [{"businessId":"202403311424330191","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-03-31 06:24:33","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","expiredTime":"20240331235959","modified":"2024-03-31 06:24:33","progress":1,"status":"DONE","targetId":"-1","taskId":"400009"},{"businessId":"202403311424330191","completeTime":"2024-03-31 06:24:33","created":"2024-03-31 06:24:33","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","modified":"2024-03-31 06:24:33","status":"DONE","targetId":"","taskId":"400009"}] | [{"amount":"10.0","batchId":"20240331","businessId":"202409211357370216202409211","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"monster","seq":"sign_in:202409211357370216202409211:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"availableSets":10,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"monster","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"10.0","currency":"SETS","index":1,"type":"SETS","vipLevel":"L1"}]}]    |


  # Example_1:  Normal用户,签到一次,一次性奖励5个 question-sets
  # Example_2:  L1用户,签到一次,一次性奖励10个 question-sets
  场景大纲: 签到,获取题组数(sets),tcc获取失败,数据回滚
    假如task-初始化任务配置task_config<taskConfig>
    同时task-已存在customer_question_sets<questionSet>
    同时activity-event-配置customerQuestionSetsBuilder.incrementAvailableSets异常
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    那么luck-校验exceptions为<exceptions>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | questionSet                                                                                 | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                              | customerQuestionSet                                                                                                                  | activityResponse     | exceptions |
      # NORMAL
      | [{"taskId":"400009","groupId":null,"isGroup":true,"showList":true,"title":"Daily Check-in","desc":"Get [[p5]] daily by checking in","labelName":"","labelColor":"","saasId":"monster","status":"ACTIVE","startTime":1698598861000,"endTime":1735491661000,"code":"sign_in","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"SETS","amount":"10","currency":"SETS","vipLevel":"L1","index":1},{"type":"SETS","amount":"5","currency":"SETS","vipLevel":"NORMAL","index":2}]},"order":1,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"app"}] | [{"saasId":"monster","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"400009"}}]                  | [{"businessId":"202403311419240192","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-03-31 06:19:24","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","expiredTime":"20240331235959","modified":"2024-03-31 06:19:24","progress":1,"status":"DONE","targetId":"-1","taskId":"400009"},{"businessId":"202403311419240192","completeTime":"2024-03-31 06:19:24","created":"2024-03-31 06:19:24","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","modified":"2024-03-31 06:19:24","status":"DONE","targetId":"","taskId":"400009"}] | [{"amount":"5.0","batchId":"20240331","businessId":"202409211420480910202409211","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"monster","seq":"sign_in:202409211420480910202409211:SETS","status":"AWARD_FAILED","userType":"Normal"}]  | [{"availableSets":0,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"monster","usedSets":0}] | [{"code":"SUCCESS"}] | null       |
      # L1
      | [{"taskId":"400009","groupId":null,"isGroup":true,"showList":true,"title":"Daily Check-in","desc":"Get [[p5]] daily by checking in","labelName":"","labelColor":"","saasId":"monster","status":"ACTIVE","startTime":1698598861000,"endTime":1735491661000,"code":"sign_in","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"daily","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"SETS","amount":"10","currency":"SETS","vipLevel":"L1","index":1},{"type":"SETS","amount":"5","currency":"SETS","vipLevel":"NORMAL","index":2}]},"order":1,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"app"}] | [{"saasId":"monster","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"400009","vipLevel":"L1"}]}] | [{"businessId":"202403311424330191","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-03-31 06:24:33","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","expiredTime":"20240331235959","modified":"2024-03-31 06:24:33","progress":1,"status":"DONE","targetId":"-1","taskId":"400009"},{"businessId":"202403311424330191","completeTime":"2024-03-31 06:24:33","created":"2024-03-31 06:24:33","customerId":"2024033102484135902204","cycle":"20240331","event":"sign_in","modified":"2024-03-31 06:24:33","status":"DONE","targetId":"","taskId":"400009"}] | [{"amount":"10.0","batchId":"20240331","businessId":"202409211420580974202409211","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"monster","seq":"sign_in:202409211420580974202409211:SETS","status":"AWARD_FAILED","userType":"Normal"}] | [{"availableSets":0,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"monster","usedSets":0}] | [{"code":"SUCCESS"}] | null       |

    #|连续签到天数|获得题组数(sets)|获得经验(exp)|
    #|1         |1             |100         |
    #|2         |1             |100         |
    #|3         |2             |200         |
    #|4         |2             |200         |
    #|5         |4             |500         |
  场景大纲: 连续签到1天,第一天获得题组1&经验奖励100
    假如task-初始化任务配置task_config<taskConfig>
    同时task-已存在customer_question_sets<questionSet>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | questionSet                                                                               | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | assetTransferInRequest                                                                                                                                                                                        | customerQuestionSet                                                                                                                | activityResponse                                                                                                                                                                                         |
      # NORMAL
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007"}}]                  | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 07:50:35","progress":1,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"100.0","batchId":"19990101","businessId":"202409211553580729202409210","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211553580729202409210:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"1.0","batchId":"19990101","businessId":"202409211553580729202409210","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211553580729202409210:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":100.0,"businessId":"202409211556440136202409210","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":1,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"1.0","currency":"SETS","index":1,"type":"SETS","vipLevel":"NORMAL"},{"amount":"100.0","currency":"EXPERIENCE","index":3,"type":"EXPERIENCE","vipLevel":"NORMAL"}]}] |
      # L1
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007","vipLevel":"L1"}]}] | [{"businessId":"202409211550350491","completeThreshold":2147483647,"created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 07:50:35","progress":1,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350491","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"100.0","batchId":"19990101","businessId":"202409211553590072202409210","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211553590072202409210:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"1.0","batchId":"19990101","businessId":"202409211553590072202409210","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211553590072202409210:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":100.0,"businessId":"202409211556440772202409210","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":1,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"1.0","currency":"SETS","index":2,"type":"SETS","vipLevel":"L1"},{"amount":"100.0","currency":"EXPERIENCE","index":4,"type":"EXPERIENCE","vipLevel":"L1"}]}]         |


  场景大纲: 连续签到2天,第二天获得题组1&经验奖励100
    假如task-初始化任务配置task_config<taskConfig>
    同时task-已存在主任务activity_task_item<primaryActivityTaskItem>
    同时task-已存在子任务activity_task_item<subActivityTaskItem>
    同时task-已存在customer_question_sets<questionSet>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | primaryActivityTaskItem                                                                                                                                                                                                                      | subActivityTaskItem                                                                                                                                                                                                                                                      | questionSet                                                                               | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | assetTransferInRequest                                                                                                                                                                                        | customerQuestionSet                                                                                                                | activityResponse                                                                                                                                                                                         |
      # NORMAL
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":1,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | [{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":null,"taskId":"408007"}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007"}}]                  | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 08:51:12","progress":2,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":"20240920","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 08:49:40","created":"2024-09-21 08:49:40","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 08:49:40","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"100.0","batchId":"19990101","businessId":"202409211553580729202409210","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211553580729202409210:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"1.0","batchId":"19990101","businessId":"202409211553580729202409210","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211553580729202409210:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":100.0,"businessId":"202409211556440136202409210","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":1,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"1.0","currency":"SETS","index":1,"type":"SETS","vipLevel":"NORMAL"},{"amount":"100.0","currency":"EXPERIENCE","index":3,"type":"EXPERIENCE","vipLevel":"NORMAL"}]}] |
      # L1
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":1,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | [{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":null,"taskId":"408007"}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007","vipLevel":"L1"}]}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 08:51:19","progress":2,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":"20240920","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 08:51:17","created":"2024-09-21 08:51:17","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 08:51:17","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"100.0","batchId":"19990101","businessId":"202409211553590072202409210","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211553590072202409210:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"1.0","batchId":"19990101","businessId":"202409211553590072202409210","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211553590072202409210:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":100.0,"businessId":"202409211556440772202409210","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":1,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"1.0","currency":"SETS","index":2,"type":"SETS","vipLevel":"L1"},{"amount":"100.0","currency":"EXPERIENCE","index":4,"type":"EXPERIENCE","vipLevel":"L1"}]}]         |

  场景大纲: 连续签到3天,第三天获得题组2&经验奖励200
    假如task-初始化任务配置task_config<taskConfig>
    同时task-已存在主任务activity_task_item<primaryActivityTaskItem>
    同时task-已存在子任务activity_task_item<subActivityTaskItem>
    同时task-已存在customer_question_sets<questionSet>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | primaryActivityTaskItem                                                                                                                                                                                                                      | subActivityTaskItem                                                                                                                                                                                                                                                      | questionSet                                                                               | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | assetTransferInRequest                                                                                                                                                                                        | customerQuestionSet                                                                                                                | activityResponse                                                                                                                                                                                         |
      # NORMAL
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":2,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | [{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":null,"taskId":"408007"}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007"}}]                  | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-20 17:12:33","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 09:12:33","progress":3,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-20 17:12:33","created":"2024-09-20 17:12:33","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-20 17:12:33","status":"DONE","targetId":"20240920","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 09:12:33","created":"2024-09-21 09:12:33","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 09:12:33","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"200.0","batchId":"19990101","businessId":"202409211550350124202409210","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211550350124202409210:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"2.0","batchId":"19990101","businessId":"202409211550350124202409210","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211550350124202409210:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":200.0,"businessId":"202409211550350124202409210","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":2,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"2.0","currency":"SETS","index":1,"type":"SETS","vipLevel":"NORMAL"},{"amount":"200.0","currency":"EXPERIENCE","index":3,"type":"EXPERIENCE","vipLevel":"NORMAL"}]}] |
      # L1
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":2,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | [{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":null,"taskId":"408007"}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007","vipLevel":"L1"}]}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-20 17:12:33","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 09:12:33","progress":3,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-20 17:12:33","created":"2024-09-20 17:12:33","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-20 17:12:33","status":"DONE","targetId":"20240920","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 09:12:33","created":"2024-09-21 09:12:33","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 09:12:33","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"200.0","batchId":"19990101","businessId":"202409211550350124202409210","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211550350124202409210:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"2.0","batchId":"19990101","businessId":"202409211550350124202409210","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211550350124202409210:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":200.0,"businessId":"202409211550350124202409210","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":2,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"2.0","currency":"SETS","index":2,"type":"SETS","vipLevel":"L1"},{"amount":"200.0","currency":"EXPERIENCE","index":4,"type":"EXPERIENCE","vipLevel":"L1"}]}]         |

  场景大纲: 连续签到4天,第四天获得题组2&经验奖励200
    假如task-初始化任务配置task_config<taskConfig>
    同时task-已存在主任务activity_task_item<primaryActivityTaskItem>
    同时task-已存在子任务activity_task_item<subActivityTaskItem>
    同时task-已存在customer_question_sets<questionSet>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | primaryActivityTaskItem                                                                                                                                                                                                                      | subActivityTaskItem                                                                                                                                                                                                                                                      | questionSet                                                                               | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | assetTransferInRequest                                                                                                                                                                                        | customerQuestionSet                                                                                                                | activityResponse                                                                                                                                                                                         |
      # NORMAL
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":3,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | [{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":null,"taskId":"408007"}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007"}}]                  | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-20 17:25:58","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 09:25:58","progress":4,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-20 17:25:58","created":"2024-09-20 17:25:58","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-20 17:25:58","status":"DONE","targetId":"20240920","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 09:25:58","created":"2024-09-21 09:25:58","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 09:25:58","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"200.0","batchId":"19990101","businessId":"202409211550350124202409210","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211550350124202409210:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"2.0","batchId":"19990101","businessId":"202409211550350124202409210","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211550350124202409210:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":200.0,"businessId":"202409211550350124202409210","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":2,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"2.0","currency":"SETS","index":1,"type":"SETS","vipLevel":"NORMAL"},{"amount":"200.0","currency":"EXPERIENCE","index":3,"type":"EXPERIENCE","vipLevel":"NORMAL"}]}] |
      # L1
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":3,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | [{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":null,"taskId":"408007"}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007","vipLevel":"L1"}]}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-20 17:25:58","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 09:25:58","progress":4,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-20 17:25:58","created":"2024-09-20 17:25:58","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-20 17:25:58","status":"DONE","targetId":"20240920","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 09:25:58","created":"2024-09-21 09:25:58","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 09:25:58","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"200.0","batchId":"19990101","businessId":"202409211550350124202409210","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211550350124202409210:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"2.0","batchId":"19990101","businessId":"202409211550350124202409210","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211550350124202409210:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":200.0,"businessId":"202409211550350124202409210","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":2,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"2.0","currency":"SETS","index":2,"type":"SETS","vipLevel":"L1"},{"amount":"200.0","currency":"EXPERIENCE","index":4,"type":"EXPERIENCE","vipLevel":"L1"}]}]         |

  场景大纲: 连续签到5天,第五天获得题组4&经验奖励500
    假如task-初始化任务配置task_config<taskConfig>
    同时task-已存在主任务activity_task_item<primaryActivityTaskItem>
    同时task-已存在子任务activity_task_item<subActivityTaskItem>
    同时task-已存在customer_question_sets<questionSet>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | primaryActivityTaskItem                                                                                                                                                                                                                      | subActivityTaskItem                                                                                                                                                                                                                                                      | questionSet                                                                               | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | assetTransferInRequest                                                                                                                                                                                        | customerQuestionSet                                                                                                                | activityResponse                                                                                                                                                                                         |
      # NORMAL
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":4,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | [{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":null,"taskId":"408007"}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007"}}]                  | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-20 17:32:05","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 09:32:05","progress":5,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-20 17:32:05","created":"2024-09-20 17:32:05","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-20 17:32:05","status":"DONE","targetId":"20240920","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 09:32:05","created":"2024-09-21 09:32:05","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 09:32:05","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"500.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211550350124202409211:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"4.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211550350124202409211:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":500.0,"businessId":"202409211550350124202409211","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":4,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"4.0","currency":"SETS","index":1,"type":"SETS","vipLevel":"NORMAL"},{"amount":"500.0","currency":"EXPERIENCE","index":3,"type":"EXPERIENCE","vipLevel":"NORMAL"}]}] |
      # L1
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":4,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | [{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":null,"taskId":"408007"}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007","vipLevel":"L1"}]}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-20 17:32:06","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 09:32:06","progress":5,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-20 17:32:06","created":"2024-09-20 17:32:06","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-20 17:32:06","status":"DONE","targetId":"20240920","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 09:32:06","created":"2024-09-21 09:32:06","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 09:32:06","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"500.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211550350124202409211:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"4.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211550350124202409211:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":500.0,"businessId":"202409211550350124202409211","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":4,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"4.0","currency":"SETS","index":2,"type":"SETS","vipLevel":"L1"},{"amount":"500.0","currency":"EXPERIENCE","index":4,"type":"EXPERIENCE","vipLevel":"L1"}]}]         |


  场景大纲: 连续签到6天,第六天获得题组1&经验奖励100——每5天一个签到周期,第6天重新开始计
    假如task-初始化任务配置task_config<taskConfig>
    同时task-已存在主任务activity_task_item<primaryActivityTaskItem>
    同时task-已存在子任务activity_task_item<subActivityTaskItem>
    同时task-已存在customer_question_sets<questionSet>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | primaryActivityTaskItem                                                                                                                                                                                                                      | subActivityTaskItem                                                                                                                                                                                                                                                      | questionSet                                                                               | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | assetTransferInRequest                                                                                                                                                                                        | customerQuestionSet                                                                                                                | activityResponse                                                                                                                                                                                         |
      # NORMAL
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":5,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | [{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":null,"taskId":"408007"}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007"}}]                  | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-20 17:46:03","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 09:46:03","progress":6,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-20 17:46:03","created":"2024-09-20 17:46:03","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-20 17:46:03","status":"DONE","targetId":"20240920","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 09:46:03","created":"2024-09-21 09:46:03","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 09:46:03","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"100.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211550350124202409211:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"1.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211550350124202409211:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":100.0,"businessId":"202409211550350124202409211","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":1,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"1.0","currency":"SETS","index":1,"type":"SETS","vipLevel":"NORMAL"},{"amount":"100.0","currency":"EXPERIENCE","index":3,"type":"EXPERIENCE","vipLevel":"NORMAL"}]}] |
      # L1
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":5,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | [{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":null,"taskId":"408007"}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007","vipLevel":"L1"}]}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-20 17:46:04","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 09:46:04","progress":6,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-20 17:46:04","created":"2024-09-20 17:46:04","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-20 17:46:04","status":"DONE","targetId":"20240920","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 09:46:04","created":"2024-09-21 09:46:04","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 09:46:04","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"100.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211550350124202409211:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"1.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211550350124202409211:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":100.0,"businessId":"202409211550350124202409211","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":1,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"1.0","currency":"SETS","index":2,"type":"SETS","vipLevel":"L1"},{"amount":"100.0","currency":"EXPERIENCE","index":4,"type":"EXPERIENCE","vipLevel":"L1"}]}]         |


  场景大纲: 连续签到2天,第3天断签,第4天重新计天数
    假如task-初始化任务配置task_config<taskConfig>
    同时task-已存在主任务activity_task_item<primaryActivityTaskItem>
    同时task-已存在子任务activity_task_item<subActivityTaskItem>
    同时task-已存在customer_question_sets<questionSet>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | primaryActivityTaskItem                                                                                                                                                                                                                      | subActivityTaskItem | questionSet                                                                               | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | assetTransferInRequest                                                                                                                                                                                        | customerQuestionSet                                                                                                                | activityResponse                                                                                                                                                                                         |
      # NORMAL
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":2,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | []                  | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007"}}]                  | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-20 18:02:53","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 10:02:53","progress":1,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 10:02:53","created":"2024-09-21 10:02:53","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 10:02:53","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"100.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211550350124202409211:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"1.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211550350124202409211:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":100.0,"businessId":"202409211550350124202409211","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":1,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"1.0","currency":"SETS","index":1,"type":"SETS","vipLevel":"NORMAL"},{"amount":"100.0","currency":"EXPERIENCE","index":3,"type":"EXPERIENCE","vipLevel":"NORMAL"}]}] |
      # L1
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","progress":2,"status":"APPENDING","targetId":"-1","taskId":"408007"}] | []                  | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007","vipLevel":"L1"}]}] | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-20 18:02:53","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 10:02:53","progress":1,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 10:02:53","created":"2024-09-21 10:02:53","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 10:02:53","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"100.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409211550350124202409211:EXPERIENCE","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"1.0","batchId":"19990101","businessId":"202409211550350124202409211","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409211550350124202409211:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":100.0,"businessId":"202409211550350124202409211","businessType":"SIGN_IN","category":"NORMAL","customerId":"2024033102484135902204","desc":"Daily Check-in","saasId":"ufool","type":"EXPERIENCE"}] | [{"availableSets":1,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS","obj":[{"amount":"1.0","currency":"SETS","index":2,"type":"SETS","vipLevel":"L1"},{"amount":"100.0","currency":"EXPERIENCE","index":4,"type":"EXPERIENCE","vipLevel":"L1"}]}]         |


  场景大纲: 连续签到1天,第一天获得题组1&经验奖励100,exp奖励发放失败,TCC数据回滚Test
    假如task-初始化任务配置task_config<taskConfig>
    同时activity-event-配置remoteAssetOperateService.transferIn异常
    同时task-已存在customer_question_sets<questionSet>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    那么luck-校验exceptions为<exceptions>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | questionSet                                                                               | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | assetTransferInRequest | customerQuestionSet                                                                                                                | activityResponse     | exceptions |
      # NORMAL
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007"}}]                  | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 07:50:35","progress":1,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"100.0","batchId":"19990101","businessId":"202409231123180890202409230","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409231123180890202409230:EXPERIENCE","status":"AWARD_FAILED","userType":"Normal"},{"amount":"1.0","batchId":"19990101","businessId":"202409231123180890202409230","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409231123180890202409230:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | []                     | [{"availableSets":1,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS"}] | null       |
      # L1
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007","vipLevel":"L1"}]}] | [{"businessId":"202409211550350491","completeThreshold":2147483647,"created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 07:50:35","progress":1,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350491","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"100.0","batchId":"19990101","businessId":"202409231123190294202409230","businessType":"sign_in","currency":"EXPERIENCE","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"EXPERIENCE","saasId":"ufool","seq":"sign_in:202409231123190294202409230:EXPERIENCE","status":"AWARD_FAILED","userType":"Normal"},{"amount":"1.0","batchId":"19990101","businessId":"202409231123190294202409230","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409231123190294202409230:SETS","status":"AWARD_SUCCESS","userType":"Normal"}] | []                     | [{"availableSets":1,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS"}] | null       |


  场景大纲: 连续签到1天,第一天获得题组1&经验奖励100,sets奖励发放失败,TCC数据回滚Test
    假如task-初始化任务配置task_config<taskConfig>
    同时activity-event-配置customerQuestionSetsBuilder.incrementAvailableSets异常
    同时task-已存在customer_question_sets<questionSet>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验customer_question_sets<customerQuestionSet>
    那么task-校验ActivityResponse<activityResponse>
    那么luck-校验exceptions为<exceptions>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | questionSet                                                                               | eventDTO                                                                                                                                                                                           | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                           | assetTransferInRequest | customerQuestionSet                                                                                                                | activityResponse     | exceptions |
      # NORMAL
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007"}}]                  | [{"businessId":"202409211550350124","completeThreshold":2147483647,"created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 07:50:35","progress":1,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350124","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"1.0","batchId":"19990101","businessId":"202409241417210994202409240","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409241417210994202409240:SETS","status":"AWARD_FAILED","userType":"Normal"}] | []                     | [{"availableSets":0,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS"}] | null       |
      # L1
      | [{"taskId":"408007","groupId":null,"isGroup":false,"showList":true,"title":"Daily Check-in","desc":"daily check in","labelName":"","labelColor":"","saasId":"ufool","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"sign_in","limitMap":{"NORMAL":-1,"L1":-1},"rewardFrequency":5,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"2":[{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"1","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"100","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"3":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"4":[{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"2","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"200","currency":"EXPERIENCE","vipLevel":"L1","index":4}],"5":[{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"NORMAL","index":1},{"type":"SETS","amount":"4","currency":"SETS","vipLevel":"L1","index":2},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"NORMAL","index":3},{"type":"EXPERIENCE","amount":"500","currency":"EXPERIENCE","vipLevel":"L1","index":4}]},"order":7,"domain":"social","vipLevel":"+0","btn":3,"showProgress":false,"channel":"pc,app","position":"task1","skipVerification":false,"callRegister":false}] | [{"saasId":"ufool","customerId":"2024033102484135902204","usedSets":0,"availableSets":0}] | [{"customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"taskId":"408007","vipLevel":"L1"}]}] | [{"businessId":"202409211550350491","completeThreshold":2147483647,"created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","expiredTime":"30000101","modified":"2024-09-21 07:50:35","progress":1,"status":"APPENDING","targetId":"-1","taskId":"408007"},{"businessId":"202409211550350491","completeTime":"2024-09-21 07:50:35","created":"2024-09-21 07:50:35","customerId":"2024033102484135902204","cycle":"19990101","event":"sign_in","modified":"2024-09-21 07:50:35","status":"DONE","targetId":"20240921","taskId":"408007"}] | [{"amount":"1.0","batchId":"19990101","businessId":"202409241417220119202409240","businessType":"sign_in","currency":"SETS","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Daily Check-in\"}","extendParamMap":{"desc":"Daily Check-in"},"rewardType":"SETS","saasId":"ufool","seq":"sign_in:202409241417220119202409240:SETS","status":"AWARD_FAILED","userType":"Normal"}] | []                     | [{"availableSets":0,"beginRewardTime":0,"customerId":"2024033102484135902204","rewardRemainTime":0,"saasId":"ufool","usedSets":0}] | [{"code":"SUCCESS"}] | null       |
