package com.kikitrade.activity.service.reward;/**
 * <AUTHOR>
 * @create 2021/9/7 6:21 下午
 * @modify
 */

import com.kikitrade.activity.api.model.ActivityResponse;
import com.kikitrade.activity.api.model.RewardsRequest;
import com.kikitrade.activity.api.model.request.InviteRewardsRequest;
import com.kikitrade.activity.api.model.request.InviteRewardsSumRequest;
import com.kikitrade.activity.api.model.request.InviteRewardsTotalRequest;
import com.kikitrade.activity.api.model.response.InviteRewardsResponse;
import com.kikitrade.activity.api.model.response.InviteRewardsSumResponse;
import com.kikitrade.activity.api.model.response.InviteRewardsTotalResponse;
import com.kikitrade.activity.dal.tablestore.model.CustomerReward;
import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.market.common.exception.MarketException;

import java.math.BigDecimal;
import java.util.List;

/**
 * ActivityRewardService
 *
 * <AUTHOR>
 * @create 2021/9/7 6:21 下午
 * @modify
 */
public interface ActivityRewardService {

    boolean save(CustomerReward reward);

    ActivityResponse fiatReward(List<CustomerReward> rewardList);

    PageResult findByCustomer(String customerId, int offset, int limit);

    Long countByCustomer(String customerId);

    Long countByCustomerAndType(String customerId, Integer type);

    Long totalRewardCount(String customerId, String currency, ActivityType type);

    Long countByInviteeAndType(String invitee, Integer type);

    BigDecimal getTotalReward(String customerId);

    BigDecimal totalReward(String customerId);

    BigDecimal totalReward(String customerId, String currency, ActivityType type);

    PageResult rewards(String customerId, ActivityType type, int offset, int limit);

    BigDecimal sumByCustomerAndType(String customerId, Integer type);

    BigDecimal sumByCustomerCurrencyAndType(String customerId, String currency, Integer type);

    /**
     * 被邀请奖励名单
     * @param customerId
     * @param offset
     * @param limit
     * @return
     */
    PageResult inviteRewards(String customerId, int offset, int limit);

    /**
     * 被邀请奖励人数
     * @param referId
     * @return
     */
    long inviteRewardCount(String referId);

    /**
     * 邀请人获取的总金额
     * @param customerId
     * @return
     */
    BigDecimal inviteRewardSum(String customerId);

    /**
     * 汇总新手任务上线之后的邀请类活动的奖励
     * @param customerId
     * @return
     */
    BigDecimal inviteRewardSumAfterTime(String customerId);

    PageResult rewards(RewardsRequest rewardsRequest, int offset, int limit);

    long countRewards(RewardsRequest rewardsRequest);

    /**
     * 查询邀请奖励总览
     * @param request
     * @return
     */
    InviteRewardsTotalResponse inviteRewardsTotal(InviteRewardsTotalRequest request) throws MarketException;

    /**
     * 按奖励类型查询邀请奖励明细
     * @param request
     * @return
     */
    InviteRewardsResponse inviteRewards(InviteRewardsRequest request) throws MarketException;

    /**
     * 按奖励类型查询奖励的总额
     * @param request
     * @return
     */
    InviteRewardsSumResponse inviteRewardsSumByType(InviteRewardsSumRequest request) throws MarketException;
}
