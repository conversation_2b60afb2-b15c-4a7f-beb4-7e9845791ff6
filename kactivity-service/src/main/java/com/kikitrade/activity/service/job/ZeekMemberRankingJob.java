package com.kikitrade.activity.service.job;

import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.NoticeService;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.common.config.RankingProperties;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.elasticjob.KiKiSimpleJob;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.model.QuestsLadderDTO;
import com.kikitrade.member.model.request.QuestLadderTokenPageRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/04 18:51
 */
@Component
@Slf4j
public class ZeekMemberRankingJob extends KiKiSimpleJob {

    @DubboReference(check = false)
    @Lazy
    private RemoteMemberService remoteMemberService;
    @Resource
    @Lazy
    private KactivityModuleProperties kactivityModuleProperties;
    @Resource
    @Lazy
    private RewardService rewardService;
    @Resource
    @Lazy
    private RedisService redisService;
    @Resource
    @Lazy
    private NoticeService noticeService;


    private static final String DINGTALK_URL = "https://oapi.dingtalk.com/robot/send?access_token=53f9e144057599dc640203a13f385167e12a757fdee7c0b45ec0a871c16eff52";

    @Override
    protected void doExecute(ShardingContext shardingContext) throws Exception {
        ex();
    }

    public void ex() {
        try {
            log.info("ZeekMemberRankingJob start");
            Map<String, RankingProperties.SeasonTime> seasonTime = kactivityModuleProperties.getRank().getZeekSeasonTime();
            Optional<Map.Entry<String, RankingProperties.SeasonTime>> entryOptional = seasonTime.entrySet().stream().filter(s -> s.getValue().getStartTime() <= OffsetDateTime.now().toEpochSecond() && s.getValue().getEndTime() > OffsetDateTime.now().toEpochSecond()).findFirst();
            if (entryOptional.isEmpty()) {
                log.info("ZeekMemberRankingJob not to season time");
                return;
            }
            Map.Entry<String, RankingProperties.SeasonTime> seasonTimeEntry = entryOptional.get();
            LocalDateTime seasonStartTime = new Date(seasonTimeEntry.getValue().getStartTime() * 1000).toInstant().atOffset(ZoneOffset.UTC).toLocalDate().atTime(0, 0, 0);
            long firstLadderTime = Date.from(seasonStartTime.with(TemporalAdjusters.next(DayOfWeek.THURSDAY)).toInstant(ZoneOffset.UTC)).getTime();
            if (OffsetDateTime.now().toEpochSecond() * 1000L < firstLadderTime + 10 * 60 * 1000) {
                log.info("ZeekMemberRankingJob not to time");
                return;
            }
            //计算获取cycle
            long c = 1;
            if (OffsetDateTime.now().toEpochSecond() * 1000L >= firstLadderTime) {
                c = ((OffsetDateTime.now().toEpochSecond() * 1000L - firstLadderTime) / 1000 / 60 / 60 / 24 / 7) + 2;
            }

            String cycle = String.format("%02d", c);
            QuestLadderTokenPageRequest request = new QuestLadderTokenPageRequest();
            request.setSaasId(kactivityModuleProperties.getRank().getZeekSaasId());
            request.setSeason(seasonTimeEntry.getKey());
            request.setLimit(kactivityModuleProperties.getRank().getLimit());
            request.setCycle(cycle);

            if (redisService.hHasKey(RedisKeyConst.ACTIVITY_ZEEK_MEMBER_RANK.getPrefix(), request.getSeason() + request.getCycle())) {
                log.info("ZeekMemberRankingJob, badge already reward,{}", request.getSeason() + request.getCycle());
                return;
            }
            for (int level = kactivityModuleProperties.getRank().getZeekLevel(); level >= 2; level--) {
                request.setLevel(level);
                TokenPage<QuestsLadderDTO> pageLadderResults = null;
                String nextToken = null;
                do {
                    request.setNextToken(nextToken);
                    pageLadderResults = remoteMemberService.pageLaddersByLevel(request);
                    if (CollectionUtils.isEmpty(pageLadderResults.getRows())) {
                        break;
                    }
                    if (nextToken == null) {
                        redisService.hSet(RedisKeyConst.ACTIVITY_ZEEK_MEMBER_RANK.getPrefix(), request.getSeason() + request.getCycle(), true);
                        String key = String.format("%s%s%s", RedisKeyConst.ACTIVITY_ZEEK_MEMBER_RANK.getPrefix(), request.getSeason(), request.getCycle());
                        noticeService.noticeDingTalk(key, DINGTALK_URL, "zeek 徽章开始发放");
                    }
                    nextToken = pageLadderResults.getNextToken();
                    reward(pageLadderResults.getRows(), seasonTimeEntry, c, cycle, level);
                } while (pageLadderResults.getNextToken() != null);
                String key = String.format("%s%s%s%s", request.getSaasId(), request.getSeason(), request.getCycle(), level);
                noticeService.noticeDingTalk(key, DINGTALK_URL, "zeek " + level + " 徽章发放完成");
            }
        } catch (Exception ex) {
            log.error("ZeekMemberRankingJob", ex);
        }
    }

    private void reward(List<QuestsLadderDTO> ladderDTOS, Map.Entry<String, RankingProperties.SeasonTime> seasonTimeEntry, long cycle, String cycleStr, int level) {
        if (CollectionUtils.isEmpty(ladderDTOS)) {
            return;
        }
        for (QuestsLadderDTO dto : ladderDTOS) {
            if (dto.getCustomerId().startsWith("0x")) {
                continue;
            }
            RewardRequest rewardRequest = RewardRequest.builder()
                    .customerId(dto.getCustomerId())
                    .rewardId(String.format("%s%03d%s", dto.getCustomerId(), Integer.parseInt(seasonTimeEntry.getKey().substring(seasonTimeEntry.getKey().length() - 1)), cycleStr))
                    .cycle(String.format("%02d", cycle - 1))
                    .level(level)
                    .seasonId(seasonTimeEntry.getKey().substring(6))
                    .amount(BigDecimal.ONE)
                    .type(ActivityConstant.AwardTypeEnum.BADGE.name())
                    .badgeType(ActivityConstant.BadgeTypeEnum.BADGE_CODE)
                    .saasId(kactivityModuleProperties.getRank().getZeekSaasId())
                    .build();
            try {
                log.info("[reward] ranking badge:{}", rewardRequest);
                rewardService.reward(rewardRequest);
            } catch (Exception ex) {
                log.error("[reward] ranking badge:{}", rewardRequest, ex);
            }
        }
    }
}
