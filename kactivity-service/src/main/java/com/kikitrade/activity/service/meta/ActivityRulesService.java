package com.kikitrade.activity.service.meta;

import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.dal.mysql.model.ActivityRules;

public interface ActivityRulesService {

    JsonResult save(ActivityRules activityRule) throws Exception;

    JsonResult delete(Integer id) throws Exception;

    JsonResult insert(ActivityRules activityRule) throws Exception;

    JsonResult update(ActivityRules activityRule) throws Exception;

    JsonResult findById(Integer id);

    JsonResult findAll();

}
