package com.kikitrade.activity.advice;

import com.kikitrade.activity.op.BusinessOP;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Aspect
@Slf4j
public class SlotLotteryServiceImplAdvice {
    @Pointcut("execution(* com.kikitrade.activity.service.lottery.LotteryService.getRandom())")
    public void getRandomPt() {
    }



    @Around("getRandomPt()")
    public Object getRandom(ProceedingJoinPoint pjp) throws Throwable {
        Object ret = pjp.proceed();
        //返回抽奖号

        if(BusinessOP.randomFlag){
            return BusinessOP.lotteryNum;
        }
        return ret;

    }

}
