<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityCustomerFeeDao">

    <select id="findAll" resultType="com.kikitrade.activity.dal.mysql.model.ActivityCustomerFee">
       SELECT *
        FROM activity_customer_fee
        WHERE tran_date= #{tran_date} and activity_type = #{type} order by customer_id asc
        LIMIT #{offset}, #{limit}
    </select>


    <select id="findByDateAndStatus" resultType="com.kikitrade.activity.dal.mysql.model.ActivityCustomerFee">
       SELECT *
        FROM activity_customer_fee
        WHERE tran_date= #{tran_date} and activity_type = #{activity_type} and status=#{status} order by customer_id asc
        LIMIT #{offset}, #{limit}
    </select>


    <select id="findByDate" resultType="com.kikitrade.activity.dal.mysql.model.ActivityCustomerFee">
       SELECT *
        FROM activity_customer_fee
        WHERE tran_date= #{tran_date} order by customer_id asc
        LIMIT #{offset}, #{limit}
    </select>


     <select id="findCurrencyByDate" resultType="com.kikitrade.activity.dal.mysql.model.ActivityCustomerFee">
       SELECT *
        FROM activity_customer_fee
        WHERE tran_date= #{tran_date} and currency=#{currency} order by customer_id asc
        LIMIT #{offset}, #{limit}
    </select>




    <update id="updateStatus">
        UPDATE activity_customer_fee
        SET  status = #{status}
        WHERE tran_date= #{tran_date} and customer_id= #{customer_id}
         and currency = #{currency} and activity_type = #{type} and status=0
    </update>

    <select id="countByTypeAndTye" resultType="java.lang.Long">
       SELECT COUNT(*)
        FROM activity_customer_fee
        WHERE tran_date= #{tran_date} and activity_type = #{type} and status = #{status}
    </select>

</mapper>
