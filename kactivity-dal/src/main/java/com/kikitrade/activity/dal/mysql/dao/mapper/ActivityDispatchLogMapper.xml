<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityDispatchLogDao">

    <resultMap type="com.kikitrade.activity.dal.mysql.model.ActivityDispatchLog" id="ActivityDispatchLog">
        <result column="created" property="created" javaType="java.util.Date"/>
        <result column="modified" property="modified" javaType="java.util.Date"/>
        <result column="tran_date" property="tranDate" javaType="java.lang.String"/>
        <result column="dispatch_status" property="dispatchStatus" javaType="java.lang.Integer"/>
        <result column="activity_id" property="activityId" javaType="java.lang.Integer"/>
        <result column="execute_type" property="executeType" javaType="java.lang.Integer"/>
        <result column="status" property="status" javaType="java.lang.Integer"/>
        <result column="limit" property="limit" javaType="java.lang.Integer"/>
        <result column="deadline" property="deadline" javaType="java.util.Date"/>
        <result column="notifyUrl" property="notifyUrl" javaType="java.lang.String"/>
    </resultMap>


    <!-- 增加 -->
    <insert id="insert" parameterType="com.kikitrade.activity.dal.mysql.model.ActivityDispatchLog">
        INSERT INTO activity_dispatch_log VALUES (
            NOW(), NOW(),
            #{tranDate},
            #{dispatchStatus},
            #{activityId},
            #{executeType},
            #{status},
            #{limit},
            #{deadline},
            #{notifyUrl}
        )
    </insert>


    <update id="updateStatus">
        UPDATE activity_dispatch_log
        SET modified = NOW(), dispatch_status = #{dispatch_status}
        WHERE tran_date = #{tran_date} and activity_id=#{activity_id}
    </update>

    <select id="findAll" resultMap="ActivityDispatchLog">
        SELECT *
        FROM activity_dispatch_log where dispatch_status !=2
    </select>

    <select id="findByActivityId" resultMap="ActivityDispatchLog">
        SELECT *
        FROM activity_dispatch_log  WHERE tran_date = #{tran_date} and activity_id=#{activity_id}
    </select>

</mapper>
