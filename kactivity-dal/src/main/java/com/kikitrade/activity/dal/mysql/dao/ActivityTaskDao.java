package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ActivityTaskDao extends BaseDao<ActivityTaskConfig, String> {

}