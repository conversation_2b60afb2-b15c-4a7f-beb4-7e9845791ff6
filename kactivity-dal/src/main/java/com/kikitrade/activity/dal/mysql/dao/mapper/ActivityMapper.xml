<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityDao">


    <!-- 增加 -->
    <!--<insert id="insert" parameterType="com.kikitrade.activity.model.Activity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO activity VALUES (
            #{id}, NOW(), NOW(),
                   #{type},
                   #{name},
                   #{url},
                   #{content},
                   #{start_time},
                   #{end_time},
                   #{is_need_audit},
                   #{top_order},
            #{execute_type},
            #{is_push},
            #{status},
            #{apply_times},
            #{publish_user_id}
        )
    </insert>-->

    <!-- 修改 -->
    <update id="update" parameterType="com.kikitrade.activity.dal.mysql.model.Activity">
        update activity
        <set>
            modified=NOW(),
            <if test="type!=null">
                type=#{type},
            </if>
            <if test="name!=null">
                name=#{name},
            </if>
            url=#{url},
            content=#{content},
            <if test="start_time!=null">
                start_time=#{start_time},
            </if>
            <if test="end_time!=null">
                end_time=#{end_time},
            </if>
            <if test="is_need_audit!=null">
                is_need_audit=#{is_need_audit},
            </if>
            <if test="top_order!=null">
                top_order=#{top_order},
            </if>
            <if test="execute_type!=null">
                execute_type=#{execute_type},
            </if>

            <if test="is_push!=null">
                is_push=#{is_push},
            </if>
            <!--<if test="status!=null">-->
            <!--status=#{status},-->
            <!--</if>-->
            <if test="apply_times!=null">
                apply_times=#{apply_times},
            </if>
            <if test="publish_user_id">
                publish_user_id=#{publish_user_id}
            </if>

        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM activity
        WHERE id = #{id}
    </delete>


    <select id="findAll" resultType="com.kikitrade.activity.dal.mysql.model.Activity">
        SELECT *
        FROM activity
        where 1=1
        <if test="flag!=true">
            and status !=5
        </if>
        <if test="type!=null">
            and type =#{type}
        </if>
        <if test="saasId!=null">
            and saas_id =#{saasId}
        </if>
        ORDER BY created DESC
        <if test="offset!=null and limit!=null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>


    <select id="findAllForRedis" resultType="com.kikitrade.activity.dal.mysql.model.Activity">
        SELECT
            id,
            type,
            start_time,
            end_time,
            is_need_audit,
            execute_type,
            is_push,
            status,
            apply_times,
            saas_id
        FROM activity
        WHERE status = 2
        ORDER BY id ASC

    </select>


    <select id="findAllByStatus" resultType="com.kikitrade.activity.dal.mysql.model.Activity">
        SELECT *
        FROM activity
        <if test="status!=null">
            where status =#{status}
        </if>
        <if test="saasId!=null">
            and saas_id =#{saasId}
        </if>
        ORDER BY created ASC
        <if test="offset!=null and limit!=null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>


    <select id="findAllByPara" resultType="com.kikitrade.activity.dal.mysql.model.Activity">
        SELECT *
        FROM activity
        <if test="execute_type!=null">
            where execute_type =#{execute_type}
        </if>
        <if test="status!=null">
            and status =#{status}
        </if>
        <if test="saasId!=null">
            and saas_id =#{saasId}
        </if>
        ORDER BY created ASC
        <if test="offset!=null and limit!=null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="findForStatsUpdate" resultType="com.kikitrade.activity.dal.mysql.model.Activity">
        SELECT *
        FROM activity
        WHERE status != 5
              AND status != 4
        ORDER BY id ASC
    </select>


    <update id="updateStatus">
        UPDATE activity
        SET modified = NOW(), status = #{status}
        WHERE id = #{id}
    </update>


    <select id="findById" resultType="com.kikitrade.activity.dal.mysql.model.Activity">
        SELECT *
        FROM activity
        WHERE id = #{id}
    </select>


    <select id="queryByType2" resultType="com.kikitrade.activity.dal.mysql.model.Activity">
       SELECT   *
        FROM  activity
        WHERE type = 2
        <if test="saasId!=null">
            and saas_id =#{saasId}
        </if>
        ORDER BY   id DESC
          LIMIT 0, 1
    </select>

    <select id="queryActivityByTypeAndStatus" resultType="com.kikitrade.activity.dal.mysql.model.Activity">
        SELECT *
        FROM activity
        WHERE type = #{type}
        <if test="saasId!=null">
            and saas_id =#{saasId}
        </if>
        and status in
        <foreach collection="statusList" index="index" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        ORDER BY id DESC
        LIMIT 0, 1
    </select>


    <select id="idList" resultType="com.kikitrade.activity.dal.mysql.model.Activity">
       SELECT  id ,name FROM activity
       WHERE status != 5 AND status != 4
        <if test="saasId!=null">
            and saas_id =#{saasId}
        </if>
       order by id desc
    </select>

</mapper>
