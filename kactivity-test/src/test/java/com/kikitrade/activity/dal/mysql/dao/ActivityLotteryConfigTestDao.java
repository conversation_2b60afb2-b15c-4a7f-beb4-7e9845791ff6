package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.ActivityLotteryConfig;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ActivityLotteryConfigTestDao{

    void createTableIfNotExist();

    void init();

    ActivityLotteryConfig select();

    List<ActivityLotteryConfig> selectAll();

    @Delete("truncate activity_lottery_config")
    void truncate();


}
