package com.kikitrade.activity.api;

import com.kikitrade.activity.api.model.LotteryConfigDTO;
import com.kikitrade.activity.api.model.LotteryResponse;
import com.kikitrade.activity.api.model.request.LotteryWinnersRequest;
import com.kikitrade.activity.api.model.response.LotteryCountResponse;
import com.kikitrade.activity.api.model.response.LotteryWinnersResponse;

public interface RemoteLotteryService {

    /**
     * 抽奖
     * @param now
     * @param customerId
     * @return
     */
    LotteryResponse lottery(String customerId, String code, String saasId) throws Exception;

    /**
     * 奖池信息
     * @param code
     * @param saasId
     * @return
     */
    LotteryConfigDTO lotteryInfo(String code, String saasId);

    /**
     * 最近抽奖次数
     * @param code
     * @param customerId
     * @return
     */
    Long lotteryCount(String customerId, String code, String saasId);

    /**
     * 最近抽奖次数 & 历史抽奖次数
     * @param code
     * @param customerId
     * @return
     */
    LotteryCountResponse lotteryCountDetail(String customerId, String code, String saasId);

    /**
     * 获奖者名单
     * @param saasId
     * @param code
     */
    LotteryWinnersResponse listLotteryWinners(String saasId, String code);

    /**
     * 保存中奖名单信息
     * @param request
     * @return
     */
    boolean saveLotteryWinners(LotteryWinnersRequest request);

    /**
     * 删除中奖名单信息
     * @param request
     * @return
     */
    boolean deleteLotteryWinners(LotteryWinnersRequest request);
}
