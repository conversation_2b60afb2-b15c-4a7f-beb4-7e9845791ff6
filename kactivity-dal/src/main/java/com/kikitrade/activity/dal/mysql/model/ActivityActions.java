package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/7/11 17:07
 */
@Getter
@Setter
@Table(name = "activity_actions")
public class ActivityActions extends BaseModel {

    @Id
    //操作id（主键）
    //@GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Integer id;  //id


    //activity type
    @Column(name = "activity_type", nullable = false)
    private Integer activity_type;

    //创建时间
    @Column(name = "create_time", nullable = false)
    private Date create_time;
    //更新时间
    @Column(name = "update_time", nullable = false)
    private Date update_time;

    //action_name for redis use
    @Column(name = "action_name", nullable = false)
    private String action_name;

    //操作描述，用于管理端新建活动时展示
    @Column(name = "desc", nullable = false)
    private String desc;

    //操作优先级，哪些操作优先执行
    @Column(name = "priority", nullable = false)
    private Integer priority;

    //0-失效 1-生效
    @Column(name = "status",  nullable = false)
    private Integer status;

    //操作参数，用于个性化配置活动奖励发放，Json存储
    @Column(name = "params")
    private String params;


}
