package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.RebateTransaction;

import java.math.BigDecimal;
import java.util.List;

/**
 * created by jacky. 2020/1/19 4:55 PM
 */
public interface RebateTransactionBuilder {
    Boolean insertIfNotExist(RebateTransaction rebateTransaction);

    BigDecimal getTotalReward(String customerId);

    long getRewardCount(String referredId);

    List<RebateTransaction> selectByRefferId(String referredId, int startIndex, int totalNum);
}
