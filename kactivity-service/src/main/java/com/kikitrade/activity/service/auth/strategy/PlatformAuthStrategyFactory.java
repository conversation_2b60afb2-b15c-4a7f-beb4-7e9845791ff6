package com.kikitrade.activity.service.auth.strategy;

import com.kikitrade.activity.service.auth.strategy.impl.*;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台认证策略工厂
 * 根据平台类型返回对应的认证策略实现
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
public class PlatformAuthStrategyFactory {

    private final Map<String, PlatformAuthStrategy> strategyMap = new HashMap<>();

    public PlatformAuthStrategyFactory(List<PlatformAuthStrategy> strategies) {
        // 初始化策略映射
        for (PlatformAuthStrategy strategy : strategies) {
            strategyMap.put(strategy.getPlatformName(), strategy);
            log.info("注册认证策略: {}", strategy.getPlatformName());
        }
    }

    /**
     * 根据平台名称获取对应的认证策略
     * @param platform 平台名称
     * @return 认证策略实现
     */
    public PlatformAuthStrategy getStrategy(String platform) {
        PlatformAuthStrategy strategy = strategyMap.get(platform);
        if (strategy == null) {
            log.warn("未找到平台 {} 对应的认证策略", platform);
        }
        return strategy;
    }

    /**
     * 根据平台枚举获取对应的认证策略
     * @param platformEnum 平台枚举
     * @return 认证策略实现
     */
    public PlatformAuthStrategy getStrategy(OpenSocialPlatformEnum platformEnum) {
        return getStrategy(platformEnum.name());
    }

    /**
     * 检查是否支持指定平台
     * @param platform 平台名称
     * @return true表示支持，false表示不支持
     */
    public boolean isSupported(String platform) {
        return strategyMap.containsKey(platform);
    }

    /**
     * 获取所有支持的平台
     * @return 支持的平台列表
     */
    public java.util.Set<String> getSupportedPlatforms() {
        return strategyMap.keySet();
    }
}
