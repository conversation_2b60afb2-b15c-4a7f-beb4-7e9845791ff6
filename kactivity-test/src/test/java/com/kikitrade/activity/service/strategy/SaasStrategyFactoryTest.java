package com.kikitrade.activity.service.strategy;

import com.kikitrade.activity.service.common.strategy.SaasStrategyFactory;
import com.kikitrade.activity.service.common.strategy.email.EmailSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.facebook.FacebookSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.line.LineSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.telegram.TelegramSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.wallet.WalletSaasStrategyService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import jakarta.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2025/7/22 19:10
 * @description: SaaS策略工厂测试
 */
@SpringBootTest
@TestPropertySource(locations = "classpath:application-junit.properties")
public class SaasStrategyFactoryTest {

    @Resource
    private SaasStrategyFactory saasStrategyFactory;

    @Test
    public void testGetFacebookService() {
        // 测试获取Facebook服务
        FacebookSaasStrategyService facebookService = saasStrategyFactory.getFacebookService("REJECT");
        assertNotNull(facebookService);
        assertEquals("REJECT", facebookService.strategy());
    }

    @Test
    public void testGetLineService() {
        // 测试获取Line服务
        LineSaasStrategyService lineService = saasStrategyFactory.getLineService("REJECT");
        assertNotNull(lineService);
        assertEquals("REJECT", lineService.strategy());
    }

    @Test
    public void testGetEmailService() {
        // 测试获取Email服务
        EmailSaasStrategyService emailService = saasStrategyFactory.getEmailService("REJECT");
        assertNotNull(emailService);
        assertEquals("REJECT", emailService.strategy());
    }

    @Test
    public void testGetTelegramService() {
        // 测试获取Telegram服务
        TelegramSaasStrategyService telegramService = saasStrategyFactory.getTelegramService("REJECT");
        assertNotNull(telegramService);
        assertEquals("REJECT", telegramService.strategy());
    }

    @Test
    public void testGetWalletService() {
        // 测试获取Wallet服务
        WalletSaasStrategyService walletService = saasStrategyFactory.getWalletService("REJECT");
        assertNotNull(walletService);
        assertEquals("REJECT", walletService.strategy());
    }

    @Test
    public void testGetSkipServices() {
        // 测试获取跳过策略服务
        FacebookSaasStrategyService facebookSkipService = saasStrategyFactory.getFacebookService("SKIP");
        assertNotNull(facebookSkipService);
        assertEquals("SKIP", facebookSkipService.strategy());

        LineSaasStrategyService lineSkipService = saasStrategyFactory.getLineService("SKIP");
        assertNotNull(lineSkipService);
        assertEquals("SKIP", lineSkipService.strategy());

        EmailSaasStrategyService emailSkipService = saasStrategyFactory.getEmailService("SKIP");
        assertNotNull(emailSkipService);
        assertEquals("SKIP", emailSkipService.strategy());

        TelegramSaasStrategyService telegramSkipService = saasStrategyFactory.getTelegramService("SKIP");
        assertNotNull(telegramSkipService);
        assertEquals("SKIP", telegramSkipService.strategy());

        WalletSaasStrategyService walletSkipService = saasStrategyFactory.getWalletService("SKIP");
        assertNotNull(walletSkipService);
        assertEquals("SKIP", walletSkipService.strategy());
    }

    @Test
    public void testGetNonExistentStrategy() {
        // 测试获取不存在的策略
        FacebookSaasStrategyService nonExistentService = saasStrategyFactory.getFacebookService("NON_EXISTENT");
        assertNull(nonExistentService);
    }
}
