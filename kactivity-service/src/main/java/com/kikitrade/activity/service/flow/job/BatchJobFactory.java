package com.kikitrade.activity.service.flow.job;

import org.apache.dubbo.config.annotation.DubboReference;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityBatchService;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.kcustomer.api.model.DingTalkMessageDTO;
import com.kikitrade.kcustomer.api.service.RemoteNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.*;
import org.springframework.batch.core.launch.JobOperator;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-08 14:27
 */
@Component
@Slf4j
public class BatchJobFactory {

    @Resource
    private Job manualRosterJob;
    @Resource
    private Job manualRewardJob;
    @Resource
    private Job semiAutoMaticRewardJob;
    @Resource
    private Job autoMaticRewardJob;
    @Resource
    private SimpleJobLauncher activityJobLauncher;
    @DubboReference
    private RemoteNotificationService remoteNotificationService;
    @Resource
    private KactivityModuleProperties kactivityModuleProperties;
    @Resource
    @Lazy
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private JobOperator jobOperator;

    private Map<String, Job> map = new HashMap<>();

    @PostConstruct
    public void init(){
        map.put(BatchConstantName.JobBeanName.MANUAL_ROSTER, manualRosterJob);
        map.put(BatchConstantName.JobBeanName.MANUAL_REWARD, manualRewardJob);
        map.put(BatchConstantName.JobBeanName.SEMI_AUTO_MATIC_REWARD, semiAutoMaticRewardJob);
        map.put(BatchConstantName.JobBeanName.AUTO_MATIC_REWARD, autoMaticRewardJob);
    }

    public Job getJob(String beanName){
        return map.get(beanName);
    }

    /**
     * 全自动发奖
     * @param activityId
     * @param batchId
     */
    public void runAutoMaticRewardJob(String activityId, String batchId){
        JobParameters parameters = new JobParametersBuilder()
                .addString("batchId", batchId)
                .addString("activityId", activityId)
                .addLong("version", System.currentTimeMillis())
                .toJobParameters();
        try {
            JobExecution jobExecution = activityJobLauncher.run(getJob(BatchConstantName.JobBeanName.AUTO_MATIC_REWARD), parameters);
            Collection<StepExecution> stepExecutions = jobExecution.getStepExecutions();
            if(stepExecutions.stream().anyMatch(stepExecution -> stepExecution.getExitStatus().compareTo(ExitStatus.COMPLETED) != 0)){
                if(jobExecution.getJobParameters().getLong("version") != null
                        && System.currentTimeMillis() - jobExecution.getJobParameters().getLong("version") < 30 * 60 * 1000){
                    TimeUnit.SECONDS.sleep(10);
                    log.info("runAutoMaticRewardJob restart, parameters:{}", parameters);
                    long instanceId = jobExecution.getJobInstance().getInstanceId();
                    jobOperator.restart(instanceId);
                }else{
                    notifyFailed(batchId);
                }
            }
        }catch (Exception e) {
            notifyFailed(batchId);
        }
    }

    /**
     * 手动导入数据
     * @param activityId
     * @param batchId
     */
    public void runManualRosterJob(String activityId, String batchId){
        JobParameters parameters = new JobParametersBuilder()
                .addString("batchId", batchId)
                .addString("activityId", activityId)
                .addLong("version", System.currentTimeMillis())
                .toJobParameters();
        try {
            JobExecution jobExecution = activityJobLauncher.run(getJob(BatchConstantName.JobBeanName.MANUAL_ROSTER), parameters);
            Collection<StepExecution> stepExecutions = jobExecution.getStepExecutions();
            if(stepExecutions.stream().anyMatch(stepExecution -> stepExecution.getExitStatus().compareTo(ExitStatus.COMPLETED) != 0)){
                if(jobExecution.getJobParameters().getLong("version") != null
                        && System.currentTimeMillis() - jobExecution.getJobParameters().getLong("version")  < 30 * 60 * 1000){
                    TimeUnit.SECONDS.sleep(10);
                    log.info("runManualRosterJob restart, parameters:{}", parameters);
                    long instanceId = jobExecution.getJobInstance().getInstanceId();
                    jobOperator.restart(instanceId);
                }
            }
        } catch (Exception e) {
            log.error("spring batch task exception: {}", batchId, e);
        }
    }

    /**
     * 手动发奖
     * @param activityId
     * @param batchId
     */
    public void runManualRewardJob(String activityId, String batchId){
        JobParameters parameters = new JobParametersBuilder()
                .addString("batchId", batchId)
                .addString("activityId", activityId)
                .addLong("version", System.currentTimeMillis())
                .toJobParameters();
        try {
            JobExecution jobExecution = activityJobLauncher.run(getJob(BatchConstantName.JobBeanName.MANUAL_REWARD), parameters);
            Collection<StepExecution> stepExecutions = jobExecution.getStepExecutions();
            if(stepExecutions.stream().anyMatch(stepExecution -> stepExecution.getExitStatus().compareTo(ExitStatus.COMPLETED) != 0)){
                log.info("runManualRewardJob druid:{}", System.currentTimeMillis() - jobExecution.getJobParameters().getLong("version"));
                if(jobExecution.getJobParameters().getLong("version") != null
                        && System.currentTimeMillis() - jobExecution.getJobParameters().getLong("version") < 30 * 60 * 1000){
                    log.info("runManualRewardJob restart, parameters:{}", parameters);
                    TimeUnit.SECONDS.sleep(10);
                    long instanceId = jobExecution.getJobInstance().getInstanceId();
                    jobOperator.restart(instanceId);
                }
            }
        } catch (Exception e) {
            notifyFailed(batchId);
            log.error("spring batch task exception: {}", batchId, e);
        }
    }

    /**
     * 半自动发奖
     * @param activityId
     * @param batchId
     */
    public void runSemiAutoMaticRewardJob(String activityId, String batchId){
        JobParameters parameters = new JobParametersBuilder()
                .addString("batchId", batchId)
                .addString("activityId", activityId)
                .addLong("version", System.currentTimeMillis())
                .toJobParameters();
        try {
            JobExecution jobExecution = activityJobLauncher.run(getJob(BatchConstantName.JobBeanName.SEMI_AUTO_MATIC_REWARD), parameters);
            Collection<StepExecution> stepExecutions = jobExecution.getStepExecutions();
            if(stepExecutions.stream().anyMatch(stepExecution -> stepExecution.getExitStatus().compareTo(ExitStatus.COMPLETED) != 0)){
                if(jobExecution.getJobParameters().getLong("version") != null
                        && System.currentTimeMillis() - jobExecution.getJobParameters().getLong("version") < 30 * 60 * 1000){
                    TimeUnit.SECONDS.sleep(10);
                    log.info("runSemiAutoMaticRewardJob restart, parameters:{}", parameters);
                    long instanceId = jobExecution.getJobInstance().getInstanceId();
                    jobOperator.restart(instanceId);
                }
            }
        } catch (Exception e) {
            log.error("spring batch task exception: {}", batchId, e);
        }
    }

    private void notifyFailed(String batchId){
        ActivityBatch activityBatch = activityBatchNewService.findByBatchId(batchId);
        DingTalkMessageDTO dto = new DingTalkMessageDTO();
        dto.setNotifyUrl(kactivityModuleProperties.getReward().getDingTalkUrl());
        dto.setMessage(String.format("批次：%s(%s), 发奖失败或生成发奖文件失败", activityBatch.getName(), activityBatch.getBatchId()));
        remoteNotificationService.send(dto);
    }
}
