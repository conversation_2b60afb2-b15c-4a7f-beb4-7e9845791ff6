package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@Data
@Table(name = "luck_fortune_refund_item")
public class LuckFortuneRefundItem implements Serializable {

    public static final String INDEX_FORTUNE_REFUND_BY_STATUS = "idx_fortune_refund_by_status";

    /**
     * 冻结的业务id
     */
    @PartitionKey(name = "origin_business_id")
    @SearchIndex(name = INDEX_FORTUNE_REFUND_BY_STATUS, column = "origin_business_id")
    private String originBusinessId;

    /**
     * 解冻的业务约束
     */
    @PartitionKey(name = "business_id", value = 1)
    @SearchIndex(name = INDEX_FORTUNE_REFUND_BY_STATUS, column = "business_id")
    private String businessId;

    /**
     * 用户id
     */
    @Column(name = "customer_id", isDefined = true)
    @SearchIndex(name = INDEX_FORTUNE_REFUND_BY_STATUS, column = "customer_id")
    private String customerId;

    /**
     * 解冻的业务id
     */
    @Column(name = "refund_id", isDefined = true)
    @SearchIndex(name = INDEX_FORTUNE_REFUND_BY_STATUS, column = "refund_id")
    private String refundId;

    /**
     * 解冻金额
     */
    @Column(name = "amount", isDefined = true)
    @SearchIndex(name = INDEX_FORTUNE_REFUND_BY_STATUS, column = "amount")
    private String amount;

    /**
     * 解冻币种
     */
    @Column(name = "currency", isDefined = true)
    @SearchIndex(name = INDEX_FORTUNE_REFUND_BY_STATUS, column = "currency")
    private String currency;

    /**
     * 退款原因
     */
    @Column(name = "message", isDefined = true)
    private String message;

    /**
     * 退款时间
     */
    @Column(name = "refund_time", isDefined = true)
    private String refundTime;

    /**
     * 退款状态
     */
    @Column(name = "status", isDefined = true, type = Column.Type.INTEGER)
    @SearchIndex(name = INDEX_FORTUNE_REFUND_BY_STATUS, column = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created", isDefined = true)
    private String created;

    /**
     * 修改时间
     */
    @Column(name = "modified", isDefined = true)
    private String modified;
}
