package com.kikitrade.activity.service.flow.job;

import com.alibaba.druid.DbType;
import com.aliyun.odps.data.Record;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.service.flow.step.listener.OdpsListener;
import com.kikitrade.activity.service.flow.step.listener.RewardListener;
import com.kikitrade.activity.service.flow.step.listener.RosterListener;
import com.kikitrade.activity.service.flow.step.processor.OdpsProcessor;
import com.kikitrade.activity.service.flow.step.processor.RewardProcessor;
import com.kikitrade.activity.service.flow.step.processor.RosterProcessor;
import com.kikitrade.activity.service.flow.step.reader.OdpsReader;
import com.kikitrade.activity.service.flow.step.reader.RewardReader;
import com.kikitrade.activity.service.flow.step.reader.RosterReader;
import com.kikitrade.activity.service.flow.step.write.OdpsWriter;
import com.kikitrade.activity.service.flow.step.write.RewardWriter;
import com.kikitrade.activity.service.flow.step.write.RosterWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.support.DefaultBatchConfiguration;
import org.springframework.batch.core.configuration.support.JobRegistryBeanPostProcessor;
import org.springframework.batch.core.configuration.support.MapJobRegistry;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.dao.DefaultExecutionContextSerializer;
import org.springframework.batch.core.repository.support.JobRepositoryFactoryBean;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.PlatformTransactionManager;

import jakarta.annotation.Resource;
import javax.sql.DataSource;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-02 14:59
 */
@Configuration
@EnableBatchProcessing
@Slf4j
public class BatchJobConfiguration extends DefaultBatchConfiguration {

    @Resource
    private OdpsReader odpsReader;
    @Resource
    private OdpsWriter odpsWriter;
    @Resource
    private OdpsProcessor odpsProcessor;
    @Resource
    private OdpsListener odpsListener;
    @Resource
    private RosterReader rosterReader;
    @Resource
    private RosterWriter rosterWrite;
    @Resource
    private RosterProcessor rosterProcessor;
    @Resource
    private RosterListener rosterListener;
    @Resource
    private RewardListener rewardListener;
    @Resource
    private RewardWriter rewardWriter;
    @Resource
    private RewardReader rewardReader;
    @Resource
    private RewardProcessor rewardProcessor;

    /**
     * JobLauncher定义，用来启动Job的接口
     * @param activityJobRepository
     * @return
     * @throws Exception
     */
    @Bean
    public SimpleJobLauncher activityJobLauncher(JobRepository activityJobRepository) throws Exception{
        try{
            SimpleJobLauncher jobLauncher = new SimpleJobLauncher();
            jobLauncher.setJobRepository(activityJobRepository);
            jobLauncher.setTaskExecutor(new SimpleAsyncTaskExecutor());
            return jobLauncher;
        }catch (Exception ex){
            log.error("activityJobLauncher init exception", ex);
            throw ex;
        }
    }

    /**
     * 持久化配置
     * @param dataSource
     * @param transactionManager
     * @return
     * @throws Exception
     */
    @Bean
    @Lazy
    public JobRepository activityJobRepository(DataSource dataSource, PlatformTransactionManager transactionManager) throws Exception{
        JobRepositoryFactoryBean factoryBean = new JobRepositoryFactoryBean();
        factoryBean.setTransactionManager(transactionManager);
        factoryBean.setDatabaseType(DbType.mysql.name());
        factoryBean.setDataSource(dataSource);
        factoryBean.setTablePrefix("kactivity_batch_");
        factoryBean.afterPropertiesSet();
        return factoryBean.getObject();
    }

    /**
     * job工厂类
     * @param activityJobRepository
     * @return
     */
    @Bean
    public JobBuilderFactory jobBuilderFactory(@Qualifier("activityJobRepository") JobRepository activityJobRepository){
        return new JobBuilderFactory(activityJobRepository);
    }

    @Bean
    public MapJobRegistry mapJobRegistry(){

        return new MapJobRegistry();
    }

    @Bean
    public JobRegistryBeanPostProcessor jobRegistryBeanPostProcessor(MapJobRegistry mapJobRegistry){
        JobRegistryBeanPostProcessor jobRegistryBeanPostProcessor = new JobRegistryBeanPostProcessor();
        jobRegistryBeanPostProcessor.setJobRegistry(mapJobRegistry);
        return jobRegistryBeanPostProcessor;
    }

    /**
     * 手动发奖--导入发奖名单到reward
     * @return
     */
    @Bean("manualRosterJob")
    public Job ManualRosterJob(JobBuilderFactory jobBuilderFactory, @Qualifier("rosterStep") Step rosterStep){
        log.info("manualRosterJob init:{}", rosterStep);
        return jobBuilderFactory.get(BatchConstantName.JobBeanName.MANUAL_ROSTER)
                .start(rosterStep)
                .build();
    }

    /**
     * 手动发奖--发奖
     * @return
     */
    @Bean("manualRewardJob")
    public Job ManualRewardJob(JobBuilderFactory jobBuilderFactory, @Qualifier("rewardStep") Step rewardStep){
        log.info("manualRewardJob init:{}", rewardStep);
        return jobBuilderFactory.get(BatchConstantName.JobBeanName.MANUAL_REWARD)
                .incrementer(new RunIdIncrementer())
                .start(rewardStep)
                .build();
    }

    /**
     * 半自动发奖
     * @return
     */
    @Bean("semiAutoMaticRewardJob")
    public Job SemiAutoMaticRewardJob(JobBuilderFactory jobBuilderFactory, @Qualifier("odpsStep") Step odpsStep, @Qualifier("rosterStep") Step rosterStep){
        return jobBuilderFactory.get(BatchConstantName.JobBeanName.SEMI_AUTO_MATIC_REWARD)
                .incrementer(new RunIdIncrementer())
                .start(odpsStep)//从odps拉取数据到roster
                .next(rosterStep)//从roster到reward
                .build();
    }

    /**
     * 自动发奖
     * @return
     */
    @Bean("autoMaticRewardJob")
    public Job AutoMaticRewardJob(JobBuilderFactory jobBuilderFactory, @Qualifier("odpsStep") Step odpsStep, @Qualifier("rosterStep") Step rosterStep, @Qualifier("rewardStep") Step rewardStep) {
        return jobBuilderFactory.get(BatchConstantName.JobBeanName.AUTO_MATIC_REWARD)
                .incrementer(new RunIdIncrementer())
                .start(odpsStep)//从odps拉取数据到roster
                .next(rosterStep)//从roster到reward
                .next(rewardStep)//调用发奖接口发奖
                .build();
    }

    /**
     * odps -> roster
     * @return
     */
    @Bean("odpsStep")
    public Step odpsStep(JobRepository activityJobRepository, PlatformTransactionManager transactionManager){
        return new StepBuilder(BatchConstantName.StepName.ODPS, activityJobRepository)
                .<Record, ActivityBatchRewardRoster>chunk(10, transactionManager)
                .reader(odpsReader)
                .processor(odpsProcessor)
                .writer(odpsWriter)
                .faultTolerant()
                .listener(odpsListener)
                .build();
    }


    @Bean("rosterStep")
    public Step rosterStep(JobRepository activityJobRepository, PlatformTransactionManager transactionManager){
        return new StepBuilder(BatchConstantName.StepName.ROSTER, activityJobRepository)
                .<ActivityBatchRewardRoster, ActivityCustomReward>chunk(10, transactionManager)
                .reader(rosterReader)
                .processor(rosterProcessor)
                .writer(rosterWrite)
                .faultTolerant()
                .listener(rosterListener)
                .build();
    }

    @Bean("rewardStep")
    public Step rewardStep(JobRepository activityJobRepository, PlatformTransactionManager transactionManager){
        return new StepBuilder(BatchConstantName.StepName.REWARD, activityJobRepository)
                .<ActivityCustomReward, ActivityCustomReward>chunk(1, transactionManager)
                .reader(rewardReader)
                .processor(rewardProcessor)
                .writer(rewardWriter)
                .faultTolerant()
                .listener(rewardListener)
                .build();
    }
}
