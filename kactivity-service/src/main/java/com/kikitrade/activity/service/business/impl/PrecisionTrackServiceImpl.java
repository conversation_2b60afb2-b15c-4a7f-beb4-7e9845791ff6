package com.kikitrade.activity.service.business.impl;

import com.kikitrade.activity.api.model.request.PrecisionRecordRequest;
import com.kikitrade.activity.dal.tablestore.builder.PrecisionPoolBuilder;
import com.kikitrade.activity.dal.tablestore.builder.PrecisionTrackBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrecisionPool;
import com.kikitrade.activity.dal.tablestore.model.PrecisionTrack;
import com.kikitrade.activity.service.business.PrecisionTrackService;
import com.kikitrade.asset.api.RemoteAssetService;
import com.kikitrade.asset.model.AssetDTO;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/12 14:00
 * @description: Precision track service implementation
 */
@Slf4j
@Service
public class PrecisionTrackServiceImpl implements PrecisionTrackService {

  private static final String SCORE_AI = "score_ai";
  private static final String SCORE_HUMAN = "score_human";
  private static final String DEFAULT_PLATFORM = "twitter";
  private static final String POOL_ID = "1";

  @Resource
  private PrecisionTrackBuilder precisionTrackBuilder;

  @Resource
  private PrecisionPoolBuilder precisionPoolBuilder;

  @DubboReference
  private RemoteCustomerBindService remoteCustomerBindService;

  @DubboReference
  private RemoteAssetService remoteAssetService;

  /**
   * Strategy interface for handling different operation types
   */
  private interface TrackEligibilityStrategy {
    boolean isEligibleForTracking(PrecisionRecordRequest request, PrecisionPool pool);
  }

  private final TrackEligibilityStrategy aiScoreStrategy = (request, pool) -> {
    try {
      double requestMetrics = Double.parseDouble(request.getMetrics());
      return true;
    } catch (NumberFormatException e) {
      log.error("Failed to parse metrics for AI score: {}", request.getMetrics(), e);
      return false;
    }
  };

  private final TrackEligibilityStrategy humanScoreStrategy = (request, pool) -> {
    try {
      if(Double.parseDouble(request.getMetrics()) <= 0 || Double.parseDouble(request.getMetrics()) > 10){
        return false;
      }
      // Validate customer binding
      CustomerBindDTO bindDTO = remoteCustomerBindService.findById(request.getSaasId(), request.getCid());
      if (bindDTO == null) {
        log.warn("No customer binding found for saasId: {} and cid: {}", request.getSaasId(), request.getCid());
        return false;
      }

      // Check user's assets
      AssetDTO asset = remoteAssetService.asset(
          request.getSaasId(), bindDTO.getUid(), AssetType.AURA, AssetCategory.NORMAL);

      if (asset == null) {
        log.warn("No asset found for user: {}", bindDTO.getUid());
        return false;
      }

      BigDecimal auraAvailable = asset.getAvailable();
      double followsCount = Optional.ofNullable(request.getSmartFollowsCount())
          .map(Integer::doubleValue)
          .orElse(0.0);

      return followsCount >= pool.getPrecisionTrackCheckFollowsCount() ||
          auraAvailable.doubleValue() >= pool.getPrecisionTrackCheckAsset();
    } catch (Exception e) {
      log.error("Error processing human score eligibility for cid: {}", request.getCid(), e);
      return false;
    }
  };

  /**
   * Save precision track record
   *
   * @param request precision track record request
   * @return whether saving succeeded
   */
  @Override
  public boolean savePrecisionTrack(PrecisionRecordRequest request) {
    log.info("Processing precision track request: type={}, resourceId={}, cid={}",
        request.getOperateType(), request.getResourceId(), request.getCid());

    // Validate request
    if (!isValidRequest(request)) {
      log.warn("Invalid request data: {}", request);
      return false;
    }

    try {
      // Get configuration
      PrecisionPool precisionPool = precisionPoolBuilder.findById(POOL_ID);
      if (precisionPool == null) {
        log.warn("Precision pool configuration not found");
        return false;
      }

      // Check eligibility based on operation type
      boolean isEligible = false;
      String operateType = request.getOperateType();

      if (SCORE_AI.equals(operateType)) {
        isEligible = aiScoreStrategy.isEligibleForTracking(request, precisionPool);
      } else if (SCORE_HUMAN.equals(operateType)) {
        isEligible = humanScoreStrategy.isEligibleForTracking(request, precisionPool);
      } else {
        log.warn("Unsupported operation type: {}", operateType);
        return false;
      }

      if (isEligible) {
        PrecisionTrack precisionTrack = convertToPrecisionTrack(request);
        boolean result = precisionTrackBuilder.insert(precisionTrack);
        log.info("Track record saved: {}, result: {}", request.getResourceId(), result);
        return result;
      } else {
        log.info("Request did not meet tracking criteria: {}", request);
        return false;
      }
    } catch (Exception e) {
      log.error("Error processing precision track request: {}", request, e);
      return false;
    }
  }

  private boolean isValidRequest(PrecisionRecordRequest request) {
    return request != null &&
        StringUtils.hasText(request.getCid()) &&
        StringUtils.hasText(request.getResourceId()) &&
        StringUtils.hasText(request.getOperateType()) &&
        StringUtils.hasText(request.getMetrics());
  }

  PrecisionTrack convertToPrecisionTrack(PrecisionRecordRequest request) {
    PrecisionTrack precisionTrack = new PrecisionTrack();
    precisionTrack.setCid(request.getCid());
    precisionTrack.setResourceId(request.getResourceId());
    precisionTrack.setResourceAuthorId(request.getResourceAuthorId());
    precisionTrack.setResourceAuthorName(request.getResourceAuthorName());
    precisionTrack.setOperateType(request.getOperateType());
    try {
      precisionTrack.setMetrics(Double.parseDouble(request.getMetrics()));
    } catch (NumberFormatException e) {
      log.error("Failed to parse metrics: {}", request.getMetrics(), e);
      precisionTrack.setMetrics(0.0);
    }
    precisionTrack.setTime(System.currentTimeMillis());
    precisionTrack.setPlatform(DEFAULT_PLATFORM);
    precisionTrack.setSaasId(request.getSaasId());
    return precisionTrack;
  }
}
