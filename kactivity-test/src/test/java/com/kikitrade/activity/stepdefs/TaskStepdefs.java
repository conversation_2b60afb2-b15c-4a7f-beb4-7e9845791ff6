package com.kikitrade.activity.stepdefs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.api.RemoteTaskService;
import com.kikitrade.activity.api.model.request.TaskListRequest;
import com.kikitrade.activity.api.model.response.TaskDetailResponse;
import com.kikitrade.activity.api.model.response.TaskListResponse;
import com.kikitrade.activity.api.model.response.TaskPopResponse;
import com.kikitrade.activity.api.model.response.TaskProgressResponse;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.dal.tablestore.model.TaskConfig;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.job.MemberRankingJob;
import com.kikitrade.activity.util.CheckUtils;
import com.kikitrade.activity.util.DateUtils;
import com.kikitrade.kevent.common.util.DateUtil;
import io.cucumber.java.zh_cn.假如;
import io.cucumber.java.zh_cn.同时;
import io.cucumber.java.zh_cn.当;
import io.cucumber.java.zh_cn.那么;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.junit.Assert;

import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.kikitrade.activity.op.BusinessOP.*;


@Slf4j
public class TaskStepdefs {

    @Resource
    RemoteTaskService remoteTaskService;
    @Resource
    MemberRankingJob memberRankingJob;


    @当("task-获取taskList{}")
    public void task获取taskList(String taskListRequest) {
        TaskListRequest request = JSON.parseObject(taskListRequest, TaskListRequest.class);
        List<TaskListResponse> taskListResponses = remoteTaskService.taskList(request);
        taskListResponseList.addAll(taskListResponses);
    }

    @当("task-获取taskEarlyBirdPop{}")
    public void task获取taskEarlyBirdPop(String taskPopRequest) {
        JSONObject jsonObject = JSON.parseObject(taskPopRequest, JSONObject.class);
        String saasId = jsonObject.getString("saasId");
        String customerId = jsonObject.getString("customerId");
        TaskPopResponse taskPopResponse = remoteTaskService.getTaskEarlyBirdPop(saasId, customerId);
        taskPopResponseList.add(taskPopResponse);
    }

    @当("task-获取taskDetail{}")
    public void task获取taskDetail(String taskDetailRequest) {
        JSONObject jsonObject = JSON.parseObject(taskDetailRequest, JSONObject.class);
        String taskId = jsonObject.getString("taskId");
        String customerId = jsonObject.getString("customerId");
        TaskDetailResponse taskDetailResponse = remoteTaskService.getTask(taskId, customerId);
        if (taskDetailResponse != null) {
            taskDetailResponseList.add(taskDetailResponse);
        }
    }

    @当("task-获取taskProgress{}")
    public void task获取taskProgress(String taskProgressRequest) {
        JSONObject jsonObject = JSON.parseObject(taskProgressRequest, JSONObject.class);
        String saasId = jsonObject.getString("saasId");
        String taskId = jsonObject.getString("taskId");
        String customerId = jsonObject.getString("customerId");
        String type = jsonObject.getString("type");
        TaskProgressResponse taskProgressResponse = remoteTaskService.getTaskProgress(saasId, taskId, customerId, type);
        taskProgressResponseList.add(taskProgressResponse);
    }

    @同时("task-设置任务{}开始时间{}结束时间{}")
    public void task设置任务开始时间结束时间(String taskId, String start, String end) {
        TaskConfig config = taskConfigMap.get(taskId);
        if (config != null) {
            Date now = new Date();
            if (!Strings.isBlank(start)) {
                Date startTime = DateUtil.addDayToDate(now, Integer.parseInt(start));
                config.setStartTime(startTime);
            }
            if (!Strings.isBlank(end)) {
                Date endTime = DateUtil.addDayToDate(now, Integer.parseInt(end));
                config.setEndTime(endTime);
            }
        }
    }

    @同时("task-设置灰度任务{}开始时间{}结束时间{}")
    public void task设置灰度任务开始时间结束时间(String taskId, String grayStart, String grayEnd) {
        TaskConfig config = taskConfigMap.get(taskId);
        if (config != null) {
            Date now = new Date();
            if (!Strings.isBlank(grayStart)) {
                Date startTime = DateUtil.addDayToDate(now, Integer.parseInt(grayStart));
                config.setStartTimeV1(startTime);
            }
            if (!Strings.isBlank(grayEnd)) {
                Date endTime = DateUtil.addDayToDate(now, Integer.parseInt(grayEnd));
                config.setEndTimeV1(endTime);
            }
        }
    }


    @当("task-执行memberRankingJob")
    public void task获取taskDetailResult() {
        memberRankingJob.ex();
    }

    @那么("task-校验TaskListResponse{}")
    public void task校验TaskListResponse(String taskListResponses) {
        List<TaskListResponse> responsesEXP = JSON.parseArray(taskListResponses, TaskListResponse.class);
        log.info(">>Expect--List<TaskListResponse>{}", JSON.toJSONString(responsesEXP));

        List<TaskListResponse> responsesACT = taskListResponseList.stream().sorted(Comparator.comparing(TaskListResponse::getTaskId)).collect(Collectors.toList());
        log.info(">>Actual--List<TaskListResponse>{}", JSON.toJSONString(responsesACT));

        CheckUtils.compare(responsesEXP, responsesACT, new String[]{"cycle","endTime"});
    }
    //taskPopResponseList

    @那么("task-校验TaskPopResponseList{}")
    public void task校验TaskPopResponseList(String taskPopResponseList) {
        List<TaskPopResponse> responsesEXP = JSON.parseArray(taskPopResponseList, TaskPopResponse.class);
        log.info(">>Expect--List<TaskPopResponse>{}", JSON.toJSONString(responsesEXP));

        List<TaskPopResponse> responsesACT = BusinessOP.taskPopResponseList.stream().sorted(Comparator.comparing(TaskPopResponse::getTaskId)).collect(Collectors.toList());
        log.info(">>Actual--List<TaskPopResponse>{}", JSON.toJSONString(responsesACT));

        CheckUtils.compare(responsesEXP, responsesACT, new String[]{"cycle"});
    }

    @那么("task-校验TaskDetailResponse{}")
    public void task校验TaskDetailResponse(String taskDetailResponse) {
        if (taskDetailResponse.equals("[null]")) {
            Assert.assertTrue(taskDetailResponseList.isEmpty());
            return;
        }
        List<TaskDetailResponse> responsesEXP = JSON.parseArray(taskDetailResponse, TaskDetailResponse.class);
        log.info(">>Expect--List<TaskDetailResponse>{}", JSON.toJSONString(responsesEXP));

        List<TaskDetailResponse> responsesACT = taskDetailResponseList.stream().sorted(Comparator.comparing(TaskDetailResponse::getTaskId)).collect(Collectors.toList());
        log.info(">>Actual--List<TaskDetailResponse>{}", JSON.toJSONString(responsesACT));

        CheckUtils.compare(responsesEXP, responsesACT, new String[]{"cycle", "startTime"});
    }
}
