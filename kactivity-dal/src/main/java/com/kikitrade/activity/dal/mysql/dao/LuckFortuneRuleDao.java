package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 *
 */
public interface LuckFortuneRuleDao extends BaseDao<LuckFortuneRule, String> {

    /**
     * 逻辑删除红包规则
     * @param luckFortuneRule
     * @return
     */
    @Update("update luck_fortune_rule set status = 1, modified = now() where id = #{luckFortuneRule.id} and status = 0")
    int delete(@Param("luckFortuneRule") LuckFortuneRule luckFortuneRule);
}
