package com.kikitrade.activity.service.tunnel;

import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.model.Column;
import com.alicloud.openservices.tablestore.model.StreamRecord;
import com.alicloud.openservices.tablestore.tunnel.worker.DefaultChannelProcessor;
import com.alicloud.openservices.tablestore.tunnel.worker.ProcessRecordsInput;
import com.kikitrade.activity.dal.tablestore.builder.PrecisionMetricsBuilder;
import com.kikitrade.activity.dal.tablestore.builder.PrecisionPoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.PrecisionMetrics;
import com.kikitrade.activity.dal.tablestore.model.PrecisionPool;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.framework.ots.autoconfigure.TunnelChannelProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class PrecisionTunnelChannelProcessor implements TunnelChannelProcessor {

    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;
    @Resource
    private PrecisionPoolBuilder precisionPoolBuilder;

    @Override
    public String tunnelId() {
        return kactivityProperties.getPrecisionMetricsTunnelId();
    }

    @Override
    public void process(ProcessRecordsInput processRecordsInput) {
        processRecordsInput.getRecords().forEach(record -> {
            log.info("recode" + record);
            if (record.getRecordType() != StreamRecord.RecordType.PUT) {
                return;
            }
            PrecisionMetrics precisionMetrics = buildMetrics(record);
            ActivityCustomReward reward = buildReward(precisionMetrics);
            if(reward == null){
                return;
            }
            LauncherParameter launcherParameter = new LauncherParameter();
            launcherParameter.setActivityCustomReward(reward);
            launcherParameter.setProvideType(ActivityTaskConstant.ProvideType.auto);

            try {
                activityRealTimeRewardTccService.reward(launcherParameter);
            } catch (Exception e) {
                log.error("PrecisionTunnelChannelProcessor reward error", e);
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public void shutdown() {
    }

    private ActivityCustomReward buildReward(PrecisionMetrics metrics) {
        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setSaasId(metrics.getSaasId());
        reward.setBatchId(metrics.getCycle());
        if(metrics.getBusinessId() == null){
            return null;
        }
        reward.setBusinessId(metrics.getBusinessId() + metrics.getUid());
        reward.setCustomerId(metrics.getUid());
        reward.setSeq(metrics.getType() + ":" + metrics.getBusinessId() + ":" + metrics.getUid());
        BigDecimal amount = calAmount(metrics.getPoolId(), metrics.getType(), metrics.getRate());
        if(amount == null || amount.compareTo(BigDecimal.ZERO) <= 0){
            return null;
        }
        reward.setAmount(String.valueOf(amount));
        reward.setRewardType(AssetType.AURA.name());
        reward.setCurrency(AssetType.AURA.name());
        reward.setBusinessType(metrics.getType());
        return reward;
    }

    private PrecisionMetrics buildMetrics(StreamRecord record) {
        PrecisionMetrics metrics = new PrecisionMetrics();

        Arrays.asList(record.getPrimaryKey().getPrimaryKeyColumns()).forEach(column -> {
            switch (column.getName()) {
                case "cycle":
                    metrics.setCycle(column.getValue().asString());
                    break;
                case "pool_id":
                    metrics.setPoolId(column.getValue().asString());
                    break;
                case "type":
                    metrics.setType(column.getValue().asString());
                    break;
                case "uid":
                    metrics.setUid(column.getValue().asString());
                    break;
            }
        });

        record.getColumns().forEach(column -> {
            Column columnColumn = column.getColumn();
            switch (columnColumn.getName()) {
                case "author_username":
                    metrics.setAuthorUsername(columnColumn.getValue().asString());
                    break;
                case "metrics":
                    metrics.setMetrics(columnColumn.getValue().asDouble());
                    break;
                case "total_metrics":
                    metrics.setTotalMetrics(columnColumn.getValue().asDouble());
                    break;
                case "rate":
                    metrics.setRate(columnColumn.getValue().asDouble());
                    break;
                case "business_id":
                    metrics.setBusinessId(columnColumn.getValue().asString());
                    break;
                case "saas_id":
                    metrics.setSaasId(columnColumn.getValue().asString());
                    break;
            }
        });

        return metrics;
    }

    public BigDecimal calAmount(String id, String type, Double rate) {
        PrecisionPool config = precisionPoolBuilder.findById(id);
        String history = config.getHistory();
        int diff = 1;
        if(StringUtils.isNotBlank(history)){
            //计算history中两个时间的时间差有多少个4小时
            String[] split = history.split("-");
            Long now = System.currentTimeMillis();
            Long startTime = Long.parseLong(split[0]);
            Long endTime = Long.parseLong(split[1]);
            if(now > startTime && now < endTime){
                diff = (int) TimeUnit.MILLISECONDS.toHours(endTime - startTime) / 4;
            }
        }
        log.info("diff:{}", diff);
        return switch (type) {
            case "distribution" ->
                    BigDecimal.valueOf(Math.max(Math.min(config.getDistributionAmount() * diff * rate, config.getDistributionMaxAmount() * diff), 0.01)).setScale(2, RoundingMode.HALF_UP);
            case "contribution_ai" ->
                    BigDecimal.valueOf(Math.max(Math.min(config.getContributionAiAmount() * diff * rate, config.getContributionAiMaxAmount() * diff), 0.01)).setScale(2, RoundingMode.HALF_UP);
            case "contribution_human" ->
                    BigDecimal.valueOf(Math.max(Math.min(config.getContributionHumanAmount() * rate, config.getContributionHumanMaxAmount()), 0.01)).setScale(2, RoundingMode.HALF_UP);
            case "contribution_deep_human" ->
                    BigDecimal.valueOf(Math.max(Math.min(config.getContributionDeepHumanAmount() * rate, config.getContributionDeepHumanMaxAmount()), 0.01)).setScale(2, RoundingMode.HALF_UP);
            default -> BigDecimal.ZERO;
        };
    }
}
