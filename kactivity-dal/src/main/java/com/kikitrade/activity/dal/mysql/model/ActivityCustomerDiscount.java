package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2019/3/19 15:07
 */
@Getter
@Setter
@Table(name = "activity_customer_discount")
public class ActivityCustomerDiscount extends BaseModel {


    //贴息时间(yyyyMMdd)
    @Column(name = "discount_date")
    private String discountDate;

    //产品ID
    @Column(name = "investment_product_id")
    private String investmentProductId;

    //用户ID
    @Column(name = "customer_id")
    private String customerId;


    //认购总金额
    @Column(name = "buy_total_amt")
    private BigDecimal buyTotalAmt;

    //贴息率
    @Column(name = "discount_rate")
    private Double discountRate;

    //活动ID
    @Column(name = "activity_id")
    private Integer activityId;

    //活动类型
    @Column(name = "activity_type")
    private Integer activityType;


    //贴息币种
    @Column(name = "discount_currency")
    private String discountCurrency;

    //贴息金额
    @Column(name = "discount_amt")
    private BigDecimal discountAmt;

    @Column(name = "status")
    private Integer status;
}
