package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityDao extends BaseDao<Activity, String> {

    int update(Activity ac);

    int deleteById(Integer id);

    List<Activity> findAll(@Param("saasId") String saasId, @Param("offset") Integer offset, @Param("limit") Integer limit,
                           @Param("flag") boolean flag, @Param("type") Integer type);

    List<Activity> findForStatsUpdate();

    List<Activity> findAllForRedis();

    List<Activity> findAllByStatus(@Param("saasId") String saasId, @Param("offset") Integer offset, @Param("limit") Integer limit,
                                   @Param("status") Integer status);

    List<Activity> findAllByPara(@Param("saasId") String saasId, @Param("offset") Integer offset, @Param("limit") Integer limit,
                                 @Param("execute_type") Integer execute_type, @Param("status") Integer status);


    int updateStatus(@Param("id") Integer id, @Param("status") Integer status);

    Activity findById(Integer id);

    Activity queryByType2(@Param("saasId") String saasId);

    Activity queryActivityByTypeAndStatus(@Param("saasId") String saasId, @Param("type") Integer type, @Param("statusList") List<Integer> statsList);

    List<Activity> idList(@Param("saasId") String saasId);

}
