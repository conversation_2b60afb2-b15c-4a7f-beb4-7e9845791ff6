package com.kikitrade.activity.mockbean;

import com.kikitrade.kcustomer.api.service.RemoteCustomerInviteService;
import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RemoteCustomerInviteServiceMockBean {
    @Bean(name = "remoteCustomerInviteServiceStub")
    public RemoteCustomerInviteService remoteCustomerInviteService(){
        return Mockito.mock(RemoteCustomerInviteService.class);
    }
}
