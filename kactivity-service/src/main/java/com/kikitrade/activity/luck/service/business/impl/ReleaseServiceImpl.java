package com.kikitrade.activity.luck.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReleaseItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneRefundItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneRelease;
import com.kikitrade.activity.luck.api.model.response.ProcessVO;
import com.kikitrade.activity.luck.service.business.ReceiveService;
import com.kikitrade.activity.luck.service.business.RefundTccService;
import com.kikitrade.activity.luck.service.business.ReleaseService;
import com.kikitrade.activity.luck.service.model.ReceiveItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.BusinessMonitorConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityJoinItemService;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.framework.common.model.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class ReleaseServiceImpl implements ReleaseService {

    @Resource
    private LuckFortuneReleaseItemBuilder luckFortuneReleaseItemBuilder;
    @Resource
    private RedisService redisService;
    @Resource
    private ReceiveService receiveService;
    @Resource
    private ActivityJoinItemService activityJoinItemService;
    @Resource
    private RefundTccService refundTccService;
    @Resource
    private KactivityModuleProperties kactivityModuleProperties;

    private static final String REFUND_RELEASE_MESSAGE = "红包未领完，退款";

    /**
     * 历史发放的红包
     *
     * @param customerId
     * @return
     */
    @Override
    public Page<LuckFortuneRelease> historyRelease(String customerId, String type, String startTime, String endTime, int offset, int limit) {
        Page<LuckFortuneReleaseItem> itemPage = luckFortuneReleaseItemBuilder.findByCustomerId(customerId, type, startTime, endTime, offset, limit);
        return Page.with(itemPage.getOffset(), itemPage.getLimit(), itemPage.getTotalCount(), buildLuckFortuneRelease(itemPage.getRows()));
    }

    /**
     * 历史发放的空投数量
     *
     * @param customerId
     * @return
     */
    @Override
    public long countRelease(String customerId) {
        return luckFortuneReleaseItemBuilder.countRelease(customerId);
    }

    /**
     * 历史发放的空投总价值
     *
     * @param customerId
     * @return
     */
    @Override
    public BigDecimal sumAmountRelease(String customerId) {
        return luckFortuneReleaseItemBuilder.sumAmountRelease(customerId);
    }

    @Override
    public LuckFortuneReleaseItem findById(String id) {
        return luckFortuneReleaseItemBuilder.findById(id);
    }

    /**
     * 校验红包id是否合法
     *
     * @param releaseId
     * @param encryptReleaseId
     * @return
     */
    @Override
    public boolean checkLuckFortuneToken(String releaseId, String encryptReleaseId) {
        return StringUtils.isNotBlank(releaseId);
    }

    /**
     * 重新构建，红包缓存
     * @param fortuneReleaseItem
     * false：不需要重构， true：需要重构
     */
    @Override
    public boolean rebuildLuckFortune(LuckFortuneReleaseItem fortuneReleaseItem) {
        if(String.valueOf(ActivityConstant.LuckFortuneReleaseStatus.BUILDING.getCode()).equals(redisService.get(RedisKeyConst.LUCK_FORTUNE_RELEASE_STATUS_KEY.getKey(fortuneReleaseItem.getId())))){
            return false;
        }
        redisService.save(RedisKeyConst.LUCK_FORTUNE_RELEASE_STATUS_KEY.getKey(fortuneReleaseItem.getId()), String.valueOf(ActivityConstant.LuckFortuneReleaseStatus.BUILDING.getCode()));
        List<String> receiveItems = receiveService.findItemByReleaseId(fortuneReleaseItem.getId()).stream()
                .filter(r -> !ActivityConstant.LuckFortuneReceiveStatus.DRAW_FAIL.getCode().equals(r.getStatus()))
                .map(LuckFortuneReceiveItem::getId).collect(Collectors.toList());

        List<ReceiveItem> detail = JSON.parseArray(fortuneReleaseItem.getDetail(), ReceiveItem.class);
        if(CollectionUtils.isNotEmpty(detail) && CollectionUtils.isNotEmpty(receiveItems) && detail.size() == receiveItems.size()){
            luckFortuneReleaseItemBuilder.updateStatusToDrew(fortuneReleaseItem);
            redisService.del(RedisKeyConst.LUCK_FORTUNE_RELEASE_STATUS_KEY.getKey(fortuneReleaseItem.getId()));
            return false;
        }

        List<String> buildList = new ArrayList<>();
        for(ReceiveItem item : detail){
            if(!receiveItems.contains(item.getId())){
                buildList.add(JSON.toJSONString(item));
            }
        }
        String key = RedisKeyConst.LUCK_FORTUNE_RELEASE_NOT_DRAW_KEY.getKey(fortuneReleaseItem.getId());
        redisService.lpushAll(key, buildList);
        redisService.save(RedisKeyConst.LUCK_FORTUNE_RELEASE_STATUS_KEY.getKey(fortuneReleaseItem.getId()), String.valueOf(ActivityConstant.LuckFortuneReleaseStatus.DRAWING.getCode()));
        long expireTime = TimeUtil.parse(fortuneReleaseItem.getExpiredTime()).getTime() - TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)).getTime();
        if(TimeUnit.MILLISECONDS.toDays(expireTime) <= 3){
            redisService.expire(RedisKeyConst.LUCK_FORTUNE_RELEASE_STATUS_KEY.getKey(fortuneReleaseItem.getId()), TimeUnit.MILLISECONDS.toSeconds(expireTime));
            redisService.expire(key, TimeUnit.MILLISECONDS.toSeconds(expireTime));
        }
        return CollectionUtils.isEmpty(buildList);
    }

    @Override
    public void finish(String id, String releaseCustomerId) {
        LuckFortuneReleaseItem fortuneReleaseItem = new LuckFortuneReleaseItem();
        fortuneReleaseItem.setId(id);
        fortuneReleaseItem.setCustomerId(releaseCustomerId);
        luckFortuneReleaseItemBuilder.updateStatusToDrew(fortuneReleaseItem);
    }

    @Override
    public String encryptReleaseId(String releaseId) {
        try{
            String releaseCode = redisService.get(RedisKeyConst.LUCK_FORTUNE_RELEASE_CODE_KEY.getKey(releaseId));
            if(StringUtils.isNotBlank(releaseCode)){
                return releaseCode;
            }
            LuckFortuneReleaseItem releaseItem = luckFortuneReleaseItemBuilder.findById(releaseId);
            if(releaseItem != null && StringUtils.isNotBlank(releaseItem.getReleaseCode())){
                return releaseItem.getReleaseCode();
            }
            return null;
        }catch (Exception ex){
            return null;
        }
    }

    @Override
    public String decryptReleaseId(String releaseCode) {
        try{
            String releaseId = redisService.get(RedisKeyConst.LUCK_FORTUNE_RELEASE_CODE_KEY.getKey(releaseCode));
            if(StringUtils.isNotBlank(releaseId)){
                return releaseId;
            }
            LuckFortuneReleaseItem releaseItem = luckFortuneReleaseItemBuilder.findByReleaseCode(releaseCode);
            if(releaseItem != null && StringUtils.isNotBlank(releaseItem.getId())){
                return releaseItem.getId();
            }
            log.warn("非法空投id，decrypt_release_id,{}, 可能被攻击", releaseCode);
            return null;
        }catch (Exception ex){
            return null;
        }

    }

    @Override
    public String decodeReleaseId(String releaseId, String customerId){
        PasswordEncoder encoder = new BCryptPasswordEncoder();
        String code = encoder.encode(releaseId).substring(40).replace("/", "A");
        if(StringUtils.isNotBlank(kactivityModuleProperties.getLuck().getOperateCustomer())
                && Arrays.asList(kactivityModuleProperties.getLuck().getOperateCustomer().split(",")).contains(customerId)){
            return activityJoinItemService.reGenerateCode(code);
        }
        return code;
    }

    /**
     * 空投过期
     *
     * @param refund
     */
    @Override
    public void expire(LuckFortuneReleaseItem refund) {
        log.info("ActivitySignExpiredJob num {}, {}", refund, refund.getExpiredTime().compareTo(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)) <= 0);
        if(refund.getExpiredTime().compareTo(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)) <= 0){
            try{
                List<String> receiveItems = redisService.lrange(RedisKeyConst.LUCK_FORTUNE_RELEASE_DREW_KEY.getMiddleKey(refund.getId()));
                refund.setReceiveNum(receiveItems == null ? 0 : receiveItems.size());
                refund.setReceiveList(JSON.toJSONString(receiveItems));
                List<LuckFortuneReceiveItem> receiveItemList = receiveService.findItemByReleaseId(refund.getId());
                long num = receiveItemList.stream().filter(item -> item.getStatus().equals(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_SUCCESS.getCode())).count();
                if(num == refund.getNum()){
                    refund.setStatus(ActivityConstant.LuckFortuneReleaseStatus.DREW.getCode());
                }else{
                    refund.setStatus(ActivityConstant.LuckFortuneReleaseStatus.EXPIRED.getCode());
                }
                luckFortuneReleaseItemBuilder.update(refund);
            }catch (Exception ex){
                log.error("ActivitySignExpiredJob error",ex);
            }
        }
    }

    /**
     * 空投退款
     *
     * @param refund
     */
    @Override
    public void refund(LuckFortuneReleaseItem refund) {
        try {
            Date expiredTime = TimeUtil.parse(refund.getExpiredTime());
            //当前事件未到过期时间
            if(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)).compareTo(TimeUtil.addMinute(expiredTime, kactivityModuleProperties.getLuck().getValidMinutes())) < 0){
                return;
            }
            LuckFortuneRefundItem luckFortuneRefundItem = buildRefundItem(refund);
            if (luckFortuneRefundItem != null) {
                refundTccService.tryReleaseRefund(luckFortuneRefundItem, refund);
            } else {
                refund.setStatus(ActivityConstant.LuckFortuneReleaseStatus.DREW.getCode());
                refund.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
                luckFortuneReleaseItemBuilder.update(refund);
            }
        } catch (Exception ex) {
            log.error("activityRefundJob error", ex);
        }
    }

    public boolean refundById(String id) {
        try {
            LuckFortuneReleaseItem refund = luckFortuneReleaseItemBuilder.findById(id);
            LuckFortuneRefundItem luckFortuneRefundItem = buildRefundItem(refund);
            refundTccService.tryReleaseRefund(luckFortuneRefundItem, refund);
            return true;
        } catch (Exception e) {
            log.error("activityRefund error {}", id, e);
        }
        return false;
    }

    private LuckFortuneRefundItem buildRefundItem(LuckFortuneReleaseItem releaseItem) {
        BigDecimal refundAmount = computeRefundAmount(releaseItem);
        if (refundAmount.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        LuckFortuneRefundItem item = new LuckFortuneRefundItem();
        item.setOriginBusinessId(releaseItem.getId());
        item.setBusinessId(releaseItem.getId());
        item.setAmount(refundAmount.toPlainString());
        item.setCurrency(releaseItem.getCurrency());
        item.setCustomerId(releaseItem.getCustomerId());
        item.setMessage(REFUND_RELEASE_MESSAGE);
        item.setRefundTime(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        item.setStatus(ActivityConstant.LuckFortuneReleaseStatus.REFUNDING.getCode());
        return item;
    }

    /**
     * 计算发放的红包退款金额
     *
     * @param releaseItem
     * @return
     */
    private BigDecimal computeRefundAmount(LuckFortuneReleaseItem releaseItem) {
        BigDecimal feeAmount = BigDecimal.ZERO;
        List<LuckFortuneReceiveItem> receiveItems = receiveService.findItemByReleaseId(releaseItem.getId());
        for (LuckFortuneReceiveItem item : receiveItems) {
            if (!ActivityConstant.LuckFortuneReceiveStatus.REFUND_FAIL.getCode().equals(item.getStatus())
                    && !ActivityConstant.LuckFortuneReceiveStatus.REFUNDING.getCode().equals(item.getStatus())
                    && !ActivityConstant.LuckFortuneReceiveStatus.REFUND_SUCCESS.getCode().equals(item.getStatus())) {
                feeAmount = feeAmount.add(new BigDecimal(item.getAmount()));
            }
        }
        return new BigDecimal(releaseItem.getAmount()).subtract(feeAmount);
    }

    private List<LuckFortuneRelease> buildLuckFortuneRelease(List<LuckFortuneReleaseItem> items){
        return items.stream().map(item ->
                LuckFortuneRelease.builder()
                .id(encryptReleaseId(item.getId()))
                .assignType(item.getAssignType())
                .amount(item.getAmount())
                .currency(item.getCurrency())
                .cover(item.getCover())
                .status(item.getStatus())
                .releaseTime(TimeUtil.parse(item.getReleaseTime()).getTime())
                .process(buildProcess(item))
                .greeting(item.getGreeting())
                .avatar(item.getAvatar())
                .expiredTime(TimeUtil.parse(item.getExpiredTime()).getTime())
                .nickName(item.getNickName())
                .num(item.getNum())
                .build()).collect(Collectors.toList());
    }

    private ProcessVO buildProcess(LuckFortuneReleaseItem item){
        ProcessVO processVO = new ProcessVO();
        processVO.setStatus(item.getStatus());
        processVO.setAmount(item.getRefundAmount());
        processVO.setCurrency(item.getCurrency());
        processVO.setNum(item.getNum());
        processVO.setReceiveNum(receiveService.findItemByReleaseId(item, "kiki").size());
        return processVO;
    }
}
