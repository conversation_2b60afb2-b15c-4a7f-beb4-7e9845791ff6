package com.kikitrade.activity.service.common.strategy.facebook;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;

/**
 * <AUTHOR>
 * @date 2025/7/22 18:10
 * @description: Facebook平台策略服务接口
 */
public interface FacebookSaasStrategyService {
    String strategy();
    void execute(OpenStrategyAuthRequest request) throws ActivityException;
}
