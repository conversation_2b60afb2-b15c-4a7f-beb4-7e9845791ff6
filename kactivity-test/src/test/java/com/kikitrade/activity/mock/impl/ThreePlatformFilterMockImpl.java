package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.api.model.response.TaskCompletedResult;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.mock.ThreePlatformFilterMock;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.business.ThreePlatformFilter;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.VerifyResult;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import jakarta.annotation.Resource;
import org.apache.commons.collections.MapUtils;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@Component
public class ThreePlatformFilterMockImpl implements ThreePlatformFilterMock {
    @Resource
    ThreePlatformFilter threePlatformFilter;
    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private ActivityTaskService activityTaskService;

    @Override
    public void verify() throws ActivityException {
        Mockito.doAnswer(c -> {
            TaskConfigDTO configDTO = c.getArgument(0);
            AccessToken token = c.getArgument(1);
            String customerId = c.getArgument(2);
            String saasId = c.getArgument(3);
            Map<String, String> ext = c.getArgument(4);

            String code = configDTO.getCode();
            token.setSaasId(saasId);

            return switch (ActivityTaskConstant.TaskCodeEnum.valueOf(code)) {
                case connect_x -> null;
                case like_post_x -> null;
                case comment_post_x -> null;
                case retweet_post_x -> null;
                case follow_x -> VerifyResult.builder().result(false).build();
                case reply_post_x -> null;
                case subject_post_x -> null;
                case name_x -> null;
                case connect_dc -> null;
                case join_dc -> null;
                case join_game -> null;
                case play_game -> null;
                case external_reward -> null;
                case osp_profile_create -> null;
                case x_authorize -> null;
                case osp_connect_tg -> null;
                case osp_callback -> VerifyResult.builder().result(true).contentIds(List.of(token.getUserId())).build();
                case join_tg -> null;
                case wallet_bind -> null;
                case boost_tg_channel -> null;
                case tg_premium -> null;
                case member_castle_level -> null;
                case create_dapp -> null;
                case  create_deek_profile -> null;
                case connect_email -> null;
                case connect_facebook -> null;
                case connect_google -> null;
                case connect_twitter -> null;
                case connect_instagram -> null;
                case connect_line -> null;
                case connect_tiktok -> null;
                case connect_telegram -> null;
            };

        }).when(threePlatformFilter).verify(any(TaskConfigDTO.class), any(AccessToken.class), anyString(), anyString(), any());
    }
}
