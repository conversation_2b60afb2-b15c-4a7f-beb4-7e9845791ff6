package com.kikitrade.activity.mock.impl;

import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchBuilder;
import com.kikitrade.activity.dal.tablestore.builder.impl.ActivityBatchBuildImpl;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.mock.ActivityBatchBuilderMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.common.Business;
import com.kikitrade.activity.util.BeanUtils;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyString;

@Component
public class ActivityBatchBuilderMockImpl implements ActivityBatchBuilderMock {

    @Resource
    ActivityBatchBuildImpl activityBatchBuilder;

    @Override
    public void insert() {
        Mockito.doCallRealMethod().when(activityBatchBuilder).insert(Mockito.any(ActivityBatch.class));
    }

    @Override
    public void putRowWithoutCondition() {
        Mockito.doAnswer(c -> {
            ActivityBatch activityBatch = c.getArgument(0);
            String activityId = activityBatch.getActivityId();
            String batchId = activityBatch.getBatchId();

            Optional<ActivityBatch> first = BusinessOP.activityBatchList.stream().filter(item -> item.getActivityId().equals(activityId) &&
                    item.getBatchId().equals(batchId)).findFirst();

            if (first.isPresent()) {
                ActivityBatch dbBatch = first.get();
                BusinessOP.activityBatchList.remove(dbBatch);
            }
            BusinessOP.activityBatchList.add(activityBatch);
            return true;
        }).when(activityBatchBuilder).putRow(Mockito.any(ActivityBatch.class));

    }

    @Override
    public void queryById() {
        Mockito.doCallRealMethod().when(activityBatchBuilder).queryById(anyString());
    }

    @Override
    public void rangeQueryOne() {
        Mockito.doAnswer(c -> {
            String tableName = c.getArgument(0);
            List<RangeQueryParameter> queryList = c.getArgument(1);

            RangeQueryParameter parameter = queryList.get(0);
            String name = parameter.getName();
            String from = parameter.getFrom().asString();

            if ("batch_id".equals(name)) {
                Optional<ActivityBatch> first = BusinessOP.activityBatchList.stream().filter(item -> item.getBatchId().equals(from)).findFirst();
                if (first.isPresent()) {
                    return BeanUtils.toBean(first.get());
                }
            }


            return null;
        }).when(activityBatchBuilder).rangeQueryOne(anyString(), Mockito.any(List.class));

    }

    @Override
    public void update() {
        Mockito.doCallRealMethod().when(activityBatchBuilder).update(Mockito.any(ActivityBatch.class));
    }

    @Override
    public void updateRowWithoutCondition() {
        Mockito.doAnswer(c -> {
            ActivityBatch activityBatch = c.getArgument(0);
            String activityId = activityBatch.getActivityId();
            String batchId = activityBatch.getBatchId();

            Optional<ActivityBatch> first = BusinessOP.activityBatchList.stream().filter(item -> item.getActivityId().equals(activityId) &&
                    item.getBatchId().equals(batchId)).findFirst();

            if (first.isPresent()) {
                ActivityBatch dbBatch = first.get();
                BusinessOP.activityBatchList.remove(dbBatch);
                BusinessOP.activityBatchList.add(activityBatch);
                return true;
            }
            return true;
        }).when(activityBatchBuilder).updateRow(Mockito.any(ActivityBatch.class));
    }
}
