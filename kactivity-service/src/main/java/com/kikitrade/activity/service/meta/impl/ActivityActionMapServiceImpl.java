package com.kikitrade.activity.service.meta.impl;

import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.dal.mysql.dao.ActivityActionMapDao;
import com.kikitrade.activity.dal.mysql.model.ActivityActionMap;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.service.meta.ActivityActionMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityActionMapServiceImpl implements ActivityActionMapService {

    @Resource
    private ActivityActionMapDao activityActionMapDao;


    @Override
    public JsonResult save(ActivityActionMap activityActionMap) {
        JsonResult result = new JsonResult();
        //  do some convert;
        try {
            int count = activityActionMapDao.insert(activityActionMap);
            if (count > 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityactionmapserviceimpl save process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }

    @Override
    public JsonResult delete(Integer activityId) {
        JsonResult result = new JsonResult();
        try {
            int count = activityActionMapDao.deleteById(activityId);
            if (count >= 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityactionmapserviceimpl delete process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }

    @Override
    public JsonResult batchInsert(List<ActivityActionMap> activityActionMapList) {
        JsonResult result = new JsonResult();
        try {

            //TODO batchInsert
            //int count = activityActionMapDAO.batchInsert(activityActionMapList);
            int count = 0;
            for (ActivityActionMap activityActionMap : activityActionMapList) {
                count += activityActionMapDao.insert(activityActionMap);
            }
            if (count > 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityactionmapserviceimpl batchInsert process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }


    @Override
    public JsonResult update(ActivityActionMap activityActionMap) {
        JsonResult result = new JsonResult();

        try {
            //  delete first
            activityActionMapDao.deleteById(activityActionMap.getActivity_id());
            // then insert
            int count = activityActionMapDao.insert(activityActionMap);
            if (count > 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityactionmapserviceimpl update process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        result.setObj(activityActionMap);

        return result;
    }

    @Override
    public JsonResult update(List<ActivityActionMap> activityActionMapList) {
        JsonResult result = new JsonResult();
        int activity_id = 0;
        if (activityActionMapList.size() > 0) {
            activity_id = activityActionMapList.get(0).getActivity_id();
        }
        try {
            //  delete first
            activityActionMapDao.deleteById(activity_id);
            // then insert
            int count = activityActionMapDao.batchInsert(activityActionMapList);
            if (count > 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityactionmapserviceimpl update process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        result.setObj(activityActionMapList);
        return result;
    }


    @Override
    public JsonResult findAll(Integer offset, Integer limit) {
        JsonResult result = new JsonResult();
        try {
            List<ActivityActionMap> activityActionMapList = activityActionMapDao.findAll(offset, limit);
            if (activityActionMapList == null || activityActionMapList.size() == 0) {
                result.setObj(activityActionMapList).setSuccess(true).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                result.setObj(activityActionMapList).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityactionmapserviceimpl findAll process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }


    @Override
    public List<ActivityActionMap> findByActivityId(Integer id) {

        try {
            return activityActionMapDao.findByActivityId(id);
        } catch (Exception e) {
            log.error("activityactionmapserviceimpl findByActivityId process failed.", e);
        }
        return null;

    }


}
