package com.kikitrade.activity.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 */
@Data
@Table(name = "luck_fortune_release_item")
public class LuckFortuneReleaseItem implements Serializable {

    public static final String SEARCH_RELEASE_BY_ID = "search_release_by_id";

    public static final String IDX_RELEASE_BY_ID = "idx_release_by_id";
    public static final String IDX_RELEASE_BY_EXPIRED_DATE = "idx_release_by_expired_date";
    public static final String IDX_RELEASE_BY_RELEASE_CODE = "idx_release_by_release_code";

    /**
     * 主键
     */
    @PartitionKey(name = "id", value = 1)
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "id")
    @Index(name = IDX_RELEASE_BY_ID, pkColumn = "id")
    @Index(name = IDX_RELEASE_BY_EXPIRED_DATE, pkColumn = "id", pkValue = 2)
    @Index(name = IDX_RELEASE_BY_RELEASE_CODE, pkColumn = "id", pkValue = 1)
    private String id;


    /**
     * 用户id
     */
    @PartitionKey(name = "customer_id")
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "customer_id")
    @Index(name = IDX_RELEASE_BY_ID, pkColumn = "customer_id", pkValue = 1)
    @Index(name = IDX_RELEASE_BY_EXPIRED_DATE, pkColumn = "customer_id", pkValue = 3)
    @Index(name = IDX_RELEASE_BY_RELEASE_CODE, pkColumn = "customer_id", pkValue = 2)
    private String customerId;

    /**
     * 红包分配方案：普通红包/拼手气红包
     */
    @Column(name = "assign_type", isDefined = true)
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "assign_type")
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "assign_type")
    private String assignType;

    /**
     * 红包数量
     */
    @Column(name = "num", isDefined = true, type = Column.Type.INTEGER)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "num")
    @Index(name = IDX_RELEASE_BY_EXPIRED_DATE, dfColumn = "num")
    private Integer num;

    /**
     * 红包金额
     */
    @Column(name = "amount", isDefined = true)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "amount")
    @Index(name = IDX_RELEASE_BY_EXPIRED_DATE, dfColumn = "amount")
    private String amount;

    /**
     * 红包币种
     */
    @Column(name = "currency", isDefined = true)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "currency")
    @Index(name = IDX_RELEASE_BY_EXPIRED_DATE, dfColumn = "currency")
    private String currency;

    /**
     * 封面
     */
    @Column(name = "cover", isDefined = true)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "cover")
    private String cover;

    /**
     * 祝福语
     */
    @Column(name = "greeting", isDefined = true)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "greeting")
    private String greeting;

    /**
     * 红包类型 -token
     */
    @Column(name = "type", isDefined = true)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "type")
    private String type;


    /**
     * 红包明细
     */
    @Column(name = "detail", isDefined = true)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "detail")
    @Index(name = IDX_RELEASE_BY_EXPIRED_DATE, dfColumn = "detail")
    private String detail;

    /**
     * 红包发放时间（utc时间）
     */
    @Column(name = "release_time", isDefined = true)
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "release_time")
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "release_time")
    private String releaseTime;

    /**
     * 红包过期时间（utc时间）
     */
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "expired_time")
    @Column(name = "expired_time", isDefined = true)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "expired_time")
    @Index(name = IDX_RELEASE_BY_EXPIRED_DATE, pkColumn = "expired_time", pkValue = 1)
    private String expiredTime;

    /**
     * 红包状态
     */
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "status", fieldType = FieldType.LONG)
    @Column(name = "status", isDefined = true, type = Column.Type.INTEGER)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "status")
    @Index(name = IDX_RELEASE_BY_EXPIRED_DATE, pkColumn = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created", isDefined = true)
    private String created;

    /**
     * 修改时间
     */
    @Column(name = "modified", isDefined = true)
    private String modified;

    /**
     * 用户头像
     */
    @Column(name = "avatar", isDefined = true)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "avatar")
    private String avatar;

    /**
     * 用户昵称
     */
    @Column(name = "nick_name", isDefined = true)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "nick_name")
    private String nickName;

    /**
     * 发放的红包usd价值
     */
    @Column(name = "cost", isDefined = true, type = Column.Type.DOUBLE)
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "cost", fieldType = FieldType.DOUBLE)
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "cost")
    private BigDecimal cost;

    /**
     * 退款金额
     */
    @Column(name = "refund_amount", isDefined = true)
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "refund_amount")
    private String refundAmount;

    /**
     * 领取数量
     */
    @Column(name = "receive_num", isDefined = true, type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "receive_num", fieldType = FieldType.LONG)
    private Integer receiveNum;

    /**
     * 领取记录
     */
    @Column(name = "receive_list", isDefined = true)
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "receive_list")
    private String receiveList;

    /**
     * 有效天数
     */
    @Column(name = "valid_time", isDefined = true, type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_RELEASE_BY_ID, column = "valid_time", fieldType = FieldType.LONG)
    private Integer validTime;

    @Column(name = "release_code", isDefined = true)
    @Index(name = IDX_RELEASE_BY_RELEASE_CODE, pkColumn = "release_code")
    @Index(name = IDX_RELEASE_BY_ID, dfColumn = "release_code")
    private String releaseCode;
}
