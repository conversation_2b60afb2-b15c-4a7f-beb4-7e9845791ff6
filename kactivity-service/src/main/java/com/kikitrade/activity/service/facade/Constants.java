package com.kikitrade.activity.service.facade;

import com.kikitrade.framework.mybatis.config.SaasContext;
import io.grpc.Context;
import io.grpc.Metadata;

import static io.grpc.Metadata.ASCII_STRING_MARSHALLER;

/**
 * <AUTHOR>
 */
public class Constants {
    public static final Metadata.Key<String> SAAS_ID_MD_KEY = Metadata.Key.of(SaasContext.REST_KEY, ASCII_STRING_MARSHALLER);
    public static final Context.Key<String> SAAS_ID_KEY = Context.key(SaasContext.REST_KEY);
}
