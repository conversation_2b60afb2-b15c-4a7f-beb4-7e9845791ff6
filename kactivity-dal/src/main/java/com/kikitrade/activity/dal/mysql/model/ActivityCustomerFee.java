package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/8/21 15:07
 */
@Getter
@Setter
@Table(name = "activity_customer_fee")
public class ActivityCustomerFee extends BaseModel {


    //交易时间(yyyyMMdd)
    @Column(name = "tran_date")
    private String tran_date;

    //用户ID
    @Column(name = "customer_id")
    private String customer_id;

    //币种
    @Column(name = "currency")
    private String currency;

    //活动ID
    @Column(name = "activity_id")
    private Integer activity_id;

    //活动类型
    @Column(name = "activity_type")
    private Integer activity_type;


    //交易总笔数
    @Column(name = "total_num")
    private Integer total_num;

    //手续费总金额
    @Column(name = "total_fee")
    private BigDecimal total_fee;

    @Column(name = "status")
    private Integer status;

}
