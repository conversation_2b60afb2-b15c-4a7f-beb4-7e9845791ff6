package com.kikitrade.activity.luck.service.business;

import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.model.Result;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReceiveDTO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceive;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceiveResponse;
import com.kikitrade.activity.luck.service.model.ReceiveItem;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
public interface ReceiveService {

    /**
     * 领取红包
     * @param luckFortuneReceiveDTO
     * @return
     */
    Result<LuckFortuneReceiveResponse> receive(LuckFortuneReceiveDTO luckFortuneReceiveDTO) throws Exception;

    /**
     * 弹出个红包
     * @param releaseItem
     * @return
     */
    ReceiveItem pullLuckFortune(LuckFortuneReleaseItem releaseItem, String receiveCode) throws Exception;

    /**
     * 查询领取记录
     * @param releaseId
     * @return
     */
    List<LuckFortuneReceiveItem> findItemByReleaseId(String releaseId);


    /**
     * 查询领取记录
     * @param item
     * @param refer
     * @return
     */
    List<LuckFortuneReceive> findItemByReleaseId(LuckFortuneReleaseItem item, String refer);

    /**
     * 查询领取记录
     * @param releaseId
     * @return
     */
    LuckFortuneReceiveItem findItemByReceiveCodeAndReleaseId(String receiveCode, String releaseId);

    /**
     * 查询领取记录
     * @param releaseId
     * @return
     */
    LuckFortuneReceiveItem findItemByCustomerIdAndReleaseId(String customerId, String releaseId);

    /**
     * 查看领取的红包
     * @param id
     * @return
     */
    LuckFortuneReceiveItem findById(String id);

    /**
     * 最近领取的红包
     * @param customer
     * @return
     */
    List<LuckFortuneReceive> lastReceiveLuckFortune(ActivityCustomerDTO customer);

    /**
     * 领取的历史红包
     * @return
     */
    Page<LuckFortuneReceive> historyReceiveLuckFortune(String customerId, String type, String startTime, String endTime, int offset, int limit);

    /**
     * 历史发放的空投数量
     * @param customerId
     * @return
     */
    long countReceive(String customerId);

    /**
     * 历史发放的空投总价值
     * @param customerId
     * @return
     */
    BigDecimal sumAmountReceive(String customerId);

    /**
     * 检查能否开启空投
     * @param receiveItem
     * @return
     * @throws Exception
     */
    Result check(ReceiveItem receiveItem, ActivityCustomerDTO customerDTO) throws Exception;

    /**
     * 空投回归
     * @param releaseId
     * @param receiveId
     * @param receiveCode
     */
    void revert(String releaseId, String receiveId, String receiveCode);
}
