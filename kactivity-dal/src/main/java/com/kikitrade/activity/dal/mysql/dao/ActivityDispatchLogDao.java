package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityDispatchLog;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityDispatchLogDao extends BaseDao<ActivityDispatchLog, String> {

    int insert(ActivityDispatchLog activityDispatchLog);

    int updateStatus(@Param("tran_date") String tran_date, @Param("activity_id") Integer activity_id, @Param("dispatch_status") Integer dispatch_status);

    List<ActivityDispatchLog> findAll();

    ActivityDispatchLog findByActivityId(@Param("tran_date") String tran_date, @Param("activity_id") Integer activity_id);

}
