package com.kikitrade.activity.api;

import com.kikitrade.activity.api.model.AuthRequest;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.model.exception.ActivityException;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/11 10:04
 */
public interface RemoteAuthService {

    Token auth(String saasId, String platform, String code, String redirectUri, String customerId) throws ActivityException;

    Token auth(AuthRequest authRequest) throws ActivityException;

    Boolean sendVerifyCode(String saasId, String customerId, String customerName, String channel, String receiver, String sceneCode) throws ActivityException;
}
