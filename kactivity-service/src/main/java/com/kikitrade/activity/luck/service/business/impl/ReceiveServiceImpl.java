package com.kikitrade.activity.luck.service.business.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.kikitrade.accounting.api.RemoteProductAccountService;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountConstant;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountParam;
import com.kikitrade.accounting.api.model.productaccount.response.ProductAccountResponse;
import com.kikitrade.accounting.api.model.productaccount.response.ProductAccountResponseCode;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisScriptConstant;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReceiveItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.service.business.AirDropEventService;
import com.kikitrade.activity.luck.service.business.RuleService;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.activity.luck.api.exception.LuckFortuneException;
import com.kikitrade.activity.luck.api.exception.LuckFortuneExceptionType;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReceiveDTO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceive;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceiveResponse;
import com.kikitrade.activity.luck.service.business.ReceiveService;
import com.kikitrade.activity.luck.service.business.ReleaseService;
import com.kikitrade.activity.luck.service.model.ReceiveItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class ReceiveServiceImpl implements ReceiveService {

    @Resource
    private RedisService redisService;
    @Resource
    private LuckFortuneReceiveItemBuilder luckFortuneReceiveItemBuilder;
    @Resource
    @Lazy
    private ReleaseService releaseService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private SeqGeneraterService seqGeneraterService;
    @DubboReference
    private RemoteProductAccountService remoteProductAccountService;
    @Resource
    private CustomerService customerService;
    @Resource
    private RuleService ruleService;
    @Resource
    private KactivityModuleProperties kactivityModuleProperties;
    @Resource
    private AirDropEventService airDropEventService;

    /**
     * 领取红包
     * @param luckFortuneReceiveDTO
     * @return
     */
    @Override
    public Result<LuckFortuneReceiveResponse> receive(LuckFortuneReceiveDTO luckFortuneReceiveDTO) throws Exception {
        //红包详情
        String releaseItemStr = redisService.get(RedisKeyConst.LUCK_FORTUNE_RELEASE_INFO_KEY.getKey(luckFortuneReceiveDTO.getReleaseId()));
        LuckFortuneReleaseItem releaseItem = null;
        if(StringUtils.isNotBlank(releaseItemStr)){
            releaseItem = JSON.parseObject(releaseItemStr, LuckFortuneReleaseItem.class);
        }
        //红包已过期
        if(releaseItem == null || releaseItem.getExpiredTime().compareTo(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)) <= 0){
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_EXPIRED);
        }
        try{
            //获取一个红包
            ReceiveItem receiveItem = pullLuckFortune(releaseItem, luckFortuneReceiveDTO.getReceiveCode());
            if(receiveItem == null){
                throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_EMPTY_ERROR);
            }
            //发送延迟mq，等待金额回退
            airDropEventService.sendReceiveEvent(receiveItem);
            return Result.of(Result.ResultCode.SUCCESS, "", LuckFortuneReceiveResponse.builder()
                    .id(releaseService.encryptReleaseId(releaseItem.getId()))
                    .receiveId(receiveItem.getId())
                    .build());
        }catch (LuckFortuneException ex){
            log.error("抢红包业务异常,request:{}", luckFortuneReceiveDTO, ex);
            throw ex;
        }catch (Exception ex){
            log.error("抢红包异常",ex);
            throw new LuckFortuneException(LuckFortuneExceptionType.SYSTEM_ERROR);
        }
    }

    /**
     * 弹出个红包
     * @param releaseItem
     * @return
     */
    @Override
    public ReceiveItem pullLuckFortune(LuckFortuneReleaseItem releaseItem, String receiveCode) throws Exception {

        List<String> keys = new ArrayList<>();
        keys.add(RedisKeyConst.LUCK_FORTUNE_RELEASE_NOT_DRAW_KEY.getMiddleKey(releaseItem.getId()));
        keys.add(RedisKeyConst.LUCK_FORTUNE_RECEIVE_DETAIL_KEY.getMiddleKey(releaseItem.getId()));
        keys.add(RedisKeyConst.LUCK_FORTUNE_RELEASE_DREW_KEY.getMiddleKey(releaseItem.getId()));

        if(StringUtils.isNotBlank(releaseItem.getExpiredTime())){
            keys.add(String.valueOf((TimeUtil.parse(releaseItem.getExpiredTime()).getTime() - new Date().getTime())/1000 + TimeUnit.DAYS.toSeconds(4)));
        }else{
            keys.add("0");
        }
        keys.add(TimeUtil.getUtcTime(TimeUtil.addDay(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)), 3), TimeUtil.YYYYMMDDHHMMSS));
        keys.add(receiveCode);
        keys.add(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        String s = redisService.scriptEval(RedisScriptConstant.RECEIVE_SCRIPT, keys, new ArrayList<>());
        if("exist".equals(s)){
            //已抢过
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_REPEAT_ERROR);
        }else if("finish".equals(s)){
            //已抢空
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_EMPTY_ERROR);
        }else{
            ReceiveItem receiveItem = JSON.parseObject(s, ReceiveItem.class);
            receiveItem.setReleaseNickName(releaseItem.getNickName());
            receiveItem.setAvatar(releaseItem.getAvatar());
            receiveItem.setGreeting(releaseItem.getGreeting());
            receiveItem.setCover(releaseItem.getCover());
            receiveItem.setAssignType(releaseItem.getAssignType());
            receiveItem.setBusinessId(seqGeneraterService.next(CustomerSeqRuleBuilder.instance(luckFortuneReceiveItemBuilder.getTableName())));
            //我待激活的红包，用户点击消失
            redisService.hSet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(receiveCode), receiveItem.getId(), JSON.toJSONString(receiveItem));
            redisService.expireAt(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(receiveCode), TimeUtil.addDay(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)), 30));
            return receiveItem;
        }
    }

    /**
     * 查询发放记录
     * @param releaseId
     * @return
     */
    @Override
    public List<LuckFortuneReceiveItem> findItemByReleaseId(String releaseId) {
        return luckFortuneReceiveItemBuilder.findByReleaseId(releaseId);
    }

    /**
     * 查询领取记录
     *
     * @param releaseItem
     * @param refer
     * @return
     */
    @Override
    public List<LuckFortuneReceive> findItemByReleaseId(LuckFortuneReleaseItem releaseItem, String refer) {
        String releaseId = releaseItem.getId();
        //refer==null, h5,返回已领取的
        if(refer == null){
            List<String> receiveItems = redisService.lrange(RedisKeyConst.LUCK_FORTUNE_RELEASE_DREW_KEY.getMiddleKey(releaseId));
            if(CollectionUtils.isEmpty(receiveItems)){
                receiveItems = JSON.parseArray(releaseItem.getReceiveList(), String.class);
            }
            if(CollectionUtils.isNotEmpty(receiveItems)){
                return receiveItems.stream().map(item -> {
                    ReceiveItem receiveItem = JSON.parseObject(item, ReceiveItem.class);
                    return toLastItem(releaseId, receiveItem);
                }).collect(Collectors.toList());
            }
            return new ArrayList<>();
        }else{
            List<LuckFortuneReceiveItem> receiveItems = luckFortuneReceiveItemBuilder.findByReleaseId(releaseId);
            return receiveItems.stream().filter(item ->
                    ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_SUCCESS.getCode().equals(item.getStatus())
            ).map(item -> toLastItem(item, refer)).collect(Collectors.toList());
        }
    }

    /**
     * 查询领取记录
     * @param receiveCode
     * @param releaseId
     * @return
     */
    @Override
    public LuckFortuneReceiveItem findItemByReceiveCodeAndReleaseId(String receiveCode, String releaseId) {
        return luckFortuneReceiveItemBuilder.findByReceiveCodeIdAndReleaseId(receiveCode, releaseId);
    }

    /**
     * 查询领取记录
     *
     * @param customerId
     * @param releaseId
     * @return
     */
    @Override
    public LuckFortuneReceiveItem findItemByCustomerIdAndReleaseId(String customerId, String releaseId) {
        return luckFortuneReceiveItemBuilder.findByCustomerIdAndReleaseId(customerId, releaseId);
    }

    /**
     * 查看领取的红包
     * @param id
     * @return
     */
    @Override
    public LuckFortuneReceiveItem findById(String id) {
        return luckFortuneReceiveItemBuilder.findById(id);
    }

    /**
     * 最近领取的红包
     *
     * @param customer
     * @return
     */
    @Override
    public List<LuckFortuneReceive> lastReceiveLuckFortune(ActivityCustomerDTO customer) {

        List<LuckFortuneReceive> result = new ArrayList<>();
        //已领取的空投
        List<LuckFortuneReceive> luckFortuneDrewReceives = getDrewList(customer);
        log.info("luckFortuneDrewReceives:{}",luckFortuneDrewReceives);
        //已过期空投
        List<LuckFortuneReceive> luckFortuneExpireReceives = getExpireList(customer);
        log.info("luckFortuneExpireReceives:{}",luckFortuneExpireReceives);
        //待激活空投
        List<LuckFortuneReceive> luckFortuneActiveReceives = getNotActiveList(customer);
        log.info("luckFortuneActiveReceives:{}",luckFortuneActiveReceives);
        List<String> receiveId = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(luckFortuneDrewReceives)){
            receiveId.addAll(luckFortuneDrewReceives.stream().map(LuckFortuneReceive::getReceiveId).collect(Collectors.toList()));
        }
        if(CollectionUtils.isNotEmpty(luckFortuneActiveReceives)){
            List<LuckFortuneReceive> tempList = luckFortuneActiveReceives.stream().filter(r -> !receiveId.contains(r.getReceiveId())).collect(Collectors.toList());
            luckFortuneActiveReceives = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(tempList)){
                luckFortuneActiveReceives.addAll(tempList);
                receiveId.addAll(tempList.stream().map(LuckFortuneReceive::getReceiveId).collect(Collectors.toList()));
            }
            log.info("luckFortuneActiveReceives tempList:{}",luckFortuneActiveReceives);
        }
        if(CollectionUtils.isNotEmpty(luckFortuneExpireReceives)){
            List<LuckFortuneReceive> tempList = luckFortuneExpireReceives.stream().filter(r -> !receiveId.contains(r.getReceiveId())).collect(Collectors.toList());
            luckFortuneExpireReceives = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(tempList)){
                luckFortuneExpireReceives.addAll(tempList);
            }
            log.info("luckFortuneExpireReceives tempList:{}",luckFortuneExpireReceives);
        }
        if(CollectionUtils.isNotEmpty(luckFortuneActiveReceives)){
            result.addAll(luckFortuneActiveReceives);
        }
        if(CollectionUtils.isNotEmpty(luckFortuneExpireReceives)){
            result.addAll(luckFortuneExpireReceives);
        }
        if(CollectionUtils.isNotEmpty(luckFortuneDrewReceives)){
            result.addAll(luckFortuneDrewReceives);
        }
        return result;
    }

    /**
     * 获取待激活的空投
     * @param customer
     * @return
     */
    private List<LuckFortuneReceive> getNotActiveList(ActivityCustomerDTO customer){
        List<ReceiveItem> activeList = new ArrayList<>();
        if(StringUtils.isNotBlank(customer.getEmail())){
            log.info("customer email:{}end", customer.getEmail());
            Map<String, String> activeEmailMap = redisService.hGetAllMap(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(customer.getEmail()));
            if(MapUtils.isNotEmpty(activeEmailMap)){
                for(Map.Entry<String,String> entry : activeEmailMap.entrySet()){
                    ReceiveItem receiveItem = JSON.parseObject(entry.getValue(), ReceiveItem.class);
                    if(receiveItem.getReceiveCode().equals(customer.getEmail())){
                        activeList.add(receiveItem);
                    }
                }
            }
        }
        if(StringUtils.isNotBlank(customer.getPhone())){
            log.info("customer phone:{}end", customer.getPhone());
            Map<String, String> activePhoneMap = redisService.hGetAllMap(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(customer.getPhone()));
            if(MapUtils.isNotEmpty(activePhoneMap)){
                for(Map.Entry<String,String> entry : activePhoneMap.entrySet()){
                    ReceiveItem receiveItem = JSON.parseObject(entry.getValue(), ReceiveItem.class);
                    if(receiveItem.getReceiveCode().equals(customer.getPhone())){
                        activeList.add(receiveItem);
                    }
                }
            }
        }
        return buildLuckFortuneReceiveForReceive(activeList);
    }

    /**
     * 获取已过期的空投
     * @param customer
     * @return
     */
    private List<LuckFortuneReceive> getExpireList(ActivityCustomerDTO customer){
        List<ReceiveItem> activeList = new ArrayList<>();
        if(StringUtils.isNotBlank(customer.getEmail())){
            log.info("customer email:{}end", customer.getEmail());
            Map<String, String> activeEmailMap = redisService.hGetAllMap(RedisKeyConst.LUCK_FORTUNE_RECEIVE_EXPIRE_KEY.getKey(customer.getUserName()));
            if(MapUtils.isNotEmpty(activeEmailMap)){
                for(Map.Entry<String,String> entry : activeEmailMap.entrySet()){
                    ReceiveItem receiveItem = JSON.parseObject(entry.getValue(), ReceiveItem.class);
                    activeList.add(receiveItem);
                }
            }
        }
        return buildLuckFortuneReceiveForReceive(activeList);
    }

    /**
     * 获取已领取的空投
     * @param customer
     * @return
     */
    private List<LuckFortuneReceive> getDrewList(ActivityCustomerDTO customer){
        List<LuckFortuneReceiveItem> drewList = luckFortuneReceiveItemBuilder.findByCustomerIdAndStatus(customer.getId(), TimeUtil.getUtcTime(TimeUtil.addDay(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)),-3),TimeUtil.YYYYMMDDHHMMSS)
                , Collections.singletonList((long)ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_SUCCESS.getCode()));
        return buildLuckFortuneReceive(drewList);
    }
    /**
     * @return
     */
    @Override
    public Page<LuckFortuneReceive> historyReceiveLuckFortune(String customerId, String type, String startTime, String endTime, int offset, int limit) {
        Page<LuckFortuneReceiveItem> page = luckFortuneReceiveItemBuilder.findByCustomerId(customerId, type, startTime, endTime, offset, limit);
        return Page.with(page.getOffset(), page.getLimit(), page.getTotalCount(), buildLuckFortuneReceive(page.getRows()));
    }

    /**
     * 历史发放的空投数量
     *
     * @param customerId
     * @return
     */
    @Override
    public long countReceive(String customerId) {
        return luckFortuneReceiveItemBuilder.countReceive(customerId);
    }

    /**
     * 历史发放的空投总价值
     *
     * @param customerId
     * @return
     */
    @Override
    public BigDecimal sumAmountReceive(String customerId) {
        return luckFortuneReceiveItemBuilder.sumAmountReceive(customerId);
    }

    @Override
    public Result check(ReceiveItem receiveItem, ActivityCustomerDTO customerDTO) throws Exception{
        ProductAccountParam param = null;
        try{
            param = buildProductAccountParam(receiveItem.getBusinessId(), customerDTO, receiveItem.getCost().toPlainString());
            Map<String, ProductAccountResponse> productAccountTestResult = remoteProductAccountService.test(param);
            for(Map.Entry<String, ProductAccountResponse> result : productAccountTestResult.entrySet()){
                if(result.getValue().getCode() == ProductAccountResponseCode.PRODUCT_ACCOUNT_NOT_PASS){
                    if(ProductAccountConstant.LimitType.LIMIT_TYPE_AMOUNT.getLimitType().equals(result.getValue().getProductAccount().getLimitType())){
                        if(ProductAccountConstant.PeriodType.PERIOD_TYPE_DAY_TOTAL.getPeriodType().equals(result.getValue().getProductAccount().getPeriodType())){
                            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_LIMIT_AMOUNT_DAY_ERROR);
                        }else if(ProductAccountConstant.PeriodType.PERIOD_TYPE_MONTH_TOTAL.getPeriodType().equals(result.getValue().getProductAccount().getPeriodType())){
                            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_LIMIT_AMOUNT_MONTH_ERROR);
                        }
                    }else{
                        if(ProductAccountConstant.PeriodType.PERIOD_TYPE_DAY_TOTAL.getPeriodType().equals(result.getValue().getProductAccount().getPeriodType())){
                            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_LIMIT_DAY_ERROR);
                        }else if(ProductAccountConstant.PeriodType.PERIOD_TYPE_MONTH_TOTAL.getPeriodType().equals(result.getValue().getProductAccount().getPeriodType())){
                            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_LIMIT_MONTH_ERROR);
                        }
                    }
                }
            }
            return new Result(Result.ResultCode.SUCCESS, "");
        }catch (Exception ex){
            log.error("Receive open check:{}", param, ex);
            if(ex instanceof LuckFortuneException){
                throw ex;
            }
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_INVALID);
        }
    }

    /**
     * 空投回归
     *
     * @param releaseId
     * @param receiveId
     * @param receiveCode
     */
    @Override
    public void revert(String releaseId, String receiveId, String receiveCode) {
        List<String> keys = new ArrayList<>();
        Object obj;
        if((obj = redisService.hGet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(receiveCode), receiveId)) == null){
            return;
        }
        keys.add(RedisKeyConst.LUCK_FORTUNE_RELEASE_NOT_DRAW_KEY.getMiddleKey(releaseId));
        keys.add(RedisKeyConst.LUCK_FORTUNE_RECEIVE_DETAIL_KEY.getMiddleKey(releaseId));
        keys.add(RedisKeyConst.LUCK_FORTUNE_RELEASE_DREW_KEY.getMiddleKey(releaseId));
        keys.add(receiveCode);
        keys.add(RedisKeyConst.LUCK_FORTUNE_RECEIVE_EXPIRE_KEY.getKey(receiveCode));
        redisService.hDel(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(receiveCode), receiveId);
        String s = redisService.scriptEval(RedisScriptConstant.RECEIVE_REVERT_SCRIPT, keys, new ArrayList<>());
        redisService.hSet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_EXPIRE_KEY.getKey(receiveCode), receiveId,String.valueOf(obj));
        log.info("receive_revert, result:{}", s);
    }

    private ProductAccountParam buildProductAccountParam(String biz, ActivityCustomerDTO customerDTO, String amount){
        LuckFortuneRule rule = ruleService.findObject(customerDTO.getKycLevel(), customerService.getKolType(customerDTO.getId()));
        ProductAccountParam param = new ProductAccountParam();
        param.setBizId(biz);
        param.setProductLimitCodes(Collections.singletonList(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), rule.getKycLevel(), rule.getUserType())));
        param.setCustomerId(customerDTO.getId());
        param.setAmount(new BigDecimal(amount));
        return param;
    }

    private LuckFortuneReceive toLastItem(LuckFortuneReceiveItem receiveItem, String refer){
        return LuckFortuneReceive.builder()
                .id(receiveItem.getReleaseId())
                .receiveId(receiveItem.getId())
                .assignType(receiveItem.getAssignType())
                .releaseNickName(receiveItem.getReleaseNickName())
                .greeting(receiveItem.getGreeting())
                .amount(receiveItem.getAmount())
                .currency(receiveItem.getCurrency())
                .avatar(receiveItem.getAvatar())
                .receiveAvatar(receiveItem.getReceiveAvatar())
                .status(receiveItem.getStatus())
                .cover(receiveItem.getCover())
                .receiveTime(TimeUtil.parse(receiveItem.getReceiveTime()).getTime())
                .receiveCode(StringUtils.isBlank(receiveItem.getNickName()) ? receiveItem.getReceiveCode() : receiveItem.getNickName()).build();
    }

    private LuckFortuneReceive toLastItem(String releaseId, ReceiveItem receiveItem){
        return LuckFortuneReceive.builder()
                .id(releaseId)
                .receiveId(receiveItem.getId())
                .amount(receiveItem.getAmount())
                .currency(receiveItem.getCurrency())
                .receiveTime(StringUtils.isNotBlank(receiveItem.getReceiveTime()) ? TimeUtil.parse(receiveItem.getReceiveTime()).getTime() : TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)).getTime())
                .receiveCode(receiveItem.getReceiveCode()).build();
    }

    private List<LuckFortuneReceive> buildLuckFortuneReceive(List<LuckFortuneReceiveItem> items){
        return items.stream().map(item ->
                LuckFortuneReceive.builder()
                        .id(releaseService.encryptReleaseId(item.getReleaseId()))
                        .receiveId(item.getId())
                        .receiveCode(item.getReceiveCode())
                        .assignType(item.getAssignType())
                        .amount(item.getAmount())
                        .currency(item.getCurrency())
                        .receiveTime(TimeUtil.parse(item.getReceiveTime()).getTime())
                        .status(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_SUCCESS.getCode())
                        .avatar(item.getAvatar())
                        .releaseNickName(item.getReleaseNickName())
                        .greeting(item.getGreeting())
                        .assignType(item.getAssignType())
                        .cover(item.getCover())
                        .build()
        ).collect(Collectors.toList());
    }

    private List<LuckFortuneReceive> buildLuckFortuneReceiveForReceive(List<ReceiveItem> items){
        return items.stream().map(item ->
                LuckFortuneReceive.builder()
                        .id(releaseService.encryptReleaseId(item.getReleaseId()))
                        .receiveId(item.getId())
                        .amount(item.getAmount())
                        .receiveCode(item.getReceiveCode())
                        .currency(item.getCurrency())
                        .receiveTime(TimeUtil.parse(item.getReceiveTime()).getTime())
                        .status(TimeUtil.addMinute(TimeUtil.parse(item.getReceiveTime()), kactivityModuleProperties.getLuck().getValidMinutes()).compareTo(new Date()) <= 0
                                ? ActivityConstant.LuckFortuneReceiveStatus.EXPIRED.getCode()
                                : ActivityConstant.LuckFortuneReceiveStatus.NOT_ACTIVATE.getCode())
                        .remainTime(TimeUtil.addMinute(TimeUtil.parse(item.getReceiveTime()), kactivityModuleProperties.getLuck().getValidMinutes()).compareTo(new Date()) <= 0
                                ? -1L
                                : TimeUtil.addMinute(TimeUtil.parse(item.getReceiveTime()), kactivityModuleProperties.getLuck().getValidMinutes()).getTime() - new Date().getTime())
                        .avatar(item.getAvatar())
                        .releaseNickName(item.getReleaseNickName())
                        .greeting(item.getGreeting())
                        .assignType(item.getAssignType())
                        .cover(item.getCover())
                        .build()
        ).sorted((o1, o2) -> (int)(o2.getReceiveTime() - o1.getReceiveTime())).collect(Collectors.toList());
    }
}