package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/8/21 21:37
 */
@Getter
@Setter
@Table(name = "activity_reward_info")
public class ActivityRewardInfo extends BaseModel {

    //交易日期yyyyMMdd
    @Column(name = "tran_date")
    private String tranDate;

    //活动ID
    @Column(name = "activity_id")
    private Integer activityId;


    //活动类型
    @Column(name = "activity_type")
    private Integer activityType;

    //总笔数
    @Column(name = "total_num")
    private Integer totalNum;

    //总金额
    @Column(name = "total_amt")
    private BigDecimal totalAmt;


}
