package com.kikitrade.activity.dal.mysql.model;

import com.kikitrade.activity.dal.IdCustomer;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@Table(name = "activity_task_config")
public class ActivityTaskConfig extends BaseModel implements Serializable {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @KeySql(genId = IdCustomer.class)
    private String id;

    /**
     * 任务事件
     */
    @Column(name = "event")
    private String event;

    /**
     * 任务所属等级lv0、lv1
     */
    @Column(name = "level")
    private String level;

    /**
     * 任务类型 @see TaskTypeEnum
     */
    @Column(name = "type")
    private String type;

    @Column(name = "name_cn")
    private String nameCn;

    @Column(name = "name_en")
    private String nameEn;

    @Column(name = "name_hk")
    private String nameHk;

    @Column(name = "activity_id")
    private String activityId;

    /**
     * 任务回调地址
     */
    @Column(name = "callback")
    private String callback;

    /**
     * 任务落地页
     */
    @Column(name = "url")
    private String url;

    /**
     * 周期类型，一次性任务，周期性任务
     */
    @Column(name = "cycle_type")
    private String cycleType;

    /**
     * 任务周期 @see TaskCycleEnum
     */
    @Column(name = "cycle")
    private String cycle;

    /**
     * 门槛 1/{5}
     */
    @Column(name = "complete_threshold")
    private Integer completeThreshold;

    @Column(name = "desc_cn")
    private String descCn;

    @Column(name = "desc_en")
    private String descEn;

    @Column(name = "desc_hk")
    private String descHk;

    @Column(name = "icon")
    private String icon;

    @Column(name = "status")
    private Integer status;

    /**
     * 奖励的积分数
     */
    @Column(name = "amount")
    @ColumnType(column = "amount", jdbcType = JdbcType.DECIMAL)
    private BigDecimal amount;

    @Column(name = "award")
    private String award;

    /**
     * 任务开始时间
     */
    @Column(name = "start_time")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    private Date startTime;

    /**
     * 任务结束时间
     */
    @Column(name = "end_time")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    private Date endTime;

    @Column(name = "award_time_type")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    private Integer awardTimeType;

    @Column(name = "complete_times")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    private Integer completeTimes;

    @Column(name = "name_i18n")
    private String nameI18n;

    @Column(name = "desc_i18n")
    private String descI18n;
}

