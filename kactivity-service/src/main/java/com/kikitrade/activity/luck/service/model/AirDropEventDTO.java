package com.kikitrade.activity.luck.service.model;

import lombok.Data;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-14 14:17
 */
@Data
public class AirDropEventDTO implements Serializable {

    /**
     * 消息类型，发放、领取
     */
    private String event;

    /**
     * 发放的空投id
     */
    private String releaseId;

    /**
     * 发放人
     */
    private String releaseCustomerId;

    /**
     * 领取空投的id
     */
    private String receiveId;

    /**
     * 领取人
     */
    private String receiveCode;
}
