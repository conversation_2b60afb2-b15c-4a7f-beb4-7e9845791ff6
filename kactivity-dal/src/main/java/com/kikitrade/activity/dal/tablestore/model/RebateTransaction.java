package com.kikitrade.activity.dal.tablestore.model;


import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @project exchange
 * @create 23/07/2018
 * @description
 **/
@Data
@Table(name = "rebate_transaction")
public class RebateTransaction implements Serializable {

    public static final String INDEX_REFERRER_CUSTOMER = "rebate_transaction_index_refer_customer";

    private static final long serialVersionUID = -1847009177261737665L;

    @PartitionKey(value = 0, name = "referrer_id")
    //推荐人id
    @SearchIndex(name = INDEX_REFERRER_CUSTOMER, column = "referrer_id")
    private String referrerId;

    @PartitionKey(value = 1, name = "business_id")
    @SearchIndex(name = INDEX_REFERRER_CUSTOMER, column = "business_id")
    private String businessId;

    //用户id
    @Column(name = "customer_id", isDefined = true)
    @SearchIndex(name = INDEX_REFERRER_CUSTOMER, column = "customer_id")
    private String customerId;

    //返佣金额
    @Column(name = "amount", isDefined = true, type = Column.Type.DOUBLE)
    @SearchIndex(name = INDEX_REFERRER_CUSTOMER, column = "amount", fieldType = FieldType.DOUBLE)
    private BigDecimal amount;

    //返佣币种
    @Column(name = "currency")
    private String currency;

    //返佣时间
    @Column(name = "payment_time")
    private Date paymentTime;

    //创建时间
    @Column(name = "create_time")
    private Date createTime;
    //更新时间
    @Column(name = "update_time")
    private Date updateTime;

}
