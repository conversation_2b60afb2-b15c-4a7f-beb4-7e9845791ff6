package com.kikitrade.activity.service.engine.process;


import com.kikitrade.activity.service.business.ActivityService;
import com.kikitrade.activity.dal.mysql.model.Activity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Setter
@Getter
@Component
public class ActivityBatchProcessExecutor {
    private int corePoolSize = 5;
    private int maximumPoolSize = 100;
    private long keepAliveTime = 30000;
    private int innerQueueCapacity = 1000;
    private static TimeUnit timeUnit = TimeUnit.MILLISECONDS;
    private BlockingQueue<Runnable> workQueue;
    private static ExecutorService executor;

    @Resource
    private ActivityService activityService;


    public ActivityBatchProcessExecutor() {

    }

    public ActivityBatchProcessExecutor(ExecutorService executorService) {
        executor = executorService;
    }

    public void init() {
        if (workQueue == null) {
            workQueue = new LinkedBlockingQueue<Runnable>(innerQueueCapacity);
        }
        if (executor == null) {
            executor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.MILLISECONDS, workQueue,new CustomThreadFactory(),new CustomRejectedExecutionHandler());
        }
    }

    public void submit(String business_id, Activity activity) {
        executor.execute(new ActivityBatchProcessThread(business_id, activity));

    }

    private class CustomThreadFactory implements ThreadFactory {
        private AtomicInteger count = new AtomicInteger(0);
        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r);
            String threadName = ActivityBatchProcessThread.class.getSimpleName() +"-"+ count.addAndGet(1);
            t.setName(threadName);
            return t;
        }
    }

    private class CustomRejectedExecutionHandler implements RejectedExecutionHandler {
        @Override
        //控制流量，减少拒绝
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            try {
                executor.getQueue().put(r);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}
