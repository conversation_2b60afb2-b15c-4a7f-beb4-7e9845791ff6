package com.kikitrade.activity.luck.service.business.impl;

import com.kikitrade.activity.luck.service.business.AirDropBaseService;
import com.kikitrade.market.client.CurrencyClient;
import com.kikitrade.market.client.TickerClient;
import com.kikitrade.market.client.service.impl.ClientExchangeService;
import com.kikitrade.market.common.model.CurrencyDTO;
import com.kikitrade.market.common.model.TickerDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @author: wang
 * @date: 2023/3/14
 * @desc:
 */
@Service
@Slf4j
public class AirDropBaseServiceImpl implements AirDropBaseService {
    @Resource
    private TickerClient tickerClient;
    @Resource
    private CurrencyClient currencyClient;
    @Resource
    private ClientExchangeService clientExchangeService;

    @Override
    public BigDecimal getPrice(BigDecimal amount, String fromCurrency, String toCurrency, Integer keepDecimalForCoin) {
        if(fromCurrency.equals(toCurrency)){
            return amount;
        }
        TickerDTO tickerDTO = tickerClient.get(String.format("%s_%s", fromCurrency, toCurrency));
        if(tickerDTO != null){
            return amount.multiply(tickerDTO.getPrice()).setScale(keepDecimalForCoin, BigDecimal.ROUND_DOWN);
        }
        return clientExchangeService.exchangeByPrecision(amount, fromCurrency, toCurrency, keepDecimalForCoin, RoundingMode.DOWN);
    }

    @Override
    public Integer getKeepDecimalForCoin(String currency) {
        CurrencyDTO currencyDTO = currencyClient.get(currency);
        return currencyDTO != null ? currencyDTO.getKeepDecimalForCoin() : 8;
    }
}
