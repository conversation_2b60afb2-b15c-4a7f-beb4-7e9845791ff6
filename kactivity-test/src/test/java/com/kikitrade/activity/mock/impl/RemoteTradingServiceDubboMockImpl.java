package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteTradingServiceDubboMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.trade.api.RemoteTradingService;
import com.kikitrade.trade.api.model.TransferBalanceDTO;
import com.kikitrade.trade.api.model.request.TransferBalanceRequest;
import com.kikitrade.trade.api.model.request.TransferRequest;
import com.kikitrade.trade.api.model.response.TransferBalanceResponse;
import com.kikitrade.trade.common.TradingResponseCode;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;

@Component
public class RemoteTradingServiceDubboMockImpl implements RemoteTradingServiceDubboMock {

    @Resource
    RemoteTradingService remoteTradingService;


    @Override
    public void transfer() {
        Mockito.doAnswer(c -> {

            TransferBalanceDTO transferBalanceDTO = TransferBalanceDTO.builder().build();
            transferBalanceDTO.setAvailable(BigDecimal.valueOf(100000L));


            TransferBalanceResponse transferBalanceResponse = TransferBalanceResponse.builder().build();
            transferBalanceResponse.setCode(TradingResponseCode.SUCCESS);
            transferBalanceResponse.setTransferBalanceDTO(transferBalanceDTO);

            return transferBalanceResponse;
        }).when(remoteTradingService).transfer(Mockito.any(TransferRequest.class));

    }

    @Override
    public void transferBalance() {

        Mockito.doAnswer(c -> {
            TransferBalanceDTO transferBalanceDTO = TransferBalanceDTO.builder().build();
            transferBalanceDTO.setAvailable(BusinessOP.available);
            transferBalanceDTO.setTransferAvailable(BigDecimal.valueOf(100000L));

            TransferBalanceResponse transferBalanceResponse = TransferBalanceResponse.builder().build();
            transferBalanceResponse.setCode(TradingResponseCode.SUCCESS);
            transferBalanceResponse.setTransferBalanceDTO(transferBalanceDTO);

            return transferBalanceResponse;
        }).when(remoteTradingService).transferBalance(Mockito.any(TransferBalanceRequest.class));

    }
}
