package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.PrecisionTrack;

/**
 * Builder for PrecisionTrack
 */
public interface PrecisionTrackBuilder {

    /**
     * Insert a new PrecisionTrack record
     * @param precisionTrack the PrecisionTrack record to insert
     * @return true if the insertion was successful, false otherwise
     */
    boolean insert(PrecisionTrack precisionTrack);

    String getTableName();
}
