package com.kikitrade.activity.service.facade.task;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
import com.kikitrade.activity.facade.task.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.activity.service.exception.BussinessException;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;


@Component
@Slf4j
public class ActivityTaskFacadeImpl extends ActivityTaskFacadeGrpc.ActivityTaskFacadeImplBase{


    @Autowired
    private ActivityTaskService activityTaskService;



    @Override
    public void save(TaskDTO request, StreamObserver<CommonResponse> responseObserver) {
        try{
            log.info("save request:{}", request);
            // 参数检查
            checkTaskDtoParam(request);
            // 参数转换
            ActivityTaskConfig activityTaskConfig = transferTaskDto2Po(request);
            log.info("TaskFacadeImpl save activityTaskConfig:{}", JSON.toJSONString(activityTaskConfig));
            //保存数据
            ActivityTaskConfig result;
            if(StringUtils.isBlank(request.getId())){
                activityTaskConfig.setId(null);
                result = activityTaskService.insert(activityTaskConfig);
            }else{
                result = activityTaskService.update(activityTaskConfig);
            }
            log.info("TaskFacadeImpl save result:{}", result);
            CommonResponse response;
            if (result == null ) {
                response = CommonResponse.newBuilder()
                        .setSuccess(false)
                        .setMessage("save activity task error")
                        .build();
            } else {
                response = CommonResponse.newBuilder()
                        .setSuccess(true)
                        .setMessage("success")
                        .setData(TaskVO.newBuilder()
                                .setId(result.getId())
                                .setActivityId(result.getActivityId())
                                .build())
                        .build();
            }
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (BussinessException e) {
            log.warn("request param error:{}", e.getMessage());
            responseObserver.onNext(
                    CommonResponse.newBuilder()
                            .setSuccess(false)
                            .setMessage(e.getMessage())
                            .build());
            responseObserver.onCompleted();
        } catch (Exception ex){
            log.error("ActivityFacade exception", ex);
            responseObserver.onError(ex);
        }
    }


    private void checkTaskDtoParam(TaskDTO request) throws BussinessException {

        String startTimeStr = request.getStartTime();
        String endTimeStr = request.getEndTime();
        if (StringUtils.isBlank(startTimeStr)) {
            throw new BussinessException("startTime为空");
        }
        Date startTime;
        Date endTime;
        try {
            startTime = TimeUtil.parse(startTimeStr);
        } catch (Exception e) {
            throw new BussinessException("startTime格式错误");
        }
        if (StringUtils.isNotBlank(endTimeStr)) {
            try {
                endTime = TimeUtil.parse(startTimeStr);
            } catch (Exception e) {
                throw new BussinessException("endTime格式化错误");
            }
            if (startTime.compareTo(endTime) > 0) {
                throw new BussinessException("endTime在startTime之前");
            }
        }
        if (StringUtils.isBlank(request.getTaskNameEn())) {
            throw new BussinessException("taskNameEn为空");
        }
        if (StringUtils.isBlank(request.getTaskNameCn())) {
            throw new BussinessException("taskNameC为空");
        }
        if (StringUtils.isBlank(request.getTaskNameHk())) {
            throw new BussinessException("taskNameHk为空");
        }
        if (StringUtils.isBlank(request.getDescEn())) {
            throw new BussinessException("descEn为空");
        }
        if (StringUtils.isBlank(request.getDescCn())) {
            throw new BussinessException("descCn为空");
        }
        if (StringUtils.isBlank(request.getDescEn())) {
            throw new BussinessException("descEn为空");
        }
        if (StringUtils.isBlank(request.getVipLevel())) {
            throw new BussinessException("vipLevel为空");
        }
        if (StringUtils.isBlank(request.getVipLevel())) {
            throw new BussinessException("vipLevel为空");
        }
        if (request.getType() == null) {
            throw new BussinessException("type为空");
        }
        if (request.getCycleType() == null) {
            throw new BussinessException("cycleType为空");
        }
        if (request.getCycle() == null) {
            throw new BussinessException("cycle为空");
        }
        if (request.getEvent() == null) {
            throw new BussinessException("event为空");
        }
    }

    private ActivityTaskConfig transferTaskDto2Po(TaskDTO request) {

        ActivityTaskConfig activityTaskConfig = new ActivityTaskConfig();
        activityTaskConfig.setId(request.getId());
        activityTaskConfig.setNameCn(request.getTaskNameCn());
        activityTaskConfig.setNameEn(request.getTaskNameEn());
        activityTaskConfig.setNameHk(request.getTaskNameHk());
        activityTaskConfig.setStatus(request.getStatus().getNumber());
        activityTaskConfig.setDescCn(request.getDescCn());
        activityTaskConfig.setDescEn(request.getDescEn());
        activityTaskConfig.setDescHk(request.getDescHk());
        activityTaskConfig.setNameI18n(activityTaskConfig.getNameEn());
        activityTaskConfig.setDescI18n(activityTaskConfig.getDescEn());
        log.info("startTime:{}, {}, {}, {}，{}", request.getStartTime(),
                TimeUtil.parse(request.getStartTime()),
                TimeUtil.getUtcTime(TimeUtil.parse(request.getStartTime())),
                TimeUtil.getFormatedDateString(TimeUtil.parse(request.getStartTime()), 0),
                TimeUtil.getFormatedDateString(TimeUtil.parse(request.getStartTime()), -8));

        activityTaskConfig.setStartTime(TimeUtil.parse(TimeUtil.getFormatedDateString(TimeUtil.parse(request.getStartTime()), -8)));
        activityTaskConfig.setEndTime(StringUtils.isBlank(request.getEndTime())? null : TimeUtil.parse(TimeUtil.getFormatedDateString(TimeUtil.parse(request.getEndTime()), -8)));
        activityTaskConfig.setLevel(request.getVipLevel());
        activityTaskConfig.setType(request.getType().name());
        activityTaskConfig.setCycleType(request.getCycleType().name());
        activityTaskConfig.setCycle(request.getCycle().name());
        activityTaskConfig.setEvent(request.getEvent().name());
        activityTaskConfig.setCompleteThreshold(StringUtils.isBlank(request.getCompleteThreshold())  ? 1 : Integer.parseInt(request.getCompleteThreshold()));
        activityTaskConfig.setAmount(new BigDecimal(request.getAward()));
        activityTaskConfig.setUrl(request.getUrl());
        activityTaskConfig.setIcon(request.getIcon());
        activityTaskConfig.setAward(ActivityConstant.AwardTypeEnum.POINT.name());
        activityTaskConfig.setAwardTimeType(request.getAwardTimeType().getNumber());
        activityTaskConfig.setCompleteTimes(StringUtils.isBlank(request.getCompleteTimes()) ? 1 : Integer.parseInt(request.getCompleteTimes()));
        return activityTaskConfig;
    }
}

