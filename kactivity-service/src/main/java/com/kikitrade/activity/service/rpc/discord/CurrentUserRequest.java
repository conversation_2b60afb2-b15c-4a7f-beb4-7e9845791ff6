package com.kikitrade.activity.service.rpc.discord;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.service.common.config.DiscordProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.json.JSONObject;

import java.time.OffsetDateTime;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 16:12
 */
@Data
@Slf4j
public class CurrentUserRequest extends AccessToken {

    public CurrentUserRequest(){}

    public CurrentUserRequest(DiscordProperties discordProperties, String accessToken){
        this.accessToken = accessToken;
    }



    public SocialUserInfo execute() {
         SocialUserInfo cache = DiscordCache.get(this.accessToken, SocialUserInfo.class);
        if(cache != null){
            return cache;
        }
        Request request = new Request.Builder()
                .url("https://discord.com/api/v10/users/@me")
                .addHeader("Authorization", "Bearer " + this.accessToken)
                .build();

        try {
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("[discord] currentUser:{}", jsonObject);

            String id = jsonObject.getString("id");
            String username = jsonObject.getString("username");
            String avatarUrl = null;
            if (jsonObject.has("avatar") && !jsonObject.isNull("avatar")) {
                avatarUrl = String.format("https://cdn.discordapp.com/avatars/%s/%s.png",
                    id, jsonObject.getString("avatar"));
            }

            SocialUserInfo userInfo = SocialUserInfo.builder()
                .userId(id)
                .userName(username)
                .profileImageUrl(avatarUrl)
                .createdAt(OffsetDateTime.now().toEpochSecond())
                .build();
            DiscordCache.put(this.accessToken, JSON.toJSONString(userInfo));
            return userInfo;
        } catch (Exception e) {
            log.error("[discord] currentUser error:{}", accessToken ,e);
            return null;
        }
    }
}
