package com.kikitrade.activity.service.common.strategy.line;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/22 18:40
 * @description: Line跳过策略服务
 */
@Component
@Slf4j
public class LineSkipStrategyService implements LineSaasStrategyService{

    @Override
    public String strategy() {
        return SaasStrategyConstant.LineStrategyEnum.SKIP.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        log.info("LineSkipStrategyService execute, request: {}", request);
        // 跳过策略，不执行任何操作
    }
}
