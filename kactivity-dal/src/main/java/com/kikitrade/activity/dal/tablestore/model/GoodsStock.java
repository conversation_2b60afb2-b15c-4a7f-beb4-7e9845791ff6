package com.kikitrade.activity.dal.tablestore.model;

import com.dipbit.dtm.client.domain.stock.StockOrderModel;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/1/3 14:18
 */
@Table(name = "goods_stock")
@Data
public class GoodsStock extends StockOrderModel {

    /**
     * 商品名称
     */
    @Column(name = "sku_name")
    private String skuName;
}
