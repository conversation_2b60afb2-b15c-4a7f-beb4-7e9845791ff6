package com.kikitrade.activity.service.auth;

import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.model.exception.ActivityException;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/11 14:46
 */
public interface AuthService {

    Token auth(String saasId, String platform, String code, String redirectUri, String customerId, String twitterId, String socialId, String socialName) throws ActivityException;

    Token getAuthToken(String saasId, String customerId, String platform);

    Boolean resetAuth(String saasId, String customerId, String platform);

    Boolean sendVerifyCode(String saasId, String customerId, String customerName, String channel, String receiver, String sceneCode);
}
