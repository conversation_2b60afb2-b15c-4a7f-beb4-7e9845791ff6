package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityActions;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityActionsDao extends BaseDao<ActivityActions, String> {


    int insert(ActivityActions aa);

    int update(ActivityActions aa);

    int deleteById(Integer id);

    int updateStatus(@Param("id") Integer id, @Param("status") Integer status);

    ActivityActions findById(Integer id);

    List<ActivityActions> findAll(@Param("offset") Integer offset, @Param("limit") Integer limit);

    List<ActivityActions> findByType(@Param("activity_type") Integer activity_type);
}
