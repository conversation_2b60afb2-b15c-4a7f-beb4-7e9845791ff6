package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.Index;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/23 20:26
 */
@Data
@Table(name = "activity_task_statis")
public class ActivityTaskStatis implements Serializable {

    @PartitionKey(name = "target_id", value = 0)
    private String targetId;

    @PartitionKey(name = "customer_id", value = 1)
    private String customerId;

    @PartitionKey(name = "cycle", value = 2)
    private String cycle;

    @PartitionKey(name = "task_id", value = 1)
    private String taskId;

    @Column(name = "progress")
    private Integer progress;

    @Column(name = "saas_id")
    private String saasId;
}
