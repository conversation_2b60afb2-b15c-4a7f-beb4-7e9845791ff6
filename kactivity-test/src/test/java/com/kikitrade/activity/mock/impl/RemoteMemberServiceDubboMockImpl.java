package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteMemberServiceDubboMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.reward.impl.CustomerServiceImpl;
import com.kikitrade.activity.service.task.action.ActivityEventAction;
import com.kikitrade.activity.util.ReflectionUtils;
import com.kikitrade.kcustomer.api.service.RemoteCustomerInviteService;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.model.MembershipRichDTO;
import com.kikitrade.member.model.QuestsLadderDTO;
import com.kikitrade.member.model.request.MembershipRequest;
import com.kikitrade.member.model.request.QuestLadderRequest;
import com.kikitrade.member.model.response.MembershipResponse;
import com.kikitrade.member.model.response.ResponseCode;
import org.mockito.Mockito;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
public class RemoteMemberServiceDubboMockImpl implements RemoteMemberServiceDubboMock {

    @Resource
    RemoteMemberService remoteMemberService;
    @Resource
    ApplicationContext applicationContext;
    @Resource
    CustomerServiceImpl customerService;
    @Resource
    ActivityEventAction activityEventAction;

//    @PostConstruct
//    public void init() throws NoSuchFieldException, ClassNotFoundException, IllegalAccessException {
//        RemoteMemberService fromSpring = applicationContext.getBean("remoteMemberServiceStub", RemoteMemberService.class);
//        String fieldName = "remoteMemberService";
//        String classPath = "com.kikitrade.activity.service.reward.impl.CustomerServiceImpl";
//        ReflectionUtils.setField(classPath, fieldName, customerService, fromSpring);
//        ReflectionUtils.setField(ActivityEventAction.class, fieldName, activityEventAction, fromSpring);
//    }


    @Override
    public void membership() {
        Mockito.doAnswer(c -> {
            MembershipRequest request = c.getArgument(0);

            MembershipRichDTO membershipRichDTO = new MembershipRichDTO();
            membershipRichDTO.setLevel(BusinessOP.vip);
            return MembershipResponse.builder().code(ResponseCode.SUCCESS).membership(membershipRichDTO).build();
        }).when(remoteMemberService).membership(Mockito.any(MembershipRequest.class));

    }

    @Override
    public void questsLaddersByLevel() {
        Mockito.doAnswer(c -> {
            QuestLadderRequest request = c.getArgument(0);
            QuestsLadderDTO ladderDTO = QuestsLadderDTO.builder().customerId("123")
                    .level(2)
                    .point(new BigDecimal("100"))
                    .build();
            List<QuestsLadderDTO> ladders = new ArrayList<>();
            ladders.add(ladderDTO);
            return ladders;
        }).when(remoteMemberService).questsLaddersByLevel(Mockito.any(QuestLadderRequest.class));
    }
}
