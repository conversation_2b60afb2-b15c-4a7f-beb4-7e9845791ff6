package com.kikitrade.activity.service.rpc.twitter;

import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.twitter.clientlib.StringUtil;
import com.twitter.clientlib.model.Tweet;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/4 15:31
 */
@Data
@Slf4j
public class ReplyPostRequest extends AccessToken {

    private TwitterProperties twitterProperties;
    private String from;
    private String keyword;
    private OffsetDateTime startTime;
    private OffsetDateTime endTime;
    private Integer maxResult;

    public ReplyPostRequest(TwitterProperties twitterProperties, AccessToken accessToken){
        super(accessToken);
        this.twitterProperties = twitterProperties;
    }

    /**
     * from回复的to
     * @param from
     * @param to
     */
    public ReplyPostRequest build(String from, String keyword, OffsetDateTime startTime, OffsetDateTime endTime){
        this.from = from;
        this.keyword = keyword;
        this.startTime = startTime;
        this.endTime = endTime;
        return this;
    }

    /**
     * from回复的to
     * @return
     */
    public List<Tweet> execute(){
        try {
            //from:mrmel_m to:cryptocasey is:reply
            return getApi(accessToken, refreshToken, twitterProperties).tweets()
                    .tweetsRecentSearch(String.format("(%s) from:%s is:reply", keyword, from))
                    .tweetFields(Set.of("in_reply_to_user_id"))
                    .startTime(this.startTime)
                    .endTime(this.endTime)
                    .execute()
                    .getData();
        }catch (Exception ex){
            log.error("ReplyPostRequest:{}", from, ex);
            return null;
        }
    }
}
