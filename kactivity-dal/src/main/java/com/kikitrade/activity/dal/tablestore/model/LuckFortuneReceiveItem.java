package com.kikitrade.activity.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;

/**
 * 红包领取记录表
 *
 */
@Data
@Table(name = "luck_fortune_receive_item")
public class LuckFortuneReceiveItem implements Serializable {

    public static final String SEARCH_RECEIVE_BY_ID = "search_receive_by_id";

    public static final String IDX_RECEIVE_BY_ID = "idx_receive_by_id";
    public static final String IDX_RECEIVE_BY_CUSTOMER_ID = "idx_receive_by_customer_id";
    public static final String IDX_RECEIVE_BY_RECEIVE_CODE = "idx_receive_by_receive_code";
    public static final String IDX_RECEIVE_BY_EXPIRED_DATE = "idx_receive_by_status_expired";

    /**
     * 红包领取人
     */
    @PartitionKey(name = "customer_id")
    @SearchIndex(name = SEARCH_RECEIVE_BY_ID, column = "customer_id")
    @Index(name = IDX_RECEIVE_BY_ID, pkColumn = "customer_id", pkValue = 1)
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, pkColumn = "customer_id", pkValue = 2)
    @Index(name = IDX_RECEIVE_BY_RECEIVE_CODE, pkColumn = "customer_id", pkValue = 2)
    @Index(name = IDX_RECEIVE_BY_EXPIRED_DATE, pkColumn = "customer_id", pkValue = 2)
    private String customerId;

    /**
     * 发放的红包id，分区键
     */
    @PartitionKey(name = "release_id", value = 1)
    @SearchIndex(name = SEARCH_RECEIVE_BY_ID, column = "release_id")
    @Index(name = IDX_RECEIVE_BY_ID, pkColumn = "release_id", pkValue = 2)
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, pkColumn = "release_id", pkValue = 1)
    @Index(name = IDX_RECEIVE_BY_RECEIVE_CODE, pkColumn = "release_id", pkValue = 1)
    @Index(name = IDX_RECEIVE_BY_EXPIRED_DATE, pkColumn = "release_id", pkValue = 3)
    private String releaseId;


    /**
     * 主键
     */
    @SearchIndex(name = SEARCH_RECEIVE_BY_ID, column = "id")
    @Index(name = IDX_RECEIVE_BY_ID, pkColumn = "id")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "id")
    @Index(name = IDX_RECEIVE_BY_RECEIVE_CODE, dfColumn = "id")
    @Index(name = IDX_RECEIVE_BY_EXPIRED_DATE, dfColumn = "id", pkValue = 3)
    @Column(name = "id", isDefined = true)
    private String id;

    /**
     * 红包发放人用户名
     */
    @Column(name = "release_customer_id", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "release_customer_id")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "release_customer_id")
    @Index(name = IDX_RECEIVE_BY_EXPIRED_DATE, dfColumn = "release_customer_id")
    private String releaseCustomerId;

    /**
     * 红包发放人用户名
     */
    @Column(name = "release_nick_name", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "release_nick_name")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "release_nick_name")
    private String releaseNickName;

    /**
     * 红包领取人用户名
     */
    @Column(name = "user_name", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "user_name")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "user_name")
    private String userName;

    /**
     * 发放的红包封面
     */
    @Column(name = "cover", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "cover")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "cover")
    @Index(name = IDX_RECEIVE_BY_RECEIVE_CODE, dfColumn = "cover")
    private String cover;

    /**
     * 祝福语
     */
    @Column(name = "greeting", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "greeting")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "greeting")
    @Index(name = IDX_RECEIVE_BY_RECEIVE_CODE, dfColumn = "greeting")
    private String greeting;

    /**
     * 红包发放人头像
     */
    @Column(name = "avatar", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "avatar")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "avatar")
    @Index(name = IDX_RECEIVE_BY_RECEIVE_CODE, dfColumn = "avatar")
    private String avatar;

    /**
     * 红包发放人头像
     */
    @Column(name = "receive_avatar", isDefined = true)
    @SearchIndex(name = SEARCH_RECEIVE_BY_ID, column = "receive_avatar")
    private String receiveAvatar;

    /**
     * 领取标识
     */
    @Column(name = "receive_code", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "receive_code")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "receive_code")
    @Index(name = IDX_RECEIVE_BY_RECEIVE_CODE, pkColumn = "receive_code")
    private String receiveCode;

    @Column(name = "amount", isDefined = true, type = Column.Type.DOUBLE)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "amount")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "amount")
    @Index(name = IDX_RECEIVE_BY_EXPIRED_DATE, dfColumn = "amount")
    private String amount;

    @SearchIndex(name = SEARCH_RECEIVE_BY_ID, column = "cost", fieldType = FieldType.DOUBLE)
    @Column(name = "cost", isDefined = true, type = Column.Type.DOUBLE)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "cost")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "cost")
    private Double cost;
    
    @Column(name = "currency", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "currency")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "currency")
    @Index(name = IDX_RECEIVE_BY_EXPIRED_DATE, dfColumn = "currency")
    private String currency;

    @Column(name = "type", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "type")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "type")
    private String type;

    @Column(name = "assign_type", isDefined = true)
    @SearchIndex(name = SEARCH_RECEIVE_BY_ID, column = "assign_type")
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "assign_type")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "assign_type")
    private String assignType;

    @Column(name = "receive_time", isDefined = true)
    @SearchIndex(name = SEARCH_RECEIVE_BY_ID, column = "receive_time")
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "receive_time")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "receive_time")
    private String receiveTime;

    @Column(name = "expired_time", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "expired_time")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "expired_time")
    @Index(name = IDX_RECEIVE_BY_EXPIRED_DATE, pkColumn = "expired_time", pkValue = 1)
    private String expiredTime;

    @Column(name = "status", isDefined = true, type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_RECEIVE_BY_ID, column = "status", fieldType = FieldType.LONG)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "status")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "status")
    @Index(name = IDX_RECEIVE_BY_RECEIVE_CODE, dfColumn = "status")
    @Index(name = IDX_RECEIVE_BY_EXPIRED_DATE, pkColumn = "status")
    private Integer status;

    @Column(name = "business_id", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "business_id")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "business_id")
    @Index(name = IDX_RECEIVE_BY_RECEIVE_CODE, dfColumn = "business_id")
    private String businessId;

    @Column(name = "nick_name", isDefined = true)
    @Index(name = IDX_RECEIVE_BY_ID, dfColumn = "nick_name")
    @Index(name = IDX_RECEIVE_BY_CUSTOMER_ID, dfColumn = "nick_name")
    @Index(name = IDX_RECEIVE_BY_RECEIVE_CODE, dfColumn = "nick_name")
    private String nickName;
}
