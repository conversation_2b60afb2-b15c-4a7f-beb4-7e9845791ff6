package com.kikitrade.activity.dal;

import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.genid.GenId;

@Component
public class IdCustomer implements GenId<String>, ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public String genId(String table, String column) {
        return applicationContext.getBean(SeqGeneraterService.class).next(CustomerSeqRuleBuilder.instance(table));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
