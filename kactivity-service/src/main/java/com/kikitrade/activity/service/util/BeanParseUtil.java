package com.kikitrade.activity.service.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.kikitrade.member.model.exception.MemberException;
import jodd.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/6/14 6:11 PM
 * @modify
 */
public class BeanParseUtil {

    public static <T> T parseYaml2Obj(String data, Class<T> clazz) throws MemberException {
        if (StringUtil.isBlank(data)) {
            return null;
        }
        try {
            ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
            return mapper.readValue(data, clazz);
        } catch (Exception e) {
            throw new MemberException(e);
        }
    }

    public static void copySkipNull(Object x, Object y) {
        BeanUtils.copyProperties(x, y, getNullPropertyFields(x));
    }

    private static String[] getNullPropertyFields(Object source) {
        BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();

        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }

        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

}
