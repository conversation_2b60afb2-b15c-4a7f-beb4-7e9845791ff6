package com.kikitrade.activity.service.reward.impl;

import com.kikitrade.activity.dal.tablestore.model.CustomerQuestionSets;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.question.QuestionSetsService;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/1 14:16
 */
@Slf4j
@Service("rewardSetsTccService")
public class RewardSetsTccServiceImpl extends AbstractRewardTccService implements RewardTccService {

    @Resource
    private QuestionSetsService questionSetsService;

    /**
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public void tryReward(RewardRequest request) throws Exception {
        try {
            // 注意：这里不能调用 findByUser
            CustomerQuestionSets questionSets = questionSetsService.findByUid(request.getSaasId(), request.getCustomerId());
            if (Objects.isNull(questionSets)) {
                log.error("[rewardSetsTccService] error: cannot find CustomerQuestionSets, request {}", request);
                throw new ActivityException(ActivityResponseCode.REWARD_FAIL);
            }
            questionSetsService.incrementAvailableSets(questionSets, request.getAmount().intValue());
        } catch (Exception e) {
            log.error("[rewardSetsTccService] error:{}", request, e);
            throw new ActivityException(ActivityResponseCode.REWARD_FAIL);
        }
    }
}
