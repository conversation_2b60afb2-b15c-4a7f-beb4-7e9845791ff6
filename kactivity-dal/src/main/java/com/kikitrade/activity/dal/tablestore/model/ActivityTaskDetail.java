package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

@Data
@Table(name = "activity_task_detail")
public class ActivityTaskDetail implements Serializable {

    public static final String SEARCH_TASK_DETAIL_BY_TARGET_ID = "search_task_detail_by_target_id";

    @PartitionKey(name = "business_id")
    @SearchIndex(name = SEARCH_TASK_DETAIL_BY_TARGET_ID, column = "business_id")
    private String businessId;

    @PartitionKey(name = "event", value = 1)
    @Column(name = "event", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_DETAIL_BY_TARGET_ID, column = "event")
    private String event;

    @PartitionKey(name = "target_id", value = 2)
    @Column(name = "target_id", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_DETAIL_BY_TARGET_ID, column = "target_id")
    private String targetId;

    @Column(name = "complete_time", isDefined = true)
    private String completeTime;

    @Column(name = "status", isDefined = true)
    private String status;

    @Column(name = "created", isDefined = true)
    private String created;

    @Column(name = "modified", isDefined = true)
    private String modified;
}

