package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.google.common.collect.Lists;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.tablestore.builder.GoodsBuilder;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/26 15:26
 */
@Slf4j
@Component
public class GoodsBuilderImpl extends WideColumnStoreBuilder<Goods> implements GoodsBuilder {

    @Resource
    private SeqClient seqClient;

    private SeqRule seqRule = new SeqRule("IDX_GOODS", 1, "10", null);

    @PostConstruct
    public void init(){
        super.init(Goods.class);
    }

    @Override
    public boolean insert(Goods goods) {
        return super.putRow(goods, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean update(Goods goods){
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(goods, condition);
    }

    @Override
    public Goods findById(String goodsId) {
        Goods goods = new Goods();
        goods.setGoodsId(goodsId);
        return super.getRow(goods);
    }

    @Override
    public Page<Goods> findAll(int offset, int limit, String saasId, String exclude) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.term("saas_id", saasId));
        builder.must(QueryBuilders.term("status", ActivityConstant.GoodsStatus.ONLINE.getCode()));
        if(exclude != null){
            builder.mustNot(QueryBuilders.term("goods_id", exclude));
        }

        Sort sort = new Sort(Lists.newArrayList(
                new FieldSort("order", SortOrder.ASC)
                , new FieldSort("end_time", SortOrder.DESC)));
        return pageSearchQuery(builder.build(), sort, offset, limit, Goods.INDEX_GOODS);
    }

    @Override
    public String nextId() {
        return seqClient.next(seqRule);
    }
}
