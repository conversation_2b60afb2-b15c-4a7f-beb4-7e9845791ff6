package com.kikitrade.activity.service.flow.step.listener;

import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.CsvService;
import com.kikitrade.activity.service.business.NoticeService;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * @Description 发奖节点
 * <AUTHOR>
 * @CreateTime 2022-12-05 11:16
 */
@Component
@StepScope
@Slf4j
public class RewardListener implements StepExecutionListener {

    @Value("#{jobParameters['batchId']}")
    private String batchId;
    @Value("#{jobParameters['activityId']}")
    private String activityId;
    @Resource
    private CsvService<ActivityCustomReward> rewardCsvService;
    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private NoticeService noticeService;
    @Resource
    private KactivityModuleProperties kactivityModuleProperties;

    @Override
    public void beforeStep(StepExecution stepExecution) {
        log.info("批次：{}, 开始发奖", batchId);
    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        //生产csv文件
        try{
            log.info("批次：{}, 发奖结束", batchId);
            TimeUnit.SECONDS.sleep(3);
            ActivityBatch activityBatch = activityBatchNewService.findByBatchId(batchId);
            boolean existNotAward = activityCustomRewardStoreBuilder.existByBatchIdAndStatus(batchId, ActivityConstant.RewardStatusEnum.NOT_AWARD.name());
            if(existNotAward){
                return ExitStatus.FAILED;
            }
            boolean success = false;
            if(activityCustomRewardStoreBuilder.existByBatchIdAndStatus(batchId, ActivityConstant.BatchRewardStatusEnum.AWARD_SUCCESS.name())){
                activityBatch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.AWARD_SUCCESS.name());
                activityBatchNewService.updateBatch(activityBatch);
                noticeService.noticeDingTalk(String.format("%s%s",batchId,"_SUCCESS"), kactivityModuleProperties.getReward().getDingTalkUrl(),
                        String.format("批次：%s(%s), 发奖成功", activityBatch.getName(), activityBatch.getBatchId()));
                success = true;
            }else{
                activityBatch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.AWARD_FAILED.name());
                activityBatchNewService.updateBatch(activityBatch);
                long count = activityCustomRewardStoreBuilder.count(batchId);
                if(count > 0){
                    noticeService.noticeDingTalk(String.format("%s%s",batchId,"_FAIL"), kactivityModuleProperties.getReward().getDingTalkUrl(),
                            String.format("批次：%s(%s), 发奖失败", activityBatch.getName(), activityBatch.getBatchId()));
                }else{
                    noticeService.noticeDingTalk(String.format("%s%s",batchId,"_EMPTY"), kactivityModuleProperties.getReward().getDingTalkUrl(),
                            String.format("批次：%s(%s), 发奖失败, 名单为空", activityBatch.getName(), activityBatch.getBatchId()));
                }
            }
            try{
                ActivityRewardPageParam pageParam = new ActivityRewardPageParam();
                pageParam.setBatchId(activityBatch.getBatchId());
                pageParam.setActivityType(activityBatch.getActivityType());
                pageParam.setPageNo(0);
                pageParam.setStatusList(Arrays.asList(ActivityConstant.BatchRewardStatusEnum.AWARD_SUCCESS.name()));
                rewardCsvService.write(String.format("%s-%s.%s",activityBatch.getName().replaceAll("\\s+", ""), activityBatch.getBatchId(), "csv"), pageParam);
                if(success){
                    return ExitStatus.COMPLETED;
                }else{
                    return ExitStatus.FAILED;
                }
            }catch (Exception ex){
                return ExitStatus.COMPLETED;
            }
        }catch (Exception ex){
            return ExitStatus.FAILED;
        }
    }
}
