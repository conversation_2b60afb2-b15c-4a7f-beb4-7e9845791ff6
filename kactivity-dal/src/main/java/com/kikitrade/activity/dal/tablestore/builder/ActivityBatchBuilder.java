package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.BatchPageResult;
import com.kikitrade.activity.dal.tablestore.param.ActivityBatchParam;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;

import java.util.List;

public interface ActivityBatchBuilder {

    String getTableName();

    boolean insert(ActivityBatch activityBatch);

    boolean update(ActivityBatch activityBatch);

    PageResult queryForList(ActivityBatchParam activityBatchParam, int offset, int limit);

    ActivityBatch queryById(String id);

    int countByStatus(String activityId, List<String> status);

    List<ActivityBatch> queryListByStatus(String status);

    int countByActivityId(String activityId);

    boolean deleteById(ActivityBatch activityBatch);

    ActivityBatch queryLastByActivityId(String activityId);
}
