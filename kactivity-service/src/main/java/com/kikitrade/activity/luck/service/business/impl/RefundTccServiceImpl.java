package com.kikitrade.activity.luck.service.business.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.accounting.api.RemoteAccountingOperateService;
import com.kikitrade.accounting.api.model.account.common.BusinessType;
import com.kikitrade.accounting.api.model.account.common.Category;
import com.kikitrade.accounting.api.model.account.request.AccountUnfreezeRequest;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReceiveItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneRefundItemBuild;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReleaseItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneRefundItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.service.business.RefundTccService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import jakarta.annotation.Resource;
import java.math.BigDecimal;

/**
 *
 */
@DubboService
public class RefundTccServiceImpl implements RefundTccService {

    @DubboReference
    private RemoteAccountingOperateService remoteAccountingOperateService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private LuckFortuneRefundItemBuild luckFortuneRefundItemBuild;
    @Resource
    private LuckFortuneReceiveItemBuilder luckFortuneReceiveItemBuilder;
    @Resource
    private LuckFortuneReleaseItemBuilder luckFortuneReleaseItemBuilder;

    /**
     * 退款
     *
     * @param luckFortuneRefundItem
     * @throws Exception
     */
    @Override
    @Compensation(confirmMethod = "confirmReceiveRefund", cancelMethod = "cancelReceiveRefund")
    public void tryReceiveRefund(LuckFortuneRefundItem luckFortuneRefundItem, LuckFortuneReceiveItem luckFortuneReceiveItem) throws Exception {
        if(luckFortuneRefundItem.getRefundId() == null){
            luckFortuneRefundItemBuild.insert(luckFortuneRefundItem);
        }
        LuckFortuneReleaseItem releaseItem = luckFortuneReleaseItemBuilder.findById(luckFortuneReceiveItem.getReleaseId());
        releaseItem.setRefundAmount(StringUtils.isBlank(releaseItem.getRefundAmount()) ? luckFortuneRefundItem.getAmount()
                : new BigDecimal(releaseItem.getRefundAmount()).add(new BigDecimal(luckFortuneRefundItem.getAmount())).toPlainString());
        luckFortuneReleaseItemBuilder.update(releaseItem);
        remoteAccountingOperateService.unfreeze(buildAccountUnfreezeRequest(luckFortuneRefundItem));
    }

    public void confirmReceiveRefund(LuckFortuneRefundItem luckFortuneRefundItem, LuckFortuneReceiveItem luckFortuneReceiveItem) throws Exception {
        luckFortuneReceiveItem.setStatus(ActivityConstant.LuckFortuneReceiveStatus.REFUND_SUCCESS.getCode());
        luckFortuneReceiveItemBuilder.update(luckFortuneReceiveItem);

        luckFortuneRefundItem.setStatus(ActivityConstant.LuckFortuneReceiveStatus.REFUND_SUCCESS.getCode());
        luckFortuneRefundItemBuild.update(luckFortuneRefundItem);
    }

    public void cancelReceiveRefund(LuckFortuneRefundItem luckFortuneRefundItem, LuckFortuneReceiveItem luckFortuneReceiveItem) throws Exception {
        luckFortuneRefundItem.setStatus(ActivityConstant.LuckFortuneReceiveStatus.REFUND_FAIL.getCode());
        luckFortuneRefundItemBuild.update(luckFortuneRefundItem);
    }

    /**
     * 退款
     *
     * @param luckFortuneRefundItem
     * @throws Exception
     */
    @Override
    @Compensation(confirmMethod = "confirmReleaseRefund", cancelMethod = "cancelReleaseRefund")
    public void tryReleaseRefund(LuckFortuneRefundItem luckFortuneRefundItem, LuckFortuneReleaseItem luckFortuneReleaseItem) throws Exception {
        LuckFortuneRefundItem item = luckFortuneRefundItemBuild.findObject(luckFortuneRefundItem.getOriginBusinessId(), luckFortuneRefundItem.getBusinessId());
        if(item != null && !ActivityConstant.LuckFortuneReleaseStatus.REFUND_FAIL.getCode().equals(item.getStatus())){
            return;
        }
        if(item == null){
            luckFortuneRefundItemBuild.insert(luckFortuneRefundItem);
        }else{
            luckFortuneRefundItemBuild.update(luckFortuneRefundItem);
        }
        luckFortuneReleaseItem.setStatus(ActivityConstant.LuckFortuneReleaseStatus.REFUNDING.getCode());
        luckFortuneReleaseItemBuilder.update(luckFortuneReleaseItem);
        remoteAccountingOperateService.unfreeze(buildAccountUnfreezeRequest(luckFortuneRefundItem));
    }

    public void confirmReleaseRefund(LuckFortuneRefundItem luckFortuneRefundItem, LuckFortuneReleaseItem luckFortuneReleaseItem) throws Exception {
        LuckFortuneReleaseItem item = luckFortuneReleaseItemBuilder.findById(luckFortuneReleaseItem.getId());
        if(item != null && ActivityConstant.LuckFortuneReceiveStatus.REFUNDING.getCode().equals(item.getStatus())){
            luckFortuneReleaseItem.setRefundAmount(luckFortuneRefundItem.getAmount());
            luckFortuneReleaseItem.setStatus(ActivityConstant.LuckFortuneReleaseStatus.REFUND_SUCCESS.getCode());
            luckFortuneReleaseItemBuilder.update(luckFortuneReleaseItem);
        }
        LuckFortuneRefundItem refundItem = luckFortuneRefundItemBuild.findObject(luckFortuneRefundItem.getOriginBusinessId(), luckFortuneRefundItem.getBusinessId());
        if(refundItem != null && ActivityConstant.LuckFortuneReleaseStatus.REFUNDING.getCode().equals(refundItem.getStatus())){
            luckFortuneRefundItem.setStatus(ActivityConstant.LuckFortuneReleaseStatus.REFUND_SUCCESS.getCode());
            luckFortuneRefundItemBuild.update(luckFortuneRefundItem);
        }
    }

    public void cancelReleaseRefund(LuckFortuneRefundItem luckFortuneRefundItem, LuckFortuneReleaseItem luckFortuneReleaseItem) throws Exception {
        LuckFortuneRefundItem refundItem = luckFortuneRefundItemBuild.findObject(luckFortuneRefundItem.getOriginBusinessId(), luckFortuneRefundItem.getBusinessId());
        if(refundItem != null && ActivityConstant.LuckFortuneReleaseStatus.REFUNDING.getCode().equals(refundItem.getStatus())){
            luckFortuneRefundItem.setStatus(ActivityConstant.LuckFortuneReleaseStatus.REFUND_FAIL.getCode());
            luckFortuneRefundItemBuild.update(luckFortuneRefundItem);
        }
        LuckFortuneReleaseItem item = luckFortuneReleaseItemBuilder.findById(luckFortuneReleaseItem.getId());
        if(item != null && ActivityConstant.LuckFortuneReceiveStatus.REFUNDING.getCode().equals(item.getStatus())){
            luckFortuneReleaseItem.setStatus(ActivityConstant.LuckFortuneReleaseStatus.EXPIRED.getCode());
            luckFortuneReleaseItemBuilder.update(luckFortuneReleaseItem);
        }
    }

    private AccountUnfreezeRequest buildAccountUnfreezeRequest(LuckFortuneRefundItem luckFortuneRefundItem) {
        AccountUnfreezeRequest request = new AccountUnfreezeRequest();
        request.setAmount(new BigDecimal(luckFortuneRefundItem.getAmount()));
        request.setBusinessId(luckFortuneRefundItem.getRefundId());
        request.setBusinessType(BusinessType.ACTIVITY_AIRDROP);
        request.setOriginalBusinessId(luckFortuneRefundItem.getOriginBusinessId());
        request.setCategory(Category.NORMAL);
        request.setCurrency(luckFortuneRefundItem.getCurrency());
        request.setCustomerId(luckFortuneRefundItem.getCustomerId());
        request.setRetro(false);
        request.setSaasId(kactivityProperties.getSaasId());
        return request;
    }
}
