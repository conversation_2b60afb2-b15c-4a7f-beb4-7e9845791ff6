package com.kikitrade.activity.api;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/27 17:46
 */
public interface
RemoteRewardService {

    @Deprecated
    Boolean receiveReward(String customerId, String address, String taskId, String extendAttr) throws Exception;

    Boolean receiveReward(String customerId, String address, String taskId) throws Exception;

    Boolean rewardPoint(String customerId, String price, String saasId, String desc, String businessId) throws Exception;
}
