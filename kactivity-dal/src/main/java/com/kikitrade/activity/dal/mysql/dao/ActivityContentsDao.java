package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityContents;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityContentsDao extends BaseDao<ActivityContents, String> {


    int insert(ActivityContents ac);

    int update(ActivityContents ac);

    int deleteById(@Param("activityId") Integer activityId);

    int deleteByPara(@Param("activityId") Integer activityId, @Param("locale") String locale);

    List<ActivityContents> findById(@Param("activityId") Integer activityId,@Param("saasId")String saasId);

    ActivityContents findByPara(@Param("activityId") Integer activityId, @Param("locale") String locale);

}
