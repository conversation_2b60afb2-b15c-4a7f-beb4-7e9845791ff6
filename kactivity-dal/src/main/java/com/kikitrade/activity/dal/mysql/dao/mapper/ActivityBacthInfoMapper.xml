<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityBatchInfoDao">

    <!-- 增加 -->
    <insert id="insert" parameterType="com.kikitrade.activity.dal.mysql.model.ActivityBatchInfo">
        INSERT INTO activity_batch_info VALUES (
            NOW(), NOW(),
            #{tran_date},
            #{activity_id},
            #{batch_id},
            #{status},
            #{remark}
        )
    </insert>


    <update id="updateStatus">
        UPDATE activity_batch_info
        SET update_time = NOW(), status = #{status}
        WHERE tran_date = #{tran_date} and activity_id=#{activity_id} and batch_id=#{batch_id}
    </update>


    <select id="findByPrimaryKey" resultType="com.kikitrade.activity.dal.mysql.model.ActivityBatchInfo">
        SELECT *
        FROM activity_batch_info
        where tran_date = #{tran_date} and activity_id=#{activity_id} and batch_id=#{batch_id}
    </select>


    <select id="findAll" resultType="com.kikitrade.activity.dal.mysql.model.ActivityBatchInfo">
        SELECT *
        FROM activity_batch_info
        where 1=1
        <if test="tran_date!=null">
            and tran_date =#{tran_date}
        </if>
        <if test="activity_id!=null">
            and activity_id =#{activity_id}
        </if>
        <if test="batch_id!=null">
            and batch_id =#{batch_id}
        </if>
        ORDER BY create_time DESC
        <if test="offset!=null and limit!=null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>


    <select id="findByBatchId" resultType="com.kikitrade.activity.dal.mysql.model.ActivityBatchInfo">
        SELECT *
        FROM activity_batch_info
        where  batch_id=#{batch_id} order by create_time desc
         LIMIT 0,1
    </select>

    <select id="findLastBatchByActivityId" resultType="com.kikitrade.activity.dal.mysql.model.ActivityBatchInfo">
        SELECT *
        FROM activity_batch_info
        where  activity_id=#{activity_id} order by create_time desc
         LIMIT 0,1
    </select>

</mapper>
