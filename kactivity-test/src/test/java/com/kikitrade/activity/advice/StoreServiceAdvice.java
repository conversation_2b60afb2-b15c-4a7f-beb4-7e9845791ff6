package com.kikitrade.activity.advice;

import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.model.StoreInfo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Component
@Aspect
@Slf4j
public class StoreServiceAdvice {

    @Pointcut("execution(* com.kikitrade.activity.service.store.StoreService.getStore(..))")
    public void getStorePt() {
    }

    @Around("getStorePt()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        Object ret = pjp.proceed();

        BusinessOP.storeInfo  = (StoreInfo) ret;
        return ret;

    }
}
