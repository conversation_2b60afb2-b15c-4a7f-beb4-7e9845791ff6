package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.CustomerReward;
import com.kikitrade.framework.common.model.PageResult;

import java.math.BigDecimal;

public interface CustomerRewardStoreBuilder {

    boolean insertIfNotExist(CustomerReward reward);

    boolean update(CustomerReward reward);

    PageResult findByCustomerId(String customerId, int offset, int limit);

    long countByCustomer(String customerId);

    long countByCustomerAndType(String customerId, Integer type);

    long countByCustomerTypeAndCurrency(String customerId, String currency, Integer type);

    long countByInviteeAndType(String invitee, Integer type);

    long countByCustomerAndInvitee(String customerId, String invitee, Integer type);

    BigDecimal sumByCustomer(String customerId);

    BigDecimal sumByCustomerAndType(String customerId, Integer type);

    BigDecimal sumByCustomerCurrencyAndType(String customerId, String currency, Integer type);

    PageResult findByCustomerIdAndType(String customerId, Integer activityType, int offset, int limit);
}
