package com.kikitrade.activity.luck.service.business;

import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReleaseDTO;
import com.kikitrade.activity.model.Result;

/**
 *
 */
public interface ReleaseTccService {

    /**
     * 发放红包
     * @param luckFortuneReleaseDTO
     * @return
     */
    Result<LuckFortuneReleaseItem> tryRelease(LuckFortuneRule rule, LuckFortuneReleaseDTO luckFortuneReleaseDTO, ActivityCustomerDTO customerDTO, String id) throws Exception;

}
