package com.kikitrade.activity.mock.impl;

import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.Direction;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.filter.ColumnValueFilter;
import com.alicloud.openservices.tablestore.model.filter.CompositeColumnValueFilter;
import com.alicloud.openservices.tablestore.model.filter.SingleColumnValueFilter;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.impl.ActivityCustomRewardStoreBuilderImpl;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.mock.ActivityCustomRewardStoreBuilderMock;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.util.BeanUtils;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import org.apache.commons.lang3.StringUtils;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;
import org.springframework.test.util.ReflectionTestUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.*;

@Component
public class ActivityCustomRewardStoreBuilderMockImpl implements ActivityCustomRewardStoreBuilderMock {

    @Resource
    ActivityCustomRewardStoreBuilderImpl activityCustomRewardStoreBuilder;

    @Override
    public void insert() {
        Mockito.doAnswer(c -> {
            ActivityCustomReward reward = c.getArgument(0);
            String batchId = reward.getBatchId();
            String customerId = reward.getCustomerId();
            String seq = reward.getSeq();

            Optional<ActivityCustomReward> first = BusinessOP.activityCustomRewardList.stream().filter(
                    item -> item.getBatchId().equals(batchId) &&
                            item.getCustomerId().equals(customerId) &&
                            item.getSeq().equals(seq)
            ).findFirst();

            if (first.isPresent()) {
                return false;
            }
            BusinessOP.activityCustomRewardList.add(BeanUtil.copyProperties(reward,ActivityCustomReward::new));
            return true;

        }).when(activityCustomRewardStoreBuilder).insert(Mockito.any(ActivityCustomReward.class));
    }

    @Override
    public void findByPrimaryId() {
        Mockito.doAnswer(c -> {
            String batchId = c.getArgument(0);
            String customerId = c.getArgument(1);
            String seq = c.getArgument(2);

            Optional<ActivityCustomReward> first = BusinessOP.activityCustomRewardList.stream().filter(
                    item -> item.getBatchId().equals(batchId) &&
                            item.getCustomerId().equals(customerId) &&
                            item.getSeq().equals(seq)
            ).findFirst();

            if (first.isPresent()) {
                return BeanUtils.toBean(first.get());
            }
            return null;

            /*Map<String, ActivityCustomReward> activityCustomRewardMap = BusinessOP.activityCustomRewardMap;

            if (activityCustomRewardMap.containsKey(batchId)) {
                ActivityCustomReward reward = activityCustomRewardMap.get(batchId);
                ActivityCustomReward rewardT = new ActivityCustomReward();
                BeanUtil.copyProperties(reward, rewardT);
                return rewardT;
            }

            return null;*/
        }).when(activityCustomRewardStoreBuilder).findByPrimaryId(anyString(), anyString(), anyString());
    }

    @Override
    public void updateStatus() {
        Mockito.doAnswer(c -> {
            ActivityCustomReward reward = c.getArgument(0);
            reward.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            String batchId = reward.getBatchId();
            String customerId = reward.getCustomerId();
            String seq = reward.getSeq();

            String status = reward.getStatus();

            Optional<ActivityCustomReward> first = BusinessOP.activityCustomRewardList.stream().filter(
                    item -> item.getBatchId().equals(batchId) &&
                            item.getCustomerId().equals(customerId) &&
                            item.getSeq().equals(seq)
            ).findFirst();

            if (first.isPresent()) {
                ActivityCustomReward customReward = first.get();
                customReward.setStatus(status);
                return true;
            }

            return false;


        }).when(activityCustomRewardStoreBuilder).updateStatus(Mockito.any(ActivityCustomReward.class));
    }

    @Override
    public void findByBatchIdAndStatus() {
        Mockito.doAnswer(c->{
            ActivityRewardPageParam activityRewardPageParam = c.getArgument(0);

            String batchId = activityRewardPageParam.getBatchId();

            List<String> statusList = new ArrayList<>();
            List<RangeQueryParameter> queryList = new ArrayList<>();
            if(StringUtils.isBlank(activityRewardPageParam.getBatchId())){
                return null;
            }

            queryList.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.fromString(activityRewardPageParam.getBatchId()), PrimaryKeyValue.fromString(activityRewardPageParam.getBatchId())));
            queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
            queryList.add(new RangeQueryParameter("seq", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
            CompositeColumnValueFilter filter = new CompositeColumnValueFilter(CompositeColumnValueFilter.LogicOperator.OR);
            for(String status : activityRewardPageParam.getStatusList()){
                filter.addFilter(new SingleColumnValueFilter("status", SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromString(status)));
                statusList.add(status);
            }

            List<ActivityCustomReward> collect = BusinessOP.activityCustomRewardList.stream().filter(item -> item.getBatchId().equals(batchId) &&
                    statusList.contains(item.getStatus())).collect(Collectors.toList());
            if(!collect.isEmpty()){
                RangeResult<ActivityCustomReward> result = new RangeResult<>(collect, new PrimaryKey());
                return result;
            }

            return null;

        }).when(activityCustomRewardStoreBuilder).findByBatchIdAndStatus(Mockito.any(ActivityRewardPageParam.class));
    }

    @Override
    public void existByBatchIdAndStatus() {

        Mockito.doAnswer(c->{

            String batchId = c.getArgument(0);
            String rewardStatus = c.getArgument(1);

            Optional<ActivityCustomReward> first = BusinessOP.activityCustomRewardList.stream().filter(
                    item -> item.getBatchId().equals(batchId) &&
                            item.getStatus().equals(rewardStatus)
            ).findFirst();

            return first.isPresent();

        }).when(activityCustomRewardStoreBuilder).existByBatchIdAndStatus(anyString(), anyString());





    }


}
