package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.luck.service.business.impl.ReceiveTccServiceImpl;
import com.kikitrade.activity.mock.RemoteCustomerInviteServiceDubboMock;
import com.kikitrade.activity.util.ReflectionUtils;
import com.kikitrade.kcustomer.api.model.BindInviteRelationRequest;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerInviteService;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.kcustomer.common.constants.CustomerConstants;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

@Component
public class RemoteCustomerInviteServiceDubboMockImpl implements RemoteCustomerInviteServiceDubboMock {
    @Resource(name = "remoteCustomerInviteServiceStub")
    RemoteCustomerInviteService remoteCustomerInviteService;
    @Autowired
    ApplicationContext applicationContext;
    @Resource
    ReceiveTccServiceImpl receiveTccService;

    @PostConstruct
    public void init() throws NoSuchFieldException, ClassNotFoundException, IllegalAccessException {
        RemoteCustomerInviteService fromSpring = applicationContext.getBean("remoteCustomerInviteServiceStub", RemoteCustomerInviteService.class);
        //通过反射注入属性
        String classPath = "com.kikitrade.activity.luck.service.business.impl.ReceiveTccServiceImpl";
        String fieldName = "remoteCustomerInviteService";
        ReflectionUtils.setField(classPath, fieldName, receiveTccService, fromSpring);
    }

    @Override
    public void bindRelation() {
        Mockito.doAnswer(c -> {
            return true;
        }).when(remoteCustomerInviteService).bindRelation(Mockito.any(BindInviteRelationRequest.class));

    }
}
