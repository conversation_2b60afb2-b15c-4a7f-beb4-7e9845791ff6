package com.kikitrade.activity.mock.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReleaseItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.service.model.ReceiveItem;
import com.kikitrade.activity.mock.LuckFortuneReleaseItemBuilderMock;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.util.DateUtil;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.util.BeanUtil;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;
import org.springframework.util.unit.DataUnit;

import jakarta.annotation.Resource;

import java.text.ParseException;
import java.util.List;
import java.util.stream.Collectors;

import static com.kikitrade.activity.op.BusinessOP.luckFortuneReleaseItemMap;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;


@Component
public class LuckFortuneReleaseItemBuilderMockImpl implements LuckFortuneReleaseItemBuilderMock {
    @Resource
    LuckFortuneReleaseItemBuilder luckFortuneReleaseItemBuilder;

    @Override
    public void insert() {
        Mockito.doAnswer(c -> {
            LuckFortuneReleaseItem luckFortuneReleaseItem = c.getArgument(0);

            String detail = luckFortuneReleaseItem.getDetail();
            List<ReceiveItem> receiveItems = JSON.parseArray(detail, ReceiveItem.class);


            for (ReceiveItem receiveItem : receiveItems) {
                BusinessOP.receiveIds.add(receiveItem.getId());
            }

            String id = luckFortuneReleaseItem.getId();
            if (luckFortuneReleaseItemMap.containsKey(id)) {
                return false;
            }
            luckFortuneReleaseItemMap.put(id, luckFortuneReleaseItem);
            BusinessOP.releaseId = id;
            BusinessOP.releaseCode = luckFortuneReleaseItem.getReleaseCode();

            return true;
        }).when(luckFortuneReleaseItemBuilder).insert(Mockito.any(LuckFortuneReleaseItem.class));
    }

    @Override
    public void updateStatusToDrawing() {
        Mockito.doAnswer(c -> {
            LuckFortuneReleaseItem luckFortuneReleaseItem = c.getArgument(0);
            String id = luckFortuneReleaseItem.getId();

            if (luckFortuneReleaseItemMap.containsKey(id)) {
                LuckFortuneReleaseItem luckFortuneReleaseItem1 = luckFortuneReleaseItemMap.get(id);
                if (luckFortuneReleaseItem1.getStatus().equals(ActivityConstant.LuckFortuneReleaseStatus.BUILDING.getCode())
                        || luckFortuneReleaseItem1.getStatus().equals(ActivityConstant.LuckFortuneReleaseStatus.APPENDING.getCode())) {
                    luckFortuneReleaseItemMap.get(id).setModified(luckFortuneReleaseItem.getModified());
                    luckFortuneReleaseItemMap.get(id).setStatus(ActivityConstant.LuckFortuneReleaseStatus.DRAWING.getCode());
                    return true;
                }

            }
            return false;
        }).when(luckFortuneReleaseItemBuilder).updateStatusToDrawing(Mockito.any(LuckFortuneReleaseItem.class));

    }

    @Override
    public void updateStatusToInvalid() {
        Mockito.doAnswer(c -> {
            LuckFortuneReleaseItem luckFortuneReleaseItem = c.getArgument(0);
            String id = luckFortuneReleaseItem.getId();

            if (luckFortuneReleaseItemMap.containsKey(id)) {
                LuckFortuneReleaseItem luckFortuneReleaseItem1 = luckFortuneReleaseItemMap.get(id);
                if (luckFortuneReleaseItem1.getStatus().equals(ActivityConstant.LuckFortuneReleaseStatus.APPENDING.getCode())) {
                    luckFortuneReleaseItemMap.get(id).setModified(luckFortuneReleaseItem.getModified());
                    luckFortuneReleaseItemMap.get(id).setStatus(ActivityConstant.LuckFortuneReleaseStatus.INVALID.getCode());
                    return true;
                }

            }
            return false;
        }).when(luckFortuneReleaseItemBuilder).updateStatusToInvalid(Mockito.any(LuckFortuneReleaseItem.class));

    }

    @Override
    public void findById() {
        Mockito.doAnswer(c -> {
            String id = c.getArgument(0);
            if (luckFortuneReleaseItemMap.containsKey(id)) {
                LuckFortuneReleaseItem luckFortuneReleaseItem = luckFortuneReleaseItemMap.get(id);
                LuckFortuneReleaseItem luckFortuneReleaseItem1 = new LuckFortuneReleaseItem();
                BeanUtil.copyProperties(luckFortuneReleaseItem, luckFortuneReleaseItem1);
                return luckFortuneReleaseItem1;
            }
            return null;
        }).when(luckFortuneReleaseItemBuilder).findById(anyString());

    }

    @Override
    public void findByExpiredTime() {
        Mockito.doAnswer(c -> {
            Integer status = c.getArgument(0);
            String endTime = c.getArgument(1);
            int offset = c.getArgument(2);

            List<LuckFortuneReleaseItem> collect = luckFortuneReleaseItemMap.values().stream().filter(item ->
                    DateUtil.parseYYYYMMDDHHMMSS(item.getExpiredTime()).before(DateUtil.parseYYYYMMDDHHMMSS(endTime)) && item.getStatus().intValue() == status).collect(Collectors.toList());

            Page<LuckFortuneReleaseItem> page = new Page<>();
            page.setRows(collect);

            return page;

        }).when(luckFortuneReleaseItemBuilder).findByExpiredTime(anyInt(), anyString(), anyInt());

    }

    @Override
    public void update() {
        Mockito.doAnswer(c -> {
            LuckFortuneReleaseItem luckFortuneReleaseItem = c.getArgument(0);
            String id = luckFortuneReleaseItem.getId();
            if (luckFortuneReleaseItemMap.containsKey(id)) {
                luckFortuneReleaseItemMap.put(id, luckFortuneReleaseItem);
                return true;

            }

            return false;
        }).when(luckFortuneReleaseItemBuilder).update(Mockito.any(LuckFortuneReleaseItem.class));

    }

    @Override
    public void updateError() {
        Mockito.doAnswer(c -> {
            throw new RuntimeException("luckFortuneReleaseItemBuilder update error");
        }).when(luckFortuneReleaseItemBuilder).update(Mockito.any(LuckFortuneReleaseItem.class));
    }

}
