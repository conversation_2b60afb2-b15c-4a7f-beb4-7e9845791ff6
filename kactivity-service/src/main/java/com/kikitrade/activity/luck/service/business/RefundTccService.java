package com.kikitrade.activity.luck.service.business;

import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneRefundItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;

/**
 *
 */
public interface RefundTccService {

    /**
     * 退款
     * @param luckFortuneRefundItem
     * @throws Exception
     */
    void tryReceiveRefund(LuckFortuneRefundItem luckFortuneRefundItem, LuckFortuneReceiveItem luckFortuneReceiveItem) throws Exception;

    /**
     * 退款
     * @param luckFortuneRefundItem
     * @throws Exception
     */
    void tryReleaseRefund(LuckFortuneRefundItem luckFortuneRefundItem, LuckFortuneReleaseItem luckFortuneReleaseItem) throws Exception;
}
