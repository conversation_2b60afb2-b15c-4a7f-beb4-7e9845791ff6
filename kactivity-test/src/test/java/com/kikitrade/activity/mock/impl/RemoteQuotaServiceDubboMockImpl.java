package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteQuotaServiceDubboMock;
import com.kikitrade.quota.api.RemoteQuotaService;
import com.kikitrade.quota.api.model.request.QuotaCheckDTO;
import com.kikitrade.quota.api.model.request.QuotaDeductDTO;
import com.kikitrade.quota.api.model.request.QuotaFreezeDTO;
import com.kikitrade.quota.api.model.request.QuotaUnfreezeDTO;
import com.kikitrade.quota.api.model.response.RpcResponse;
import jakarta.annotation.Resource;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import static com.kikitrade.activity.op.BusinessOP.*;
import static org.mockito.ArgumentMatchers.any;

@Component
public class RemoteQuotaServiceDubboMockImpl implements RemoteQuotaServiceDubboMock {
    @Resource
    RemoteQuotaService remoteQuotaService;

    @Override
    public void freeze() {
        Mockito.doAnswer(c -> {
            QuotaFreezeDTO freezeDTO = c.getArgument(0);
            quotaFreezeDTOList.add(freezeDTO);
            return null;
        }).when(remoteQuotaService).freeze(any(QuotaFreezeDTO.class));
    }

    @Override
    public void unfreezeDeduct() {
        Mockito.doAnswer(c -> {
            QuotaUnfreezeDTO unfreezeDTO = c.getArgument(0);
            quotaUnfreezeDeductList.add(unfreezeDTO);
            return null;
        }).when(remoteQuotaService).unfreezeDeduct(any(QuotaUnfreezeDTO.class));
    }

    @Override
    public void unfreeze() {
        Mockito.doAnswer(c -> {
            QuotaUnfreezeDTO unfreezeDTO = c.getArgument(0);
            quotaUnfreezeDTOList.add(unfreezeDTO);
            return null;
        }).when(remoteQuotaService).unfreeze(any(QuotaUnfreezeDTO.class));
    }

    @Override
    public void deduct() {
        Mockito.doAnswer(c -> {
            QuotaDeductDTO quotaDeductDTO = c.getArgument(0);
            quotaDeductDTOList.add(quotaDeductDTO);
            return null;
        }).when(remoteQuotaService).deduct(any(QuotaDeductDTO.class));
    }

    @Override
    public void check() throws ExecutionException, InterruptedException, TimeoutException {
        Mockito.doAnswer(c ->{
            RpcResponse<Object> build = RpcResponse.builder().success(true).build();
            return build;
        }).when(remoteQuotaService).check(any(QuotaCheckDTO.class));
    }

    @Override
    public void checkError() throws ExecutionException,InterruptedException,TimeoutException{
        Mockito.doAnswer(c ->{
            RpcResponse<Object> build = RpcResponse.builder().success(false).code("0001").build();
            return build;
        }).when(remoteQuotaService).check(any(QuotaCheckDTO.class));
    }
}
