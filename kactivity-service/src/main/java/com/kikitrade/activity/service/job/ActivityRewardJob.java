package com.kikitrade.activity.service.job;

import com.google.common.base.Splitter;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.flow.job.BatchJobFactory;
import com.kikitrade.activity.service.importing.award.ActivityOfflineRewardLauncher;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class ActivityRewardJob implements SimpleJob {

    @Resource
    @Lazy
    private ActivityOfflineRewardLauncher activityOfflineRewardLauncher;
    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ElasticJobService elasticJobService;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private RedisService redisService;
    @Resource
    private BatchJobFactory batchJobFactory;

    private static final String BATCH_KEY = "batchId";
    private static final String ACTIVITY_KEY = "activityId";
    private static final Splitter.MapSplitter splitter = Splitter.on(";").withKeyValueSeparator(":");

    /**
     * 上游传递参数有activityId, batchId
     * @param shardingContext
     */
    @Override
    public void execute(ShardingContext shardingContext) {
        if(StringUtils.isBlank(shardingContext.getJobParameter())){
            return;
        }

        Map<String, String> param = splitter.split(shardingContext.getJobParameter());
        String batchId = param.get(BATCH_KEY);
        String activityId = param.get(ACTIVITY_KEY);

        if(StringUtils.isBlank(batchId) || StringUtils.isBlank(activityId)){
            return;
        }
        ActivityBatch activityBatch = activityBatchNewService.findByBatchId(batchId);
        if(activityBatch == null){
            elasticJobService.removeJob(shardingContext.getJobName());
            return;
        }

        //如果批次还未审核通过，等待下次执行
        if(ActivityConstant.BatchRewardStatusEnum.valueOf(activityBatch.getRewardStatus()).getCode() < ActivityConstant.BatchRewardStatusEnum.APPROVE.getCode()){
            return;
        }

        //如果批次已经发奖完成，删除定时任务
        if(ActivityConstant.BatchRewardStatusEnum.AWARD_SUCCESS.isEquals(activityBatch.getRewardStatus())
                && !activityCustomRewardStoreBuilder.existByBatchIdAndStatus(batchId, ActivityConstant.RewardStatusEnum.NOT_AWARD.name())){
            log.info("reward job finish， batchId:{}", activityBatch.getBatchId());
            elasticJobService.removeJob(shardingContext.getJobName());
            return;
        }

        //未到达发奖时间
        if(BooleanUtils.isTrue(activityBatch.getScheduled())
                && TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)).compareTo(TimeUtil.parse(activityBatch.getScheduledTime())) < 0){
            return;
        }
        batchJobFactory.runManualRewardJob(activityBatch.getActivityId(), activityBatch.getBatchId());
        elasticJobService.removeJob(shardingContext.getJobName());
    }

    public void exec(ActivityBatch activityBatch, ShardingContext shardingContext){
        log.info(shardingContext.getJobName() + shardingContext.getShardingItem() + " ready");
        //未到达发奖时间
        if(BooleanUtils.isTrue(activityBatch.getScheduled())
                && TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)).compareTo(TimeUtil.parse(activityBatch.getScheduledTime())) < 0){
            return;
        }
        String key = null;
        try {
            //添加分布式锁
            key = redisService.lock(String.format("%s_%s_%s","rewardJob", activityBatch.getBatchId(), shardingContext.getShardingItem()), 180);
            if(StringUtils.isBlank(key)){
                return;
            }
            log.info(shardingContext.getJobName() + shardingContext.getShardingItem() + " start");
            LauncherParameter parameter = new LauncherParameter();
            parameter.setBatch(activityBatch);
            parameter.setShard((long)shardingContext.getShardingItem());
            parameter.setTotalShard((long)shardingContext.getShardingTotalCount());
            activityOfflineRewardLauncher.run(parameter);
        }catch (Exception ex){
            log.error("activity rewardJob error:{}", activityBatch.getBatchId(), ex);
            activityBatchNewService.updateBatchStatus(activityBatch.getBatchId(), ActivityConstant.BatchRewardStatusEnum.AWARD_FAILED.name());
        }finally {
            if(StringUtils.isNotBlank(key)){
                redisService.unLock(key);
            }
        }
    }
}
