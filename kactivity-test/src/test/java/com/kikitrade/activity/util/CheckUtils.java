package com.kikitrade.activity.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class CheckUtils {

    static String[] addUnnecessaryFields;

    static String[] addNecessaryFields;


    /**
     * @param expectedObject
     * @param actualObject
     * @param addUnnecessaryFields 额外添加不用校验的字段
     * @param addNecessaryFields   额外添加需要校验的字段
     */
    public static void compare(Object expectedObject, Object actualObject, String[] addUnnecessaryFields, String[] addNecessaryFields) {
        CheckUtils.addUnnecessaryFields = addUnnecessaryFields;
        CheckUtils.addNecessaryFields = addNecessaryFields;
        compare(expectedObject, actualObject);
    }

    /**
     * @param expectedObject
     * @param actualObject
     * @param addUnnecessaryFields 额外添加不用校验的字段
     */
    public static void compare(Object expectedObject, Object actualObject, String[] addUnnecessaryFields) {
        CheckUtils.addUnnecessaryFields = addUnnecessaryFields;
        //CheckUtils.addNecessaryFields = addNecessaryFields;
        compare(expectedObject, actualObject);
    }


    /**
     * 1.两个对象
     * 2.两个可以转化为JSON的字符串
     * <p>
     * 字符串：
     * 这个字符串可以转化为JSONArray
     *
     * @param expectedObject
     * @param actualObject
     */
    public static void compare(Object expectedObject, Object actualObject) {
        if (expectedObject == null || actualObject == null) {
            assertNull(expectedObject);
            assertNull(actualObject);
            return;
        }


        //只有同一类型的才能继续比较
        assertEquals(expectedObject.getClass(), actualObject.getClass(), "仅支持相同数据类型的比较:");

        String expectedJSONString = JSON.toJSONString(expectedObject);
        String actualJSONString = JSON.toJSONString(actualObject);

        //矫正格式
        String expectedCorrectedString = correctFormat(expectedJSONString);
        String actualCorrectedString = correctFormat(actualJSONString);

        //筛选字段
        String expectedFilteredString = filterField(expectedCorrectedString);
        String actualFilteredString = filterField(actualCorrectedString);


        if (isJSONObject(expectedFilteredString) && isJSONObject(actualFilteredString)) {

            checkJSONObject(expectedFilteredString, actualFilteredString);

        } else if (isJSONArray(expectedFilteredString) && isJSONArray(actualFilteredString)) {

            checkJSONArray(expectedFilteredString, actualFilteredString);

        } else {

            assertEquals(expectedFilteredString, actualFilteredString);
        }
    }

    private static void checkJSONArray(String expectedFilteredString, String actualFilteredString) {
        //校验JSONArray
        JSONArray expectedArray = JSON.parseArray(expectedFilteredString);
        JSONArray actualArray = JSON.parseArray(actualFilteredString);

        assertEquals( expectedArray.size(), actualArray.size(), "Size: ");
        if (expectedArray.size() == 0) {
            return;
        }
        for (int i = 0; i < expectedArray.size(); i++) {
            String expectedString = JSON.toJSONString(expectedArray.get(i));
            String actualString = JSON.toJSONString(actualArray.get(i));

            if (isJSONObject(expectedString) && isJSONObject(actualString)) {
                checkJSONObject(expectedString, actualString);
            } else if (isJSONArray(expectedString) && isJSONArray(actualString)) {
                checkJSONArray(expectedString, actualString);
            } else {
                //Assert.assertEquals(expectedString, actualString);
                if (canTransfer2BigDecimal(expectedString) && canTransfer2BigDecimal(actualString)) {
                    BigDecimal expectedDecimal = new BigDecimal(expectedString);
                    BigDecimal actualDecimal = new BigDecimal(actualString);
                    assertEquals(0, expectedDecimal.compareTo(actualDecimal), "期望:" + expectedDecimal + ",实际:" + actualDecimal);
                } else {
                    assertEquals(expectedString, actualString);
                }
            }
        }
    }

    private static void checkJSONObject(String expectedFilteredString, String actualFilteredString) {

        //校验JSONObject
        JSONObject expectedJSONObject = JSON.parseObject(expectedFilteredString);
        JSONObject actualJSONObject = JSON.parseObject(actualFilteredString);

        assertEquals(expectedJSONObject.size(), actualJSONObject.size(), "数据大小:");
        if (expectedJSONObject.size() == 0) {
            return;
        }
        Set<String> keySet = expectedJSONObject.keySet();
        for (String key : keySet) {
            String expectedJSONObjectString = expectedJSONObject.getString(key);
            String actualJSONObjectString = actualJSONObject.getString(key);
            if (isJSONObject(expectedJSONObjectString) && isJSONObject(actualJSONObjectString)) {
                checkJSONObject(expectedJSONObjectString, actualJSONObjectString);
            } else if (isJSONArray(expectedJSONObjectString) && isJSONArray(actualJSONObjectString)) {
                checkJSONArray(expectedJSONObjectString, actualJSONObjectString);
            } else {
                String expectedValue = expectedJSONObject.getString(key);
                String actualValue = actualJSONObject.getString(key);
                if (canTransfer2BigDecimal(expectedValue) && canTransfer2BigDecimal(actualValue)) {
                    BigDecimal expectedDecimal = new BigDecimal(expectedValue);
                    BigDecimal actualDecimal = new BigDecimal(actualValue);
                    assertEquals(0, expectedDecimal.compareTo(actualDecimal), key + " 期望:" + expectedDecimal + ",实际:" + actualDecimal);
                } else {
                    assertEquals(expectedJSONObject.getString(key), actualJSONObject.getString(key), key);
                }
            }
        }
    }

    private static boolean canTransfer2BigDecimal(String value) {

        try {
            new BigDecimal(value);
        } catch (Exception e) {
            return false;
        }
        return true;

    }

    private static String filterField(String correctedString) {

        //默认不需要校验的字段
//        String[] unnecessaryFields = new String[]{"id", "created", "modify", "releaseTime", "expiredTime",
//                "businessId", "releaseId", "bizId", "releaseCode", "receiveTime", "originalBusinessId", "refundTime",
//                "refundId", "modified", "originBusinessId", "nextBearingTime", "nextSubscribeTime", "operateTime",
//                "closeTime", "timestamp", "TIMESTAMP", "CLOSE_TIME"};

        String[] unnecessaryFields = new String[]{};

        List<String> unnecessaryFieldsList = new ArrayList<>(Arrays.asList(unnecessaryFields));

        if (addUnnecessaryFields != null) {
            for (String addUnnecessaryField : addUnnecessaryFields) {
                if (unnecessaryFieldsList.contains(addUnnecessaryField)) {
                    //如果包含，直接pass
                    continue;
                }
                //如果不包含，则将元素添加到列表中
                unnecessaryFieldsList.add(addUnnecessaryField);
            }
        }

        if (addNecessaryFields != null) {
            for (String addNecessaryField : addNecessaryFields) {
                //从默认列表删除对应元素
                unnecessaryFieldsList.remove(addNecessaryField);
            }
        }

        //筛选掉不需要校验的字段
        for (String field : unnecessaryFieldsList) {
            String reg = ",\"" + field + "\":\".*?\"";
            Pattern pattern = Pattern.compile(reg);
            Matcher matcher = pattern.matcher(correctedString);
            correctedString = matcher.replaceAll("");
        }

        //筛选掉不需要校验的字段
        for (String field : unnecessaryFieldsList) {
            String reg = "\"" + field + "\":\".*?\",";
            Pattern pattern = Pattern.compile(reg);
            Matcher matcher = pattern.matcher(correctedString);
            correctedString = matcher.replaceAll("");
        }

        for (String field : unnecessaryFieldsList) {
            //String reg = "\"" + field + "\":.*?(,)?";
            String reg = ",\"" + field + "\":(([0-9]\\d*\\.?\\d*)|(0\\.\\d*[0-9]))";
            Pattern p = Pattern.compile(reg);
            Matcher m = p.matcher(correctedString);
            correctedString = m.replaceAll("");
        }

        for (String field : unnecessaryFieldsList) {
            //String reg = "\"" + field + "\":.*?(,)?";
            String reg = "\"" + field + "\":(([0-9]\\d*\\.?\\d*)|(0\\.\\d*[0-9])),";
            Pattern p = Pattern.compile(reg);
            Matcher m = p.matcher(correctedString);
            correctedString = m.replaceAll("");
        }

//        for (String field : unnecessaryFieldsList) {
//            //String reg = "\"" + field + "\":.*?(,)?";
//            String reg = "\"" + field + "\":\\[[0-9]\\d*\\.?\\d*]|\\[0\\.\\d*[0-9]],";
//            Pattern p = Pattern.compile(reg);
//            Matcher m = p.matcher(correctedString);
//            correctedString = m.replaceAll("");
//        }

        for (String field : unnecessaryFieldsList) {
            //String reg = "\"" + field + "\":.*?(,)?";
            String reg = "\"" + field + "\":\\[.*?],";
            Pattern p = Pattern.compile(reg);
            Matcher m = p.matcher(correctedString);
            correctedString = m.replaceAll("");
        }

        for (String field : unnecessaryFieldsList) {
            //String reg = "\"" + field + "\":.*?(,)?";
            String reg = ",\"" + field + "\":\\[.*?]";
            Pattern p = Pattern.compile(reg);
            Matcher m = p.matcher(correctedString);
            correctedString = m.replaceAll("");
        }

        boolean rePar = true;
        String temp;
        while (rePar) {
            temp = correctedString;
            String reg = ",}";
            Pattern p = Pattern.compile(reg);
            Matcher m = p.matcher(correctedString);
            correctedString = m.replaceAll("}");
            rePar = !correctedString.equals(temp);
        }

        rePar = true;
        while (rePar) {
            temp = correctedString;
            String reg = ",]";
            Pattern p = Pattern.compile(reg);
            Matcher m = p.matcher(correctedString);
            correctedString = m.replaceAll("]");
            rePar = !correctedString.equals(temp);
        }

        rePar = true;
        while (rePar) {
            temp = correctedString;
            String reg = "\\{,";
            Pattern p = Pattern.compile(reg);
            Matcher m = p.matcher(correctedString);
            correctedString = m.replaceAll("{");
            rePar = !correctedString.equals(temp);
        }

        rePar = true;
        while (rePar) {
            temp = correctedString;
            String reg = "\\[,";
            Pattern p = Pattern.compile(reg);
            Matcher m = p.matcher(correctedString);
            correctedString = m.replaceAll("[");
            rePar = !correctedString.equals(temp);
        }


        StringBuffer sb = new StringBuffer();
        String reg = ":\"\\[\\{.*?}]\"";
        Pattern p = Pattern.compile(reg);
        Matcher matcher = p.matcher(correctedString);


        int start = 0;
        int end = 0;
        String replace = correctedString;

        while (matcher.find()) {
            start = matcher.start();
            end = matcher.end();

            String group = matcher.group();

            String substring = correctedString.substring(start + 2, end - 1);
            System.out.println(substring);


//
            String r = "\"";
            Pattern compile = Pattern.compile(r);
            Matcher m = compile.matcher(substring);
            String s = m.replaceAll("\\\\\"");

            String tp = replace.replace(substring, s);
            replace = tp;


        }


        correctedString = replace;

        return correctedString;
    }


    private static String correctFormat(String JSONString) {

        String temp;

//        List<String> regs = Arrays.asList("\\\\", "\"\\{", "\\}\"", "\"\\[", "\\]\"");
//        List<String> replaces = Arrays.asList("", "{", "}", "[", "]");

//        List<String> regs = Arrays.asList("\\\\", "\"\\{","\"}", "\"\\[", "]\"");
        List<String> regs = Arrays.asList("\\\\", "\"\\[", "]\"", "\"\\{\"", "}\"");
        List<String> replaces = Arrays.asList("", "[", "]", "\\{\"", "}");


        for (int i = 0; i < regs.size(); i++) {
            boolean rePattern = true;
            while (rePattern) {
                temp = JSONString;
                //System.out.println("temp: {}"+ temp);
                Pattern pattern = Pattern.compile(regs.get(i));
                Matcher matcher = pattern.matcher(JSONString);
                JSONString = matcher.replaceAll(replaces.get(i));
                //System.out.println(JSONString);
                rePattern = !JSONString.equals(temp);
            }
        }
        return JSONString;
    }

    private static boolean isJSONArray(Object expectedObject) {
        if (StringUtils.isBlank((String) expectedObject)) {
            return false;
        }


        boolean isJSONArray = true;
        try {
            JSON.parseArray(String.valueOf(expectedObject));
        } catch (Exception e) {
            isJSONArray = false;
        }

        return isJSONArray;
    }

    private static boolean isJSONObject(Object expectedObject) {

        if (StringUtils.isBlank((String) expectedObject)) {
            return false;
        }


        boolean isJSONObject = true;
        try {
            JSON.parseObject(String.valueOf(expectedObject));
        } catch (Exception e) {
            isJSONObject = false;
        }

        return isJSONObject;
    }

}


