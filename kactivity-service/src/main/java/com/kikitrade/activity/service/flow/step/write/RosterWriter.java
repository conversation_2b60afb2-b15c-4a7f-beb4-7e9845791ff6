package com.kikitrade.activity.service.flow.step.write;

import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.flow.AbstractDataService;
import com.kikitrade.activity.service.importing.roster.RewardImportingProcess;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import com.kikitrade.kcustomer.common.constants.CustomerReferralConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-12 11:46
 */
@Component
@StepScope
@Slf4j
public class RosterWriter extends AbstractDataService implements ItemWriter<ActivityCustomReward> {

    @Resource
    private RedisService redisService;
    @Resource
    private CustomerService customerService;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Autowired
    private List<RewardImportingProcess> processes;
    @Resource
    private ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;
    @Resource
    private ActivityBatchNewService activityBatchNewService;

    @Value("#{jobParameters['batchId']}")
    private String batchId;
    @Value("#{jobParameters['activityId']}")
    private String activityId;

    @Override
    public void write(Chunk<? extends ActivityCustomReward> chunk) throws Exception {

        List<ActivityCustomReward> rewards = new ArrayList<>(chunk.getItems());

        ActivityEntity activityEntity = activityCache.getUnchecked(activityId);
        ActivityBatch activityBatch = batchCache.getUnchecked(batchId);

        com.kikitrade.activity.dal.mysql.model.ActivityEntity finalActivityEntity = activityEntity;
        ActivityBatch finalActivityBatch = activityBatch;
        if(StringUtils.isBlank(activityEntity.getTemplateCode())){
            List<ActivityCustomReward> activityCustomRewards = supplementRewardItem(rewards);
            if(CollectionUtils.isNotEmpty(activityCustomRewards)){
                rewards.addAll(activityCustomRewards);
            }
        }

        List<String> list = rewards.stream().map(ActivityCustomReward::getCustomerId).collect(Collectors.toList());
        List<CustomerCacheDTO> customerDTOS = customerService.getByIds(list);
        Map<String, CustomerCacheDTO> customerMap = customerDTOS.stream().collect(Collectors.toMap(CustomerCacheDTO::getId, Function.identity()));

        for(ActivityCustomReward reward : rewards){
            processes.forEach(process -> process.process(finalActivityEntity, finalActivityBatch, reward, customerMap.get(reward.getCustomerId())));
        }
        boolean insertSuccess = activityCustomRewardStoreBuilder.batchInsert(new ArrayList<>(rewards));
        if(insertSuccess){
            for(ActivityCustomReward item : chunk.getItems()){
                ActivityBatchRewardRoster builder = activityBatchRewardRosterStoreBuilder.findById(item.getBatchId(), item.getCustomerId(), item.getSeq());
                builder.setStatus(ActivityConstant.ImportStatusEnum.IMPORT_SUCCESS.name());
                activityBatchRewardRosterStoreBuilder.updateStatus(builder);
            }
            if(CollectionUtils.isNotEmpty(rewards)){
                activityBatch.setRewardType(rewards.get(0).getRewardType());
                activityBatchNewService.updateBatch(activityBatch);
            }
        }
    }

    private List<ActivityCustomReward> supplementRewardItem(List<ActivityCustomReward> inviteeList){

        List<ActivityCustomReward> inviterList = new ArrayList<>();
        //被邀请人
        for(ActivityCustomReward invitee : inviteeList){

            ActivityBatch activityBatch = batchCache.getUnchecked(invitee.getBatchId());
            ActivityEntity activityEntity = activityCache.getUnchecked(activityBatch.getActivityId());

            if(!ActivityConstant.ActivityTypeEnum.INVITE.name().equalsIgnoreCase(activityEntity.getType())){
                continue;
            }
            //被邀请人
            String nickName = customerService.getMiscByCustomerId(invitee.getCustomerId()).getNickName();
            invitee.setNickName(nickName);
            invitee.setSide(ActivityConstant.SideEnum.INVITEE.name());

            String referrerId = customerService.getInviteInfo(invitee.getCustomerId());
            if(StringUtils.isBlank(referrerId) || CustomerReferralConstants.EMPTY_ID.equals(referrerId)){
                continue;
            }
            invitee.setReferId(referrerId);

            String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_REWARD_SEQ_KEY.getPrefix(), activityBatch.getBatchId());
            Long seq = redisService.hIncrease(key, referrerId, 1L);
            redisService.expire(key, 8 * 60 * 60);
            ActivityCustomReward inviter = new ActivityCustomReward();
            inviter.setBatchId(invitee.getBatchId());
            inviter.setCustomerId(referrerId);
            inviter.setSeq(ActivityConstant.SeqPrefix.INVITEE_SEQ.getPrefix() + seq);
            //邀请人
            inviter.setSide(ActivityConstant.SideEnum.INVITER.name());
            //被邀请人昵称
            inviter.setNickName(nickName);
            inviter.setScope(invitee.getScope());
            inviter.setCreated(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            inviter.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            inviterList.add(inviter);
        }

        return inviterList;
    }
}
