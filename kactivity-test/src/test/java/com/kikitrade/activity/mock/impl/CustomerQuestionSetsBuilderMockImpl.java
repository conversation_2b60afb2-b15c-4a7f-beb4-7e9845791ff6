package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.tablestore.builder.CustomerQuestionSetsBuilder;
import com.kikitrade.activity.dal.tablestore.model.CustomerQuestionSets;
import com.kikitrade.activity.mock.CustomerQuestionSetsBuilderMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.common.SpringUtil;
import com.kikitrade.activity.util.SpringBootBeanUtils;
import com.kikitrade.framework.common.util.BeanUtil;
import jakarta.annotation.Resource;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import static com.kikitrade.activity.op.BusinessOP.customerQuestionSetsList;

@Component
public class CustomerQuestionSetsBuilderMockImpl implements CustomerQuestionSetsBuilderMock {
    @Resource
    CustomerQuestionSetsBuilder customerQuestionSetsBuilder;

    @Override
    public void findByUser() {
        Mockito.doAnswer(c -> {
            String saasId = c.getArgument(0);
            String customerId = c.getArgument(1);
            CustomerQuestionSets customerQuestionSets = customerQuestionSetsList.stream().filter(q -> q.getSaasId().equals(saasId) && q.getCustomerId().equals(customerId)).findFirst().orElse(null);
            return BeanUtil.copyProperties(customerQuestionSets, CustomerQuestionSets::new);
        }).when(customerQuestionSetsBuilder).findByUser(Mockito.anyString(), Mockito.anyString());
    }

    @Override
    public void incrementAvailableSets() {
        Mockito.doAnswer(c -> {
            CustomerQuestionSets customerQuestionSets = c.getArgument(0);
            String saasId = customerQuestionSets.getSaasId();
            String customerId = customerQuestionSets.getCustomerId();
            int inc = c.getArgument(1);

            CustomerQuestionSets customerQuestionSetsDB = customerQuestionSetsList.stream().filter(q -> q.getSaasId().equals(saasId) && q.getCustomerId().equals(customerId)).findFirst().orElse(null);
            if (customerQuestionSetsDB != null) {
                customerQuestionSetsDB.setAvailableSets(customerQuestionSetsDB.getAvailableSets() + inc);
                return customerQuestionSetsDB.getAvailableSets().longValue();
            }
            return 0L;
        }).when(customerQuestionSetsBuilder).incrementAvailableSets(Mockito.any(CustomerQuestionSets.class), Mockito.anyInt());
    }

    @Override
    public void incrementAvailableSets_error() {
        Mockito.doAnswer(c -> {
            throw new RuntimeException("error");
        }).when(customerQuestionSetsBuilder).incrementAvailableSets(Mockito.any(CustomerQuestionSets.class), Mockito.anyInt());
    }
}
