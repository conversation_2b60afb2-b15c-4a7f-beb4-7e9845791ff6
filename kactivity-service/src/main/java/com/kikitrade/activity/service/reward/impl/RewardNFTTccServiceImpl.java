package com.kikitrade.activity.service.reward.impl;

import com.alibaba.fastjson.JSONObject;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.ActivityAirDrop;
import com.kikitrade.activity.service.reward.model.ActivityAirDropRequest;
import com.kikitrade.activity.service.reward.model.ActivityAirDropResponse;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;


@Slf4j
@Service("rewardNFTTccService")
public class RewardNFTTccServiceImpl extends AbstractRewardTccService implements RewardTccService {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private KactivityModuleProperties kactivityModuleProperties;

    @Override
    public void tryReward(RewardRequest rewardRequest) throws Exception {
        try {
            ActivityAirDropRequest request = new ActivityAirDropRequest();
            request.setJobId(rewardRequest.getRewardId());
            request.setActivityId(kactivityModuleProperties.getNft().getActivityId());
            request.setActivityKey(kactivityModuleProperties.getNft().getActivityKey());
            request.setReason(kactivityModuleProperties.getNft().getReason());

            ActivityAirDrop airDrop = new ActivityAirDrop();
            airDrop.setUser(rewardRequest.getAddress());
            airDrop.setBoxId(Integer.parseInt(rewardRequest.getCurrency()));
            airDrop.setAmount(rewardRequest.getAmount().intValue());
            request.setAirDrops(List.of(airDrop));
            log.error("rewardNFTTccService request:{}", request);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Loa-Secret", kactivityModuleProperties.getNft().getSecret());
            HttpEntity<String> requestData = new HttpEntity<String>(JSONObject.toJSONString(request), headers);
            ActivityAirDropResponse response = restTemplate.postForObject(kactivityModuleProperties.getNft().getUrl() + "/ActivityAirDrop", requestData, ActivityAirDropResponse.class);
            if (response == null || response.getCode() == null) {
                log.info("rewardNFTTccService fail...{}", JSONObject.toJSONString(response));
                throw new ActivityException(ActivityResponseCode.REWARD_FAIL);
            }
            log.info("rewardNFTTccService success...{}", JSONObject.toJSONString(response));
        }catch (Exception ex){
            log.error("rewardNFTTccService error:{}", rewardRequest, ex);
            throw ex;
        }
    }
}
