package com.kikitrade.activity.application;

import com.aliyun.odps.Odps;
import com.kikitrade.accounting.api.RemoteAccountingOperateService;
import com.kikitrade.accounting.api.RemoteProductAccountService;
import com.kikitrade.accounting.api.domain.activity.RemoteAccountingActivityService;
import com.kikitrade.activity.api.RemoteActivityService;
import com.kikitrade.activity.controller.ActivityBatchController;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.tablestore.builder.*;
import com.kikitrade.activity.dal.tablestore.builder.impl.*;
import com.kikitrade.activity.luck.service.business.impl.ReceiveTccServiceImpl;
import com.kikitrade.activity.service.business.ThreePlatformFilter;
import com.kikitrade.activity.service.business.impl.RewardCsvServiceImpl;
import com.kikitrade.activity.service.common.UploadOssUtil;
import com.kikitrade.activity.service.common.config.RateLimiterConfig;
import com.kikitrade.activity.service.config.KactivityConfigListener;
import com.kikitrade.activity.service.config.PropertiesConfigService;
import com.kikitrade.activity.service.engine.action.InviteFirstDepositAction;
import com.kikitrade.activity.service.flow.step.reader.OdpsReader;
import com.kikitrade.activity.service.job.ElasticJobService;
import com.kikitrade.activity.service.mq.RewardDivideListener;
import com.kikitrade.activity.service.mq.RewardSyncMessageService;
import com.kikitrade.activity.service.mq.TopicConfig;
import com.kikitrade.activity.service.reward.impl.RewardBadgeTccServiceImpl;
import com.kikitrade.activity.service.reward.impl.RewardSetsTccServiceImpl;
import com.kikitrade.activity.service.task.ActivityTaskStatisService;
import com.kikitrade.asset.api.RemoteAssetOperateService;
import com.kikitrade.framework.bdd.testing.embeded.EmbededRedisConfiguration;
import com.kikitrade.framework.bdd.testing.embeded.EmbededZookeeperConfiguration;
import com.kikitrade.framework.ignite.IgniteAutoConfiguration;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.frameworks.odps.OdpsTemplate;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kcustomer.api.service.RemoteCustomerExtraService;
import com.kikitrade.kcustomer.api.service.RemoteNotificationService;
import com.kikitrade.ksocial.api.service.RemoteSocialService;
import com.kikitrade.market.client.CurrencyCache;
import com.kikitrade.market.client.CurrencyClient;
import com.kikitrade.market.client.TickerClient;
import com.kikitrade.market.client.service.impl.ClientExchangeService;
import com.kikitrade.quota.api.RemoteQuotaConfigService;
import com.kikitrade.trade.api.RemoteTradingService;
import io.cucumber.spring.CucumberContextConfiguration;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;


@ExtendWith(SpringExtension.class)
@CucumberContextConfiguration
@SpringBootTest(classes = KActivityApplication.class,
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Import({EmbededRedisConfiguration.class, EmbededZookeeperConfiguration.class, ApplicationConfiguration.class})
//@Import({EmbededZookeeperConfiguration.class, ApplicationConfiguration.class})
@TestPropertySource(locations = "classpath:application-junit.properties")
public class KActivityApplicationLocalTest {

    @MockBean
    UploadOssUtil uploadOssUtil;
    @MockBean
    SeqGeneraterService seqGeneraterService;
    @MockBean
    RemoteSocialService remoteSocialService;
    @MockBean
    ElasticJobService elasticJobService;
    @MockBean
    OnsProducer onsProducer;
    @MockBean
    ActivityLotteryItemBuilder activityLotteryItemBuilder;
    @MockBean
    RemoteProductAccountService remoteProductAccountService;
    @MockBean
    ActivityCustomRewardStoreBuilderImpl activityCustomRewardStoreBuilder;
    @MockBean
    RemoteAccountingOperateService remoteAccountingOperateService;
    @MockBean
    RemoteCustomerExtraService remoteCustomerExtraService;
    //@MockBean
    RemoteNotificationService remoteNotificationService;
    @MockBean
    RemoteAssetOperateService remoteAssetOperateService;
    @MockBean
    RewardSyncMessageService rewardSyncMessageService;
    @MockBean
    ClientExchangeService clientExchangeService;
    @MockBean
    CurrencyClient currencyClient;
    @MockBean
    TickerClient tickerClient;
    @MockBean
    RemoteTradingService remoteTradingService;
    @MockBean
    LuckFortuneReleaseItemBuilder luckFortuneReleaseItemBuilder;
    @MockBean
    LuckFortuneReceiveItemBuilder luckFortuneReceiveItemBuilder;
    @MockBean
    RemoteAccountingActivityService remoteAccountingActivityService;
    @MockBean
    LuckFortuneRefundItemBuild luckFortuneRefundItemBuild;
    @MockBean
    ActivityTaskItemBuilder activityTaskItemBuilder;
    //    @MockBean
//    ActivityTaskDetailBuilder activityTaskDetailBuilder;
    @MockBean
    CustomerReferralStoreBuilder customerReferralStoreBuilder;
    @MockBean
    ActivityBatchStatusStoreBuilder activityBatchStatusStoreBuilder;
    @MockBean
    ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;
    @MockBean
    ActivityRecordsBuilder activityRecordsBuilder;
    @MockBean
    ActivityUserTotalDataBuilder activityUserTotalDataBuilder;
    @MockBean
    CustomerRewardStoreBuilder customerRewardStoreBuilder;
    @MockBean
    ActivityJoinItemBuilder activityJoinItemBuilder;
    @MockBean
    OdpsTemplate odpsTemplate;
    @MockBean
    ActivityBizResultBuilder activityBizResultBuilder;
    @MockBean
    RebateTransactionBuilder rebateTransactionBuilder;
    @MockBean
    RewardCsvServiceImpl rewardCsvService;
    @MockBean
    PropertiesConfigService propertiesConfigService;
    @MockBean
    KactivityConfigListener kactivityConfigListener;
    @MockBean
    Odps odps;
    @MockBean
    OdpsReader odpsReader;
    @MockBean
    RateLimiterConfig rateLimiterConfig;
    @MockBean
    TaskConfigBuilderImpl taskConfigBuilder;
    @MockBean
    GoodsBuilderImpl goodsBuilder;
    @MockBean
    GoodsStockBuilder goodsStockBuilder;
    @MockBean
    BannerBuilder bannerBuilder;
    @MockBean
    LotteryConfigBuilder lotteryConfigBuilder;
    @MockBean
    RemoteCustomerBindService remoteCustomerBindService;
    @MockBean
    RewardBadgeTccServiceImpl rewardBadgeTccServiceImpl;
    @MockBean
    TopicConfig topicConfig;
    @MockBean
    CouponConfigBuilder couponConfigBuilder;
    @MockBean
    RemoteQuotaConfigService remoteQuotaConfigService;
    @MockBean
    ClaimItemBuilder claimItemBuilder;
    @MockBean
    ActivityCumulateItemBuilderImpl activityCumulateItemBuilder;
    @MockBean
    CustomerQuestionSetsBuilder customerQuestionSetsBuilder;
    @MockBean
    ActivityTaskStatisBuilder activityTaskStatisBuilder;
    @MockBean
    ThreePlatformFilter threePlatformFilter;
    @MockBean
    PrecisionTrackBuilder precisionTrackBuilder;
    @MockBean
    PrecisionPoolBuilder precisionPoolBuilder;
    @MockBean
    PrecisionMetricsBuilder precisionMetricsBuilder;
//    @MockBean
//    RewardSetsTccServiceImpl rewardSetsTccService;
    //    @MockBean
//    RemoteActivityService remoteActivityService;
//    @MockBean
//    CurrencyCache currencyCache;
//    @MockBean
//    IgniteAutoConfiguration igniteAutoConfiguration;
//    @MockBean
//    ActivityBatchController activityBatchController;
//    @MockBean
//    InviteFirstDepositAction inviteFirstDepositAction;
//    @MockBean
//    EmbededRedisConfiguration embededRedisConfiguration;
//    @MockBean
//    OkHttpClient okHttpClient;
//    @MockBean
//    RewardDivideListener rewardDivideListener;


}
