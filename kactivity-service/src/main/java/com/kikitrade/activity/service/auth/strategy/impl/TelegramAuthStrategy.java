package com.kikitrade.activity.service.auth.strategy.impl;

import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.service.auth.strategy.AuthContext;
import com.kikitrade.activity.service.auth.strategy.AuthResult;
import com.kikitrade.activity.service.auth.strategy.PlatformAuthStrategy;
import com.kikitrade.activity.service.model.OpenAuthRequest;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Telegram认证策略实现
 * Telegram认证不使用OAuth2流程，而是直接传递用户信息
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
public class TelegramAuthStrategy implements PlatformAuthStrategy {

    @Override
    public String getPlatformName() {
        return OpenSocialPlatformEnum.telegram.name();
    }

    @Override
    public AuthResult authenticate(AuthContext context) {
        log.info("[Telegram] 开始Telegram认证: customerId={}, socialId={}, socialName={}", 
            context.getCustomerId(), context.getSocialId(), context.getSocialName());

        try {
            // 构建社交用户信息
            SocialUserInfo socialUserInfo = SocialUserInfo.builder()
                    .userId(context.getSocialId())
                    .userName(context.getSocialName())
                    .build();

            // Telegram认证不需要token
            Token token = new Token();
            token.setSocialCustomerId(socialUserInfo.getUserId());
            token.setUserName(socialUserInfo.getUserName());

            return AuthResult.success(token, socialUserInfo);

        } catch (Exception e) {
            log.error("[Telegram] 认证过程中发生异常", e);
            return AuthResult.failure("Telegram认证失败");
        }
    }

    @Override
    public OpenAuthRequest buildAuthRequest(String saasId, String code, String redirectUri) {
        // Telegram认证不需要OAuth2请求
        return null;
    }

    @Override
    public OpenAuthRequest buildRefreshRequest(String saasId, String refreshToken) {
        // Telegram认证不需要刷新令牌
        return null;
    }

    @Override
    public SocialUserInfo getCurrentUser(String saasId, String accessToken, String refreshToken) {
        // Telegram使用Bot API获取用户信息，这里不需要实现
        return null;
    }

    @Override
    public boolean supportsRefreshToken() {
        return false;
    }
}
