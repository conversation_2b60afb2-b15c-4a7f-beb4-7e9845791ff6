package com.kikitrade.activity.service.flow;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2023-01-20 15:39
 */
@Slf4j
public class AbstractDataService{

    protected LoadingCache<String, ActivityBatch> batchCache = null;
    protected LoadingCache<String, ActivityEntity> activityCache = null;
    protected Cache<String, Boolean> rewardCache = null;
    protected volatile boolean isUpdateRewardType = false;

    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ActivityEntityService activityEntityService;

    @PostConstruct
    public void init(){
        log.info("AbstractDataService init");
        batchCache = CacheBuilder.newBuilder().
                maximumSize(1024 * 1024 * 1024).//1M
                expireAfterAccess(1, TimeUnit.MINUTES)
                .build(new CacheLoader<String, ActivityBatch>() {
                    @Override
                    public ActivityBatch load(String s) throws Exception {
                        return activityBatchNewService.findByBatchIdFromCache(s);
                    }
                });
        activityCache= CacheBuilder.newBuilder().
                maximumSize(500 * 1024 * 1024).//500k
                expireAfterAccess(1, TimeUnit.MINUTES)
                .build(new CacheLoader<String, ActivityEntity>() {
                    @Override
                    public ActivityEntity load(String s) throws Exception {
                        return activityEntityService.findByIdFromCache(s);
                    }
                });
        rewardCache = CacheBuilder.newBuilder().
                maximumSize(500 * 1024 * 1024).//500k
                expireAfterAccess(5, TimeUnit.MINUTES)
                .build();
    }
}
