package com.kikitrade.activity.api.model.request;

import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.market.common.model.constant.CoinCodeConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 邀请总览：已获得奖励
 * <AUTHOR>
 * @create 2022/12/13 2:39 下午
 * @modify
 */
@Data
public class InviteRewardsTotalRequest implements Serializable {
    private String customerId;
    private List<ActivityConstant.RewardBusinessType> rewardBusinessTypes;
    private List<ActivityConstant.AwardTypeEnum> rewardTypes;
    private String currency = CoinCodeConstant.USDT.getCode();
    private int offset = 0;
    private int limit = 10;
    private String saasId;
}
