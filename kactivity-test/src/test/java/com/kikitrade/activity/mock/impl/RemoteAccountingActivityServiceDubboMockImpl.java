package com.kikitrade.activity.mock.impl;

import com.kikitrade.accounting.api.domain.activity.RemoteAccountingActivityService;
import com.kikitrade.accounting.api.domain.activity.model.AccountActivityRequest;
import com.kikitrade.accounting.api.model.account.AccountException;
import com.kikitrade.accounting.api.model.account.response.AccountOperateResponse;
import com.kikitrade.accounting.api.model.account.response.AccountingResponseCode;
import com.kikitrade.activity.mock.RemoteAccountingActivityServiceDubboMock;
import com.kikitrade.activity.op.BusinessOP;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class RemoteAccountingActivityServiceDubboMockImpl implements RemoteAccountingActivityServiceDubboMock {

    @Resource
    RemoteAccountingActivityService remoteAccountingActivityService;

    @Override
    public void transfer() throws AccountException {
        Mockito.doAnswer(c -> {
            AccountActivityRequest accountActivityRequest = c.getArgument(0);
            BusinessOP.accountActivityRequests.add(accountActivityRequest);

            return AccountOperateResponse.builder()
                    .code(AccountingResponseCode.SUCCESS)
                    .build();


        }).when(remoteAccountingActivityService).transfer(Mockito.any(AccountActivityRequest.class));

    }

    @Override
    public void transferError() throws AccountException {
        Mockito.doAnswer(c -> {

            throw new RuntimeException("remoteAccountingActivityService transfer error");

        }).when(remoteAccountingActivityService).transfer(Mockito.any(AccountActivityRequest.class));

    }
}
