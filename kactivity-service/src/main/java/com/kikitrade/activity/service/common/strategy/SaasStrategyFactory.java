package com.kikitrade.activity.service.common.strategy;

import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.service.common.strategy.discord.DiscordSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.email.EmailSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.facebook.FacebookSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.google.GoogleSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.line.LineSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.telegram.TelegramSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.twitter.TwitterSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.wallet.WalletSaasStrategyService;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4 15:44
 */
@Component
public class SaasStrategyFactory {

    @Autowired
    private List<TwitterSaasStrategyService> twitterSaasStrategyServiceList;
    @Autowired
    private List<DiscordSaasStrategyService> discordSaasStrategyServiceList;
    @Autowired
    private List<GoogleSaasStrategyService> googleSaasStrategyServiceList;
    @Autowired
    private List<FacebookSaasStrategyService> facebookSaasStrategyServiceList;
    @Autowired
    private List<LineSaasStrategyService> lineSaasStrategyServiceList;
    @Autowired
    private List<EmailSaasStrategyService> emailSaasStrategyServiceList;
    @Autowired
    private List<TelegramSaasStrategyService> telegramSaasStrategyServiceList;
    @Autowired
    private List<WalletSaasStrategyService> walletSaasStrategyServiceList;

    public static Map<String, TwitterSaasStrategyService> twitterMap = new HashMap<>();
    public static Map<String, DiscordSaasStrategyService> discordMap = new HashMap<>();
    public static Map<String, GoogleSaasStrategyService> googleMap = new HashMap<>();
    public static Map<String, FacebookSaasStrategyService> facebookMap = new HashMap<>();
    public static Map<String, LineSaasStrategyService> lineMap = new HashMap<>();
    public static Map<String, EmailSaasStrategyService> emailMap = new HashMap<>();
    public static Map<String, TelegramSaasStrategyService> telegramMap = new HashMap<>();
    public static Map<String, WalletSaasStrategyService> walletMap = new HashMap<>();

    @PostConstruct
    public void init(){
        twitterSaasStrategyServiceList.forEach(s -> {
            twitterMap.put(s.strategy(), s);
        });

        discordSaasStrategyServiceList.forEach(s -> {
            discordMap.put(s.strategy(), s);
        });

        googleSaasStrategyServiceList.forEach(s -> {
            googleMap.put(s.strategy(), s);
        });

        facebookSaasStrategyServiceList.forEach(s -> {
            facebookMap.put(s.strategy(), s);
        });

        lineSaasStrategyServiceList.forEach(s -> {
            lineMap.put(s.strategy(), s);
        });

        emailSaasStrategyServiceList.forEach(s -> {
            emailMap.put(s.strategy(), s);
        });

        telegramSaasStrategyServiceList.forEach(s -> {
            telegramMap.put(s.strategy(), s);
        });

        walletSaasStrategyServiceList.forEach(s -> {
            walletMap.put(s.strategy(), s);
        });
    }

    public TwitterSaasStrategyService getTwitterService(String strategy){
        return twitterMap.get(strategy);
    }

    public DiscordSaasStrategyService getDiscordService(String strategy){
        return discordMap.get(strategy);
    }

    public GoogleSaasStrategyService getGoogleService(String strategy){
        return googleMap.get(strategy);
    }

    public FacebookSaasStrategyService getFacebookService(String strategy){
        return facebookMap.get(strategy);
    }

    public LineSaasStrategyService getLineService(String strategy){
        return lineMap.get(strategy);
    }

    public EmailSaasStrategyService getEmailService(String strategy){
        return emailMap.get(strategy);
    }

    public TelegramSaasStrategyService getTelegramService(String strategy){
        return telegramMap.get(strategy);
    }

    public WalletSaasStrategyService getWalletService(String strategy){
        return walletMap.get(strategy);
    }
}
