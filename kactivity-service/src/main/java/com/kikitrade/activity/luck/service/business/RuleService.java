package com.kikitrade.activity.luck.service.business;

import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO;

/**
 *
 */
public interface RuleService {

    /**
     * 新增
     * @param luckFortuneRuleDTO
     * @return
     * @throws Exception
     */
    @Deprecated
    String trySaveRule(LuckFortuneRuleDTO luckFortuneRuleDTO, String id) throws Exception;


    /**
     * 修改
     * @param luckFortuneRuleDTO
     * @return
     * @throws Exception
     */
    @Deprecated
    String tryUpdateRule(LuckFortuneRuleDTO luckFortuneRuleDTO) throws Exception;

    /**
     * 新增
     * @param luckFortuneRuleDTO
     * @return
     * @throws Exception
     */
    String trySaveRuleV1(LuckFortuneRule luckFortuneRule, String id) throws Exception;


    /**
     * 修改
     * @param luckFortuneRuleDTO
     * @return
     * @throws Exception
     */
    String tryUpdateRuleV1(LuckFortuneRule luckFortuneRule) throws Exception;

    /**
     * 查询配置
     * @param kycLevel
     * @param userType
     * @return
     */
    LuckFortuneRule findObject(String kycLevel, String userType);
}
