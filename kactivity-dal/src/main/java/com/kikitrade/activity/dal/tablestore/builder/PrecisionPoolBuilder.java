package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.PrecisionPool;

/**
 * Builder for PrecisionPool
 */
public interface PrecisionPoolBuilder {

    /**
     * Insert a new PrecisionPool record
     * @param precisionPool the PrecisionPool record to insert
     * @return true if the insertion was successful, false otherwise
     */
    boolean insert(PrecisionPool precisionPool);

    boolean update(PrecisionPool precisionPool);

    /**
     * Find a PrecisionPool record by its ID
     * @param id the ID of the PrecisionPool record to find
     * @return the found PrecisionPool record, or null if not found
     */
    PrecisionPool findById(String id);

    String getTableName();
}
