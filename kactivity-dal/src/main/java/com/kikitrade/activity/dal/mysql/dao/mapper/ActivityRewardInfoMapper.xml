<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityRewardInfoDao">

    <resultMap type="com.kikitrade.activity.dal.mysql.model.ActivityRewardInfo" id="ActivityRewardInfo">
        <result column="tran_date" property="tranDate" javaType="java.lang.String"/>
        <result column="activity_id" property="activityId" javaType="java.lang.Integer"/>
        <result column="activity_type" property="activityType" javaType="java.lang.Integer"/>
        <result column="total_num" property="totalNum" javaType="java.lang.Integer"/>
        <result column="total_amt" property="totalAmt" javaType="java.math.BigDecimal"/>
    </resultMap>


    <select id="findByActivityId" resultMap="ActivityRewardInfo">
        SELECT *
        FROM activity_reward_info
        WHERE activity_id = #{activity_id}
        order by tran_date desc
    </select>


</mapper>
