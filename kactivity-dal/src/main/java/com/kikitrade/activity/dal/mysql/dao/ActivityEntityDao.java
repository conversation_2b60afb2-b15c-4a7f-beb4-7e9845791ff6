package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

public interface ActivityEntityDao extends BaseDao<ActivityEntity, String> {

    @Update("update activity_entity set next_create_time = #{activityEntity.nextCreateTime}, modified = #{activityEntity.modified} where id = #{activityEntity.id}")
    int updateNextCreateTime(@Param("activityEntity") ActivityEntity activityEntity);

    @Select("select `id`,`name`,`type`,`start_time` as startTime,`end_time` as endTime,`remark`,`area`,`cycle`,`auto_create_batch` as autoCreateBatch,`next_create_time` as nextCreateTime, `reward_config` as rewardConfig,`status`,`created`,`modified`,`saasId`, `template_code` as templateCode, conditions, task_id as taskId, source, sub_type as subType from activity_entity where auto_create_batch = 1 and next_create_time is not null and next_create_time <= #{date} order by id desc")
    List<ActivityEntity> findByAutoCreateBatch(Date date);
}
