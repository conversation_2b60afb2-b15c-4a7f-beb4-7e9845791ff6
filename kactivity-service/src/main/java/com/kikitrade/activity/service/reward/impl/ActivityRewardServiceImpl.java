package com.kikitrade.activity.service.reward.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import com.kikitrade.activity.api.model.ActivityResponse;
import com.kikitrade.activity.api.model.RewardDTO;
import com.kikitrade.activity.api.model.RewardsRequest;
import com.kikitrade.activity.api.model.activity.InviteRewardDTO;
import com.kikitrade.activity.api.model.activity.RewardTotalDTO;
import com.kikitrade.activity.api.model.activity.RewardVO;
import com.kikitrade.activity.api.model.request.InviteRewardsRequest;
import com.kikitrade.activity.api.model.request.InviteRewardsRequest.QueryParam;
import com.kikitrade.activity.api.model.request.InviteRewardsSumRequest;
import com.kikitrade.activity.api.model.request.InviteRewardsTotalRequest;
import com.kikitrade.activity.api.model.response.InviteRewardsResponse;
import com.kikitrade.activity.api.model.response.InviteRewardsSumResponse;
import com.kikitrade.activity.api.model.response.InviteRewardsTotalResponse;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.CustomerRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.CustomerReward;
import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.reward.ActivityRewardService;
import com.kikitrade.activity.service.reward.KikiActivityGiftTccService;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.kpay.common.util.BigDecimalUtil;
import com.kikitrade.market.client.CurrencyClient;
import com.kikitrade.market.client.MarketExchangeClient;
import com.kikitrade.market.common.exception.MarketException;
import com.kikitrade.market.common.model.constant.CoinCodeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ActivityRewardServiceImpl
 *
 * <AUTHOR>
 * @create 2021/9/7 7:32 下午
 * @modify
 */
@Slf4j
@Component
public class ActivityRewardServiceImpl implements ActivityRewardService {

    private final static int TOTAL_TOKEN_PRECISION = 2;
    private final static int LIST_TOKEN_PRECISION = 8;
    
    @Resource
    private CustomerRewardStoreBuilder customerRewardStoreBuilder;
    @Resource
    private KikiActivityGiftTccService kikiInviteGiftTccService;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private CurrencyClient currencyClient;
    @Resource
    private MarketExchangeClient exchangeClient;

    @Resource
    private KactivityProperties kactivityProperties;

    @Override
    public boolean save(CustomerReward reward) {
        return customerRewardStoreBuilder.insertIfNotExist(reward);
    }


    public ActivityResponse fiatReward(List<CustomerReward> rewardList) {
        boolean success = rewardList.stream().map(reward ->
                {
                    try {
                        return kikiInviteGiftTccService.tryFiatReward(reward);
                    } catch (Exception e) {
                        log.error("fiatReward process fail, reward:{}", JSONObject.toJSONString(reward), e);
                    }
                    return false;
                }
        ).allMatch(f -> f && true);
        return ActivityResponse.builder().success(success).build();
    }

    @Override
    public PageResult findByCustomer(String customerId, int offset, int limit) {
        return customerRewardStoreBuilder.findByCustomerId(customerId, offset, limit);
    }

    @Override
    public Long countByCustomer(String customerId) {
        return customerRewardStoreBuilder.countByCustomer(customerId);
    }

    @Override
    public Long countByCustomerAndType(String customerId, Integer type) {
        return customerRewardStoreBuilder.countByCustomerAndType(customerId, type);
    }


    @Override
    public Long totalRewardCount(String customerId, String currency, ActivityType type) {
        return customerRewardStoreBuilder.countByCustomerTypeAndCurrency(customerId, currency, type.getCode());
    }


    @Override
    public Long countByInviteeAndType(String invitee, Integer type) {
        return customerRewardStoreBuilder.countByInviteeAndType(invitee, type);
    }

    @Override
    public BigDecimal getTotalReward(String customerId) {
        return customerRewardStoreBuilder.sumByCustomer(customerId);
    }


    @Override
    public BigDecimal totalReward(String customerId) {
        return sumByCustomerAndType(customerId, ActivityType.KIKI_INVITE_GIFT.getCode());
    }


    @Override
    public BigDecimal totalReward(String customerId, String currency, ActivityType type) {
        return sumByCustomerAndType(customerId, type.getCode());

    }

    @Override
    public PageResult rewards(String customerId, ActivityType type, int offset, int limit) {
        int activityType = type.getCode();
        PageResult pageResult = customerRewardStoreBuilder.findByCustomerIdAndType(customerId, activityType, offset, limit);
        Long count = customerRewardStoreBuilder.countByCustomerAndType(customerId, activityType);
        pageResult.setTotalPage(count);

        List<CustomerReward> crRows = pageResult.getRows();
        List<RewardDTO> rows = crRows.stream().map(cr -> {
            RewardDTO dto = new RewardDTO();
            BeanUtils.copyProperties(cr, dto);
            return dto;
        }).collect(Collectors.toList());

        pageResult.setRows(rows);

        return pageResult;
    }

    @Override
    public BigDecimal sumByCustomerAndType(String customerId, Integer type) {
        //目前只会有 一种货币（USDT） 不存在换算问题。
        return customerRewardStoreBuilder.sumByCustomerAndType(customerId, type);
    }

    @Override
    public BigDecimal sumByCustomerCurrencyAndType(String customerId, String currnecy, Integer type) {
        //目前只会有 一种货币（USDT） 不存在换算问题。
        return customerRewardStoreBuilder.sumByCustomerCurrencyAndType(customerId, currnecy, type);
    }

    @Override
    public PageResult inviteRewards(String customerId, int offset, int limit) {
        return activityCustomRewardStoreBuilder.findByReferId(customerId, offset, limit);
    }

    /**
     * 被邀请奖励人数
     * @param referId
     * @return
     */
    @Override
    public long inviteRewardCount(String referId) {
        return activityCustomRewardStoreBuilder.countByReferId(referId);
    }

    @Override
    public BigDecimal inviteRewardSum(String customerId) {
        //Integer coin = currencyClient.get("USD").getShowKeepDecimalForCoin();
        BigDecimal sum = activityCustomRewardStoreBuilder.sumCostByCustomerIdAndSide(customerId, ActivityConstant.SideEnum.INVITER.name());
        return sum.setScale(TOTAL_TOKEN_PRECISION, RoundingMode.DOWN);
    }

    @Override
    public BigDecimal inviteRewardSumAfterTime(String customerId) {
        BigDecimal sum = activityCustomRewardStoreBuilder.sumCostByCustomerIdAndSideTime(customerId, ActivityConstant.SideEnum.INVITER.name(), kactivityProperties.getInviteStartTime());
        return sum.setScale(TOTAL_TOKEN_PRECISION, RoundingMode.DOWN);
    }

    @Override
    public PageResult rewards(RewardsRequest rewardsRequest, int offset, int limit) {
        return activityCustomRewardStoreBuilder.findByCustomerIdAndBusinessType(rewardsRequest.getCustomerId(), rewardsRequest.getBusinessType(), offset, limit);
    }

    @Override
    public long countRewards(RewardsRequest rewardsRequest) {
        return activityCustomRewardStoreBuilder.countByCustomerIdAndBusinessType(rewardsRequest.getCustomerId(), rewardsRequest.getBusinessType());
    }

    @Override
    public InviteRewardsTotalResponse inviteRewardsTotal(InviteRewardsTotalRequest request) throws MarketException {

        if(CollectionUtils.isEmpty(request.getRewardBusinessTypes())){
            request.setRewardBusinessTypes(Arrays.asList(ActivityConstant.RewardBusinessType.reward));
        }

        // 查询明细
        long total = activityCustomRewardStoreBuilder.countRewardsByTypeSide(request.getCustomerId(), request.getRewardBusinessTypes(), request.getRewardTypes(), null, kactivityProperties.getInviteStartTime());
        PageResult pageResult = activityCustomRewardStoreBuilder.getRewardsByTypeSide(request.getCustomerId(),
                request.getRewardBusinessTypes(), request.getRewardTypes(), null, kactivityProperties.getInviteStartTime(), request.getOffset(), request.getLimit());

        // 奖励总计
        BigDecimal tokenTotalAmount = BigDecimal.ZERO, pointTotalAmount = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(request.getRewardTypes())
                || request.getRewardTypes().contains(ActivityConstant.AwardTypeEnum.TOKEN) || request.getRewardTypes().contains(ActivityConstant.AwardTypeEnum.Token)) {
            tokenTotalAmount = activityCustomRewardStoreBuilder.getSumByTypeSide(request.getCustomerId(), request.getRewardBusinessTypes(),
                    Lists.newArrayList(ActivityConstant.AwardTypeEnum.TOKEN, ActivityConstant.AwardTypeEnum.Token), null, kactivityProperties.getInviteStartTime());
        }
        if (CollectionUtils.isEmpty(request.getRewardTypes())
                || request.getRewardTypes().contains(ActivityConstant.AwardTypeEnum.POINT)) {
            pointTotalAmount = activityCustomRewardStoreBuilder.getSumByTypeSide(request.getCustomerId(), request.getRewardBusinessTypes(),
                    Lists.newArrayList(ActivityConstant.AwardTypeEnum.POINT), null, kactivityProperties.getInviteStartTime());
        }
        return InviteRewardsTotalResponse.builder()
                .code(ActivityResponseCode.SUCCESS)
                .reward(parseToInviteReward(tokenTotalAmount, pointTotalAmount, pageResult, total))
                .build();
    }

    private InviteRewardDTO parseToInviteReward(BigDecimal tokenTotalAmount, BigDecimal pointTotalAmount, PageResult pageResult, long total) throws MarketException {
        return InviteRewardDTO.builder()
                .rewards(parseToRewardVO(pageResult, total))
                .rewardTotal(RewardTotalDTO.builder().tokenTotalAmount(tokenTotalAmount.setScale(LIST_TOKEN_PRECISION, RoundingMode.DOWN)).pointTotalAmount(pointTotalAmount).build())
                .currency(CoinCodeConstant.USD.getCode())
                .build();
    }

    private Page<RewardVO> parseToRewardVO(PageResult pageResult, long total) throws MarketException {
        Page<RewardVO> result = new Page();
        if(pageResult == null){
            return result;
        }
        if(CollectionUtils.isNotEmpty(pageResult.getRows())){
            List<ActivityCustomReward> rows = (List<ActivityCustomReward>)pageResult.getRows();
            List<RewardVO> rewardVOList = new ArrayList<>();
            for (ActivityCustomReward item : rows) {
                RewardVO rewardVO = RewardVO.builder()
                        .currency(item.getCurrency())
                        .userName(item.getUserName())
                        .nickName(item.getNickName())
                        .activityName(item.getActivityName())
                        .rewardTime(item.getRewardTime())
                        .businessType(item.getBusinessType())
                        .rewardType(StringUtils.upperCase(item.getRewardType()))
                        .extendParam(item.getExtendParamMap())
                        .build();
                if (StringUtils.equals(rewardVO.getRewardType(), ActivityConstant.AwardTypeEnum.TOKEN.name())) {
                    rewardVO.setAmount(new BigDecimal(item.getAmount()).setScale(LIST_TOKEN_PRECISION, RoundingMode.DOWN).toPlainString());
                } else {
                    rewardVO.setAmount(item.getAmount());
                }
                rewardVOList.add(rewardVO);
            }
            result.setRows(rewardVOList);
        }
        result.setTotalCount(total);
        return result;
    }

    @Override
    public InviteRewardsResponse inviteRewards(InviteRewardsRequest request) throws MarketException {
        Map<ActivityConstant.RewardBusinessType, InviteRewardDTO> map = new HashMap<>();
        for (Map.Entry<ActivityConstant.RewardBusinessType, QueryParam> entry : request.getQueryMap().entrySet()) {
            QueryParam queryParam = entry.getValue();
            // 查询明细
            long total = activityCustomRewardStoreBuilder.countRewardsByTypeSide(request.getCustomerId(), Arrays.asList(entry.getKey()), queryParam.getRewardTypes(), request.getRole(), null);
            PageResult pageResult = activityCustomRewardStoreBuilder.getRewardsByTypeSide(request.getCustomerId(),
                    Arrays.asList(entry.getKey()), queryParam.getRewardTypes(), request.getRole(), null, queryParam.getOffset(), queryParam.getLimit());

            // 查询金额总计
            BigDecimal tokenTotalAmount = BigDecimal.ZERO, pointTotalAmount = BigDecimal.ZERO;
            if (CollectionUtils.isEmpty(queryParam.getRewardTypes())
                    || queryParam.getRewardTypes().contains(ActivityConstant.AwardTypeEnum.TOKEN) || queryParam.getRewardTypes().contains(ActivityConstant.AwardTypeEnum.Token)) {
                tokenTotalAmount = activityCustomRewardStoreBuilder.getSumByTypeSide(request.getCustomerId(), Arrays.asList(entry.getKey()),
                        Lists.newArrayList(ActivityConstant.AwardTypeEnum.TOKEN, ActivityConstant.AwardTypeEnum.Token), request.getRole(), null);
            }
            if (CollectionUtils.isEmpty(queryParam.getRewardTypes())
                    || queryParam.getRewardTypes().contains(ActivityConstant.AwardTypeEnum.POINT)) {
                pointTotalAmount = activityCustomRewardStoreBuilder.getSumByTypeSide(request.getCustomerId(), Arrays.asList(entry.getKey()),
                        Lists.newArrayList(ActivityConstant.AwardTypeEnum.POINT), request.getRole(), null);
            }

            map.put(entry.getKey(), parseToInviteReward(tokenTotalAmount, pointTotalAmount, pageResult, total));
        }
        return InviteRewardsResponse.builder()
                .code(ActivityResponseCode.SUCCESS)
                .rewards(map)
                .build();
    }

    @Override
    public InviteRewardsSumResponse inviteRewardsSumByType(InviteRewardsSumRequest request) throws MarketException {
        Map<ActivityConstant.RewardBusinessType, Map<ActivityConstant.AwardTypeEnum, BigDecimal>> map = new HashMap<>();
        // 按 businessType, rewardType 进行 groupBy, 查询每个 groupBy 的总奖励金额
        Map<String, Map<String, BigDecimal>> rewards = activityCustomRewardStoreBuilder.getSumGroupByBusinessAward(request.getCustomerId(), request.getRewardsType(), null, kactivityProperties.getInviteStartTime());

        for (ActivityConstant.RewardBusinessType businessType : request.getRewardsType()) {
            if (!map.containsKey(businessType)) {
                map.put(businessType, new HashMap<>());
            }
            Map<String, BigDecimal> rewardMap = rewards.getOrDefault(businessType.name(), new HashMap<>());
            for (ActivityConstant.AwardTypeEnum awardType : ActivityConstant.AwardTypeEnum.values()) {
                BigDecimal amount = rewardMap.getOrDefault(awardType.name(), BigDecimal.ZERO);
                ActivityConstant.AwardTypeEnum awardTypeUpper = ActivityConstant.AwardTypeEnum.valueOf(awardType.name().toUpperCase());
                if (StringUtils.equalsIgnoreCase(awardType.name(), ActivityConstant.AwardTypeEnum.TOKEN.name())) {
                    // 精度转换
                    BigDecimal total = amount.add(map.get(businessType).getOrDefault(awardTypeUpper, BigDecimal.ZERO));
                    map.get(businessType).put(awardTypeUpper, exchangeClient.exchange(total, CoinCodeConstant.USD.getCode(), CoinCodeConstant.USDT.getCode()));
                } else {
                    map.get(businessType).put(awardTypeUpper, amount.add(map.get(businessType).getOrDefault(awardTypeUpper, BigDecimal.ZERO)));
                }
            }
        }
        return InviteRewardsSumResponse.builder()
                .code(ActivityResponseCode.SUCCESS)
                .rewards(map)
                .build();
    }
}
