package com.kikitrade.activity.service.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.framework.ons.OnsProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 两阶段tcc异步业务消息， 一般的场景为1）内部转账之后，在到账之后发起汇兑欧购币   2） 增加账务额度之后，发起核减操作并做二阶段业务处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PhaseTwoMessageServiceImpl implements PhaseTwoMessageService {
    @Resource
    private OnsProducer onsProducer;

    @Resource
    private TopicConfig topicConfig;


    @Override
    public <T> void sendSyncPhaseTwoMessage(String phaseTwoBusinessType, T msg) {
        PhaseTwoMessage<T> message = new PhaseTwoMessage<T>(phaseTwoBusinessType, msg);
        SendResult send = onsProducer.send(topicConfig.getTopicPhaseTwo(), JSON.toJSONString(message));
        log.info("sent message {} id is {}", send.getTopic(), send.getMessageId());
    }

}
