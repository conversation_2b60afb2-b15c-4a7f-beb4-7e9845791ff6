package com.kikitrade.activity.service.rpc.line;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.service.common.config.LineProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.json.JSONObject;

import java.time.OffsetDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/20 11:43
 * @description:
 */
@Data
@Slf4j
public class CurrentUserRequest extends AccessToken {

    public CurrentUserRequest(){}

    private LineProperties lineProperties;

    public CurrentUserRequest(LineProperties lineProperties, String accessToken){
        this.lineProperties = lineProperties;
        this.accessToken = accessToken;
    }



    public SocialUserInfo execute() {
        SocialUserInfo cache = LineCache.get(this.accessToken, SocialUserInfo.class);
        if (cache != null) {
            return cache;
        }

        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String query = String.format("access_token=%s&client_id=%s",
            accessToken,
            lineProperties.getClientId().get(saasId));
        RequestBody body = RequestBody.create(mediaType, query);

        Request request = new Request.Builder()
            .url("https://api.line.me/oauth2/v2.1/userinfo")
            .method("POST", body)
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .build();
        try {
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("[line] currentUser:{}", jsonObject);

            String id = jsonObject.getString("sub");
            String username = jsonObject.getString("name");

            SocialUserInfo userInfo =
                SocialUserInfo.builder()
                    .userId(id)
                    .userName(username)
                    .createdAt(OffsetDateTime.now().toEpochSecond())
                    .build();
            LineCache.put(this.accessToken, JSON.toJSONString(userInfo));
            return userInfo;
        } catch (Exception e) {
            log.error("[line] currentUser error:{}", accessToken, e);
            return null;
        }
    }
}
