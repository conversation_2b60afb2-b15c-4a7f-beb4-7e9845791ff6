package com.kikitrade.activity.service.engine.action.base;

import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.tablestore.model.CustomerReward;
import com.kikitrade.activity.model.ActivityEventMassage;
import com.kikitrade.activity.model.ActivityMessage;
import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.market.client.CurrencyClient;
import com.kikitrade.market.common.model.CurrencyDTO;
import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
public abstract class AbstractBaseAction implements BaseAction {

    @Resource
    private CurrencyClient currencyClient;

    public boolean actionDataPrepare(ActivityEventMassage activityEventMassage) throws Exception {
        return true;
    }

    public boolean actionPrepare(ActivityMessage activityMassage, Activity activity) throws Exception {
        return true;
    }

    public String getBusinessId(String business_id, Integer activity_id) {
        return activity_id + "-" + business_id;
    }

    public BigDecimal getRewardAmt(String currency, BigDecimal rewardAmtOrig) {
        //币精度判断
        BigDecimal rewardAmt = rewardAmtOrig;
        CurrencyDTO currencyDTO = currencyClient.get(currency);
        Integer keepDecimalForCoin = currencyDTO.getKeepDecimalForCoin();
        log.info("AbstractBaseAction currency [{}] ,rewardAmtOrig [{}] ,keepDecimalForCoin [{}]:", currency, rewardAmtOrig, keepDecimalForCoin);
        if (currencyDTO != null && keepDecimalForCoin != null) {
            rewardAmt = rewardAmtOrig.setScale(keepDecimalForCoin, BigDecimal.ROUND_DOWN);
            log.info("AbstractBaseAction  currency [{}] ,rewardAmtOrig [{}] ,rewardAmt round down [{}]:", currency, rewardAmtOrig, rewardAmt);
        }
        return rewardAmt;
    }

    public CustomerReward toCustomerReward(ActivityMessage msg, String currency, BigDecimal amount, Integer type) {
        CustomerReward reward = new CustomerReward();
        reward.setActivityId(msg.getActivityId());
        reward.setCustomerId(msg.getCustomerId());
        reward.setType(type);
        reward.setSecondStep(ActivityType.getType(type).getSecondStep().name());
        reward.setCurrency(currency);
        reward.setAmount(amount);
        reward.setRewardTime(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        reward.setBusinessId(msg.getBusinessId());
        return reward;
    }

}
