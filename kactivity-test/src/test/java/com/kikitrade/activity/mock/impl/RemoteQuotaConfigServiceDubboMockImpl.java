package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteQuotaConfigServiceDubboMock;
import com.kikitrade.quota.api.RemoteQuotaConfigService;
import com.kikitrade.quota.api.model.request.QuotaConfigDTO;
import jakarta.annotation.Resource;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import static com.kikitrade.activity.op.BusinessOP.quotaConfigDTOList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;

@Component
public class RemoteQuotaConfigServiceDubboMockImpl implements RemoteQuotaConfigServiceDubboMock {
    @Resource
    RemoteQuotaConfigService remoteQuotaConfigService;

    @Override
    public void upsert() {
        Mockito.doAnswer(c -> {
            QuotaConfigDTO configDTO = c.getArgument(0);
            Boolean isReturnConfig = c.getArgument(1);
            quotaConfigDTOList.add(configDTO);
            return null;
        }).when(remoteQuotaConfigService).upsert(any(QuotaConfigDTO.class), anyBoolean());
    }
}
