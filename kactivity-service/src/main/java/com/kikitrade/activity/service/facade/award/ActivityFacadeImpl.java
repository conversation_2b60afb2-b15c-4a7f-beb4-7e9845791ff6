package com.kikitrade.activity.service.facade.award;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.aliyun.odps.Instance;
import com.google.protobuf.ProtocolStringList;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryConfig;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.param.ActivityBatchParam;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.facade.award.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.autoreward.AutoRewardService;
import com.kikitrade.activity.service.business.*;
import com.kikitrade.activity.service.common.mapper.ActivityBatchMapStruct;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.service.config.KactivitySqlConfig;
import com.kikitrade.activity.service.facade.domain.LotteryFacadeDTO;
import com.kikitrade.activity.service.facade.domain.LotteryItemFacadeDTO;
import com.kikitrade.activity.service.importing.source.CsvSourceService;
import com.kikitrade.activity.service.importing.source.domain.ImportSourceDTO;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.kcustomer.api.model.credential.OssObjectDTO;
import com.kikitrade.kcustomer.api.model.credential.SignOssUrlRequest;
import com.kikitrade.kcustomer.api.service.RemoteCredentialService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.text.NumberFormat;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ActivityFacadeImpl extends ActivityFacadeGrpc.ActivityFacadeImplBase {

    @Resource
    private ActivityEntityService activityEntityService;
    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ActivityBatchMapStruct activityBatchMapStruct;
    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;
    @Resource
    private CsvService rewardCsvService;
    @Resource
    private List<CsvSourceService> csvSourceService;
    @DubboReference
    private RemoteCredentialService remoteCredentialService;
    @Resource
    private ActivityLotteryDetailConfigService activityLotteryDetailConfigService;
    @Resource
    private AutoRewardService autoRewardService;

    /**
     * <pre>
     * *
     * 保存或修改活动
     * </pre>
     */
    public void saveOrUpdateActivity(com.kikitrade.activity.facade.award.ActivityDTO request,
                                     io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityResponse> responseObserver) {
        try {
            Result<String> result;
            log.info("saveOrUpdateActivity request:{}", request);

            if (StringUtils.isBlank(request.getId())) {
                result = activityEntityService.save(request, ActivityConstant.ActivitySourceEnum.OPERATE, null, null);
            } else {
                result = activityEntityService.updateCheckType(request);
            }
            log.info("ActivityFacadeImpl saveOrUpdateActivity result:{}", result);
            responseObserver.onNext(
                    ActivityResponse.newBuilder()
                            .setSuccess(result.isSuccess())
                            .setMessage(result.getMessage())
                            .setId(result.isSuccess() ? result.getData() : "")
                            .build());
            responseObserver.onCompleted();
        } catch (Exception ex) {
            log.error("ActivityFacade exception", ex);
            responseObserver.onError(ex);
        }
    }


    /**
     * <pre>
     * *
     * 新增或修改批次
     * </pre>
     */
    public void saveOrUpdateBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request,
                                  io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse> responseObserver) {
        try {
            Result<ActivityBatch> result = activityBatchNewService.saveOrUpdate(request);
            if (result.isSuccess()) {
                responseObserver.onNext(ActivityBatchResponse.newBuilder()
                        .setSuccess(result.isSuccess())
                        .setMessage(result.getMessage())
                        .setActivityBatch(activityBatchMapStruct.serviceToRpc(result.getData()))
                        .build());
            } else {
                responseObserver.onNext(ActivityBatchResponse.newBuilder()
                        .setSuccess(result.isSuccess())
                        .setMessage(result.getMessage())
                        .build());
            }

            responseObserver.onCompleted();
        } catch (Exception ex) {
            log.error("ActivityFacadeImpl saveOrUpdateBatch error", ex);
            responseObserver.onError(ex);
        }
    }

    /**
     * <pre>
     * *
     * 删除批次
     * </pre>
     */
    public void deleteBatch(com.kikitrade.activity.facade.award.ActivityBatchDTO request,
                            io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchResponse> responseObserver) {
        Result<String> result = activityBatchNewService.deleteBatch(request.getId());
        responseObserver.onNext(ActivityBatchResponse.newBuilder().setSuccess(result.isSuccess()).setMessage(result.getMessage()).build());
        responseObserver.onCompleted();
    }

    /**
     * <pre>
     * *
     * 批次列表查询
     * </pre>
     */
    public void queryBatchForList(com.kikitrade.activity.facade.award.ActivityBatchRequest request,
                                  io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatchListResponse> responseObserver) {
        ActivityBatchParam builder = ActivityBatchParam.builder()
                .batchId(request.getId())
                .batchName(request.getBatchName())
                .rewardStatus(request.getStatus().name())
                .activityId(request.getActivityId())
                .build();
        try {
            log.info("queryBatchForList request :{}", JSON.toJSONString(builder));
            PageResult batchList = activityBatchNewService.findForPage(builder, request.getPageNo(), request.getPageSize());
            List list = activityBatchMapStruct.serviceToRpcList(batchList.getRows());
            responseObserver.onNext(ActivityBatchListResponse.newBuilder().setSuccess(true).setMessage("success").addAllActivityBatch(list).build());
        } catch (Exception ex) {
            log.error("queryBatchForList ex", ex);
            responseObserver.onError(ex);
        }
        responseObserver.onCompleted();
    }

    /**
     * <pre>
     * *
     * 批次详情接口
     * </pre>
     */
    public void queryDetail(com.kikitrade.activity.facade.award.ActivityBatchDetailRequest request,
                            io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ActivityBatch> responseObserver) {
        try {
            log.info("queryDetail request:{}", request);
            ActivityBatch batch = activityBatchNewService.findByBatchId(request.getId());
            if(batch == null){
                responseObserver.onNext(com.kikitrade.activity.facade.award.ActivityBatch.newBuilder().build());
                responseObserver.onCompleted();
                return;
            }
            log.info("ActivityFacadeImpl batch:{}", batch);
            List<String> urls = new ArrayList<>();
            if (StringUtils.isNotBlank(batch.getOssUrl())) {
                urls.add(batch.getOssUrl());
            }
            if (StringUtils.isNotBlank(batch.getSourceOssUrl())) {
                urls.add(batch.getSourceOssUrl());
            }
            Map<String, String> urlMap = signOssUrl(batch.getSaasId(), urls);
            batch.setOssUrl(urlMap.getOrDefault(batch.getOssUrl(), batch.getOssUrl()));
            batch.setSourceOssUrl(urlMap.getOrDefault(batch.getSourceOssUrl(), batch.getSourceOssUrl()));
            com.kikitrade.activity.facade.award.ActivityBatch activityBatch = activityBatchMapStruct.serviceToRpc(batch);
            log.info("queryDetail activityBatch:{}", activityBatch);
            responseObserver.onNext(com.kikitrade.activity.facade.award.ActivityBatch.newBuilder(activityBatch).build());
        } catch (Exception ex) {
            log.error("queryDetail ex", ex);
            responseObserver.onError(ex);
        }
        responseObserver.onCompleted();
    }

    /**
     * <pre>
     * *
     * 终审通过
     * </pre>
     */
    public void audit(com.kikitrade.activity.facade.award.AuditRequest request,
                      io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.AuditResponse> responseObserver) {
        Result<String> result = activityBatchNewService.audit(request.getId(), ActivityConstant.AuditTypeEnum.valueOf(request.getAuditType().name()));
        responseObserver.onNext(AuditResponse.newBuilder().setSuccess(result.isSuccess()).setMessage(result.getMessage()).build());
        responseObserver.onCompleted();
    }

    /**
     * <pre>
     * *
     * 查询奖例列表
     * </pre>
     */
    public void queryRewardList(com.kikitrade.activity.facade.award.AwardRequest request,
                                io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.AwardListResponse> responseObserver) {

    }

    /**
     * <pre>
     * *
     * 删除详情
     * </pre>
     */
    public void deleteReward(com.kikitrade.activity.facade.award.AwardDTO request,
                             io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.ModifyAwardResponse> responseObserver) {
        ProtocolStringList requestIdList = request.getIdList();
        if (CollectionUtils.isEmpty(requestIdList)) {
            return;
        }
        List<String> ids = new ArrayList<>();
        Map<String, String> failMap = new HashMap<>();
        Map<String, ActivityBatch> batchMap = new HashMap<>();
        ActivityBatch batch = null;
        for (String id : requestIdList) {
            String[] idArr = id.split("-");
            if (idArr.length != 3) {
                failMap.put(id, "非法格式id，请复制cvs中第一列进行删除");
                continue;
            }
            if (MapUtils.isEmpty(batchMap)) {
                batch = activityBatchNewService.findByBatchId(id.split("-")[0].trim());
                if (batch == null) {
                    failMap.put(id, "删除失败，批次id不存在");
                    continue;
                }
                batchMap.put(idArr[0].trim(), batch);
            }
            if (MapUtils.isNotEmpty(batchMap) && !batchMap.containsKey(idArr[0].trim())) {
                failMap.put(id, "删除失败，id不合法，不属于该批次");
                continue;
            }
            if (!ActivityConstant.BatchRewardStatusEnum.UNAUDITED.name().equals(batch.getRewardStatus())) {
                failMap.put(id, "删除失败，状态不符，批次未处于未审核状态");
                continue;
            }
            ids.add(id);
        }
        List<ModifyDetail> modifyResult = new ArrayList<>();
        //存在非法格式id，直接返回
        if (MapUtils.isNotEmpty(failMap)) {
            for (Map.Entry<String, String> entry : failMap.entrySet()) {
                modifyResult.add(ModifyDetail.newBuilder().setId(entry.getKey()).setMessage(entry.getValue()).build());
            }
            responseObserver.onNext(ModifyAwardResponse.newBuilder().addAllDetail(modifyResult).setSuccess(false).build());
            responseObserver.onCompleted();
        }

        //删除id
        Result<Map<String, String>> result = activityCustomRewardStoreBuilder.delete(ids);
        Map<String, String> data = result.getData();

        for (Map.Entry<String, String> entry : data.entrySet()) {
            modifyResult.add(ModifyDetail.newBuilder().setId(entry.getKey()).setMessage(entry.getValue()).build());
        }
        //删除成功，重新生成cvs文件
        if (result.isSuccess()) {
            ActivityRewardPageParam pageParam = new ActivityRewardPageParam();
            pageParam.setBatchId(batch.getBatchId());
            pageParam.setPageNo(0);
            pageParam.setActivityType(batch.getActivityType());
            rewardCsvService.write(String.format("%s-%s.%s", batch.getName(), batch.getBatchId(), "csv"), pageParam);
            responseObserver.onNext(ModifyAwardResponse.newBuilder().addAllDetail(modifyResult).setSuccess(result.isSuccess()).build());
        } else {
            responseObserver.onNext(ModifyAwardResponse.newBuilder().addAllDetail(modifyResult).setSuccess(result.isSuccess()).build());
        }
        responseObserver.onCompleted();
    }

    public void uploadFile(com.kikitrade.activity.facade.award.UploadRequest request,
                           io.grpc.stub.StreamObserver<com.kikitrade.activity.facade.award.UploadResponse> responseObserver) {
        ImportSourceDTO dto = new ImportSourceDTO();
        dto.setBatchId(request.getBatchId());

        ActivityBatch batch = activityBatchNewService.findByBatchId(request.getBatchId());
        ActivityEntity activityEntity = activityEntityService.findById(batch.getActivityId());

        Result<String> result = csvSourceService.stream().filter(service -> service.support(activityEntity.getType())).findFirst().get().importSource(request.getFileName(), dto);
        responseObserver.onNext(UploadResponse.newBuilder().setSuccess(result.isSuccess()).setMessage(result.getMessage()).build());
        responseObserver.onCompleted();
    }

    private Map<String, String> signOssUrl(String saasId, List<String> urls) {
        try {
            Map<String, String> map = new HashMap<>();
            if (urls.size() > 0) {
                List<OssObjectDTO> results = remoteCredentialService.signOssUrl(SignOssUrlRequest.builder()
                        .saasId(saasId)
                        .expirationInSecond((int) Duration.ofDays(1).getSeconds())
                        .urls(urls)
                        .build());
                map = results.stream().collect(Collectors.toMap(o -> o.getOriginUrl(), o -> o.getSignedUrl()));
            }
            log.info("signOssUrl urls:{}, map:{}", JSON.toJSONString(urls), map);
            return map;
        } catch (Exception e) {
            log.error("signOssUrl failed, urls:{}", JSON.toJSONString(urls), e);
        }
        return new HashMap<>();
    }

    /**
     * 保存奖池
     * @param request
     * @param responseObserver
     */
    @Override
    public void saveLottery(LotteryDTO request, StreamObserver<LotteryResponse> responseObserver) {

    }

    /**
     * 删除奖池
     * @param request
     * @param responseObserver
     */
    @Override
    public void deleteLottery(LotteryDeleteDTO request, StreamObserver<LotteryResponse> responseObserver) {

    }

    /**
     * 奖池列表
     * @param request
     * @param responseObserver
     */
    @Override
    public void lotteryList(LotteryRequest request, StreamObserver<LotteryListResponse> responseObserver) {

    }

    /**
     * <pre>
     * *
     * 奖池详情
     * </pre>
     * @param request
     * @param responseObserver
     */
    @Override
    public void lotteryDetail(LotteryDetailRequest request, StreamObserver<LotteryVO> responseObserver) {

    }

    @Override
    public void getConditionCodes(EmptyRequest request, StreamObserver<ConditionCode> responseObserver) {
        Set<String> values = KactivitySqlConfig.values();
        log.info("getConditionCodes values:{}", values);
        responseObserver.onNext(ConditionCode.newBuilder().addAllCode(values).build());
        responseObserver.onCompleted();
    }

    @Override
    public void getCondition(ConditionRequest request, StreamObserver<ConditionResponse> responseObserver) {
        log.info("getCondition:{}", request);
        KactivitySqlConfig.SqlTemplate sqlTemplate = KactivitySqlConfig.getValue(request.getCode());
        List<ConditionVO> conditionVOList = new ArrayList<>();
        List<KactivitySqlConfig.Condition> conditions = sqlTemplate.getConditions();
        for(KactivitySqlConfig.Condition condition : conditions){
            conditionVOList.add(ConditionVO.newBuilder()
                    .setName(condition.getName())
                    .setAlisa(condition.getAlias())
                    .setFilter(condition.getFilter())
                    .build());
        }
        ConditionResponse response = ConditionResponse.newBuilder()
                .addAllCondition(conditionVOList)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void importData(ImportDataRequest request, StreamObserver<ImportDataResponse> responseObserver) {
        try{
            log.info("importData request:{},{}", request.getId(), request.getBatchId());
            ActivityEntity activityEntity = activityEntityService.findById(request.getId());
            Instance instance = autoRewardService.executeTradeRecord(activityEntity, request.getBatchId());
            ActivityBatch batch = activityBatchNewService.findByBatchId(request.getBatchId());
            batch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.ODPS_RUNNING.name());
            batch.setInstance(instance.getId());
            activityBatchNewService.updateBatch(batch);
            responseObserver.onNext(ImportDataResponse.newBuilder()
                    .setSuccess(true)
                    .setMessage("任务已提交，数据正在导入中").build());
        }catch (Exception ex){
            log.error("importData exception, batchId:{}",request.getBatchId(), ex);
            responseObserver.onError(ex);
        }
        responseObserver.onCompleted();
    }
}