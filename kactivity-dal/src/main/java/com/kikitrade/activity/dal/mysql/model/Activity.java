
package com.kikitrade.activity.dal.mysql.model;

import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.kseq.api.model.SeqRule;
import com.kikitrade.kseq.api.model.SeqRuleBuilder;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/7/11 17:07
 */
@Data
@Table(name = "activity")
public class Activity implements Serializable {

    @Id
    //活动ID（主键）
    @GeneratedValue(generator = "JDBC")
    @Column(name = "id", unique = true, nullable = false)
    private Integer id;  //id

    /**
     * {@link ActivityType#getCode()}
     */
    @Column(name = "type", nullable = false)
    private Integer type;

    //活动名称
    @Column(name = "name", nullable = false)
    private String name;


    //活动链接
    @Column(name = "url")
    private String url;

    //活动详情
    @Column(name = "content")
    private String content;

    @Transient
    private String locale;

    //开始时间
    @Column(name = "start_time", nullable = false)
    private Date start_time;

    //结束时间
    @Column(name = "end_time", nullable = false)
    private Date end_time;

    //报名是否需要审核
    @Column(name = "is_need_audit", nullable = false)
    private Integer is_need_audit;


    //置顶顺序
    @Column(name = "top_order")
    private Integer top_order;

    //执行类型
    //0-自动
    //1-手动
    //2-定时

    @Column(name = "execute_type", nullable = false)
    private Integer execute_type;

    //是否发送消息
    //0-不发送
    //1-发送
    @Column(name = "is_push", nullable = false)
    private Integer is_push;

    //活动状态
    //0-新建
    //1-已发布
    //2-进行中
    //3-已结束
    //4-暂停
    //5-失效
    @Column(name = "status", nullable = false)
    private Integer status;

    //参加次数
    @Column(name = "apply_times", nullable = false)
    private Integer apply_times;


    //活动发布者用户ID
    @Column(name = "publish_user_id", nullable = false)
    private String publish_user_id;

    /**
     * SassId
     */
    @Column(name="saas_id")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String saas_id;

    /**
     * 创建时间
     */
    @Column(name="created")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    private Date created;
    /**
     * 修改时间
     */
    @Column(name="modified")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    private Date modified;

    @Transient
    private List<ActivityActionMap> actionConfig;

    @Transient
    private List<ActivityRuleMap> ruleConfig;

    @Transient
    private List<ActivityContents> activityContents;

    @Transient
    private String business_id;
    @Transient
    private String checkResult;

    @Transient
    private Integer batchStatus;
}
