package com.kikitrade.activity.service.reward.impl;

import cn.hutool.core.util.URLUtil;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @desc
 * @date 2024/1/11 14:16
 */
@Slf4j
@Service("rewardGameItemTccService")
public class RewardGameItemTccServiceImpl extends AbstractRewardTccService implements RewardTccService {

    @Resource
    private ThreePlatformProperties threePlatformProperties;

    /**
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public void tryReward(RewardRequest request) throws Exception {
        Map<String, Object> param = new HashMap<>();
        param.put("verifyId", request.getGoodsId() + "num" + request.getAmount().intValue());
        param.put("verifyAddress", request.getAddress());
        String query = URLUtil.buildQuery(param, Charset.defaultCharset());

        OkHttpClient client = new OkHttpClient();
        Request httpRequest = new Request.Builder()
                .url(String.format("%s?%s",threePlatformProperties.getGame().getGameItemUrl(), query))
                .build();
        try {
            Response response = client.newCall(httpRequest).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("JoinGameRequest:{}", jsonObject);
            JSONObject data = jsonObject.getJSONObject("data");
            boolean result = !data.getBoolean("result");
            if(!result){
                throw new ActivityException(ActivityResponseCode.REWARD_FAIL);
            }
        } catch (Exception e) {
            log.error("JoinGameRequest error:{}", request, e);
            throw new ActivityException(ActivityResponseCode.REWARD_FAIL);
        }
    }
}
