package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/8/21 21:37
 */
@Getter
@Setter
@Table(name = "activity_batch_info")
public class ActivityBatchInfo extends BaseModel {

    //创建时间
    @Column(name = "create_time")
    private Date create_time;

    //更新时间
    @Column(name = "update_time")
    private Date update_time;


    //交易时间(yyyyMMdd)
    @Column(name = "tran_date")
    private String tran_date;

    //活动ID
    @Column(name = "activity_id")
    private Integer activity_id;

    //批次ID
    @Column(name = "batch_id")
    private String batch_id;

    //批次状态 0-已登记 1-处理中 2-已完成
    @Column(name = "status")
    private Integer status;

    //备注
    @Column(name = "remark")
    private String remark;


}
