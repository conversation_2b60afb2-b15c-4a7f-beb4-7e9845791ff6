package com.kikitrade.activity.service.engine.process;

import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.tablestore.builder.ActivityRecordsBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityRecords;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.ActivityMessage;
import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.activity.service.business.ActivityCommonService;
import com.kikitrade.activity.service.common.SpringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Arrays;


public class ActivityBatchProcessThread implements Runnable {

    private static Log log = LogFactory.getLog(ActivityBatchProcessThread.class);
    private String business_id = null;
    private Activity activity = null;
    private ActivityCommonService activityCommonService = (ActivityCommonService) SpringUtil.getBean(ActivityCommonService.class);
    private ActivityRecordsBuilder activityRecordsAccessorBuilder = (ActivityRecordsBuilder) SpringUtil.getBean(ActivityRecordsBuilder.class);

    public ActivityBatchProcessThread(String business_id, Activity activity) {

        this.business_id = business_id;
        this.activity = activity;

    }

    @Override
    public void run() {
        log.info("ActivityBatchProcessThread start~~~~~~~~~~~~~~~");
        try {
            ActivityRecords activityRecords = activityRecordsAccessorBuilder.findByBusinessId(this.activity.getExecute_type(), this.business_id, this.activity.getId(), ActivityConstant.RecordStatus.RECORDED.getCode());
            if (activityRecords != null && this.activity != null) {
                ActivityMessage activityMessage = new ActivityMessage();
                activityMessage.setBusinessId(activityRecords.getBusiness_id());
                activityMessage.setCustomerId(activityRecords.getCustomer_id());
                activityMessage.setSaasId(activityRecords.getSaasId());
                activityMessage.setParams(activityRecords.getParams());
                activityMessage.setEvent(ActivityType.getType(activity.getType()).getEvent().name());

                // 需要把checkRule的时候计算的奖励信息填充到ActivityMessage中
                if (StringUtils.isNotBlank(activityRecords.getRewardCurrency()) && activityRecords.getRewardAmount() != null) {
                    activityMessage.putRewardResult(this.activity.getId(), activityRecords.getRewardCurrency(), activityRecords.getRewardAmount(),
                            activityRecords.getExchangeCurrency(), activityRecords.getCustomer_id());
                }

                //do action
                activityCommonService.doActions(activityMessage, Arrays.asList(this.activity));
            }

        } catch (Exception e) {
            log.error("ActivityBatchProcessThread doActions process fail, error msg is [{}]", e);
            try {
                activityRecordsAccessorBuilder.updateStatus(this.business_id, this.activity.getId(), this.activity.getExecute_type(), ActivityConstant.RecordStatus.FAILED.getCode(), ActivityConstant.MsgStatus.ACTION_FAILED.getCodeAndDesc());
            } catch (Exception e1) {
                log.error("ActivityBatchProcessThread update status fail, error msg is [{}]", e);
            }

        }
        log.info("ActivityBatchProcessThread end~~~~~~~~~~~~~~~");
    }


}
