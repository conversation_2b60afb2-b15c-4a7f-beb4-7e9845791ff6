package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "activity_batch_roster")
public class ActivityBatchRewardRoster implements Serializable {

    public static String getTableName(){
        return "activity_batch_roster";
    }

    public static String getTunnelName(){
        return "activity_batch_roster_tunnel";
    }

    //多元索引
    public static final String INDEX_ACTIVITY_BATCH_ROSTER_DEFAULT_INDEX = "idx_batch_roster_index";
    //二级索引
    public static final String INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX = "idx_batch_roster_by_status_v4";

    @PartitionKey(name = "batch_id")
    @SearchIndex(name = INDEX_ACTIVITY_BATCH_ROSTER_DEFAULT_INDEX, column = "batch_id")
    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, pkColumn = "batch_id", pkValue = 1)
    private String batchId;

    @PartitionKey(name = "customer_id", value = 1)
    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, pkColumn = "customer_id", pkValue = 2)
    private String customerId;

    @PartitionKey(name = "seq", value = 2)
    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, pkColumn = "seq", pkValue = 3)
    private String seq;

    @Column(name = "status", isDefined = true)
    @SearchIndex(name = INDEX_ACTIVITY_BATCH_ROSTER_DEFAULT_INDEX, column = "status")
    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, pkColumn = "status")
    private String status;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "user_name")
    @Column(name = "user_name", isDefined = true)
    private String userName;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "phone")
    @Column(name = "phone", isDefined = true)
    private String phone;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "email")
    @Column(name = "email", isDefined = true)
    private String email;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "created")
    @Column(name = "created", isDefined = true, type = Column.Type.INTEGER)
    private Date created;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "modified")
    @Column(name = "modified", isDefined = true, type = Column.Type.INTEGER)
    private Date modified;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "amount")
    @Column(name = "amount", isDefined = true)
    private String amount;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "currency")
    @Column(name = "currency", isDefined = true)
    private String currency;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "reward_type")
    @Column(name = "reward_type", isDefined = true)
    private String rewardType;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "scope")
    @Column(name = "scope", isDefined = true)
    private String scope;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "vip_level")
    @Column(name = "vip_level", isDefined = true)
    private String vipLevel;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "side")
    @Column(name = "side", isDefined = true)
    private String side;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "refer_id")
    @Column(name = "refer_id", isDefined = true)
    private String referId;

    @Index(name = INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, dfColumn = "nick_name")
    @Column(name = "nick_name", isDefined = true)
    private String nickName;
}
