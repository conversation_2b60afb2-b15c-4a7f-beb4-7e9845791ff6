package com.kikitrade.activity.dotest;

import com.aliyun.openservices.ons.api.ConsumeContext;
import com.kikitrade.activity.model.util.TimeUtil;
import lombok.SneakyThrows;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.*;

public class SimpleDateFormatDemo {
    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static void main(String[] args) throws InterruptedException {
        long time1 = 1683008184000L;
        Date date1 = new Date(time1);
        long time2 = 1680416184000L;
        Date date2 = new Date(time2);

//        Runnable runnable = () -> {
//            try {
//                Date date = sdf.parse("2023-06-02 08:52:10");
////                String str = TimeUtil.YYYYMMDDHHMMSS.format(date2);
////                String utcTime = TimeUtil.getUtcTime(new Date(), TimeUtil.YYYYMMDDHHMMSS);
//                //System.out.println("Thread: " + Thread.currentThread().getName() + ", result: " + str);
////                System.out.println("Thread: " + Thread.currentThread().getName() + ", result: " + utcTime);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        };

        // 启动多个线程并发执行，访问同一个 SimpleDateFormat 实例进行日期格式化和解析
//        for (int i = 0; i < 10; i++) {
//            new Thread(runnable).start();
//        }

        int len = 50;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch latch = new CountDownLatch(len);
        final CountDownLatch downLatch = new CountDownLatch(1);

        for (int i = 0; i < len; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        latch.countDown();
                        downLatch.await();
                        System.out.println(System.currentTimeMillis());
                        //String parse = TimeUtil.getDataStr(TimeUtil.parseUnittime(time1), TimeUtil.YYYYMMDDHHMMSS);
                        String parse = TimeUtil.getUtcTime(date1,TimeUtil.YYYYMMDDHHMMSS);
                        System.out.println("parse1: "+ parse);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    endLatch.countDown();
                }
            }).start();
        }

        try {
            latch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        downLatch.countDown();
        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
        try {
            endLatch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
//        SimpleDateFormatDemo simpleDateFormatDemo = new SimpleDateFormatDemo();
//        simpleDateFormatDemo.fun();
    }


    public void fun() throws InterruptedException {
        // 开始并发冻结
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        final CountDownLatch latch = new CountDownLatch(5);
        Runnable runnable0 = () -> {
            try {
                latch.countDown();
                latch.await();
                System.out.println("子线程0" + Thread.currentThread().getName() + "开始执行");
                System.out.println(System.currentTimeMillis());
                String utcTime = TimeUtil.getUtcTime(new Date(), TimeUtil.YYYYMMDDHHMMSS);
                Date date = TimeUtil.parse("2023-06-02 08:52:10");
//                Date date = sdf.parse("2023-06-02 08:52:10");
                System.out.println("Thread: " + Thread.currentThread().getName() + ", result: " + date);
                System.out.println("子线程0" + Thread.currentThread().getName() + "执行结束");
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        Future future0 = executorService.submit(runnable0);

        Runnable runnable1 = () -> {
            try {
                latch.countDown();
                latch.await();
                System.out.println("子线程0" + Thread.currentThread().getName() + "开始执行");
                System.out.println(System.currentTimeMillis());
                String utcTime = TimeUtil.getUtcTime(new Date(), TimeUtil.YYYYMMDDHHMMSS);
                Date date = TimeUtil.parse("2023-06-02 08:52:10");
//                Date date = sdf.parse("2023-06-02 08:52:10");
                System.out.println("Thread: " + Thread.currentThread().getName() + ", result: " + date);
                System.out.println("子线程0" + Thread.currentThread().getName() + "执行结束");
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        Future future1 = executorService.submit(runnable1);


        Runnable runnable2 = () -> {
            try {
                latch.countDown();
                latch.await();
                System.out.println("子线程0" + Thread.currentThread().getName() + "开始执行");
                System.out.println(System.currentTimeMillis());
                String utcTime = TimeUtil.getUtcTime(new Date(), TimeUtil.YYYYMMDDHHMMSS);
                Date date = TimeUtil.parse("2023-06-02 08:52:10");
//                Date date = sdf.parse("2023-06-02 08:52:10");
                System.out.println("Thread: " + Thread.currentThread().getName() + ", result: " + date);
                System.out.println("子线程0" + Thread.currentThread().getName() + "执行结束");
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        Future future2 = executorService.submit(runnable2);

        Runnable runnable3 = () -> {
            try {
                latch.countDown();
                latch.await();
                System.out.println("子线程0" + Thread.currentThread().getName() + "开始执行");
                System.out.println(System.currentTimeMillis());
                String utcTime = TimeUtil.getUtcTime(new Date(), TimeUtil.YYYYMMDDHHMMSS);
                Date date = TimeUtil.parse("2023-06-02 08:52:10");
//                Date date = sdf.parse("2023-06-02 08:52:10");
                System.out.println("Thread: " + Thread.currentThread().getName() + ", result: " + date);
                System.out.println("子线程0" + Thread.currentThread().getName() + "执行结束");
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        Future future3 = executorService.submit(runnable3);

        Runnable runnable4 = () -> {
            try {
                latch.countDown();
                latch.await();
                System.out.println("子线程0" + Thread.currentThread().getName() + "开始执行");
                System.out.println(System.currentTimeMillis());
                String utcTime = TimeUtil.getUtcTime(new Date(), TimeUtil.YYYYMMDDHHMMSS);
                Date date = TimeUtil.parse("2023-06-02 08:52:10");
//                Date date = sdf.parse("2023-06-02 08:52:10");
                System.out.println("Thread: " + Thread.currentThread().getName() + ", result: " + date);
                System.out.println("子线程0" + Thread.currentThread().getName() + "执行结束");
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        Future future4 = executorService.submit(runnable4);

        System.out.println("主线程" + Thread.currentThread().getName() + "等待子线程执行完成");
        //阻塞当前线程，直到计数器的值为0
        latch.await();
        System.out.println("主线程" + Thread.currentThread().getName() + "开始执行排序...");

        executorService.shutdown();
        try {
            while (future0.get() != null || future1.get() != null || future2.get() != null || future3.get() != null || future4.get() != null) {//如果Future's get返回null，任务完成
                System.out.println("-------------任务执行中-----------");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        Thread.sleep(1000);


//            AccountOperateResponse response = accountKernelOperationService.operate(accountOperateFreezeRequest);

    }
}
