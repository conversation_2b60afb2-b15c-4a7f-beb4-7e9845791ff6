package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/9 10:49
 */
@Data
@Table(name = "claim_item")
public class ClaimItem implements Serializable {

    public static final String SEARCH_CUSTOMER = "search_customer";

    @PartitionKey(name = "business_type")
    @SearchIndex(name = SEARCH_CUSTOMER, column = "business_type")
    private String businessType;
    @PartitionKey(name = "created", value = 1)
    @SearchIndex(name = SEARCH_CUSTOMER, column = "created")
    private String created;
    @PartitionKey(name = "code", value = 2)
    @SearchIndex(name = SEARCH_CUSTOMER, column = "code")
    private String code;
    @Column(name = "customer_id")
    @SearchIndex(name = SEARCH_CUSTOMER, column = "customer_id")
    private String customerId;

    /**
     * 兑换时间
     */
    @Column(name = "ts", type = Column.Type.INTEGER)
    private Long ts;

    @Column(name = "saas_id")
    private String saasId;

    /**
     * 兑换的奖品
     */
    @Column(name = "award")
    private String award;
}