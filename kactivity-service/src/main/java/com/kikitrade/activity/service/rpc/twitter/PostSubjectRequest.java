package com.kikitrade.activity.service.rpc.twitter;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.twitter.clientlib.ApiException;
import com.twitter.clientlib.model.Tweet;
import com.twitter.clientlib.model.TweetCreateRequest;
import com.twitter.clientlib.model.TweetCreateResponseData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.SpelEvaluationException;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.net.URLDecoder;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/4 14:17
 */
@Data
@Slf4j
public class PostSubjectRequest extends AccessToken {

    private TwitterProperties twitterProperties;
    private String targetUserName;
    private String subject;
    private String keyword;
    private OffsetDateTime startTime;
    private OffsetDateTime endTime;
    private Integer maxResult;

    public PostSubjectRequest(TwitterProperties twitterProperties, AccessToken accessToken){
        super(accessToken);
        this.twitterProperties = twitterProperties;
    }

    public PostSubjectRequest build(String userName, String keyword, OffsetDateTime startTime, OffsetDateTime endTime, Integer maxResult){
        this.targetUserName = userName;
        this.keyword = keyword;
        this.startTime = startTime;
        this.endTime = endTime;
        this.maxResult = maxResult;
        return this;
    }

    public List<Tweet> execute() throws ActivityException{
        try{
            String query = String.format("(%s) from:%s", keyword, targetUserName);
            List<Tweet> data = this.getApi(this.accessToken, this.refreshToken, this.twitterProperties).tweets()
                    .tweetsRecentSearch(query)
                    .startTime(startTime)
                    .endTime(endTime)
                    .maxResults(maxResult <= 10 ? 10 : (maxResult >= 100 ? 100 : maxResult)).execute().getData();
            log.info("[twitter] post result:{},{}", targetUserName, data);
            return data;
        }catch (ApiException ax){
            log.error("[twitter] post error{},{}", this.keyword ,ax);
            if(ax.getCode() == 88){
                throw new ActivityException(ActivityResponseCode.ACTIVITY_HOT);
            }
            return null;
        }catch (Exception ex){
            log.error("[twitter] post error{},{}", this.keyword ,ex);
            return null;
        }
    }
}
