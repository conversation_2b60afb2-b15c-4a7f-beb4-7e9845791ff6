package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.LuckFortuneRefundItem;
import com.kikitrade.framework.common.model.PageResult;

import java.util.List;

/**
 *
 */
public interface LuckFortuneRefundItemBuild {

    String getTableName();

    /**
     * 插入一个退款的红包
     * @param luckFortuneRefundItem
     * @return
     */
    boolean insert(LuckFortuneRefundItem luckFortuneRefundItem);

    /**
     * 更新红包到退款成功
     * @param luckFortuneRefundItem
     * @return
     */
    boolean update(LuckFortuneRefundItem luckFortuneRefundItem);

    /**
     * 根据状态查询退款记录
     * @param status
     * @return
     */
    PageResult findByStatus(Integer status, int offset, int limit);

    /**
     * 查询退款记录
     * @param originBusinessId
     * @param businessId
     * @return
     */
    LuckFortuneRefundItem findObject(String originBusinessId, String businessId);
}
