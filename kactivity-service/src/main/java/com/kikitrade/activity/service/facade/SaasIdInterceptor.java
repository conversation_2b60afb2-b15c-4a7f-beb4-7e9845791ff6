package com.kikitrade.activity.service.facade;

import com.google.common.base.Strings;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import io.grpc.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class SaasIdInterceptor implements ServerInterceptor {

    @Autowired
    private KactivityProperties kactivityProperties;

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(ServerCall<ReqT, RespT> serverCall, Metadata metadata, ServerCallHandler<ReqT, RespT> serverCallHandler) {
        String saasId = metadata.get(Constants.SAAS_ID_MD_KEY);
        Context ctx;
        if (Strings.isNullOrEmpty(saasId)) {
            ctx = Context.current().withValue(Constants.SAAS_ID_KEY, kactivityProperties.getSaasId());
        } else {
            ctx = Context.current().withValue(Constants.SAAS_ID_KEY, saasId);
        }
        return Contexts.interceptCall(ctx, serverCall, metadata, serverCallHandler);
    }
}
