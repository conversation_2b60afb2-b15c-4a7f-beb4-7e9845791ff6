package com.kikitrade.activity.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "customer_reward")
public class CustomerReward implements Serializable {

    public static final String INDEX_CUSTOMER_REWARD_DEFAULT_INDEX = "idx_customer_reward_default_index";

    private static final long serialVersionUID = 2102582570892680312L;

    @PartitionKey(name = "customer_id")
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "customer_id")
    private String customerId;

    @Column(isDefined = true)
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "userName")
    private String userName;

    @PartitionKey(value = 1, name = "activity_id")
    private Integer activityId;

    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "reward_time", fieldType = FieldType.LONG)
    @PartitionKey(value = 2, name = "reward_time")
    private Date rewardTime;

    @Column(isDefined = true)
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "invitee")
    private String invitee;

    @Column(isDefined = true)
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "inviteeUserName")
    private String inviteeUserName;
    /**
     * {@link ActivityType}
     */
    @Column(isDefined = true)
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "type", fieldType = FieldType.LONG)
    private Integer type;

    @Column(isDefined = true)
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "currency")
    private String currency;

    @Column(isDefined = true, type = Column.Type.DOUBLE)
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "amount", fieldType = FieldType.DOUBLE)
    private BigDecimal amount;

    @Column(isDefined = true, type = Column.Type.BOOLEAN)
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "autoExchange", fieldType = FieldType.BOOLEAN)
    private boolean autoExchange = false;

    @Column(isDefined = true)
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "secondStep")
    private String secondStep;

    @Column(isDefined = true)
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "quoteCurrency")
    private String quoteCurrency;

    @Column(isDefined = true, type = Column.Type.DOUBLE)
    @SearchIndex(name = INDEX_CUSTOMER_REWARD_DEFAULT_INDEX, column = "quoteQuantity", fieldType = FieldType.DOUBLE)
    private BigDecimal quoteQuantity;

    @Transient
    private String remark;
    @Transient
    private Date created = new Date();
    @Transient
    private Date updated = new Date();
    @Transient
    private String businessId;
    @Transient
    private String orderNum;

    @Transient
    private String symbol;

    @Transient
    private String transactionId;
}
