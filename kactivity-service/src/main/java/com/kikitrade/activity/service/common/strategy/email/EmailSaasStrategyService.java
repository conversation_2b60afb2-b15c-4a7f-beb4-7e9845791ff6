package com.kikitrade.activity.service.common.strategy.email;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;

/**
 * <AUTHOR>
 * @date 2025/7/22 18:10
 * @description: Email平台策略服务接口
 */
public interface EmailSaasStrategyService {
    String strategy();
    void execute(OpenStrategyAuthRequest request) throws ActivityException;
}
