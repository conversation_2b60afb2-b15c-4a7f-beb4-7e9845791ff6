package com.kikitrade.activity.service.rpc.twitter;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.twitter.clientlib.model.Expansions;
import com.twitter.clientlib.model.Get2UsersMeResponse;
import com.twitter.clientlib.model.Tweet;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/30 17:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class CurrentUserPostRequest extends AccessToken {
    private TwitterProperties twitterProperties;
    private String keyword;
    private OffsetDateTime startTime;
    private OffsetDateTime endTime;

    public CurrentUserPostRequest() {
    }

    public CurrentUserPostRequest(TwitterProperties twitterProperties, AccessToken accessToken) {
        super(accessToken);
        this.twitterProperties = twitterProperties;
    }

    public CurrentUserPostRequest build(String keyword, OffsetDateTime startTime, OffsetDateTime endTime) {
        this.keyword = keyword;
        this.startTime = startTime;
        this.endTime = endTime;
        return this;
    }

    public List<Tweet> execute() {
        try {
            Get2UsersMeResponse response = getApi(this.accessToken, this.refreshToken, this.twitterProperties)
                    .users()
                    .findMyUser()
                    .userFields(Set.of("created_at"))
                    .expansions(Set.of("most_recent_tweet_id"))
                    .tweetFields(Set.of("created_at"))
                    .execute();
            log.info("twitter current user newest post response:{}", response);

            List<Tweet> tweets = Optional.ofNullable(response)
                    .map(Get2UsersMeResponse::getIncludes)
                    .map(Expansions::getTweets).orElse(null);
            if (CollectionUtils.isNotEmpty(tweets)) {
                return tweets.stream()
                        .filter(x -> tweetCreatTimeCheck(x, startTime, endTime))
                        .filter(x -> tweetKeywordsCheck(x, keyword))
                        .toList();
            }
            log.warn("[twitter] get current user most_recent_tweet error:{}", response);
            return null;
        } catch (Exception ex) {
            log.error("twitter CurrentUserRequest error, properties:{}, accessToken:{}, refreshToken:{}", this.twitterProperties, this.accessToken, this.refreshToken, ex);
            return null;
        }
    }

    private Boolean tweetCreatTimeCheck(Tweet tweet, OffsetDateTime startTime, OffsetDateTime endTime) {
        OffsetDateTime creatTime = tweet.getCreatedAt();
        return creatTime != null && creatTime.isAfter(startTime) && creatTime.isBefore(endTime);
    }

    private Boolean tweetKeywordsCheck(Tweet tweet, String keywords) {
        String[] keywordList = keywords.split("\\s+");
        String tweetText = tweet.getText();
        for (String keyword : keywordList) {
            if (!tweetText.contains(keyword)) {
                return false;
            }
        }
        return true;
    }
}
