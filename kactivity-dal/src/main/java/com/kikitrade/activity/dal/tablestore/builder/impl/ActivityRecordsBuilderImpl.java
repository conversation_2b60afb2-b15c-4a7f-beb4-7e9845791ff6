package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.Direction;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.filter.ColumnValueFilter;
import com.alicloud.openservices.tablestore.model.filter.CompositeColumnValueFilter;
import com.alicloud.openservices.tablestore.model.filter.SingleColumnValueFilter;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.ActivityRecordsBuilder;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.dal.tablestore.model.ActivityRecords;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static com.kikitrade.activity.dal.tablestore.model.ActivityRecords.*;

/**
 * created by jacky. 2020/1/15 3:57 PM
 */
@Slf4j
@Repository
public class ActivityRecordsBuilderImpl extends WideColumnStoreBuilder<ActivityRecords> implements ActivityRecordsBuilder {

    @PostConstruct
    public void init() {
        init(ActivityRecords.class);
    }

    @Override
    public Boolean saveIfNotExist(ActivityRecords ars) {
        return putRow(ars, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public long countByBusinessId(Integer execute_type, String business_id, Integer activity_id) {

        String aggName = "count_agg_business_id" + UUID.randomUUID();

        SearchQuery query = SearchQuery.newBuilder()
                .query(QueryBuilders.bool()
                        .filter(QueryBuilders.term("execute_type", execute_type))
                        .filter(QueryBuilders.term("activity_id", activity_id))
                        .filter(QueryBuilders.term("business_id", business_id)))
                .addAggregation(AggregationBuilders.count(aggName, "business_id")).build();

        return searchCount(query, INDEX_COUNT, aggName);
    }

    @Override
    public long countByCustomerId(Integer execute_type, String customerId, Integer activity_id) {
        String aggName = "count_agg_customer_id" + UUID.randomUUID();
        SearchQuery query = SearchQuery.newBuilder()
                .query(QueryBuilders.bool()
                        .filter(QueryBuilders.term("execute_type", execute_type))
                        .filter(QueryBuilders.term("activity_id", activity_id))
                        .filter(QueryBuilders.term("customer_id", customerId)))
                .addAggregation(AggregationBuilders.count(aggName, "business_id")).build();

        return searchCount(query, INDEX_COUNT, aggName);
    }

    @Override
    public List<ActivityRecords> getByCustomerIdAndStatus(Integer execute_type, String customerId, Integer activity_id, ActivityConstant.RecordStatus[] includeStatus, int limit) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.fromString(customerId), PrimaryKeyValue.fromString(customerId)));
        queryList.add(new RangeQueryParameter("activity_id", PrimaryKeyValue.fromLong(activity_id), PrimaryKeyValue.fromLong(activity_id)));
        queryList.add(new RangeQueryParameter("execute_type", PrimaryKeyValue.fromLong(execute_type), PrimaryKeyValue.fromLong(execute_type)));
        queryList.add(new RangeQueryParameter("business_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));

        ColumnValueFilter filter = null;
        if (includeStatus.length > 1) {
            CompositeColumnValueFilter compositeColumnValueFilter = new CompositeColumnValueFilter(CompositeColumnValueFilter.LogicOperator.OR);
            for (ActivityConstant.RecordStatus recordStatus : includeStatus) {
                compositeColumnValueFilter.addFilter(new SingleColumnValueFilter("status", SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromLong(recordStatus.getCode())));
            }
            filter = compositeColumnValueFilter;
        } else if (includeStatus.length == 1){
            filter = new SingleColumnValueFilter("status", SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromLong(includeStatus[0].getCode()));
        }
        RangeResult<ActivityRecords> rangeResult = super.rangeQueryWithNextToken(INDEX_ACTIVITY_RECORDS_BY_CUSTOMER, queryList, filter, Direction.FORWARD, limit, null);
        return rangeResult.list;
    }

    @Override
    public List<ActivityRecords> getByCustomerIdAndStatusFromTime(Integer execute_type, String customerId, Integer activity_id, Date fromTime, ActivityConstant.RecordStatus[] includeStatus, int limit) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.fromString(customerId), PrimaryKeyValue.fromString(customerId)));
        queryList.add(new RangeQueryParameter("activity_id", PrimaryKeyValue.fromLong(activity_id), PrimaryKeyValue.fromLong(activity_id)));
        queryList.add(new RangeQueryParameter("execute_type", PrimaryKeyValue.fromLong(execute_type), PrimaryKeyValue.fromLong(execute_type)));
        queryList.add(new RangeQueryParameter("business_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));

        // 其他过滤条件
        CompositeColumnValueFilter filter = new CompositeColumnValueFilter(CompositeColumnValueFilter.LogicOperator.AND);
        // status
        if (includeStatus.length > 1) {
            CompositeColumnValueFilter statusFilter = new CompositeColumnValueFilter(CompositeColumnValueFilter.LogicOperator.OR);
            for (ActivityConstant.RecordStatus recordStatus : includeStatus) {
                statusFilter.addFilter(new SingleColumnValueFilter("status", SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromLong(recordStatus.getCode())));
            }
            filter.addFilter(statusFilter);
        } else if (includeStatus.length == 1){
            filter.addFilter(new SingleColumnValueFilter("status", SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromLong(includeStatus[0].getCode())));
        }

        // create_time
        filter.addFilter(new SingleColumnValueFilter("create_time", SingleColumnValueFilter.CompareOperator.GREATER_EQUAL, ColumnValue.fromLong(fromTime.getTime())));

        RangeResult<ActivityRecords> rangeResult = super.rangeQueryWithNextToken(INDEX_ACTIVITY_RECORDS_BY_CUSTOMER, queryList, filter, Direction.FORWARD, limit, null);
        return rangeResult.list;
    }

    @Override
    public long countByActivityType(Integer execute_type, Date deadline, Integer status) {
        String aggName = "count_agg_activity_type" + UUID.randomUUID();
        SearchQuery query = SearchQuery.newBuilder()
                .query(QueryBuilders.bool()
                        .filter(QueryBuilders.term("execute_type", execute_type))
                        .filter(QueryBuilders.term("status", status))
                        .filter(QueryBuilders.range("create_time")
                                .lessThanOrEqual(deadline.getTime())))
                .addAggregation(AggregationBuilders.count(aggName, "business_id")).build();
        return searchCount(query, INDEX_COUNT, aggName);
    }

    @Override
    public long countByActivityIdAndType(int activity_id, Integer execute_type, Date deadline, Integer status) {
        String aggName = "count_agg_activity_id_type" + UUID.randomUUID();
        SearchQuery query = SearchQuery.newBuilder()
                .query(QueryBuilders.bool()
                        .filter(QueryBuilders.term("execute_type", execute_type))
                        .filter(QueryBuilders.term("activity_id", activity_id))
                        .filter(QueryBuilders.term("status", status))
                        .filter(QueryBuilders.range("create_time")
                                .lessThanOrEqual(deadline.getTime()))
                )
                .addAggregation(AggregationBuilders.count(aggName, "business_id")).build();
        return searchCount(query, INDEX_COUNT, aggName);
    }

    @Override
    public List<ActivityRecords> findForDispatchById(int activity_id, Integer execute_type, Date startTime, Date deadline, Integer status, Integer limit) {
        BoolQuery q = QueryBuilders.bool()
                .filter(QueryBuilders.term("execute_type", execute_type))
                .filter(QueryBuilders.term("activity_id", activity_id))
                .filter(QueryBuilders.term("status", status))
                .filter(QueryBuilders.range("create_time")
                        .greaterThanOrEqual(startTime.getTime())
                        .lessThanOrEqual(deadline.getTime())
                ).build();
        return super.search(q, null, 0, limit, INDEX_COUNT);
    }

    @Override
    public List<ActivityRecords> findForDispatchByType(Integer execute_type, Date startTime, Date deadline, Integer status, Integer limit) {
        BoolQuery q = QueryBuilders.bool()
                .filter(QueryBuilders.term("execute_type", execute_type))
                .filter(QueryBuilders.term("status", status))
                .filter(QueryBuilders.range("create_time")
                        .greaterThanOrEqual(startTime.getTime())
                        .lessThanOrEqual(deadline.getTime())
                ).build();
        return super.search(q, null, 0, limit, INDEX_COUNT);
    }

    @Override
    public boolean updateStatus(String business_id, Integer activity_id, Integer execute_type, Integer status, String remark) {
        ActivityRecords update = new ActivityRecords();
        update.setBusiness_id(business_id);
        update.setActivity_id(activity_id);
        update.setExecute_type(execute_type);
        update.setStatus(status);
        update.setRemark(remark);
        if (status == ActivityConstant.RecordStatus.COMPLETED.getCode()) {
            update.setRewardTime(new Date());
        }
        return updateRow(update);
    }

    @Override
    public ActivityRecords findByBusinessId(Integer execute_type, String business_id, Integer activity_id, Integer status) {
        BoolQuery q = QueryBuilders.bool()
                .filter(QueryBuilders.term("execute_type", execute_type))
                .filter(QueryBuilders.term("business_id", business_id))
                .filter(QueryBuilders.term("activity_id", activity_id))
                .filter(QueryBuilders.term("status", status))
                .build();
        List<ActivityRecords> list = super.search(q, null, 0, 1, INDEX_EXECUTE_TYPE_STATUS);
        return !CollectionUtils.isEmpty(list) ? list.get(0) : null;
    }

    @Override
    public List<ActivityRecords> findByCustomerAndActivity(String customerId, Integer activityId, int offset, int limit) {
        BoolQuery q = QueryBuilders.bool()
                .filter(QueryBuilders.term("customer_id", customerId))
                .filter(QueryBuilders.term("activity_id", activityId))
                .build();
        return super.search(q, null, offset, limit, INDEX_ACTIVITY_CUSTOMER);
    }

}
