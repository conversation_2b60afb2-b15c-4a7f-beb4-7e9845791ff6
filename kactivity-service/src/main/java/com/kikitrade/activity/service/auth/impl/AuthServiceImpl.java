package com.kikitrade.activity.service.auth.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.auth.AuthService;
import com.kikitrade.activity.service.auth.strategy.*;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import com.kikitrade.activity.service.common.strategy.SaasStrategyFactory;
import com.kikitrade.activity.service.common.strategy.discord.DiscordSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.email.EmailSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.facebook.FacebookSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.google.GoogleSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.line.LineSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.telegram.TelegramSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.twitter.TwitterSaasStrategyService;
import com.kikitrade.activity.service.config.SaasConfig;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.model.OpenAuthRequest;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import com.kikitrade.activity.service.task.action.ActivityEventAction;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kevent.client.EventClient;
import com.kikitrade.kevent.common.model.EventDTO;
import com.kikitrade.knotify.api.NotifyResult;
import com.kikitrade.knotify.api.NotifyService;
import com.kikitrade.knotify.api.model.EmailMessageDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/11 14:46
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    @Resource
    private ThreePlatformProperties threePlatformProperties;
    @Resource
    private RedisService redisService;
    @Resource
    private RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    private SaasStrategyFactory saasStrategyFactory;
    @Resource
    private PlatformAuthStrategyFactory platformAuthStrategyFactory;
    @Resource
    private OkHttpClient okHttpClient;
    @DubboReference(check = false)
    public NotifyService notifyService;
    @Autowired(required = false)
    private EventClient eventClient;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    @Lazy
    private ActivityEventAction activityEventAction;


    /**
     * 社交平台授权认证
     * 使用策略模式处理不同平台的认证逻辑
     *
     * @param saasId SaaS ID
     * @param platform 平台名称
     * @param code 授权码
     * @param redirectUri 重定向URI
     * @param customerId 客户ID
     * @param twitterId 登陆用户的 twitterId（兼容性保留）
     * @param socialId 社交平台ID（用于Telegram等）
     * @param socialName 社交平台用户名（用于Telegram等）
     * @return Token
     * @throws ActivityException 认证异常
     */
    @Override
    public Token auth(String saasId, String platform, String code, String redirectUri, String customerId, String twitterId, String socialId, String socialName) throws ActivityException {
        log.info("[auth] 开始认证: platform={}, customerId={}, twitterId={}, socialId={}, socialName={}", platform, customerId, twitterId, socialId, socialName);

        // 获取对应平台的认证策略
        PlatformAuthStrategy strategy = platformAuthStrategyFactory.getStrategy(platform);
        if (strategy == null) {
            log.error("[auth] 不支持的平台: platform={}, customerId={}", platform, customerId);
            throw new ActivityException(ActivityResponseCode.AUTH_CODE_INVALID);
        }

        // 构建认证上下文
        AuthContext context = AuthContext.builder()
                .saasId(saasId)
                .platform(platform)
                .code(code)
                .redirectUri(redirectUri)
                .customerId(customerId)
                .twitterId(twitterId)
                .socialId(socialId)
                .socialName(socialName)
                .build();

        // 执行认证
        AuthResult authResult = strategy.authenticate(context);
        if (!authResult.isSuccess()) {
            log.error("[auth] 认证失败: platform={}, customerId={}, error={}",
                platform, customerId, authResult.getErrorMessage());
            throw new ActivityException(ActivityResponseCode.AUTH_CODE_INVALID);
        }

        Token token = authResult.getToken();
        SocialUserInfo socialUserInfo = authResult.getSocialUserInfo();
        // 获取创建时间
        String ctime = socialUserInfo.getCreatedAt() == null ?
            String.valueOf(OffsetDateTime.now().toEpochSecond()) :
            String.valueOf(socialUserInfo.getCreatedAt());

        // 获取SaaS配置
        SaasConfig saasConfig = SaasConfigLoader.getConfig(saasId);
        log.info("[auth] SaaS配置: {}", saasConfig);

        // 根据平台类型执行相应的策略
        executeStrategyForPlatform(platform, saasConfig, saasId, customerId,
            socialUserInfo.getUserId(), socialUserInfo.getUserName(), socialUserInfo.getEmail(), twitterId, ctime);

        // 存储令牌信息到Redis（仅对支持OAuth2的平台）
        if (strategy.supportsRefreshToken() && token.getAccessToken() != null) {
            storeTokenToRedis(saasId, customerId, platform, token, socialUserInfo, ctime);
        }

        // 绑定社交账号
        try {
            remoteCustomerBindService.bindSocial(saasId, customerId, platform,
                socialUserInfo.getUserId(), socialUserInfo.getUserName(), Long.parseLong(ctime));
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findByUid(saasId, customerId);//拿到cid去回调保存用户信息
            sendSaveBindInfoEvent(saasId, customerBindDTO.getCid(), platform, saasConfig, socialUserInfo);
            doTask(customerId, "connect_" + platform.toLowerCase() ,saasId);
        } catch (Exception e) {
            log.error("[auth] 绑定社交账号失败", e);
        }

        log.info("[auth] 认证成功: platform={}, customerId={}, socialUserId={}",
            platform, customerId, socialUserInfo.getUserId());
        return token;
    }

    /**
     * 根据平台类型执行相应的策略
     */
    private void executeStrategyForPlatform(String platform, SaasConfig saasConfig, String saasId,
                                          String customerId, String authId, String authHandleName, String authEmail,
                                          String loginTwitterId, String createdAt) throws ActivityException {
        OpenStrategyAuthRequest strategyRequest = OpenStrategyAuthRequest.builder()
                .saasId(saasId)
                .customerId(customerId)
                .authId(authId)
                .authHandleName(authHandleName)
                .authEmail(authEmail)
                .loginTwitterId(loginTwitterId)
                .createdAt(createdAt)
                .build();

        switch (platform.toLowerCase()) {
            case "twitter" -> {
                strategyRequest.setTwitterClientId(threePlatformProperties.getTwitter().getClientId().get(saasId));
                strategyRequest.setAuthVersion(threePlatformProperties.getTwitter().getAuthVersion().get(saasId));
                TwitterSaasStrategyService twitterService = saasStrategyFactory.getTwitterService(saasConfig.getTwitterStrategy());
                if (twitterService != null) {
                    twitterService.execute(strategyRequest);
                }
            }
            case "discord" -> {
                DiscordSaasStrategyService discordService = saasStrategyFactory.getDiscordService(saasConfig.getDiscordStrategy());
                if (discordService != null) {
                    discordService.execute(strategyRequest);
                }
            }
            case "google" -> {
                GoogleSaasStrategyService googleService = saasStrategyFactory.getGoogleService(saasConfig.getGoogleStrategy());
                if (googleService != null) {
                    googleService.execute(strategyRequest);
                }
            }
            case "facebook" -> {
                FacebookSaasStrategyService facebookService = saasStrategyFactory.getFacebookService(saasConfig.getFacebookStrategy());
                if (facebookService != null) {
                    facebookService.execute(strategyRequest);
                }
            }
            case "line" -> {
                LineSaasStrategyService lineService = saasStrategyFactory.getLineService(saasConfig.getLineStrategy());
                if (lineService != null) {
                    lineService.execute(strategyRequest);
                }
            }
            case "email" -> {
                EmailSaasStrategyService emailService = saasStrategyFactory.getEmailService(saasConfig.getEmailStrategy());
                if (emailService != null) {
                    emailService.execute(strategyRequest);
                }
            }
            case "telegram" -> {
                TelegramSaasStrategyService telegramService = saasStrategyFactory.getTelegramService(saasConfig.getTgStrategy());
                if (telegramService != null) {
                    telegramService.execute(strategyRequest);
                }
            }
            default -> log.warn("[auth] 未知平台类型: {}", platform);
        }
    }

    @Override
    public Token getAuthToken(String saasId, String customerId, String platform) {
        String key = buildTokenKey(saasId, customerId, platform);
        if (key == null) {
            log.warn("[getAuthToken] 不支持的平台: {}", platform);
            return null;
        }

        Map<String, Object> tokenMap = redisService.hGetAllMap(key);
        log.info("[getAuthToken] 获取令牌: key={}, tokenMap={}", key, tokenMap);

        if (MapUtils.isEmpty(tokenMap)) {
            log.info("[getAuthToken] 未进行过授权: customerId={}", customerId);
            return null;
        }

        Long expireTime = Long.parseLong(String.valueOf(tokenMap.get("ex")));
        if (expireTime <= System.currentTimeMillis()) {
            log.info("[getAuthToken] 令牌已过期，尝试刷新: customerId={}", customerId);
            return refreshToken(key, saasId, platform, String.valueOf(tokenMap.get("rt")));
        }

        Token token = new Token();
        token.setAccessToken(String.valueOf(tokenMap.get("at")));
        token.setRefreshToken(String.valueOf(tokenMap.get("rt")));
        token.setUserName(String.valueOf(tokenMap.get("uname")));
        token.setSocialCustomerId(String.valueOf(tokenMap.get("uid")));
        return token;
    }

    @Override
    public Boolean resetAuth(String saasId, String customerId, String platform) {
        String key = buildTokenKey(saasId, customerId, platform);
        if (key == null) {
            log.warn("[resetAuth] 不支持的平台: {}", platform);
            return false;
        }

        redisService.del(key);
        log.info("[resetAuth] 重置认证成功: customerId={}, platform={}", customerId, platform);
        return true;
    }

    /**
     * 刷新访问令牌
     */
    private Token refreshToken(String key, String saasId, String platform, String refreshToken) {
        try {
            // 获取对应平台的认证策略
            PlatformAuthStrategy strategy = platformAuthStrategyFactory.getStrategy(platform);
            if (strategy == null || !strategy.supportsRefreshToken()) {
                log.warn("[refreshToken] 平台不支持刷新令牌: {}", platform);
                return null;
            }

            // 构建刷新请求
            OpenAuthRequest authRequest = strategy.buildRefreshRequest(saasId, refreshToken);
            if (authRequest == null) {
                log.error("[refreshToken] 构建刷新请求失败: platform={}", platform);
                return null;
            }

            // 执行刷新请求（这里可以复用OAuth2的逻辑）
            Token token = performRefreshRequest(authRequest);
            if (token == null) {
                log.error("[refreshToken] 刷新令牌失败: platform={}", platform);
                return null;
            }

            // 更新Redis中的令牌信息
            updateTokenInRedis(key, token);

            log.info("[refreshToken] 刷新令牌成功: platform={}", platform);
            return token;
        } catch (Exception e) {
            log.error("[refreshToken] 刷新令牌异常: platform={}", platform, e);
            return null;
        }
    }

    /**
     * 存储令牌信息到Redis
     */
    private void storeTokenToRedis(String saasId, String customerId, String platform,
                                  Token token, SocialUserInfo socialUserInfo, String ctime) {
        try {
            // 获取clientId用于构建key
            String clientId = getClientIdForPlatform(saasId, platform);
            if (clientId == null) {
                log.warn("[storeTokenToRedis] 无法获取clientId: platform={}", platform);
                return;
            }

            // 构建Redis key
            String key = RedisKeyConst.ACTIVITY_AUTH_TOKEN.getKey(
                String.format("%s:%s:%s",
                    threePlatformProperties.getTwitter().getAuthVersion().get(saasId),
                    customerId,
                    clientId));

            // 构建存储的数据
            Map<String, String> tokenMap = new HashMap<>();
            tokenMap.put("rt", token.getRefreshToken());
            tokenMap.put("at", token.getAccessToken());
            tokenMap.put("uid", socialUserInfo.getUserId());
            tokenMap.put("cid", customerId);
            tokenMap.put("uname", socialUserInfo.getUserName());
            tokenMap.put("ctime", ctime);

            // 计算过期时间（默认30天，Discord为30分钟）
            long expiresIn = OpenSocialPlatformEnum.discord.name().equals(platform) ? 30 * 60 : 30 * 24 * 60 * 60;
            tokenMap.put("ex", String.valueOf(System.currentTimeMillis() + (expiresIn - 30L) * 1000));

            // 存储到Redis
            redisService.hSetAll(key, tokenMap);
            redisService.expire(key, (int) expiresIn);

            log.info("[storeTokenToRedis] 令牌存储成功: platform={}, key={}", platform, key);
        } catch (Exception e) {
            log.error("[storeTokenToRedis] 存储令牌失败: platform={}", platform, e);
        }
    }

    /**
     * 根据平台获取clientId
     */
    private String getClientIdForPlatform(String saasId, String platform) {
        return switch (OpenSocialPlatformEnum.valueOf(platform)) {
            case discord -> threePlatformProperties.getDiscord().getClientId().get(saasId);
            case twitter -> threePlatformProperties.getTwitter().getClientId().get(saasId);
            case google -> threePlatformProperties.getGoogle().getClientId().get(saasId);
            case facebook -> threePlatformProperties.getFacebook().getClientId().get(saasId);
            case line -> threePlatformProperties.getLine().getClientId().get(saasId);
            default -> null;
        };
    }

    /**
     * 构建Token存储的Redis Key
     */
    private String buildTokenKey(String saasId, String customerId, String platform) {
        String clientId = getClientIdForPlatform(saasId, platform);
        if (clientId == null) {
            return null;
        }

        return RedisKeyConst.ACTIVITY_AUTH_TOKEN.getKey(
            String.format("%s:%s:%s",
                threePlatformProperties.getTwitter().getAuthVersion().get(saasId),
                customerId,
                clientId));
    }

    /**
     * 执行刷新令牌请求
     */
    private Token performRefreshRequest(OpenAuthRequest authRequest) {
        try {
            // 创建一个临时的OAuth2策略来执行刷新请求
            // 这里复用AbstractOAuth2AuthStrategy中的逻辑
            AbstractOAuth2AuthStrategy tempStrategy = new AbstractOAuth2AuthStrategy(okHttpClient) {
                @Override
                public String getPlatformName() {
                    return "temp";
                }

                @Override
                public OpenAuthRequest buildAuthRequest(String saasId, String code, String redirectUri) {
                    return null;
                }

                @Override
                public OpenAuthRequest buildRefreshRequest(String saasId, String refreshToken) {
                    return null;
                }

                @Override
                public SocialUserInfo getCurrentUser(String saasId, String accessToken, String refreshToken) {
                    return null;
                }
            };

            return tempStrategy.performOAuth2Request(authRequest);
        } catch (Exception e) {
            log.error("[performRefreshRequest] 执行刷新请求失败", e);
            return null;
        }
    }

    /**
     * 更新Redis中的令牌信息
     */
    private void updateTokenInRedis(String key, Token token) {
        try {
            Map<String, String> updateMap = new HashMap<>();
            updateMap.put("rt", token.getRefreshToken());
            updateMap.put("at", token.getAccessToken());
            updateMap.put("ex", String.valueOf(System.currentTimeMillis() + 30 * 24 * 60 * 60 * 1000L));
            updateMap.put("rtime", String.valueOf(System.currentTimeMillis()));

            // 批量更新Redis字段
            for (Map.Entry<String, String> entry : updateMap.entrySet()) {
                redisService.hSet(key, entry.getKey(), entry.getValue());
            }

            redisService.expire(key, 30 * 24 * 60 * 60);
            log.info("[updateTokenInRedis] 更新令牌成功: key={}", key);
        } catch (Exception e) {
            log.error("[updateTokenInRedis] 更新Redis令牌信息失败", e);
        }
    }



    @Override
    public Boolean sendVerifyCode(String saasId, String customerId, String customerName, String channel, String receiver, String sceneCode) throws ActivityException{
        log.info("sendVerifyCode start, saasId:{}, customerId:{}, customerName:{}, channel:{}, receiver:{}, sceneCode:{}", saasId, customerId, customerName, channel, receiver, sceneCode);

        //根据saasId和sceneCode从缓存中获取域名白名单，进行校验
        if (!verifyDomain(saasId, receiver)) {
            log.error("sendVerifyCode verifyDomain failed, saasId:{}, customerId:{}, customerName:{}, channel:{}, receiver:{}, sceneCode:{}", saasId, customerId, customerName, channel, receiver, sceneCode);
            throw new ActivityException(ActivityResponseCode.EMAIL_DOMAIN_NOT_SUPPORT);
        }
        // 生成随机6位验证码
        String verifyCode = RandomStringUtils.randomNumeric(6);

        // 构建缓存数据，同时存储验证码和邮箱地址
        Map<String, String> verifyData = new HashMap<>();
        verifyData.put("code", verifyCode);
        verifyData.put("email", receiver);
        verifyData.put("customerName", customerName);
        verifyData.put("timestamp", String.valueOf(System.currentTimeMillis()));

        // 将验证码和邮箱地址一起缓存到Redis
        String redisKey = RedisKeyConst.ACTIVITY_EMAIL_VERIFY_CODE.getKey(customerId);
        redisService.hSetAll(redisKey, verifyData);
        redisService.expire(redisKey, kactivityProperties.getVerifyCodeExpireMinutes() * 60); // 默认5分钟过期

        // 发送邮件验证码
        Map<String, Object> emailParams = new HashMap<>();
        emailParams.put("verifyCode", verifyCode);
        emailParams.put("receiver", receiver);
        emailParams.put("customerName", customerName);

        if (sceneCode == null) {
            sceneCode = "social_bind_email";
        }
        EmailMessageDTO emailMessageDTO = new EmailMessageDTO();
        emailMessageDTO.setAddress(receiver);
        emailMessageDTO.setTemplateId(sceneCode);
        emailMessageDTO.setParameterMap(emailParams);
        emailMessageDTO.setSaasId(saasId);

        log.info("emailMsgSend start...{}", JSONObject.toJSONString(emailMessageDTO));
        NotifyResult notifyResult = notifyService.sendEmail(emailMessageDTO);
        boolean res = notifyResult.isSuccess();
        log.info("emailMsgSend process success[{}], verifyCode cached with email: {}", res, receiver);
        return res;
    }



    private void sendSaveBindInfoEvent(String saasId, String customerId, String platform, SaasConfig saasConfig, SocialUserInfo currentUser) {

        if (saasConfig.getSaveBindInfo()) {
            //发送mq消息，谁需要绑定信息谁消费
            EventDTO eventDTO = new EventDTO();
            JSONObject eventBody = new JSONObject();
            eventBody.put("saasId", saasId);
            eventBody.put("socialId", currentUser.getUserId());
            eventBody.put("socialHandleName", currentUser.getUserName());
            eventBody.put("socialEmail", currentUser.getEmail());
            eventBody.put("socialProfileImage", currentUser.getProfileImageUrl());
            eventBody.put("platform", platform);

            eventDTO.setBody(eventBody);
            eventDTO.setCustomerId(customerId);
            eventDTO.setTime(new Date().getTime());
            eventDTO.setGlobalUid(RandomUtil.randomString(12) + "_" + customerId + "_" + platform);
            eventDTO.setName("bindSocialInfo");
            eventClient.push(eventDTO);
        }
    }

    private void doTask(String customerId, String eventCode, String saasId) {
        ActivityEventMessage activityEventMessage = new ActivityEventMessage();
        activityEventMessage.setCustomerId(customerId);
        activityEventMessage.setEventCode(eventCode);
        activityEventMessage.setEventTime(System.currentTimeMillis());
        Map<String,Object> body = new HashMap<>();
        body.put("saasId", saasId);
        activityEventMessage.setBody(body);
        log.info("do action activityTaskDTO = {}", activityEventMessage);
        try{
            ActivityResponse<List<Award>> action = activityEventAction.action(activityEventMessage);
        }catch (Exception ex){
            log.error("remoteTaskService task error:{}", activityEventMessage, ex);
        }
    }
}
