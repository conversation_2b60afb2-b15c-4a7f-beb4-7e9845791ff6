package com.kikitrade.activity.service.meta.impl;

import com.kikitrade.activity.dal.mysql.dao.ActivityBatchInfoDao;
import com.kikitrade.activity.dal.mysql.model.ActivityBatchInfo;
import com.kikitrade.activity.service.meta.ActivityBatchInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityBatchInfoServiceImpl implements ActivityBatchInfoService {


    @Resource
    private ActivityBatchInfoDao activityBatchInfoDao;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(ActivityBatchInfo activityBatchInfo) throws Exception {
        int count = 0;
        try {
            count = activityBatchInfoDao.insert(activityBatchInfo);

        } catch (Exception e) {
            log.error("activitybatchinfoserviceimpl save process failed.", e);
            throw e;
        }
        return count;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatus(String tran_date, Integer activity_id, String batch_id, Integer status) {
        int count = 0;
        try {
            count = activityBatchInfoDao.updateStatus(tran_date, activity_id, batch_id, status);
        } catch (Exception e) {
            log.error("activitybatchinfoserviceimpl updateStatus process failed.", e);
            throw e;
        }
        return count;
    }

    @Override
    public ActivityBatchInfo findByPrimaryKey(String tran_date, Integer activity_id, String batch_id) {
        ActivityBatchInfo activityBatchInfo = null;
        try {
            activityBatchInfo = activityBatchInfoDao.findByPrimaryKey(tran_date, activity_id, batch_id);
        } catch (Exception e) {
            log.error("activitybatchinfoserviceimpl findByPrimaryKey process failed.", e);
        }
        return activityBatchInfo;
    }

    @Override
    public ActivityBatchInfo findByBatchId(String batch_id) {
        ActivityBatchInfo activityBatchInfo = null;
        try {
            activityBatchInfo = activityBatchInfoDao.findByBatchId(batch_id);
        } catch (Exception e) {
            log.error("activitybatchinfoserviceimpl findByBatchId process failed.", e);
        }
        return activityBatchInfo;
    }

    @Override
    public ActivityBatchInfo findLastBatchByActivityId(Integer activity_id) {

        return activityBatchInfoDao.findLastBatchByActivityId(activity_id);
    }

    @Override
    public List<ActivityBatchInfo> findAll(String tran_date, Integer activity_id, String batch_id, Integer offset, Integer limit) {
        List<ActivityBatchInfo> activityBatchInfoList = null;
        try {
            activityBatchInfoList = activityBatchInfoDao.findAll(tran_date, activity_id, batch_id, offset, limit);
        } catch (Exception e) {
            log.error("activitybatchinfoserviceimpl findAll process failed.", e);
        }
        return activityBatchInfoList;
    }

    @Override
    public String getBatchInfoId(String processDataTableName, int activityType, String batchPt) {

        return processDataTableName + "_" + activityType + "_" + batchPt;
    }

}
