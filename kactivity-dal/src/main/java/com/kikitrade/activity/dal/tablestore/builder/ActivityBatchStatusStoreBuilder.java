package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus;

import java.util.List;

public interface ActivityBatchStatusStoreBuilder {

    List<ActivityBatchStatus> findByStatus(String status, int limit);

    boolean updateStatusAndNum(String batchId, String status, Integer num);

    ActivityBatchStatus getById(String batchId);

    boolean insert(ActivityBatchStatus activityBatchStatus);
}
