package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.PrecisionMetrics;

/**
 * Builder for PrecisionMetrics
 */
public interface PrecisionMetricsBuilder {

    /**
     * Insert a new PrecisionMetrics record
     * @param precisionMetrics the PrecisionMetrics record to insert
     * @return true if the insertion was successful, false otherwise
     */
    boolean insert(PrecisionMetrics precisionMetrics);

    String getTableName();
}
