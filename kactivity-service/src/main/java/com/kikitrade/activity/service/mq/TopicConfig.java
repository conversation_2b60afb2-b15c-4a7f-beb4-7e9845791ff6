package com.kikitrade.activity.service.mq;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2021/7/6 7:27 下午
 * @modify
 */
@Configuration
@Data
public class TopicConfig {

    @Value("${ons.topic.activity}")
    private String topicActivity;

    @Value("${ons.topic.phase.two}")
    private String topicPhaseTwo;

    @Value("${ons.topic.activity.event}")
    private String topicActivityEvent;

    @Value("${ons.topic.activity.customer}")
    private String topicActivityCustomer;

    @Value("${ons.topic.reward}")
    private String topicReward;

    // 注意：正在用的 topic 不能修改
    @Deprecated
    @Value("${ons.topic.customer.event}")
    private String topicCustomerEvent;

    @Value("${ons.topic.event.call}")
    private String topicEventCall;

    @Value("${ons.topic.order.event}")
    private String orderEvent;

    @Value("${ons.topic.reward.divide}")
    private String topicRewardDivide;
}
