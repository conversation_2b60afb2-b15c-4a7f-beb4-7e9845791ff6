package com.kikitrade.activity.dal.tablestore.builder;

import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
public interface LuckFortuneReceiveItemBuilder {

    String getTableName();

    /**
     * 插入一个领取的红包
     * @param luckFortuneReceiveItem
     * @return
     */
    boolean insert(LuckFortuneReceiveItem luckFortuneReceiveItem);

    /**
     * 更新发放的红包
     * @param luckFortuneReceiveItem
     * @return
     */
    boolean update(LuckFortuneReceiveItem luckFortuneReceiveItem);

    /**
     * 根据红包id，查询红包
     * @param id
     * @return
     */
    LuckFortuneReceiveItem findById(String id);

    /**
     * 查询领取记录
     * @param releaseId
     * @return
     */
    List<LuckFortuneReceiveItem> findByReleaseId(String releaseId);

    /**
     * 查询在某个红包中的领取记录
     * @param releaseId
     * @return
     */
    LuckFortuneReceiveItem findByCustomerIdAndReleaseId(String customerId, String releaseId);

    /**
     * 查询在某个红包中的领取记录
     * @param releaseId
     * @return
     */
    LuckFortuneReceiveItem findByReceiveCodeIdAndReleaseId(String receiveCode, String releaseId);

    /**
     * 查询某个电话或邮箱的领取记录
     * @param receiveCode
     * @return
     */
    List<LuckFortuneReceiveItem> findByReceiveCode(String receiveCode);

    /**
     * 某个账号领取的记录
     * @param customerId
     * @return
     */
    Page<LuckFortuneReceiveItem> findByCustomerId(String customerId, String type, String startTime, String endTime, int offset, int limit);

    /**
     * 查询已过期的红包
     * @param expiredTime
     * @return
     */
    List<LuckFortuneReceiveItem> findByExpiredTime(Integer status, String expiredTime);

    /**
     * 发放的红包总数
     * @param customerId
     * @return
     */
    long countReceive(String customerId);

    /**
     * 历史发放的空投总价值
     * @param customerId
     * @return
     */
    BigDecimal sumAmountReceive(String customerId);

    /**
     * 根据状态查询最近领取的红包
     * @param customerId
     * @param statusList
     * @return
     */
    List<LuckFortuneReceiveItem> findByCustomerIdAndStatus(String customerId, String startTime, List<Long> statusList);
}
