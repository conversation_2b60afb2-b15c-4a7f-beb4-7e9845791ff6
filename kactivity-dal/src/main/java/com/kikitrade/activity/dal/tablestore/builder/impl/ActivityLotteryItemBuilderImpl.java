package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityLotteryItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.Collections;

import static com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem.SEARCH_ACTIVITY_LOTTERY;

@Service
public class ActivityLotteryItemBuilderImpl extends WideColumnStoreBuilder<ActivityLotteryItem> implements ActivityLotteryItemBuilder {

    @Override
    public String getTableName() {
        return "activity_lottery_item";
    }

    @PostConstruct
    public void init() {
        super.init(ActivityLotteryItem.class);
    }

    @Override
    public boolean insert(ActivityLotteryItem activityLotteryItem) {
        return super.putRow(activityLotteryItem, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean update(ActivityLotteryItem activityLotteryItem) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(activityLotteryItem, condition);
    }

    @Override
    public long countByCustomer(String customerId) {
        String aggName = "count_activity_lottery_by_customer";
        SearchQuery searchQuery = SearchQuery.newBuilder().query(QueryBuilders.term("customer_id", customerId))
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();
        return searchCount(searchQuery, SEARCH_ACTIVITY_LOTTERY, aggName);
    }

    @Override
    public long countByCustomer(String customerId, String code, Long startTime, Long endTime) {
        String aggName = "count_activity_lottery_by_customer";
        SearchQuery searchQuery = SearchQuery.newBuilder()
                .query(QueryBuilders.bool()
                        .must(QueryBuilders.term("customer_id",customerId))
                        .must(QueryBuilders.term("code", code))
                        .must(QueryBuilders.range("drew_time").greaterThanOrEqual(startTime).lessThanOrEqual(endTime))
                        .mustNot(QueryBuilders.terms("status").terms(ActivityConstant.LotteryStatus.FAIL.name(),
                                ActivityConstant.LotteryStatus.CANCEL.name(),
                                ActivityConstant.LotteryStatus.NO_PAID.name())))
                .addAggregation(AggregationBuilders.count(aggName, "drew_time")).build();
        return searchCount(searchQuery, SEARCH_ACTIVITY_LOTTERY, aggName);
    }

    @Override
    public long cumulateCountByCustomer(String customerId, String code) {
        String aggName = "count_history_activity_lottery_by_customer";
        SearchQuery searchQuery = SearchQuery.newBuilder()
                .query(QueryBuilders.bool()
                        .must(QueryBuilders.term("customer_id",customerId))
                        .must(QueryBuilders.term("code", code))
                        .must(QueryBuilders.term("is_cumulate", true))
                        .mustNot(QueryBuilders.terms("status").terms(ActivityConstant.LotteryStatus.FAIL.name(),
                                ActivityConstant.LotteryStatus.CANCEL.name(),
                                ActivityConstant.LotteryStatus.NO_PAID.name())))
                .limit(0)
                .addAggregation(AggregationBuilders.count(aggName, "is_cumulate")).build();
        return searchCount(searchQuery, SEARCH_ACTIVITY_LOTTERY, aggName);
    }

    @Override
    public PageResult findByCustomer(String customerId, int offset, int limit) {
        BoolQuery boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id",customerId))
                .build();
        Sort sort = new Sort(Collections.singletonList(new FieldSort("drew_time", SortOrder.DESC)));
        return pageSearch(boolQuery, sort, offset, limit, SEARCH_ACTIVITY_LOTTERY);
    }

    @Override
    public ActivityLotteryItem findById(String customerId, String id) {
        ActivityLotteryItem item = new ActivityLotteryItem();
        item.setCustomerId(customerId);
        item.setId(id);
        return getRow(item);
    }
}
