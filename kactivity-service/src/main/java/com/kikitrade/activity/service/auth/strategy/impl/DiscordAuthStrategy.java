package com.kikitrade.activity.service.auth.strategy.impl;

import com.kikitrade.activity.service.auth.strategy.AbstractOAuth2AuthStrategy;
import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.model.OpenAuthRequest;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import com.kikitrade.activity.service.rpc.ThreePlatformApi;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Component;

import java.util.Base64;

/**
 * Discord认证策略实现
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
public class DiscordAuthStrategy extends AbstractOAuth2AuthStrategy {

    private static final String DC_AUTH_URL = "https://discord.com/api/v10/oauth2/token";

    private final ThreePlatformProperties threePlatformProperties;
    private final ThreePlatformApi threePlatformApi;

    public DiscordAuthStrategy(OkHttpClient okHttpClient, 
                              ThreePlatformProperties threePlatformProperties,
                              ThreePlatformApi threePlatformApi) {
        super(okHttpClient);
        this.threePlatformProperties = threePlatformProperties;
        this.threePlatformApi = threePlatformApi;
    }

    @Override
    public String getPlatformName() {
        return OpenSocialPlatformEnum.discord.name();
    }

    @Override
    public OpenAuthRequest buildAuthRequest(String saasId, String code, String redirectUri) {
        try {
            return OpenAuthRequest.builder()
                    .url(DC_AUTH_URL)
                    .query(String.format("code=%s&grant_type=authorization_code&redirect_uri=%s", 
                        code, redirectUri))
                    .clientId(threePlatformProperties.getDiscord().getClientId().get(saasId))
                    .basic(Base64.getEncoder().encodeToString(
                        String.format("%s:%s",
                            threePlatformProperties.getDiscord().getClientId().get(saasId), 
                            threePlatformProperties.getDiscord().getClientSecret().get(saasId)).getBytes()))
                    .build();
        } catch (Exception e) {
            log.error("[Discord] 构建认证请求失败", e);
            return null;
        }
    }

    @Override
    public OpenAuthRequest buildRefreshRequest(String saasId, String refreshToken) {
        try {
            return OpenAuthRequest.builder()
                    .url(DC_AUTH_URL)
                    .query(String.format("grant_type=refresh_token&refresh_token=%s", refreshToken))
                    .basic(Base64.getEncoder().encodeToString(
                        String.format("%s:%s",
                            threePlatformProperties.getDiscord().getClientId().get(saasId), 
                            threePlatformProperties.getDiscord().getClientSecret().get(saasId)).getBytes()))
                    .build();
        } catch (Exception e) {
            log.error("[Discord] 构建刷新请求失败", e);
            return null;
        }
    }

    @Override
    public SocialUserInfo getCurrentUser(String saasId, String accessToken, String refreshToken) {
        try {
            AccessToken build = AccessToken.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .saasId(saasId)
                    .build();
            return threePlatformApi.discord().getCurrentUserRequest(build).execute();
        } catch (Exception e) {
            log.error("[Discord] 获取用户信息失败", e);
            return null;
        }
    }
}
