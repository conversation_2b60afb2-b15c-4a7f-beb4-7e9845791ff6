package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.tablestore.builder.TaskConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.TaskConfig;
import com.kikitrade.activity.mock.TaskConfigBuilderMock;
import com.kikitrade.activity.model.constant.ActivityConstant;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.stream.Collectors;

import static com.kikitrade.activity.op.BusinessOP.taskConfigMap;
import static org.mockito.ArgumentMatchers.anyString;

@Component
public class TaskConfigBuilderMockImpl implements TaskConfigBuilderMock {
    @Autowired
    TaskConfigBuilder taskConfigBuilder;

    @Override
    public void getTaskById() {
        Mockito.doAnswer(c -> {
            String taskId = c.getArgument(0);
            return taskConfigMap.get(taskId);
        }).when(taskConfigBuilder).getTaskById(anyString());
    }

    @Override
    public void getTaskByCode() {
        Mockito.doAnswer(c -> {
            String saasId = c.getArgument(0);
            String code = c.getArgument(1);
            return taskConfigMap.values().stream().filter(t -> saasId.equals(t.getSaasId()) && code.equals(t.getCode())).collect(Collectors.toList());
        }).when(taskConfigBuilder).getTaskByCode(anyString(), anyString());
    }

    @Override
    public void getTaskByGroupId() {
        Mockito.doAnswer(c -> {
            String groupId = c.getArgument(0);
//            return taskConfigMap.values().stream().filter(t -> t.getGroupId().equals(groupId)).collect(Collectors.toList());
            return taskConfigMap.values().stream().filter(t -> groupId.equals(t.getGroupId()) && !groupId.equals(t.getTaskId())).collect(Collectors.toList());
        }).when(taskConfigBuilder).getTaskByGroupId(anyString());
    }

    @Override
    public void findTaskBySaasId() {
        Mockito.doAnswer(c -> {
            String saasId = c.getArgument(0);
            String channel = c.getArgument(1);
            String position = c.getArgument(2);

            return taskConfigMap.values().stream()
                    .filter(t -> t.getSaasId().equals(saasId) && t.getShowList() && (t.getStatus().equals(ActivityConstant.CommonStatus.ACTIVE.name().toString()) || t.getStatus().equals(ActivityConstant.CommonStatus.GRAY.name().toString())))
                    .filter(t -> channel == null || t.getChannel().equals(channel))
                    .filter(t -> position == null || t.getPosition().equals(position))
                    .sorted(Comparator.comparing(TaskConfig::getOrder))
                    .collect(Collectors.toList());

        }).when(taskConfigBuilder).findTaskBySaasId(Mockito.any(), Mockito.any(), Mockito.any());
    }
}
