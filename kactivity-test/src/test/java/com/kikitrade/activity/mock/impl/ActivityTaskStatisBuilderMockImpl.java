package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskStatisBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskStatis;
import com.kikitrade.activity.mock.ActivityTaskStatisBuilderMock;
import com.kikitrade.framework.common.util.BeanUtil;
import jakarta.annotation.Resource;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.kikitrade.activity.op.BusinessOP.activityTaskStatisList;

@Component
public class ActivityTaskStatisBuilderMockImpl implements ActivityTaskStatisBuilderMock {

    @Resource
    private ActivityTaskStatisBuilder activityTaskStatisBuilder;

    @Override
    public void findDetail() {
        Mockito.doAnswer(c -> {
            ActivityTaskStatis statis = c.getArgument(0);
            ActivityTaskStatis activityTaskStatisDB = activityTaskStatisList.stream()
                    .filter(item -> item.getTaskId().equals(statis.getTaskId())
                            && item.getCustomerId().equals(statis.getCustomerId())
                            && item.getCycle().equals(statis.getCycle())
                            && item.getTargetId().equals(statis.getTargetId())).findFirst().orElse(null);

            return BeanUtil.copyProperties(activityTaskStatisDB, ActivityTaskStatis::new);

        }).when(activityTaskStatisBuilder).findDetail(Mockito.any(ActivityTaskStatis.class));
    }

    @Override
    public void insert() {
        Mockito.doAnswer(c -> {

            ActivityTaskStatis statis = c.getArgument(0);
            ActivityTaskStatis activityTaskStatisDB = activityTaskStatisList.stream()
                    .filter(item -> item.getTaskId().equals(statis.getTaskId())
                            && item.getCustomerId().equals(statis.getCustomerId())
                            && item.getCycle().equals(statis.getCycle())
                            && item.getTargetId().equals(statis.getTargetId())).findFirst().orElse(null);

            if (Objects.isNull(activityTaskStatisDB)) {
                activityTaskStatisList.add(statis);
                return true;
            }
            return false;

        }).when(activityTaskStatisBuilder).insert(Mockito.any(ActivityTaskStatis.class));
    }

    @Override
    public void incrementProgress() {
        Mockito.doAnswer(c -> {
            ActivityTaskStatis statis = c.getArgument(0);
            int incr = c.getArgument(1);
            ActivityTaskStatis activityTaskStatisDB = activityTaskStatisList.stream()
                    .filter(item -> item.getTaskId().equals(statis.getTaskId())
                            && item.getCustomerId().equals(statis.getCustomerId())
                            && item.getCycle().equals(statis.getCycle())
                            && item.getTargetId().equals(statis.getTargetId())).findFirst().orElse(null);

            activityTaskStatisDB.setProgress(activityTaskStatisDB.getProgress() + incr);

            return activityTaskStatisDB.getProgress().longValue();

        }).when(activityTaskStatisBuilder).incrementProgress(Mockito.any(ActivityTaskStatis.class), Mockito.anyInt());
    }
}
