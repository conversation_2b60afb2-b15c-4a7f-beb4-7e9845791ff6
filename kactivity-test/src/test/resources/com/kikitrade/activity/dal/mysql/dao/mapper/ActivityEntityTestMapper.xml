<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityEntityTestDao">
    <resultMap id="BaseResultMap" type="com.kikitrade.activity.dal.mysql.model.ActivityEntity">
        <result column="sub_type" property="subType"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="auto_create_batch" property="autoCreateBatch"/>
        <result column="next_create_time" property="nextCreateTime"/>
        <result column="reward_config" property="rewardConfig"/>
        <result column="template_code" property="templateCode"/>
        <result column="auto_approve" property="autoApprove"/>
        <result column="task_id" property="taskId"/>
    </resultMap>
    <insert id="insert">

        INSERT INTO `activity_entity` (`id`, `name`, `type`, `start_time`, `end_time`, `remark`, `area`, `cycle`,
                                       `auto_create_batch`, `status`, `created`, `modified`, `saasId`,
                                       `next_create_time`, `reward_config`, `conditions`, `template_code`,
                                       `auto_approve`, `task_id`, `source`, `sub_type`)
        VALUES (#{id}, #{name}, #{type}, #{startTime}, #{endTime}, #{remark},
                #{area}, #{cycle}, #{autoCreateBatch}, #{status}, #{created}, #{modified}, #{saasId}, #{nextCreateTime},
                #{rewardConfig}, #{conditions}, #{templateCode}, #{autoApprove}, #{taskId}, #{source}, #{subType});


    </insert>

    <update id="createTableIfNotExist">
        CREATE TABLE `activity_entity`
        (
            `id`                varbinary(64) NOT NULL,
            `name`              varchar(128) NOT NULL COMMENT '活动名称',
            `type`              varchar(32)  NOT NULL COMMENT '活动类型',
            `sub_type`          varchar(32)  DEFAULT NULL,
            `start_time`        datetime     NOT NULL COMMENT '活动开始时间',
            `end_time`          datetime     DEFAULT NULL COMMENT '活动结束时间',
            `remark`            varchar(255) DEFAULT NULL COMMENT '活动描述',
            `area`              varchar(128) DEFAULT NULL COMMENT '活动区域',
            `source`            varchar(32)  DEFAULT NULL COMMENT '活动创建来源',
            `cycle`             varchar(32)  DEFAULT NULL COMMENT '自动创建批次周期',
            `auto_create_batch` tinyint(4) DEFAULT '0' COMMENT '自动创建批次',
            `next_create_time`  datetime     DEFAULT NULL COMMENT '下次创建批次时间',
            `reward_config`     text COMMENT '活动奖励配置',
            `conditions`        longtext COMMENT '数据查询条件',
            `template_code`     varchar(128) DEFAULT NULL COMMENT '模版code',
            `auto_approve`      tinyint(4) DEFAULT '0' COMMENT '是否自动审核',
            `task_id`           varchar(32)  DEFAULT NULL COMMENT '任务id',
            `status`            tinyint(4) NOT NULL COMMENT '活动状态',
            `created`           datetime     NOT NULL COMMENT '创建时间',
            `modified`          datetime     NOT NULL COMMENT '修改时间',
            `saasId`            varchar(16)  NOT NULL COMMENT 'saasId',
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='活动表'
        ;
    </update>


</mapper>

