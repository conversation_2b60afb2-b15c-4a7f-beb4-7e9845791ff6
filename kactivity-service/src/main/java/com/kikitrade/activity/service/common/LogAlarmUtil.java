package com.kikitrade.activity.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.kikitrade.framework.mybatis.config.BusiContext;
import com.kikitrade.framework.mybatis.config.SaasContext;
import com.kikitrade.trade.common.TradingResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.kikitrade.activity.service.common.LogAlarmConstant.*;

/**
 * <AUTHOR>
 * @date 2020/11/16 14:52
 **/

@Slf4j
@Service
public class LogAlarmUtil {


    @Value("${log.alarm.owner.config}")
    private String logConfigJson;

    private static Map<String, String> ownerConfig;

    private Map<String, String> getOwnerConfig() {
        if (ownerConfig != null) {
            return ownerConfig;
        }
        synchronized (LogAlarmUtil.class) {
            if (StringUtils.isNotBlank(logConfigJson)) {
                ownerConfig = JSONObject.parseObject(logConfigJson, new TypeReference<Map<String, String>>() {
                }.getType());
            }
        }
        return ownerConfig;
    }


    /**
     * @param checkPoint 当前业务检查点 objects未上送TradingResponseCode，默认 alarm 参数为true 才进行业务报警
     * @param logMsg     日志描述
     * @param objects    需要打印的信息(对象，异常...）如果传递 TradingResponseCode ：TradingResponseCode logAlarm 参数为true 才进行业务报警
     * @return void
     * @Description 用于业务流程标记及报警
     * <AUTHOR>
     * @date 2020/11/16 16:14
     */
    public void alarm(LogAlarmConstant.CheckPoint checkPoint, String logMsg, Object... objects) {
        try {

            if (StringUtils.isEmpty(MDC.get(BUSI_TYPE)) && checkPoint != null) {
                MDC.put(BUSI_TYPE, checkPoint.getBusiCategory().getBusiness().name().toUpperCase());
            }

            if (StringUtils.isEmpty(MDC.get(OWNER)) && checkPoint != null) {
                MDC.put(OWNER, StringUtils.join(getOwner(checkPoint.getBusiCategory().getBusiness()), ","));
            }


            if (StringUtils.isEmpty(MDC.get(BUSI_CATEGORY)) && checkPoint != null) {
                MDC.put(BUSI_CATEGORY, checkPoint.getBusiCategory().getCategory());
            }

            if (StringUtils.isEmpty(MDC.get(CHECK_POINT)) && checkPoint != null) {
                MDC.put(CHECK_POINT, checkPoint.name());
            }

            try {

                Object msgEnum = null, exObj = null, contextObj = null;

                if (objects != null) {
                    for (Object o : objects) {

                        if (contextObj == null && o instanceof BusiContext) {
                            contextObj = o;
                        }

                        if (exObj == null && o instanceof Throwable) {
                            exObj = o;
                        }
                        if (msgEnum == null && o instanceof TradingResponseCode) {
                            msgEnum = o;
                        }
                    }
                }


                if (contextObj != null) {
                    BusiContext context = (BusiContext) contextObj;
                    if (StringUtils.isEmpty(MDC.get(CUSTOMER_ID))) {
                        if (context != null && StringUtils.isNotEmpty(context.getCustomerId())) {
                            MDC.put(CUSTOMER_ID, context.getCustomerId());
                        }
                    }
                } else {
                    if (StringUtils.isEmpty(MDC.get(CUSTOMER_ID))) {
                        BusiContext busiContext = SaasContext.getBusiContext();
                        if (busiContext != null && StringUtils.isNotEmpty(busiContext.getCustomerId())) {
                            MDC.put(CUSTOMER_ID, busiContext.getCustomerId());
                        }
                    }
                }


                if (exObj != null) {
                    Throwable throwable = (Throwable) exObj;
                    String msg = throwable.getCause() != null ? throwable.getCause().getMessage() : "";
                    MDC.put(ERROR_MSG, msg = msg != null ? msg.replaceAll("\r|\n", "") : "");
                    StackTraceElement stackTrace = throwable.getStackTrace() != null && throwable.getStackTrace().length > 0 ? throwable.getStackTrace()[0] : null;
                    if (stackTrace != null) {
                        MDC.put(STACK_TRACE, stackTrace.getClassName() + "." + stackTrace.getMethodName() + "(" + stackTrace.getFileName() + ":" + stackTrace.getLineNumber() + ")");
                    }
                }

                if (msgEnum != null) {
                    TradingResponseCode TradingResponseCode = (TradingResponseCode) msgEnum;
                    MDC.put(LOG_ALARM, String.valueOf(TradingResponseCode.isLogAlarm()));
                    if (StringUtils.isEmpty(MDC.get(ERROR_MSG))) {
                        MDC.put(ERROR_MSG, TradingResponseCode.getCode() + "-" + TradingResponseCode.getKey());
                    }
                } else {
                    MDC.put(LOG_ALARM, String.valueOf(checkPoint.isAlarm()));
                }

                if (StringUtils.isEmpty(MDC.get(ERROR_MSG)) && StringUtils.isNotBlank(logMsg)) {
                    if (exObj != null) {
                        MDC.put(ERROR_MSG, logMsg + "--->" + exObj.getClass().getCanonicalName());
                    } else {
                        MDC.put(ERROR_MSG, logMsg);
                    }
                }

            } catch (Exception e) {
                log.error("LogAlarmUtil alarm process error.{}", JSON.toJSONString(MDC.getCopyOfContextMap()), e);
            }

            //日志打印
            log.error("LogAlarmUtil alarm--->" + logMsg, objects);

        } catch (Exception ex) {
            log.error("LogAlarmUtil alarm process error.{}", JSON.toJSONString(MDC.getCopyOfContextMap()), ex);
        } finally {
            MDC.remove(CUSTOMER_ID);
            MDC.remove(BUSI_TYPE);
            MDC.remove(OWNER);
            MDC.remove(BUSI_CATEGORY);
            MDC.remove(LOG_ALARM);
            MDC.remove(ERROR_MSG);
            MDC.remove(CHECK_POINT);
            MDC.remove(STACK_TRACE);
        }
    }


    /**
     * @param checkPoint 当前业务检查点 logAlarm 参数为true 才进行业务报警
     * @param logMsg     日志描述
     * @return void
     * @Description 用于业务流程标记及报警
     * <AUTHOR>
     * @date 2020/11/16 16:14
     */
    public void alarm(CheckPoint checkPoint, String logMsg) {
        alarm(checkPoint, logMsg, null);
    }


    /**
     * @param checkPoint 当前业务检查点 logAlarm 参数为true 才进行业务报警
     * @param objects    需要打印的信息(对象，异常...）如果传递 TradingResponseCode ：TradingResponseCode logAlarm 参数为true 才进行业务报警
     * @return void
     * @Description 用于业务流程标记及报警
     * <AUTHOR>
     * @date 2020/11/16 16:14
     */
    public void alarm(CheckPoint checkPoint, Object... objects) {
        alarm(checkPoint, checkPoint.getDesc(), objects);
    }


    /**
     * @param checkPoint 当前业务检查点 logAlarm 参数为true 才进行业务报警
     * @return void
     * @Description 用于业务流程标记及报警
     * <AUTHOR>
     * @date 2020/11/16 16:14
     */
    public void alarm(CheckPoint checkPoint) {
        alarm(checkPoint, checkPoint.getDesc(), null);
    }


    /**
     * @param
     * @return java.lang.String
     * @Description 获取当前业务zipkin traceId
     * <AUTHOR>
     * @date 2020/11/16 16:16
     */
    public String getTraceId() {
        return MDC.get(TRACE_ID);
    }


    /**
     * @param
     * @return java.lang.String
     * @Description 获取当前业务zipkin spanId
     * <AUTHOR>
     * @date 2020/11/16 16:16
     */
    public String getSpanId() {
        return MDC.get(SPAN_ID);
    }


    public List<String> getOwner(Business type) {
        Map<String, String> map = getOwnerConfig();
        List<String> owerList = map.keySet().stream().filter(key -> map.get(key).contains(type.name())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(owerList)) {
            owerList = Arrays.asList();
        }
        return owerList;
    }
}
