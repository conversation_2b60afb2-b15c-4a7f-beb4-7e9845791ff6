package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2019/3/19 15:07
 */
@Getter
@Setter
@Table(name = "activity_customer_current_interest")
public class ActivityCustomerCurrentInterest extends BaseModel {


    //贴息时间(yyyyMMdd)
    @Column(name = "trans_date")
    private String transDate;

    //活动ID
    @Column(name = "activity_id")
    private Integer activityId;

    //活动类型
    @Column(name = "activity_type")
    private Integer activityType;


    //用户ID
    @Column(name = "customer_id")
    private String customerId;


    //用户理财账户余额
    @Column(name = "balance")
    private BigDecimal balance;

    //年化
    @Column(name = "rate")
    private Double rate;

    //币种
    @Column(name = "currency")
    private String currency;

    //利息
    @Column(name = "interest")
    private BigDecimal interest;

    @Column(name = "status")
    private Integer status;
}
