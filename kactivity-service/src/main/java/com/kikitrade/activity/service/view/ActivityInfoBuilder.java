package com.kikitrade.activity.service.view;

import com.kikitrade.activity.api.model.ActivityRequest;
import com.kikitrade.activity.api.model.activity.ActivityInfoVO;
import com.kikitrade.activity.dal.mysql.model.ActivityMaterial;
import com.kikitrade.activity.service.config.KactivityMaterialTemplateConfig;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface ActivityInfoBuilder {

    default void build(ActivityRequest activityRequest, ActivityInfoVO activityInfoVO, Map<String, ActivityMaterial> material){
        //拉取物料配置模版
        List<KactivityMaterialTemplateConfig.MaterialTemplate> templateList = KactivityMaterialTemplateConfig.getValue().stream().filter(materialTemplate ->
                materialTemplate.getLocale().equals(activityRequest.getLocale()) || "all".equals(materialTemplate.getLocale())).collect(Collectors.toList());
        //构建活动对象
        doBuild(activityRequest, activityInfoVO, material, templateList);
    }

    void doBuild(ActivityRequest activityRequest, ActivityInfoVO activityInfoVO, Map<String, ActivityMaterial> material, List<KactivityMaterialTemplateConfig.MaterialTemplate> templateList);
}
