package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.kikitrade.activity.dal.tablestore.builder.CouponConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.dal.tablestore.model.CouponConfig;
import com.kikitrade.activity.dal.tablestore.model.TaskConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/8 17:23
 */
@Component
public class CouponConfigBuilderImpl extends WideColumnStoreBuilder<CouponConfig> implements CouponConfigBuilder {

    @PostConstruct
    public void init() {
        init(CouponConfig.class);
    }

    @Override
    public Boolean batchInsert(List<CouponConfig> couponConfigs) {
        return batchPutRow(couponConfigs, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public List<CouponConfig> batchGetByCode(List<String> codes, String saasId, String businessType) {
        if(CollectionUtils.isEmpty(codes)) {
            return List.of();
        }
        List<CouponConfig> couponConfigs = codes.stream().filter(StringUtils::isNotBlank).map(code -> {
            CouponConfig config = new CouponConfig();
            config.setCouponCode(code);
            config.setSaasId(saasId);
            config.setBusinessType(businessType);
            return config;
        }).collect(Collectors.toList());
        return batchGetRow(couponConfigs);
    }

    @Override
    public CouponConfig getConfigByPK(String code, String saasId, String businessType) {
        CouponConfig config = new CouponConfig();
        config.setCouponCode(code);
        config.setSaasId(saasId);
        config.setBusinessType(businessType);
        return super.getRow(config);
    }
}
