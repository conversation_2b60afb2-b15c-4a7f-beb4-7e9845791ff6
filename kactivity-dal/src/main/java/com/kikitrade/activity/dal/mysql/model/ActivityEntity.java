package com.kikitrade.activity.dal.mysql.model;

import com.kikitrade.activity.dal.IdCustomer;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@Table(name = "activity_entity")
public class ActivityEntity extends BaseModel {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @KeySql(genId = IdCustomer.class)
    private String id;

    @Column(name = "name")
    private String name;

    @Column(name = "type")
    private String type;

    @Column(name = "start_time")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    private Date startTime;

    @Column(name = "end_time")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    private Date endTime;

    @Column(name = "remark")
    private String remark;

    @Column(name = "status")
    private Integer status;

    @Column(name = "auto_create_batch")
    private Boolean autoCreateBatch;

    //批次创建周期 one、每一天
    @Column(name = "cycle")
    private String cycle;

    //下一次创建批次时间
    @Column(name = "next_create_time")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    private Date nextCreateTime;

    //活动规则
    @Column(name = "reward_config")
    private String rewardConfig;

    @Column(name = "area")
    private String area;

    @Column(name = "source")
    private String source;

    @Column(name = "sub_type")
    private String subType;

    @Column(name = "task_id")
    private String taskId;

    @Column(name = "auto_approve")
    private Boolean autoApprove;

    @Column(name = "template_code")
    private String templateCode;

    @Column(name = "conditions")
    private String conditions;
}
