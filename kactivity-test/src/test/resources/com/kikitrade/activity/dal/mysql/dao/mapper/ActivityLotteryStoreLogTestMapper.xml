<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityLotteryStoreLogTestDao">
    <resultMap id="BaseResultMap" type="com.kikitrade.activity.dal.mysql.model.ActivityLotteryStoreLog">
    </resultMap>

    <update id="createTableIfNotExist">

        CREATE TABLE `activity_lottery_store_log`
        (
            `id`       varbinary(64) NOT NULL,
            `time`     bigint(20) DEFAULT NULL COMMENT '抽奖时间',
            `status`   tinyint(4) DEFAULT NULL COMMENT '操作十分有效',
            `created`  datetime    NOT NULL COMMENT '创建时间',
            `modified` datetime    NOT NULL COMMENT '修改时间',
            `saasId`   varchar(16) NOT NULL COMMENT 'saasId',
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8
        ;


    </update>

</mapper>

