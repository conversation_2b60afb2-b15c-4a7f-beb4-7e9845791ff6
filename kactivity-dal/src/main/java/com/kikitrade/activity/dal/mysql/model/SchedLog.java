package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/8/21 21:37
 */
@Getter
@Setter
@Table(name = "sched_log")
public class SchedLog extends BaseModel {

    //主键
    @Column(name = "id")
    private Integer id;

    //任务名字
    @Column(name = "job_nm")
    private String job_nm;

    //调度周期 hh:小时 dd:天 mi:分钟
    @Column(name = "sched_tp")
    private String sched_tp;

    //业务日期
    @Column(name = "bizdate")
    private String bizdate;

    //调度批次
    @Column(name = "batch_pt")
    private Date batch_pt;

    //结束时间
    @Column(name = "end_time")
    private Date end_time;

    //是否逻辑删除
    @Column(name = "is_deleted")
    private String is_deleted;

    //备注
    @Column(name = "remark")
    private String remark;


}
