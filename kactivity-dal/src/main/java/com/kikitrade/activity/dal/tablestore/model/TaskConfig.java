package com.kikitrade.activity.dal.tablestore.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicloud.openservices.tablestore.model.search.FieldSchema;
import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/20 11:22
 */
@Data
@Table(name = "task_config")
public class TaskConfig implements Serializable {

    public static final String SEARCH_TASK_CONFIG = "search_task_config";

    @PartitionKey(name = "task_id")
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "task_id")
    private String taskId;

    @Column(name = "group_id", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "group_id")
    private String groupId;

    @Column(name = "show_list", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "show_list", fieldType = FieldType.BOOLEAN)
    private Boolean showList;

    @Column(name = "title", isDefined = true)
    private String title;

    @Column(name = "desc", isDefined = true)
    private String desc;

    @Column(name = "label_name")
    private String labelName;

    @Column(name = "label_color")
    private String labelColor;

    /**
     * 任务图片
     */
    @Column(name = "image", isDefined = true)
    private String image;

    @Column(name = "saasId", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "saasId")
    private String saasId;

    /**
     * 任务状态
     * @see ActivityConstant.CommonStatus
     */
    @Column(name = "status", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "status")
    private String status;

    @Column(name = "start_time", isDefined = true, type = Column.Type.INTEGER)
    private Date startTime;

    @Column(name = "end_time", isDefined = true, type = Column.Type.INTEGER)
    private Date endTime;

    @Column(name = "code", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "code")
    private String code;

    @Column(name = "show_code")
    private String showCode;

    /**
     * 周期任务上线
     */
    @Column(name = "limit", isDefined = true, type = Column.Type.INTEGER)
    private Integer limit;

    @Column(name = "limit_map", isDefined = true, type = Column.Type.STRING)
    private String limitMap;

    /**
     * 奖励频率
     */
    @Column(name = "reward_frequency", isDefined = true, type = Column.Type.INTEGER)
    private Integer rewardFrequency;

    /**
     * 任务周期
     * @see ActivityTaskConstant.TaskCycleEnum
     */
    @Column(name = "cycle", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "cycle")
    private String cycle;

    /**
     * 任务进度计算方式
     * @see ActivityTaskConstant.ProgressTypeEnum
     */
    @Column(name = "progress_type", isDefined = true)
    private String progressType;

    /**
     * 奖励方式
     * @see ActivityTaskConstant.RewardForm
     */
    @Column(name = "reward_form", isDefined = true)
    private String rewardForm;

    /**
     * 奖励方式
     * @see ActivityTaskConstant.ProvideType
     */
    @Column(name = "provide_type", isDefined = true)
    private String provideType;

    /**
     * 任务详情页
     */
    @Column(name = "url")
    private String url;

    /**
     * 任务授权页
     */
    @Column(name = "connect_url")
    private String connectUrl;

    @Column(name = "connect_url_pc")
    private String connectUrlPc;

    /**
     * 奖品
     */
    @Column(name = "reward", isDefined = true)
    private String reward;

    @Column(name = "order", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "order", fieldType = FieldType.LONG)
    private Integer order;

    @Column(name = "filter", isDefined = true)
    private String filter;

    @Column(name = "domain", isDefined = true)
    private String domain;

    @Column(name = "attr")
    private String attr;

    /**
     * 允许的最低vip等级
     * 0：注册用户
     * 1：1级会员
     * 0+：大于等于注册用户
     * 1+：大于等于1级会员
     */
    @Column(name = "vip_level")
    private String vipLevel;

    @Column(name = "btn")
    private Integer btn;

    @Column(name = "link")
    private String link;

    @Column(name = "showProgress", type = Column.Type.BOOLEAN)
    private Boolean showProgress;

    @Column(name = "channel", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "channel", fieldType = FieldType.TEXT,
            analyzer = FieldSchema.Analyzer.Split, delimiter = ",")
    private String channel;

    @Column(name = "position", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "position")
    private String position;

    @Column(name = "ledger_title")
    private String ledgerTitle;

    @Column(name = "call_register", type = Column.Type.BOOLEAN)
    private Boolean callRegister;

    @Column(name = "last_post", type = Column.Type.BOOLEAN)
    private Boolean lastPost;

    @Column(name = "client_type", type = Column.Type.STRING)
    private String clientType;

    @Column(name = "skip_verification", type = Column.Type.BOOLEAN)
    private Boolean skipVerification;

    @Column(name = "type", type = Column.Type.STRING)
    private String type;

    @Column(name = "show_list_v1", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "show_list_v1", fieldType = FieldType.BOOLEAN)
    private Boolean showListV1;

    @Column(name = "title_v1", isDefined = true)
    private String titleV1;

    @Column(name = "desc_v1", isDefined = true)
    private String descV1;

    @Column(name = "label_name_v1")
    private String labelNameV1;

    @Column(name = "label_color_v1")
    private String labelColorV1;

    /**
     * 任务图片
     */
    @Column(name = "image_v1", isDefined = true)
    private String imageV1;

    @Column(name = "start_time_v1", isDefined = true, type = Column.Type.INTEGER)
    private Date startTimeV1;

    @Column(name = "end_time_v1", isDefined = true, type = Column.Type.INTEGER)
    private Date endTimeV1;

    @Column(name = "show_code_v1")
    private String showCodeV1;

    /**
     * 周期任务上线
     */
    @Column(name = "limit_v1", isDefined = true, type = Column.Type.INTEGER)
    private Integer limitV1;

    @Column(name = "limit_map_v1", isDefined = true, type = Column.Type.STRING)
    private String limitMapV1;

    /**
     * 奖励频率
     */
    @Column(name = "reward_frequency_v1", isDefined = true, type = Column.Type.INTEGER)
    private Integer rewardFrequencyV1;

    /**
     * 任务周期
     * @see ActivityTaskConstant.TaskCycleEnum
     */
    @Column(name = "cycle_v1", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "cycle_v1")
    private String cycleV1;

    /**
     * 任务进度计算方式
     * @see ActivityTaskConstant.ProgressTypeEnum
     */
    @Column(name = "progress_type_v1", isDefined = true)
    private String progressTypeV1;

    /**
     * 奖励方式
     * @see ActivityTaskConstant.RewardForm
     */
    @Column(name = "reward_form_v1", isDefined = true)
    private String rewardFormV1;

    /**
     * 奖励方式
     * @see ActivityTaskConstant.ProvideType
     */
    @Column(name = "provide_type_v1", isDefined = true)
    private String provideTypeV1;

    /**
     * 任务详情页
     */
    @Column(name = "url_v1")
    private String urlV1;

    /**
     * 任务授权页
     */
    @Column(name = "connect_url_v1")
    private String connectUrlV1;

    @Column(name = "connect_url_pc_v1")
    private String connectUrlPcV1;

    /**
     * 奖品
     */
    @Column(name = "reward_v1", isDefined = true)
    private String rewardV1;

    @Column(name = "order_v1", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "order_v1", fieldType = FieldType.LONG)
    private Integer orderV1;

    @Column(name = "filter_v1", isDefined = true)
    private String filterV1;

    @Column(name = "domain_v1", isDefined = true)
    private String domainV1;

    @Column(name = "attr_v1")
    private String attrV1;

    /**
     * 允许的最低vip等级
     * 0：注册用户
     * 1：1级会员
     * 0+：大于等于注册用户
     * 1+：大于等于1级会员
     */
    @Column(name = "vip_level_v1")
    private String vipLevelV1;

    @Column(name = "btn_v1")
    private Integer btnV1;

    @Column(name = "link_v1")
    private String linkV1;

    @Column(name = "showProgress_v1", type = Column.Type.BOOLEAN)
    private Boolean showProgressV1;

    @Column(name = "channel_v1", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "channel_v1", fieldType = FieldType.TEXT,
        analyzer = FieldSchema.Analyzer.Split, delimiter = ",")
    private String channelV1;

    @Column(name = "position_v1", isDefined = true)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "position_v1")
    private String positionV1;

    @Column(name = "ledger_title_v1")
    private String ledgerTitleV1;

    @Column(name = "call_register_v1", type = Column.Type.BOOLEAN)
    private Boolean callRegisterV1;

    @Column(name = "last_post_v1", type = Column.Type.BOOLEAN)
    private Boolean lastPostV1;

    @Column(name = "client_type_v1", type = Column.Type.STRING)
    private String clientTypeV1;

    @Column(name = "skip_verification_v1", type = Column.Type.BOOLEAN)
    private Boolean skipVerificationV1;

    @Column(name = "type_v1", type = Column.Type.STRING)
    private String typeV1;
    @Column(name = "check_reward", type = Column.Type.STRING)
    @SearchIndex(name = SEARCH_TASK_CONFIG, column = "check_reward")
    private String checkReward;
    @Column(name = "postTaskId", type = Column.Type.STRING)
    private String postTaskId;

    @Column(name = "postTaskCode", type = Column.Type.STRING)
    private String postTaskCode;

    @Column(name = "postTaskDesc", type = Column.Type.STRING)
    private String postTaskDesc;

    @Column(name = "postTaskReward", type = Column.Type.INTEGER)
    private Integer postTaskReward;


    public Map<String, String> getImageMap(){
        if(StringUtils.isBlank(this.image)){
            return new HashMap<>();
        }
        return JSON.parseObject(this.image, Map.class);
    }

    public Map<String, String> getImageV1Map(){
        if(StringUtils.isBlank(this.imageV1)){
            return new HashMap<>();
        }
        return JSON.parseObject(this.imageV1, Map.class);
    }

    public Map<String, String> getAttrMap(){
        if(StringUtils.isBlank(this.attr)){
            return new HashMap<>();
        }
        return JSON.parseObject(this.attr, Map.class);
    }
    public Map<String, String> getAttrV1Map(){
        if(StringUtils.isBlank(this.attrV1)){
            return new HashMap<>();
        }
        return JSON.parseObject(this.attrV1, Map.class);
    }

    public Map<String, List<Award>> getRewardMap(){
        if(StringUtils.isBlank(this.reward)){
            return new HashMap<>();
        }
        Map<String, JSONArray> object = JSON.parseObject(this.reward, Map.class);
        Map<String, List<Award>> r = new HashMap<>();
        for(Map.Entry<String, JSONArray> entry : object.entrySet()){
            List<Award> list = new ArrayList<>();
            for(JSONObject j : entry.getValue().toJavaList(JSONObject.class)){
                Award o = j.toJavaObject(Award.class);
                list.add(o);
            }
            r.put(entry.getKey(), list);
        }
        return r;
    }
    public Map<String, List<Award>> getRewardV1Map(){
        if(StringUtils.isBlank(this.rewardV1)){
            return new HashMap<>();
        }
        Map<String, JSONArray> object = JSON.parseObject(this.rewardV1, Map.class);
        Map<String, List<Award>> r = new HashMap<>();
        for(Map.Entry<String, JSONArray> entry : object.entrySet()){
            List<Award> list = new ArrayList<>();
            for(JSONObject j : entry.getValue().toJavaList(JSONObject.class)){
                Award o = j.toJavaObject(Award.class);
                list.add(o);
            }
            r.put(entry.getKey(), list);
        }
        return r;
    }

    public Map<String, String> getLinkMap(){
        if(this.link == null){
            return new HashMap<>();
        }
        return JSON.parseObject(this.link, Map.class);
    }
    public Map<String, String> getLinkV1Map(){
        if(this.linkV1 == null){
            return new HashMap<>();
        }
        return JSON.parseObject(this.linkV1, Map.class);
    }
}
