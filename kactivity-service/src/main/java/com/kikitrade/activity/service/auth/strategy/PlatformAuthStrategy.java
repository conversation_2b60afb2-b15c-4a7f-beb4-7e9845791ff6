package com.kikitrade.activity.service.auth.strategy;

import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.model.OpenAuthRequest;
import com.kikitrade.activity.service.rpc.SocialUserInfo;

/**
 * 平台认证策略接口
 * 定义不同社交平台的认证处理逻辑
 * 
 * <AUTHOR>
 * @date 2025/7/24
 */
public interface PlatformAuthStrategy {

    /**
     * 获取平台名称
     * @return 平台名称
     */
    String getPlatformName();

    /**
     * 执行认证流程
     * @param context 认证上下文
     * @return 认证结果
     * @throws ActivityException 认证异常
     */
    AuthResult authenticate(AuthContext context) throws ActivityException;

    /**
     * 构建认证请求
     * @param saasId SaaS ID
     * @param code 授权码
     * @param redirectUri 重定向URI
     * @return 认证请求对象
     */
    OpenAuthRequest buildAuthRequest(String saasId, String code, String redirectUri);

    /**
     * 构建刷新令牌请求
     * @param saasId SaaS ID
     * @param refreshToken 刷新令牌
     * @return 认证请求对象
     */
    OpenAuthRequest buildRefreshRequest(String saasId, String refreshToken);

    /**
     * 获取当前用户信息
     * @param saasId SaaS ID
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @return 用户信息
     */
    SocialUserInfo getCurrentUser(String saasId, String accessToken, String refreshToken);

    /**
     * 是否支持刷新令牌
     * @return true表示支持，false表示不支持
     */
    default boolean supportsRefreshToken() {
        return true;
    }
}
