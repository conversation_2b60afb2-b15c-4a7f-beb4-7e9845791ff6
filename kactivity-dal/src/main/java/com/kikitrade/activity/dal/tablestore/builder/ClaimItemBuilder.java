package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.ClaimItem;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/9 11:24
 */
public interface ClaimItemBuilder {

    Boolean batchInsert(List<ClaimItem> claimItems);

    Boolean insert(ClaimItem claimItems);

    ClaimItem getById(String businessType, String code, String created);

    Boolean exist(String businessType, String customerId, String created);

    long countByIds(String businessType, List<String> code, String created);
}
