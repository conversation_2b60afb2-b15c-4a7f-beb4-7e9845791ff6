package com.kikitrade.activity.dal.mysql.model;

import com.kikitrade.activity.dal.IdCustomer;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 物料表
 */
@Data
@Table(name = "activity_material")
public class ActivityMaterial extends BaseModel{

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @KeySql(genId = IdCustomer.class)
    private String id;

    @Column(name = "activity_id")
    private String activityId;

    @Column(name = "code")
    private String code;

    @Column(name = "value")
    private String value;

    @Column(name = "status")
    private Integer status;

    public static String getTableName(){
        return "activity_material";
    }
}
