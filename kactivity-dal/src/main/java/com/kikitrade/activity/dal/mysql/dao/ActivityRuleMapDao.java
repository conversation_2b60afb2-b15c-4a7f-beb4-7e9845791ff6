package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityRuleMap;
import com.kikitrade.framework.mybatis.BaseDao;

import java.util.List;

public interface ActivityRuleMapDao extends BaseDao<ActivityRuleMap, String> {

    int insert(ActivityRuleMap aam);

    int deleteById(Integer activity_id);

    List<ActivityRuleMap> findByActivityId(Integer activity_id);

    List<ActivityRuleMap> findAll(Integer offset, Integer limit);

    int batchInsert(List<ActivityRuleMap> activityRuleMapList);
}
