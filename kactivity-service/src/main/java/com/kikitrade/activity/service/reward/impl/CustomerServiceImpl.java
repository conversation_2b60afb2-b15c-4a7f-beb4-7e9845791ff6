package com.kikitrade.activity.service.reward.impl;

import com.kikitrade.kcustomer.api.constants.SecurityVerifyScene;
import com.kikitrade.kcustomer.api.model.block.SecurityVerifyRequest;
import com.kikitrade.kcustomer.api.model.block.SecurityVerifyResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.common.config.RateLimiterConfig;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.model.CustomerMiscDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteBlockBusinessService;
import com.kikitrade.kcustomer.api.model.CustomerInviteDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerInviteService;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.ksocial.api.model.dto.CustomerKolDTO;
import com.kikitrade.ksocial.api.service.RemoteSocialService;
import com.kikitrade.ksocial.common.constants.CustomerConstants;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.model.request.MembershipRequest;
import com.kikitrade.member.model.response.MembershipResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {

    @DubboReference
    public RemoteCustomerService remoteCustomerService;
    @DubboReference
    public RemoteBlockBusinessService remoteBlockBusinessService;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private RedisService redisService;
    @Resource
    private RateLimiterConfig rateLimiterConfig;
    @DubboReference
    private RemoteSocialService remoteSocialService;
    @DubboReference
    private RemoteMemberService remoteMemberService;
    @DubboReference
    private RemoteCustomerInviteService remoteCustomerInviteService;

    @Override
    public CustomerCacheDTO getById(String customerId) {
        String saasId = kactivityProperties.getSaasId();
        String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_CUSTOMER_KEY.getPrefix(), saasId + customerId);
        try{
            String customerCache = redisService.get(key);
            if (!StringUtils.isEmpty(customerCache)) {
                return JSON.parseObject(customerCache, CustomerCacheDTO.class);
            }
            rateLimiterConfig.acquire("remoteCustomerService");
            CustomerDTO customer = remoteCustomerService.getById(saasId, customerId);
            if (customer != null) {
                CustomerCacheDTO cacheDTO = CustomerCacheDTO.toEntity(customer);
                redisService.setIfAbsent(key, JSON.toJSONString(cacheDTO), TimeUnit.HOURS.toSeconds(4));
                return cacheDTO;
            }
        }catch (Exception ex){
            log.error("customerService getById error:{}", customerId,ex);
        }
        return null;
    }

    /**
     * 查询社区信息
     * @param customerId
     * @return
     */
    @Override
    public CustomerMiscDTO getMiscByCustomerId(String customerId){
        CustomerCacheDTO customerCacheDTO = getById(customerId);
        if(customerCacheDTO == null){
            return new CustomerMiscDTO();
        }
        CustomerMiscDTO customerMiscDTO = new CustomerMiscDTO();
        customerMiscDTO.setNickName(customerCacheDTO.getNickName());
        customerMiscDTO.setAvatar(customerCacheDTO.getIcon());
        return customerMiscDTO;
    }
    /**
     * 查询用户信息
     * @param userName
     * @return
     */
    @Override
    public CustomerCacheDTO getByPhoneOrEmail(String userName) {
        String saasId = kactivityProperties.getSaasId();
        String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_CUSTOMER_KEY.getPrefix(), saasId + userName);
        try{
            String customerCache = redisService.get(key);
            if (!StringUtils.isEmpty(customerCache)) {
                return JSON.parseObject(customerCache, CustomerCacheDTO.class);
            }
            rateLimiterConfig.acquire("remoteCustomerService");
            CustomerDTO customer = remoteCustomerService.getByUserName(saasId, userName);
            if (customer != null) {
                CustomerCacheDTO cacheDTO = CustomerCacheDTO.toEntity(customer);
                redisService.setIfAbsent(key, JSON.toJSONString(cacheDTO), TimeUnit.HOURS.toSeconds(4));
                return cacheDTO;
            }
        }catch (Exception ex){
            log.error("customerService getByPhone error:{}", userName,ex);
        }
        return null;
    }
    /**
     * 查询用户信息
     *
     * @param customIds
     * @return
     */
    @Override
    public List<CustomerCacheDTO> getByIds(List<String> customIds) {
        Set<String> set = new HashSet<>(customIds);
        List<String> list = new ArrayList<>(set);

        String saasId = kactivityProperties.getSaasId();
        List<String> customIdList = new ArrayList<>();
        List<CustomerDTO> customerDTOS = new ArrayList<>();
        for(int i = 0; i < list.size(); i++){
            customIdList.add(list.get(i));
            if(i % 20 == 0 && i > 0){
                List<CustomerDTO> dtoList = listByIds(saasId, customIdList, false);
                if(CollectionUtils.isNotEmpty(dtoList)){
                    customerDTOS.addAll(dtoList);
                }
                customIdList = new ArrayList<>();
            }
        }
        if(customIdList.size() > 0){
            List<CustomerDTO> dtoList = listByIds(saasId, customIdList, false);
            if(CollectionUtils.isNotEmpty(dtoList)){
                customerDTOS.addAll(dtoList);
            }
        }
        if(CollectionUtils.isEmpty(customerDTOS)){
            return new ArrayList<>();
        }
        List<CustomerCacheDTO> result = new ArrayList<>();
        for(CustomerDTO customerDTO : customerDTOS){
            try{
                CustomerCacheDTO cacheDTO = CustomerCacheDTO.toEntity(customerDTO);
                String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_CUSTOMER_KEY.getPrefix(), saasId + cacheDTO.getId());
                redisService.setIfAbsent(key, JSON.toJSONString(cacheDTO), TimeUnit.HOURS.toSeconds(4));
                result.add(cacheDTO);
            }catch (Exception e){}
        }
        return result;
    }

    private List<CustomerDTO> listByIds(String saasId, List<String> customerIds, boolean isSimple){
        rateLimiterConfig.acquire("remoteCustomerServiceList");
        return remoteCustomerService.listByIds(saasId, customerIds, false);
    }

    /**
     * 获取用户邀请信息
     * @param customerId
     * @return
     */
    @Override
    public String getInviteInfo(String customerId){
        try{
            CustomerInviteDTO inviteDTO = remoteCustomerInviteService.inviterByCustomerId(kactivityProperties.getSaasId(), customerId);
            if(inviteDTO == null){
                return "";
            }
            return inviteDTO.getInviterId();
        }catch (Exception ex){
            log.error("CustomerService getInviteInfo error:{}", customerId, ex);
            return null;
        }
    }

    @Override
    public String getKolType(String customerId){
        try{
            CustomerKolDTO socialKol = remoteSocialService.getSocialKol(kactivityProperties.getSaasId(), customerId);
            if(socialKol != null){
                return socialKol.getRole().name();
            }
        }catch (Exception ex){
            log.error("remoteSocialService getKolType exception:{}", customerId ,ex);
        }
        return CustomerConstants.KolRole.COMMON.name();
    }

    /**
     * 获取vip等级
     *
     * @param customerId
     * @return
     */
    @Override
    public String getVipLevel(String customerId) {
        MembershipRequest request = new MembershipRequest();
        request.setCustomerId(customerId);
        request.setSaasId(kactivityProperties.getSaasId());
        MembershipResponse membership = remoteMemberService.membership(request);
        String vipLevel;
        if(membership == null || membership.getMembership() == null){
            vipLevel = "L0";
        }else{
            vipLevel = membership.getMembership().getLevel();
        }
        return vipLevel == null ? "L0" : vipLevel;
    }

    @Override
    public SecurityVerifyResponse verify(String customerId) {
        SecurityVerifyRequest request = SecurityVerifyRequest.builder().customerId(customerId).scene(SecurityVerifyScene.REWARD).build();
        SecurityVerifyResponse response = remoteBlockBusinessService.verify(request);
        return response;
    }
}
