package com.kikitrade.activity.service.common.strategy.campus;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4 17:10
 */
@Component
@Slf4j
public class CampusCheckTrexStrategyService implements CampusSaasStrategyService {

    @Resource
    private RemoteCustomerBindService remoteCustomerBindService;

    @Override
    public String strategy() {
        return SaasStrategyConstant.CampusStrategyEnum.CHECK_TREX.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        List<TCustomerDTO> campus =
            remoteCustomerBindService.findTCustomerBySocial(request.getSaasId(), "campus", request.getAuthEmail());
        //如果campus不为空，则表示邮箱已经被别人绑定了，直接拒绝
        if (campus != null && !campus.isEmpty()) {
            log.info("campus check trex auth repeat:{}", request);
            throw new ActivityException(ActivityResponseCode.AUTH_REPEAT, ActivityResponseCode.AUTH_REPEAT.getKey());
        }
    }
}
