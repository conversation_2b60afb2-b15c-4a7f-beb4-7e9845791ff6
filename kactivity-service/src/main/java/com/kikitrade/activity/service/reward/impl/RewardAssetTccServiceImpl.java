package com.kikitrade.activity.service.reward.impl;

import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.asset.model.request.AssetTransferOutRequest;
import org.apache.dubbo.config.annotation.DubboReference;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.common.config.RateLimiterConfig;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.asset.api.RemoteAssetOperateService;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.asset.model.request.AssetTransferInRequest;
import com.kikitrade.asset.model.response.AssetOperateResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.math.BigDecimal;

@Slf4j
@Service("rewardAssetTccService")
public class RewardAssetTccServiceImpl extends AbstractRewardTccService implements RewardTccService {

    @Resource
    private KactivityProperties kactivityProperties;

    @DubboReference
    private RemoteAssetOperateService remoteAssetOperateService;

    @Resource
    private RateLimiterConfig rateLimiterConfig;

    /**
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public void tryReward(RewardRequest request) throws Exception {
        rateLimiterConfig.acquire("rewardAssert");

        log.info("[reward] transfer request:{}", request);
        String saasId = request.getSaasId() == null ? kactivityProperties.getSaasId() : request.getSaasId();
        AssetOperateResponse assetOperateResponse = null;
        if (request.getAmount().compareTo(BigDecimal.ZERO) == 0) {
            log.info("[reward] invalid amount, no transfer operation required");
            return;
        } else if (request.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            AssetTransferInRequest assetTransferInRequest = AssetTransferInRequest.builder()
                    .businessId(TimeUtil.getCurrentUtcTime("yyyyMMddHHmmss") + request.getRewardId() + AssetType.valueOf(request.getType()).code())
                    .customerId(request.getCustomerId())
                    .businessType("lottery".equals(request.getBusinessType()) ? AssetBusinessType.ACTIVITY_LOTTERY : AssetBusinessType.valueOfByCodeDesc(request.getBusinessType()))
                    .type(AssetType.valueOf(request.getType()))
                    .amount(request.getAmount())
                    .saasId(saasId)
                    .desc(request.getDesc())
                    .build();
            assetOperateResponse = remoteAssetOperateService.transferIn(assetTransferInRequest);
        } else {
            AssetTransferOutRequest transferOutRequest = AssetTransferOutRequest.builder()
                    .businessId(TimeUtil.getCurrentUtcTime("yyyyMMddHHmmss") + request.getRewardId())
                    .customerId(request.getCustomerId())
                    .businessType("lottery".equals(request.getBusinessType()) ? AssetBusinessType.ACTIVITY_LOTTERY : AssetBusinessType.valueOfByCodeDesc(request.getBusinessType()))
                    .type(AssetType.valueOf(request.getType()))
                    .amount(request.getAmount().abs())
                    .saasId(saasId)
                    .desc(request.getDesc())
                    .build();
            if (SaasConfigLoader.getConfig(saasId).getAllowNegativeAsset()) {
                transferOutRequest.setStrictCheck(false);
            }
            assetOperateResponse = remoteAssetOperateService.transferOut(transferOutRequest);
        }
        log.info("[reward] transfer response:{}", assetOperateResponse);
    }

}
