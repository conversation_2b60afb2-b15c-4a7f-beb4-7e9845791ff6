package com.kikitrade.activity.luck.service.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.service.business.ReceiveService;
import com.kikitrade.activity.luck.service.business.ReleaseService;
import com.kikitrade.activity.luck.service.model.AirDropEventDTO;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.framework.ons.OnsMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-14 15:54
 */
@Component
@Slf4j
public class AirDropEventListener implements OnsMessageListener {

    @Resource
    private KactivityModuleProperties kactivityModuleProperties;
    @Resource
    private ReleaseService releaseService;
    @Resource
    private ReceiveService receiveService;

    @Override
    public String topic() {
        return kactivityModuleProperties.getLuck().getEventTopic();
    }

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        log.info("AirDropEventListener message;{}", message);
        AirDropEventDTO eventDTO = JSON.parseObject(new String(message.getBody()), AirDropEventDTO.class);
        ActivityConstant.AirDropEventEnum eventEnum = ActivityConstant.AirDropEventEnum.valueOf(eventDTO.getEvent());
        switch (eventEnum){
            case REVERT:
                return revert(eventDTO);
            case EXPIRE:
                return expire(eventDTO);
            case REFUND:
                return refund(eventDTO);
        }
        return Action.CommitMessage;
    }

    /**
     * 回归领取的空投到空投队列
     * @param eventDTO
     * @return
     */
    private Action revert(AirDropEventDTO eventDTO){
        try{
            receiveService.revert(eventDTO.getReleaseId(), eventDTO.getReceiveId(), eventDTO.getReceiveCode());
            return Action.CommitMessage;
        }catch (Exception ex){
            log.error("AirDropEventListener exception {}", eventDTO ,ex);
            return Action.ReconsumeLater;
        }
    }

    /**
     * 过期空投
     * @param eventDTO
     * @return
     */
    private Action expire(AirDropEventDTO eventDTO){
        try{
            LuckFortuneReleaseItem refund = releaseService.findById(eventDTO.getReleaseId());
            if(refund.getCustomerId().equals(eventDTO.getReleaseCustomerId())){
                releaseService.expire(refund);
            }
            return Action.CommitMessage;
        }catch (Exception ex){
            log.error("AirDropEventListener exception {}", eventDTO ,ex);
            return Action.ReconsumeLater;
        }
    }

    /**
     * 退款
     * @param eventDTO
     * @return
     */
    private Action refund(AirDropEventDTO eventDTO){
        try{
            LuckFortuneReleaseItem refund = releaseService.findById(eventDTO.getReleaseId());
            if(refund.getCustomerId().equals(eventDTO.getReleaseCustomerId())){
                releaseService.refund(refund);
            }
            return Action.CommitMessage;
        }catch (Exception ex){
            log.error("AirDropEventListener exception {}", eventDTO ,ex);
            return Action.ReconsumeLater;
        }
    }
}
