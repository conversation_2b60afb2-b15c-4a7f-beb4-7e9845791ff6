package com.kikitrade.activity.service.common.strategy;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/4 15:50
 */
public interface SaasStrategyConstant {

    enum TwitterStrategyEnum{
        REJECT, UNBIND, CHECK_OSP, CHECK_CREATE_DAYS, CHECK_TREX
    }

    enum DiscordStrategyEnum{
        REJECT, SKIP, CHECK_OSP, CHECK_TREX
    }

    enum GoogleStrategyEnum{
        REJECT, SKIP, CHECK_TREX,
    }

    enum FacebookStrategyEnum{
        REJECT, SKIP, CHECK_TREX
    }

    enum LineStrategyEnum{
        REJECT, SKIP, CHECK_TREX
    }

    enum EmailStrategyEnum{
        REJECT, SKIP, CHECK_TREX
    }

    enum TelegramStrategyEnum{
        REJECT, SKIP, CHECK_TREX
    }

    enum WalletStrategyEnum{
        REJECT, SKIP, CHECK_TREX
    }

    enum CampusStrategyEnum{
        CHECK_TREX
    }
}
