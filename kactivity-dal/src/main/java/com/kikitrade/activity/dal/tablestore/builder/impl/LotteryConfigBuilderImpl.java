package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.LotteryConfigBuilder;
import com.kikitrade.activity.dal.tablestore.model.LotteryConfig;
import com.kikitrade.activity.dal.tablestore.model.TaskConfig;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/6 15:01
 */
@Component
public class LotteryConfigBuilderImpl extends WideColumnStoreBuilder<LotteryConfig> implements LotteryConfigBuilder {

    @Resource
    private SeqClient seqClient;

    private SeqRule seqRule = new SeqRule("IDX_LOTTERY_CONFIG", 5, "1", null);

    @PostConstruct
    public void init() {
        super.init(LotteryConfig.class);
    }

    @Override
    public LotteryConfig findByCode(String code) {
        BoolQuery.Builder builder = QueryBuilders.bool()
            .must(QueryBuilders.term("code", code))
            .must(QueryBuilders.term("status", "ACTIVE"));
        return searchOne(builder.build() , LotteryConfig.SEARCH_LOTTERY_CODE);
    }

    @Override
    public boolean update(LotteryConfig lotteryConfig) {
        return super.updateRow(lotteryConfig);
    }

    @Override
    public boolean insert(LotteryConfig lotteryConfig) {
        return super.putRow(lotteryConfig, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public String nextId() {
        return seqClient.next(seqRule);
    }
}
