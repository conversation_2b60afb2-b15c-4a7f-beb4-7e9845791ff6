//package com.kikitrade.activity.mock.impl;
//
//import com.kikitrade.activity.mock.HttpPoolUtilMock;
//import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
//import jakarta.annotation.Resource;
//import org.mockito.Mockito;
//import org.springframework.stereotype.Component;
//
//@Component
//public class OkHttpClientMockImpl implements HttpPoolUtilMock {
//    private HttpPoolUtil httpPoolUtil;
//
//
//    @Override
//    public void getHttpClient() {
//        Mockito.doAnswer(c -> {
//            return null;
//
//        }).when(HttpPoolUtil.getHttpClient());
//    }
//}
