package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.ActivityTaskStatis;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/23 20:44
 */
public interface ActivityTaskStatisBuilder {

    String getTableName();

    boolean insert(ActivityTaskStatis activityTaskStatis);

    Long incrementProgress(ActivityTaskStatis activityTaskStatis, int incr);

    ActivityTaskStatis findDetail(ActivityTaskStatis taskStatis);
}
