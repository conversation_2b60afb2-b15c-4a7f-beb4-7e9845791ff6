package com.kikitrade.activity.service.lottery.impl;

import com.kikitrade.activity.service.lottery.LotteryService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/1/5 18:33
 */
@Component
public class LotteryServiceRoute {

    @Resource(name = "goldBoxLotteryService")
    private LotteryService goldBoxLotteryService;

    @Resource(name = "purpleGoldBoxLotteryService")
    private LotteryService purpleGoldBoxLotteryService;

    public LotteryService getService(String type) {
        if("purple-gold-box".equals(type)){
            return purpleGoldBoxLotteryService;
        }
        return goldBoxLotteryService;
    }
}
