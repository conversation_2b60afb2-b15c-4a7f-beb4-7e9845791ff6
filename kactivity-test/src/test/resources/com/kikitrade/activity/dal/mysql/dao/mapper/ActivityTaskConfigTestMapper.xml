<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityTaskConfigTestDao">
    <resultMap id="BaseResultMap" type="com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig">
        <result column="name_cn" property="nameCn"/>
        <result column="name_en" property="nameEn"/>
        <result column="name_hk" property="nameHk"/>
        <result column="activity_id" property="activityId"/>
        <result column="cycle_type" property="cycleType"/>
        <result column="complete_threshold" property="completeThreshold"/>
        <result column="desc_cn" property="descCn"/>
        <result column="desc_en" property="descEn"/>
        <result column="desc_hk" property="descHk"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="award_time_type" property="awardTimeType"/>
        <result column="complete_times" property="completeTimes"/>
        <result column="name_i18n" property="nameI18n"/>
        <result column="desc_i18n" property="descI18n"/>
    </resultMap>

    <update id="createTableIfNotExist">

        /******************************************/
/*   DatabaseName = exchange_new   */
/*   TableName = activity_task_config   */
/******************************************/
        CREATE TABLE `activity_task_config` (
                                                `id` varbinary(64) NOT NULL COMMENT '主键',
                                                `level` varchar(32) NOT NULL COMMENT '任务所属等级l0/l1/l2',
                                                `event` varchar(32) NOT NULL COMMENT '任务出发事件',
                                                `type` varchar(32) NOT NULL COMMENT '任务类型：日常任务、进阶任务',
                                                `name_cn` varchar(64) NOT NULL COMMENT '任务简体名称',
                                                `name_en` varchar(64) NOT NULL COMMENT '任务英文名称',
                                                `name_hk` varchar(64) NOT NULL COMMENT '任务繁体名称',
                                                `activity_id` varchar(64) NOT NULL COMMENT '活动id',
                                                `url` varchar(255) DEFAULT NULL COMMENT '任务完成页',
                                                `callback` varchar(255) DEFAULT NULL,
                                                `award_time_type` tinyint(4) NOT NULL COMMENT '发奖时间类型',
                                                `cycle_type` varchar(32) NOT NULL COMMENT '周期类型',
                                                `cycle` varchar(32) DEFAULT NULL COMMENT '周期',
                                                `complete_threshold` tinyint(4) DEFAULT NULL COMMENT '任务门槛',
                                                `desc_cn` text NOT NULL COMMENT '任务描述简体',
                                                `desc_en` text NOT NULL COMMENT '任务描述英文',
                                                `desc_hk` text NOT NULL COMMENT '活动描述繁体',
                                                `icon` varchar(255) DEFAULT NULL,
                                                `amount` decimal(10,2) NOT NULL COMMENT '奖励数量',
                                                `award` varchar(32) NOT NULL COMMENT '奖励类型，积分，道具',
                                                `status` tinyint(4) NOT NULL COMMENT '任务状态',
                                                `start_time` datetime NOT NULL COMMENT '任务开始时间',
                                                `end_time` datetime DEFAULT NULL COMMENT '任务结束时间',
                                                `created` datetime NOT NULL COMMENT '创建时间',
                                                `modified` datetime NOT NULL COMMENT '修改时间',
                                                `saasId` varchar(16) NOT NULL COMMENT 'saasId',
                                                `complete_times` int(11) DEFAULT NULL COMMENT '任务可完成次数',
                                                `name_i18n` varchar(64) DEFAULT NULL COMMENT '任务国际化名称',
                                                `desc_i18n` text COMMENT '任务国际化描述',
                                                PRIMARY KEY (`id`),
                                                KEY `idx_event` (`event`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='活动任务表'
        ;


    </update>
    <update id="update">

        update activity_task_config
        <set>
            <if test="status != null and status != ''">
                status = #{status}
            </if>
            <if test="completeThreshold != null and completeThreshold != ''">
                complete_threshold = #{completeThreshold}
            </if>
            <if test="endTime != null">
                end_time = #{endTime}
            </if>
            <if test="cycleType != null and cycleType != ''">
                cycle_type = #{cycleType}
            </if>
            <if test="cycle != null and cycle != ''">
                cycle = #{cycle}
            </if>
            <if test="completeTimes != null and completeTimes != ''">
                complete_times = #{completeTimes}
            </if>
            <if test="event != null and event != ''">
                event = #{event}
            </if>
            where id = '2022082405582497101000';
        </set>


    </update>
    <select id="selectAll" resultMap="BaseResultMap">


    </select>

    <insert id="init">
        INSERT INTO `activity_task_config` (`id`, `level`, `event`, `type`, `name_cn`, `name_en`, `name_hk`,
                                            `activity_id`, `url`, `callback`, `award_time_type`, `cycle_type`, `cycle`,
                                            `complete_threshold`, `desc_cn`, `desc_en`, `desc_hk`, `icon`, `amount`,
                                            `award`, `status`, `start_time`, `end_time`, `created`, `modified`,
                                            `saasId`, `complete_times`)
        VALUES ('2022082405582497101000', 'L0', 'POST', 'NORMAL', 'L0发布帖子', 'L0Post post', 'L0發佈帖子',
                '2022082405582496809807', '{"type": "internal", "page": "ReleaseLongPost", "index":0 }', null, 0,
                'CYCLE', 'DAILY', 1, '每发布1篇帖子奖励4积分', '4 points for every post', '每發佈1篇帖子獎勵4積分',
                'https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908155737540434__w:80__h:80.png',
                65.00, 'POINT', 0, '2022-12-20 01:30:00', '2023-05-31 06:16:21', '2022-08-24 05:58:25',
                '2023-01-01 07:29:12', 'kiki', 2);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082208072466000201','L2','SIGN_IN','NORMAL','L2每日签到','L2Sign in daily','L2每日簽到','2022082208072465609001','',null,0,'CYCLE','DAILY',1,'进入任务中心即完成签到','Enter the task center to complete the sign-in','進入任務中心即完成簽到','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908162414669993__w:80__h:80.png',1.00,'POINT',0,'2022-08-24 16:00:00',null,'2022-08-22 08:07:25','2022-11-03 07:12:33','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082209341211700400','L0','FOLLOW','NORMAL','关注用户（关注3完成2次）','Follow Users','關注用戶','2022082209341210309200','{"type": "internal", "page": "KolRanking", "index":0 }',null,0,'CYCLE','DAILY',3,'周期内完成关注3个用户任务可获得30个燃料值','30 fuel values can be obtained by completing the task of paying attention to 3 users in the cycle','週期內完成關注3個用戶任務可獲得30個燃料值','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908144619160992.png',30.00,'POINT',1,'2022-08-22 16:00:00',null,'2022-08-22 09:34:12','2022-09-08 06:46:19','kiki',2);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082306433105100600','L0','COMMENT','NORMAL','L0完成帖子评论','L0Complete comments','L0完成帖子的評論','2022082306433103109400','{"type": "internal", "page": "Social", "index":0 }',null,0,'CYCLE','DAILY',1,'每完成1篇帖子的评论奖励1积分','1 point for every completed comment on 1 post','每完成1篇帖子的評論獎勵1積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908161643390612.png',1.30,'POINT',0,'2022-08-24 08:00:00','2022-12-31 08:40:59','2022-08-23 06:43:31','2022-12-19 10:05:31','kiki',6);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082307054083900800','L0','LIKE','NORMAL','L0完成2篇帖子的点赞','L0Complete the likes of 2 posts','L0完成2篇帖子的點贊','2022082307054081509600','{"type": "internal", "page": "Social", "index":0 }',null,0,'CYCLE','DAILY',1,'完成2篇帖子的点赞奖励99积分','99 points will be awarded for completing the likes of 10 posts','完成2篇帖子的點贊獎勵99積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220829101332538883.webp',0.20,'POINT',0,'2022-09-05 12:30:00','2022-09-10 08:40:59','2022-08-23 07:05:41','2022-09-14 10:49:39','kiki',5);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082407031025801001','L0','NONE','ADVANCED','参与社区活动','Participate in community activities','參與社區活動','2022082407031025609810','{"type": "external", "page": "https://www.baidu.com", "index":0 }',null,1,'ONCE','DAILY',1,'参与活动奖励50积分','Reward 50 points for participating in the activity','參與活動獎勵50積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220829191031423106__w:680__h:680.jpeg',50.00,'POINT',0,'2022-08-22 07:00:58','2022-10-31 07:00:58','2022-08-24 07:03:10','2022-09-19 03:25:04','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082407072904201002','L0','NONE','ADVANCED','L0邀请好友活动','L0Invite friends to activities','L0邀請好友活動','2022082407072904009811',' {"type": "internal", "page": "InviteFriend", "index":0 }',null,1,'ONCE','DAILY',1,'L0完成邀请3位好友活动获得30积分','L0Invite 3 friends to get 30 points','L0完成邀請3位好友活動獲得30積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908163350508504.png',30.00,'POINT',0,'2022-08-22 07:00:58','2022-09-18 03:30:00','2022-08-24 07:07:29','2022-09-19 03:19:13','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082407245581801004','L0','NONE','ADVANCED','L0月累计净入金额度大于100USD','L0Monthly accumulated net deposit limit is greate','L0月累計淨入金額度大於100USD','2022082407245581609813','',null,1,'CYCLE','MONTHLY',1,'月累计净入金额度大于100USD获取200积分','L0The monthly accumulated net deposit limit is more than 100USD','月累計淨入金額度大於100USD獲取200積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908154052801918.png',200.00,'POINT',0,'2022-08-24 07:00:58','2022-10-31 07:00:58','2022-08-24 07:24:56','2022-09-19 03:24:23','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082409354642201200','L1','NONE','NORMAL','11{  "API_KEY": [   "db13b597-fd7a-49a9-8bc7-94ba855251",   "4be','1','1','2022082409354640710000','',null,0,'CYCLE','DAILY',1,'1{  "API_KEY": [   "db13b597-fd7a-49a9-8bc7-94ba85055251",   "4be16b1a-52e1-413c-8953-4f91983aa994"  ],  "EXCHANGE_CUSTOMER_FACADE_URL": "*************:13104",  "EXCHANGE_EX_PRODUCT_FACADE_URL": "api.dev.dipbit.xyz:13205",  "EXCHANGE_FACADE_URL": "api.dev.dipbit.xyz:8191",  "EXCHANGE_KACTIVITY_FACADE_URL": "api.dipbit.xyz:14504",  "EXCHANGE_KMEMBER_FACADE_URL": "api.dev.dipbit.xyz:15904",  "EXCHANGE_KFINANCE_FACADE_URL": "api.dev.dipbit.xyz:14404",  "EXCHANGE_KMARKET_FACADE_URL": "api.dev.dipbit.xyz:13205",  "EXCHANGE_K_ACCOUNTING_FACADE_URL": "api.dipbit.xyz:13004",  "EXCHANGE_PAY_FACADE_URL": "api.dipbit.xyz:13804",  "EXCHANGE_SOCIAL_FACADE_URL": "api.dev.dipbit.xyz:50880",  "EXCHANGE_TRADE_FACADE_URL": "api.dipbit.xyz:60880",  "EXCHANGE_VIBRA_FACADE_URL": "**************:13904",  "EXCHANGE_WALLET_FACADE_URL": "api.dev.dipbit.xyz:14004",  "EXCHANGE_ZONE_FACADE_URL": "api.dev.dipbit.xyz:13205",  "INVALID_COIN_CODE": [   "EUSDT",   "USD",   "HKD"  ],  "LANGUAGE_COUNTRY_MAPPING": {   "e','1','1','http://upload.dipbit.xyz/public/images/6392b786-f983-4d53-9e5e-72835e2644e8__w:1650__h:1100.jpeg',1.00,'POINT',0,'2022-08-25 15:46:37',null,'2022-08-24 09:35:46','2022-08-24 10:17:24','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082506512558801400','L1','SHARE_POST','NORMAL','L1分享文章','L1Share information','L1分享文章','2022082506512557110200','{"type": "internal", "page": "Social", "index":0 }',null,0,'CYCLE','DAILY',1,'每分享1篇文章获取3积分','Get 3 points for every article you share','每分享1篇文章獲取3積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908154658083986.png',3.00,'POINT',0,'2022-08-28 16:00:00',null,'2022-08-25 06:51:26','2022-09-08 07:47:27','kiki',6);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082902280403001600','L1','SIGN_IN','NORMAL','L1-每日签到','L1-Sign in daily','L1-每日簽到','2022082902280401110400','',null,0,'CYCLE','DAILY',1,'进入任务中心即完成签到','Enter the task center to complete the sign-in','進入任務中心即完成簽到','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908162330963464__w:80__h:80.png',1.00,'POINT',0,'2022-08-29 02:30:00',null,'2022-08-29 02:28:04','2022-11-03 07:11:55','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082906570139401800','L0','SHARE_POST','NORMAL','L0分享社区帖子（分1可完成3次）','L0Share community posts','L0分享社區帖子','2022082906570137510600',' {"type": "internal", "page": "Social", "index":0 }',null,0,'CYCLE','DAILY',1,'L0分享社区帖子1篇获得2积分','L0Share 1community posts and get 2 points','L0分享社區帖子1篇獲得2積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220829145659088873.webp',20000.00,'POINT',1,'2022-08-28 16:00:00','2022-09-07 16:00:00','2022-08-29 06:57:01','2022-09-16 11:05:03','kiki',4);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082912423378202000','L0','POST','NORMAL','L1任务-中文-发布五篇帖子获得50燃料值','L1任务-英文-发布五篇帖子获得50燃料值','L1任务-繁体-发布五篇帖子获得50燃料值','2022082912423376210800','',null,0,'CYCLE','DAILY',5,'L1任务-中文-发布五篇帖子获得50燃料值-测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数','L1任务-英文-发布五篇帖子获得50燃料值-测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数','L1任务-繁体-发布五篇帖子获得50燃料值-测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数测试描述可以展示行数','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220829204145814181.webp',50.00,'POINT',1,'2022-08-29 13:00:00','2022-09-02 13:00:00','2022-08-29 12:42:34','2022-09-08 08:14:10','kiki',2);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022082913113939402001','L0','SIGN_IN','NORMAL','L0每日签到','L0Sign in daily','L0每日签到','2022082913113939010801','',null,0,'CYCLE','DAILY',1,'进入任务中心即完成签到','Enter the task center to complete the sign-in','進入任務中心即完成簽到','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908162236771460__w:80__h:80.png',1.00,'POINT',0,'2022-08-30 13:00:00',null,'2022-08-29 13:11:39','2022-12-21 09:09:49','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022083001415492702200','L2','POST','NORMAL','L2任务-简体-发布5篇帖子','L2任务-英文-发布5篇帖子','L2任务-繁体-发布5篇帖子','2022083001415491611000','{"type": "internal", "page": "ReleaseLongPost", "index":0 }',null,0,'CYCLE','WEEKLY',5,'L2任务-简体-发布一篇帖子可获取50积分','L2任务-英文-发布一篇帖子可获取50积分','L2任务-繁体-发布一篇帖子可获取50积分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220830094152127963.webp',50.00,'POINT',1,'2022-08-01 13:00:00',null,'2022-08-30 01:41:55','2022-09-08 08:14:03','kiki',7);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022083108443850202400','L0','NONE','NORMAL','test不要展示1','test不要展示1','test不要展示1','2022083108443847311200','',null,0,'ONCE','DAILY',1,'test不要展示3','test不要展示2','test不要展示21','https://234.com',1.00,'POINT',1,'2022-09-09 05:37:23','2022-09-12 05:36:58','2022-08-31 08:44:39','2022-09-20 07:31:38','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090510245485302600','L0','LIKE','NORMAL','L0点赞帖子','L0Dian Zan Tie Zhi','L0点赞帖子','2022090510245482111400','{"type": "internal", "page": "Social", "index":0 }',null,0,'CYCLE','DAILY',1,'每点赞不同帖子获得0.1积分','Get 0.1 points for each like a different post','每点赞不同帖子获得0.1积分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908163455636953.png',0.10,'POINT',0,'2022-09-05 10:00:00',null,'2022-09-05 10:24:55','2022-12-20 02:18:39','kiki',20);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090805561650102800','L1','NONE','ADVANCED','L1 日累计交易额 10000USD','L1Ri Lei Ji Jiao Yi E10000USD','L1日累计交易额10000USD','2022090805561647011800','{"type": "internal", "page": "Market", "index":1 }',null,1,'CYCLE','DAILY',1,'日累计交易额 10000USD 奖励319积分','Ri Lei Ji Jiao Yi E10000USD，Jiang Li319积分','日累计交易额 10000USD 奖励319积分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908160008486540__w:80__h:80.png',319.00,'POINT',0,'2022-09-09 03:51:04',null,'2022-09-08 05:56:17','2022-09-23 04:15:42','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090806021181002801','L2','NONE','NORMAL','L2 日累计交易额 30000USD','L1Ri Lei Ji Jiao Yi E10000USD','L2 日累计交易额 30000USD','2022090806021180611801','{"type": "internal", "page": "Market", "index":1 } ',null,1,'CYCLE','DAILY',1,'日累计交易额 30000USD奖励451积分','Ri Lei Ji Jiao Yi E10000USDJiang Li 451积分','日累计交易额 30000USD奖励451积分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908155910096829__w:80__h:80.png',451.00,'POINT',0,'2022-09-08 06:48:13',null,'2022-09-08 06:02:12','2022-11-01 09:40:43','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090806064928802802','L0','FOLLOW','NORMAL','L0关注用戶x10','Follow  users x20','L0關注用戶x20','2022090806064928411802','{"type": "internal", "page": "KolRanking", "index":0 }',null,0,'CYCLE','DAILY',1,'每关注1位普通用户奖励2积分','Reward 2 points for every 1 regular user you follow','每关注1位普通用户奖励2积分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908160821856835.png',2.00,'POINT',0,'2022-09-08 06:30:02',null,'2022-09-08 06:06:49','2022-11-04 09:03:40','kiki',10);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090806151123902803','L1','FOLLOW','NORMAL','L1关注用户x20','L1Guan Zhu Yong Hu x20','L1关注用户x20','2022090806151123711803','{"type": "internal", "page": "KolRanking", "index":0 }',null,0,'CYCLE','DAILY',1,'每关注1位普通用户奖励4积分','Reward 4 points for every 1 regular user you follow','每关注1位普通用户奖励4积分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908160907540766__w:80__h:80.png',4.00,'POINT',0,'2022-09-08 06:30:02',null,'2022-09-08 06:15:11','2022-09-08 09:26:01','kiki',80);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090806175537802804','L2','FOLLOW','ADVANCED','L2关注用户x20','L2Follow users x20','L2關注用戶x20','2022090806175537611804','{"type": "internal", "page": "KolRanking", "index":0 }',null,0,'CYCLE','DAILY',1,'每关注1位普通用户奖励6积分','Reward 2 points for every 1 regular user you follow','每关注1位普通用户奖励6积分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908161056163860__w:80__h:80.png',6.00,'POINT',0,'2022-09-08 06:31:02',null,'2022-09-08 06:17:55','2022-11-01 09:41:26','kiki',120);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090806294938202805','L1','COMMENT','NORMAL','L1完成帖子的评论','L1Complete comments','L1完成帖子的評論','2022090806294938011805','',null,0,'CYCLE','DAILY',1,'每完成1篇帖子的评论奖励2积分','2 point for every completed comment on 1 post','每完成1篇帖子的評論獎勵2積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908144425581851.png',2.00,'POINT',0,'2022-09-08 10:00:00',null,'2022-09-08 06:29:49','2022-09-08 08:17:32','kiki',60);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090806425139502806','L2','COMMENT','NORMAL','L2完成帖子的评论','L2Complete comments','L2完成帖子的評論','2022090806425139311806','{"type": "internal", "page": "Social", "index":0 }',null,0,'CYCLE','DAILY',1,'每完成1篇帖子的评论奖励3积分','3 point for every completed comment on 1 post','每完成1篇帖子的評論獎勵3積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908150017392171__w:80__h:80.png',3.00,'POINT',0,'2022-09-08 06:44:00',null,'2022-09-08 06:42:51','2022-09-08 08:18:03','kiki',90);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090807023740003000','L1','POST','NORMAL','L1发布帖子','L1Post post','L1發佈帖子','2022090807023738212000','{"type": "internal", "page": "ReleaseLongPost", "index":0 }',null,0,'CYCLE','DAILY',1,'每发布1篇帖子奖励5积分','5 points for every post','每發佈1篇帖子獎勵5積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908155534785549__w:80__h:80.png',8.00,'POINT',0,'2022-09-08 07:06:50',null,'2022-09-08 07:02:37','2022-12-16 03:40:48','kiki',12);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090807043592403001','L2','POST','NORMAL','L2发布帖子','L2Post post','L2發佈帖子','2022090807043592012001','{"type": "internal", "page": "ReleaseLongPost", "index":0 }',null,0,'CYCLE','DAILY',1,'每发布1篇帖子奖励8积分','8 points for every 1 post published','每發佈1篇帖子獎勵8積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908155446007497__w:80__h:80.png',1.00,'POINT',0,'2022-09-08 07:09:00',null,'2022-09-08 07:04:36','2022-11-03 09:22:13','kiki',12);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090807104943602807','L2','SHARE_POST','NORMAL','L2分享文章','L2Share information','L2分享文章','2022090807104943311807','{"type": "internal", "page": "Social", "index":0 }',null,0,'CYCLE','DAILY',1,'每分享1篇文章获取5积分','Get 5 points for every article you share','每分享1篇文章獲取5積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908154756360496.png',5.00,'POINT',0,'2022-09-08 07:15:00',null,'2022-09-08 07:10:49','2022-09-08 07:47:57','kiki',6);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090807144582903002','L0','SHARE_NEWS','NORMAL','L0分享资讯','L1Share information','L0分享資訊','2022090807144582612002','{"type": "internal", "page": "News", "index":0 }',null,0,'CYCLE','DAILY',1,'每分享1篇资讯获取2积分','Get 5 points for every piece of information you share','每分享1篇資訊獲取2積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908154518632779.png',2.00,'POINT',0,'2022-09-08 07:16:00',null,'2022-09-08 07:14:46','2022-09-08 07:45:33','kiki',4);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090807170302503003','L1','SHARE_NEWS','NORMAL','L1分享资讯','L1Share information','L1分享資訊','2022090807170302212003','{"type": "internal", "page": "News", "index":0 }',null,0,'CYCLE','DAILY',1,'每分享1篇资讯获取3积分','Get 3 points for every piece of information you share','每分享1篇資訊獲取3積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908154423509632.png',3.00,'POINT',0,'2022-09-08 07:18:00',null,'2022-09-08 07:17:03','2022-09-08 07:45:52','kiki',6);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090807190208302808','L2','SHARE_NEWS','NORMAL','L2分享资讯','L2Share information','L2分享資訊','2022090807190208111808','{"type": "internal", "page": "News", "index":0 }',null,0,'CYCLE','DAILY',1,'每分享1篇资讯获取5积分','Get 5 points for every piece of information you share','每分享1篇資訊獲取5積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908154300012698__w:80__h:80.png',5.00,'POINT',0,'2022-09-08 07:20:00',null,'2022-09-08 07:19:02','2022-09-08 07:46:17','kiki',6);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090807245192103004','L1','NONE','ADVANCED','L1月累计净入金额度大于5000USD','L1Monthly accumulated net deposit limit is greate','L1月累計淨入金額度大於5000USD','2022090807245191812004','{"type": "internal", "page": "Deposit", "index":0 }',null,1,'CYCLE','MONTHLY',1,'月累计净入金额度大于5000USD获取1000积分','L0The monthly accumulated net deposit limit is more than 5000USD','月累計淨入金額度大於5000USD獲取1000積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908154019941694.png',1000.00,'POINT',0,'2022-09-08 07:26:00',null,'2022-09-08 07:24:52','2022-09-23 03:59:46','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090807270927303005','L2','NONE','ADVANCED','L2月累计净入金额度大于20000USD','L2Monthly accumulated net deposit limit is greate','L2月累計淨入金額度大於20000USD','2022090807270927012005','{"type": "internal", "page": "Deposit", "index":0 }',null,1,'CYCLE','MONTHLY',1,'月累计净入金额度大于20000USD获取2000积分','L2The monthly accumulated net deposit limit is more than 20000USD','月累計淨入金額度大於20000USD獲取2000積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908153745112496.png',2000.00,'POINT',0,'2022-09-08 07:28:00',null,'2022-09-08 07:27:09','2022-09-23 03:59:59','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090807534398903006','L0','SHARE_POST','NORMAL','L0分享文章测试任务展示状态','L0share article','L0分享文章','2022090807534398612006','{"type": "internal", "page": "Social", "index":0 }',null,0,'CYCLE','DAILY',1,'每分享1篇文章获取2积分','Get 2 points for every article you share','每分享1篇文章獲取2積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908155310530884.png',2.00,'POINT',0,'2022-09-08 07:53:00',null,'2022-09-08 07:53:44','2022-09-20 07:31:56','kiki',4);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090808055129603007','L0','NONE','NORMAL','L0日累计交易额 2000USD','L0Ri Lei Ji Jiao Yi E2000USD','L0日累計交易額2000USD','2022090808055129212007','',null,1,'CYCLE','DAILY',1,'日累计交易额2000USD奖励122积分','Daily cumulative transaction volume of 2000USD rewards 122 points','日累計交易額2000USD獎勵122積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908160501562817__w:80__h:80.png',122.00,'POINT',0,'2022-09-08 08:05:46',null,'2022-09-08 08:05:51','2022-10-20 09:14:57','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090808181042103008','L1','NONE','ADVANCED','L1邀请好友活动','L1Invite friends to activities','L1邀請好友活動','2022090808181041812008','{"type": "internal", "page": "InviteFriend", "index":0 }',null,1,'ONCE','DAILY',1,'L1完成邀请3位好友活动获得40积分','L1 Invite 3 friends to get 40 points','L1完成邀請3位好友活動獲得40積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908163310983540__w:80__h:80.png',40.00,'POINT',0,'2022-09-08 08:19:00',null,'2022-09-08 08:18:10','2022-09-08 08:33:11','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090808200895603009','L2','NONE','ADVANCED','L2邀请好友活动','L2 Invite friends to activities','L2邀請好友活動','2022090808200895312009',' {"type": "internal", "page": "InviteFriend", "index":0 }',null,1,'ONCE','DAILY',1,'L2 完成邀请3位好友活动获得50积分','L2Invite 3 friends to get 50 points','L2完成邀請3位好友活動獲得50積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908163333270983.png',50.00,'POINT',0,'2022-09-08 08:21:00',null,'2022-09-08 08:20:09','2022-09-08 08:33:34','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090808253710203010','L1','LIKE','NORMAL','L1点赞帖子','L1Dian Zan Tie Zhi','L1点赞帖子','2022090808253709912010','{"type": "internal", "page": "Social", "index":0 }',null,0,'CYCLE','DAILY',1,'每点赞不同帖子获得0.1积分','Get 0.1 points for each like a different post','每点赞不同帖子获得0.1积分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908163426269528.png',0.10,'POINT',0,'2022-09-08 08:26:00',null,'2022-09-08 08:25:37','2022-09-08 08:34:27','kiki',800);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090808271901903011','L2','LIKE','NORMAL','L2点赞帖子','L2 Dian Zan Tie Zhi','L2点赞帖子','2022090808271901712011','{"type": "internal", "page": "Social", "index":0 }',null,0,'CYCLE','DAILY',1,'每点赞不同帖子获得0.1积分','Get 0.1 points for each like a different post','每点赞不同帖子获得0.1积分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220908163440464128__w:80__h:80.png',0.10,'POINT',0,'2022-09-08 08:29:00',null,'2022-09-08 08:27:19','2022-09-08 08:34:41','kiki',800);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090907030516503200','L0','NONE','ADVANCED','L0 - test - 月累计净入金额度大于10USD','L0 - test - Monthly net deposit more than 10USD','L0 - test - 月累計净入金額度大於10USD','2022090907030513412200','',null,1,'CYCLE','MONTHLY',1,'test月累计净入金额度大于10USD获取20积分','test L0The monthly accumulated net deposit limit is more than 10USD','tes月累計淨入金額度大於10USD獲取20積分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220909150220901167__w:321__h:273.png',20.00,'POINT',0,'2022-09-09 07:03:02','2022-09-10 06:49:25','2022-09-09 07:03:05','2022-09-09 07:06:25','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022090907111736703400','L0','POST','NORMAL','L0 TEST 发布2个帖子','L0 TEST Post 2 Feeds','L0 TEST 發佈2個帖子','2022090907111734612400','{"type": "internal", "page": "ReleaseLongPost", "index":0 }',null,0,'CYCLE','DAILY',2,'L0 TEST 发布2个帖子獲 10 分','L0 TEST Post 2 Feeds Get 10 points','L0 TEST 發佈2個帖子獲 10 分','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220909151048125390__w:321__h:273.png',10.00,'POINT',1,'2022-09-09 07:34:03','2022-09-17 07:07:03','2022-09-09 07:11:17','2022-09-15 02:54:05','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022091903162381503600','L1','POST','NORMAL','测试任务展示状态简体','测试任务展示状态英文','测试任务展示状态繁体','2022091903162379812600','',null,0,'CYCLE','DAILY',1,'测试任务展示状态简体','测试任务展示状态英文','测试任务展示状态繁体','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20220919111622118634.webp',0.30,'POINT',1,'2022-09-20 03:20:00','2022-09-21 03:45:00','2022-09-19 03:16:24','2022-12-19 07:27:32','kiki',5);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022112310235754303800','L0','NONE','ADVANCED','每周优质创作者们可得到100燃料','Weekly selected high quality posts gets 100 fuel','每週優質創作者們可得到100燃料','2022112310235753113800','{"type": "external", "page": "https://kikitrade.zendesk.com/hc/zh-tw/articles/5884542930703", "index":0 }',null,0,'CYCLE','WEEKLY',1,'每周优质创作者们可得到100燃料','Weekly selected high quality posts gets 100 fuel','每週優質創作者們可得到100燃料','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20221123181900222664.png',1.00,'POINT',0,'2022-11-23 10:23:15',null,'2022-11-23 10:23:58','2022-12-15 09:52:24','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022121509035717004000','L0','NONE','ADVANCED','LV0日交易额达到100U每日发放9.99燃料-简体【VIP1.2】','The daily trading volume of LV0 reaches 100U send 9.99-英【VIP1.2】','LV0日交易額達到100U每日發放9.99燃料-繁体【VIP1.2】','2022121509035716214800','',null,1,'CYCLE','DAILY',1,'LV0日交易额达到100U每日发放9.99燃料-简体描述【VIP1.2】','The daily trading volume of LV0 reaches 100U send 9.99 points-英文描述【VIP1.2】','LV0日交易額達到100U每日發放9.99燃料-繁体描述【VIP1.2】','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20221215170353920111__w:150__h:150.jpg',9.99,'POINT',0,'2022-11-30 16:00:00','2022-12-17 12:00:00','2022-12-15 09:03:57','2022-12-16 07:33:51','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022121509072626604001','L1','NONE','ADVANCED','LV1日交易额达到200U每日发放19.99燃料-简体【VIP1.2】','The daily trading volume of LV1 reaches 200U send 19.99 【VIP1.2】','LV1日交易額達到200U每日發放19.99燃料-繁体【VIP1.2】','2022121509072626414801','',null,1,'CYCLE','DAILY',1,'LV1日交易额达到200U每日发放19.99燃料-简体描述【VIP1.2】','The daily trading volume of LV1 reaches 200U send 19.99 points-英文描述【VIP1.2】','LV1日交易額達到200U每日發放19.99燃料-繁体描述【VIP1.2】','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20221215170724496156__w:150__h:150.jpg',19.99,'POINT',0,'2022-12-15 16:00:00',null,'2022-12-15 09:07:26','2022-12-16 06:15:21','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022121509093497304200','L2','NONE','ADVANCED','LV2日交易额达到300U每日发放100燃料-简体【VIP1.2】','The daily trading volume of LV2 reaches 300U send 100 【VIP1.2】','LV2日交易额达到300U每日发放100燃料-繁体【VIP1.2】','2022121509093496914601','',null,1,'CYCLE','DAILY',1,'LV2日交易额达到300U每日发放100燃料-简体描述【VIP1.2】','The daily trading volume of LV2 reaches 300U send 100 points-英文描述【VIP1.2】','LV2日交易额达到300U每日发放100燃料-繁体描述【VIP1.2】','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20221215170930274248__w:150__h:150.jpg',100.00,'POINT',0,'2022-12-14 16:00:00','2022-12-15 12:00:00','2022-12-15 09:09:35','2022-12-16 06:15:48','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022121606210707204002','L0','POST_SPLENDID','NORMAL','发布精选帖子实时发放燃料-简体【VIP1.2】','Publish splendid posts-英文【VIP1.2】','发布精选帖子实时发放燃料-繁体【VIP1.2】','2022121606210707014802','',null,0,'CYCLE','DAILY',1,'发布精选帖子实时发放燃料-简体描述【VIP1.2】','Publish splendid posts-英文描述【VIP1.2】','发布精选帖子实时发放燃料-繁体描述【VIP1.2】','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20221216142105471534__w:150__h:150.jpg',20.30,'POINT',0,'2022-12-14 16:00:00',null,'2022-12-16 06:21:07','2022-12-27 04:32:14','kiki',3);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022121606233759304201','L0','TRANSACTION_ANALYSIS','NORMAL','发布交易分析帖子-简体【VIP1.2】','Post transaction analysis-英文【VIP1.2】','发布交易分析帖子-繁体【VIP1.2】','2022121606233759014603','',null,0,'CYCLE','DAILY',1,'发布交易分析帖子-简体描述【VIP1.2】','Post transaction analysis-英文描述【VIP1.2】','发布交易分析帖子-繁体描述【VIP1.2】','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20221216142336063053__w:150__h:150.jpg',5.80,'POINT',0,'2022-12-17 16:00:00',null,'2022-12-16 06:23:38','2022-12-27 04:54:08','kiki',3);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022121909155550404400','L0','ASSET_ANALYSIS','NORMAL','发布资产分析帖子-简体【VIP1.2】','Post asset analysis-英文【VIP1.2】','发布资产分析帖子-繁体【VIP1.2】','2022121909155548615000','',null,0,'CYCLE','DAILY',1,'发布资产分析帖子-简体描述【VIP1.2】','Post asset analysis-英文描述【VIP1.2】','发布资产分析帖子-繁体描述【VIP1.2】','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20221219171548829254__w:150__h:150.jpg',5.60,'POINT',0,'2022-12-17 16:00:00',null,'2022-12-19 09:15:56','2022-12-27 04:53:56','kiki',2);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022121910215506004401','L0','ORDER_CREATED','NORMAL','下单任务-简体【VIP1.2】','order create-英文【VIP1.2】','下单任务-繁体【VIP1.2】','2022121910215505715001','',null,0,'CYCLE','DAILY',1,'下单任务-简体描述【VIP1.2】','order create-英文描述【VIP1.2】','下单任务-繁体描述【VIP1.2】','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20221219182131122038__w:150__h:150.jpg',8.80,'POINT',0,'2022-12-18 10:00:00',null,'2022-12-19 10:21:55','2022-12-23 02:50:15','kiki',15);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022122002262606104402','L0','KYC','NORMAL','KYC1一次性任务-简体【VIP1.2】','KYC1 task-英文【VIP1.2】','KYC1一次性任务-繁体【VIP1.2】','2022122002262605615002','',null,0,'ONCE','DAILY',1,'KYC1一次性任务-简体描述【VIP1.2】','KYC1 task-英文描述【VIP1.2】','KYC1一次性任务-繁体描述【VIP1.2】','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20221220102622690664__w:150__h:150.jpg',9.99,'POINT',1,'2022-12-17 16:00:00',null,'2022-12-20 02:26:26','2022-12-28 08:42:31','kiki',1);
--         INSERT INTO `activity_task_config` (`id`,`level`,`event`,`type`,`name_cn`,`name_en`,`name_hk`,`activity_id`,`url`,`callback`,`award_time_type`,`cycle_type`,`cycle`,`complete_threshold`,`desc_cn`,`desc_en`,`desc_hk`,`icon`,`amount`,`award`,`status`,`start_time`,`end_time`,`created`,`modified`,`saasId`,`complete_times`) VALUES ('2022122002292029704403','L0','KYC_L2','NORMAL','KYC2一次性任务-简体【VIP1.2】','KYC2 task-英文【VIP1.2】','KYC2一次性任务-繁体【VIP1.2】','2022122002292029415003','',null,0,'ONCE','DAILY',1,'KYC2一次性任务-简体描述【VIP1.2】','KYC2 task-英文描述【VIP1.2】','KYC2一次性任务-繁体描述【VIP1.2】','https://kiki-beta.oss-ap-southeast-1.aliyuncs.com/0000000000000000000000/operation/public/MissionCentre/20221220102918180519__w:150__h:150.jpg',19.99,'POINT',0,'2022-12-18 02:00:00',null,'2022-12-20 02:29:20','2022-12-23 02:32:08','kiki',1);

    </insert>

</mapper>

