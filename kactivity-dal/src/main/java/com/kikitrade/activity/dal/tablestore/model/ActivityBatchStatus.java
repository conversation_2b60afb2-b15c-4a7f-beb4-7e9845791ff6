package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "activity_batch_status")
public class ActivityBatchStatus implements Serializable {

    public static final String INDEX_ACTIVITY_BATCH_STATUS_DEFAULT_INDEX = "activity_batch_status_by_status";

    @PartitionKey(name = "batch_id")
    @Index(name = INDEX_ACTIVITY_BATCH_STATUS_DEFAULT_INDEX, pkColumn = "batch_id", pkValue = 1)
    private String batchId;

    @Index(name = INDEX_ACTIVITY_BATCH_STATUS_DEFAULT_INDEX, pkColumn = "status")
    @Column(name = "status", isDefined = true)
    private String status;

    @Index(name = INDEX_ACTIVITY_BATCH_STATUS_DEFAULT_INDEX, dfColumn = "num")
    @Column(name = "num", isDefined = true, type = Column.Type.INTEGER)
    private Integer num;

    @Column(name = "created", isDefined = true, type = Column.Type.INTEGER)
    private Date created;

    @Column(name = "modified", isDefined = true, type = Column.Type.INTEGER)
    private Date modified;
}
