package com.kikitrade.activity.service.rpc.osp;

import com.alibaba.fastjson2.JSONObject;
import com.kikitrade.activity.service.common.config.OspProperties;
import com.kikitrade.activity.service.config.SaasConfig;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.http.HttpMethod;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/08 17:18
 */
@Data
@Slf4j
public class OspCreateDappRequest {

    private static final String APP_KEY = "app_key";

    private OspProperties ospProperties;

    private String cid;
    private String saasId;

    public OspCreateDappRequest() {
    }

    public OspCreateDappRequest(OspProperties ospProperties) {
        this.ospProperties = ospProperties;
    }

    public OspCreateDappRequest build(String saasId, String cid) {
        this.saasId = saasId;
        this.cid = cid;
        return this;
    }


    public String execute() {
        try {
            return createDappVerify(this.cid, this.saasId);
        } catch (Exception e) {
            log.error("[OspCallBackRequest] execute exception, cid:{}, error:", cid, e);
            return null;
        }
    }

    private String createDappVerify(String customerId, String saasId) {
        try {
            SaasConfig saasConfig = SaasConfigLoader.getConfig(saasId);
            Headers headers = new Headers.Builder()
                    .add(APP_KEY, "quests")
                    .add("os-app-id", saasConfig.getOspAppId())
                    .add("os-Api-Key", saasConfig.getOspAppKey())
                    .build();

            String url = saasConfig.getApiHost() + "/v2/s2s/dapps?ownerId=" + customerId + "&limit=1";
            Request request = new Request.Builder()
                    .url(url)
                    .method(HttpMethod.GET.name(), null)
                    .headers(headers)
                    .build();

            log.info("[ospDappVerify] request = {}", request);

            try (Response response = HttpPoolUtil.getHttpClient().newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(responseBody);
                    log.info("[OspCallBackRequest] ospDappVerify responseBody:{}", jsonObject.toJSONString());

                    if (jsonObject.getJSONObject("data").getInteger("total") > 0) {
                        return jsonObject.getJSONObject("data").getJSONArray("rows").getJSONObject(0).getString("id");
                    }
                }
            }
        } catch (Exception e) {
            log.error("[osp] ospDappVerify Exception:{}, error:", customerId, e);
        }
        return null;
    }

}
