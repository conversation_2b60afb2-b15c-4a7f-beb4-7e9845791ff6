<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityRuleMapDao">


    <!-- 增加 -->
    <insert id="insert" parameterType="com.kikitrade.activity.dal.mysql.model.ActivityRuleMap">
        INSERT INTO activity_rule_map VALUES (
            #{activity_id},
            #{rule_id},
            #{rule_name},
            #{priority},
            #{status},
            #{params}
        )
    </insert>


    <delete id="deleteById">
        DELETE FROM activity_rule_map
        WHERE activity_id = #{activity_id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">

        insert into activity_rule_map (activity_id, rule_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.activity_id,jdbcType=INTEGER},
            #{item.rule_id,jdbcType=INTEGER}
            #{item.params,jdbcType=VARCHAR}
            )
        </foreach>

    </insert>


    <select id="findAll" resultType="com.kikitrade.activity.dal.mysql.model.ActivityRuleMap">
        SELECT *
        FROM activity_rule_map
        ORDER BY activity_id DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByActivityId" resultType="com.kikitrade.activity.dal.mysql.model.ActivityRuleMap">
        SELECT *
        FROM activity_rule_map
        WHERE activity_id = #{activity_id}
    </select>


</mapper>
