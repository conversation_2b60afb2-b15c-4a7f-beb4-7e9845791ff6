package com.kikitrade.activity.luck.service.business.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.accounting.api.RemoteAccountingOperateService;
import com.kikitrade.accounting.api.RemoteProductAccountService;
import com.kikitrade.accounting.api.domain.activity.RemoteAccountingActivityService;
import com.kikitrade.accounting.api.domain.activity.model.AccountActivityRequest;
import com.kikitrade.accounting.api.model.account.common.BusinessType;
import com.kikitrade.accounting.api.model.account.common.Category;
import com.kikitrade.accounting.api.model.account.request.AccountUnfreezeRequest;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountParam;
import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReceiveItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.api.exception.LuckFortuneException;
import com.kikitrade.activity.luck.api.exception.LuckFortuneExceptionType;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneOpenDTO;
import com.kikitrade.activity.luck.service.business.AirDropEventService;
import com.kikitrade.activity.luck.service.business.ReceiveTccService;
import com.kikitrade.activity.luck.service.business.RuleService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import com.kikitrade.kcustomer.api.model.BindInviteRelationRequest;
import com.kikitrade.kcustomer.api.service.RemoteCustomerInviteService;
import com.kikitrade.kevent.common.constant.EventConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;

/**
 *
 */
@Service
@Slf4j
public class ReceiveTccServiceImpl implements ReceiveTccService {

    @DubboReference
    private RemoteProductAccountService remoteProductAccountService;
    @DubboReference
    private RemoteAccountingOperateService remoteAccountingOperateService;
    @DubboReference
    private RemoteAccountingActivityService remoteAccountingActivityService;
    @Resource
    private RedisService redisService;
    @Resource
    private LuckFortuneReceiveItemBuilder luckFortuneReceiveItemBuilder;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private CustomerService customerService;
    @Resource
    private RuleService ruleService;
    @DubboReference
    private RemoteCustomerInviteService remoteCustomerInviteService;
    @Resource
    private AirDropEventService airDropEventService;

    /**
     * 开启红包
     *
     * @param luckFortuneOpenDTO
     * @param luckFortuneReleaseItem
     * @param luckFortuneReceiveItem
     * @return
     */
    /**
     * 开启红包
     *
     * @param luckFortuneOpenDTO
     * @return
     */
    @Override
    @Compensation(confirmMethod = "confirmOpen", cancelMethod = "cancelOpen")
    public LuckFortuneReceiveItem tryOpen(LuckFortuneOpenDTO luckFortuneOpenDTO, LuckFortuneReleaseItem luckFortuneReleaseItem, LuckFortuneReceiveItem luckFortuneReceiveItem) throws Exception {
        try{
            remoteProductAccountService.freeze(buildProductAccountParam(luckFortuneReceiveItem.getBusinessId(), luckFortuneOpenDTO.getCustomer(), String.valueOf(luckFortuneReceiveItem.getCost())));
        }catch (Exception ex){
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_LIMIT_AMOUNT_DAY_ERROR);
        }
        //领取和发放人相同，解冻
        if(luckFortuneOpenDTO.getCustomer().getId().equals(luckFortuneReleaseItem.getCustomerId())){
            remoteAccountingOperateService.unfreeze(buildAccountUnfreezeRequest(luckFortuneReceiveItem));
        }else{
            remoteAccountingActivityService.transfer(buildAccountTransferRequest(luckFortuneOpenDTO, luckFortuneReleaseItem, luckFortuneReceiveItem));
        }
        return luckFortuneReceiveItem;
    }

    private ProductAccountParam buildProductAccountParam(String biz, ActivityCustomerDTO customerDTO, String amount){
        LuckFortuneRule rule = ruleService.findObject(customerDTO.getKycLevel(), customerService.getKolType(customerDTO.getId()));
        ProductAccountParam param = new ProductAccountParam();
        param.setBizId(biz);
        param.setProductLimitCodes(Collections.singletonList(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), rule.getKycLevel(), rule.getUserType())));
        param.setProductLimitCode(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), rule.getKycLevel(), rule.getUserType()));
        param.setCustomerId(customerDTO.getId());
        param.setAmount(new BigDecimal(amount));
        return param;
    }

    public void confirmOpen(LuckFortuneOpenDTO luckFortuneOpenDTO, LuckFortuneReleaseItem luckFortuneReleaseItem, LuckFortuneReceiveItem luckFortuneReceiveItem) throws Exception {
        luckFortuneReceiveItem.setStatus(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_SUCCESS.getCode());
        log.info("confirm open:{},{}", luckFortuneReceiveItem, luckFortuneOpenDTO);
        LuckFortuneReceiveItem item = luckFortuneReceiveItemBuilder.findById(luckFortuneReceiveItem.getId());
        if(item != null && ActivityConstant.LuckFortuneReceiveStatus.ACTIVATING.getCode().equals(item.getStatus())){
            luckFortuneReceiveItemBuilder.update(luckFortuneReceiveItem);
            redisService.hDel(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(luckFortuneOpenDTO.getCustomer().getEmail()), luckFortuneOpenDTO.getId());
            redisService.hDel(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(luckFortuneOpenDTO.getCustomer().getPhone()), luckFortuneOpenDTO.getId());
            CustomerCacheDTO customerDTO = customerService.getById(luckFortuneOpenDTO.getCustomer().getId());
            log.info("customerDTO:{}, {}", customerDTO, luckFortuneReceiveItem.getReceiveTime());
            if(customerDTO != null && TimeUtil.getUtcTime(customerDTO.getRegisterTime()).compareTo(luckFortuneReceiveItem.getReceiveTime()) > 0){
                BindInviteRelationRequest request = new BindInviteRelationRequest();
                request.setSaasId(kactivityProperties.getSaasId());
                request.setInviteeId(luckFortuneOpenDTO.getCustomer().getId());
                request.setInviterId(luckFortuneReleaseItem.getCustomerId());
                remoteCustomerInviteService.bindRelation(request);
            }
            airDropEventService.reportEvent(EventConstants.EventName.RECEIVE_RED_POCKET, item);
        }
    }

    public void cancelOpen(LuckFortuneOpenDTO luckFortuneOpenDTO, LuckFortuneReleaseItem luckFortuneReleaseItem, LuckFortuneReceiveItem luckFortuneReceiveItem) throws Exception {
        luckFortuneReceiveItem.setStatus(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_FAIL.getCode());
        LuckFortuneReceiveItem item = luckFortuneReceiveItemBuilder.findById(luckFortuneReceiveItem.getId());
        if(item != null && ActivityConstant.LuckFortuneReceiveStatus.ACTIVATING.getCode().equals(item.getStatus())){
            log.info("cancel open:{},{}", luckFortuneReceiveItem, luckFortuneOpenDTO);
            luckFortuneReceiveItemBuilder.update(luckFortuneReceiveItem);
        }
    }

    private AccountUnfreezeRequest buildAccountUnfreezeRequest(LuckFortuneReceiveItem receiveItem) {
        AccountUnfreezeRequest request = new AccountUnfreezeRequest();
        request.setAmount(new BigDecimal(receiveItem.getAmount()));
        request.setBusinessId(receiveItem.getBusinessId());
        request.setBusinessType(BusinessType.ACTIVITY_AIRDROP);
        request.setOriginalBusinessId(receiveItem.getReleaseId());
        request.setCategory(Category.NORMAL);
        request.setCurrency(receiveItem.getCurrency());
        request.setCustomerId(receiveItem.getCustomerId());
        request.setRetro(false);
        request.setSaasId(kactivityProperties.getSaasId());
        return request;
    }

    private AccountActivityRequest buildAccountTransferRequest(LuckFortuneOpenDTO luckFortuneOpenDTO, LuckFortuneReleaseItem releaseItem, LuckFortuneReceiveItem receiveItem){
        AccountActivityRequest request = new AccountActivityRequest();
        request.setOriginalBusinessId(receiveItem.getReleaseId());
        request.setBusinessId(receiveItem.getBusinessId());
        request.setBusinessType(BusinessType.ACTIVITY_AIRDROP);
        request.setAmount(new BigDecimal(receiveItem.getAmount()));
        request.setCurrency(releaseItem.getCurrency());
        request.setFromCategory(Category.NORMAL);
        request.setFromCustomerId(releaseItem.getCustomerId());
        request.setToCategory(Category.NORMAL);
        request.setToCustomerId(luckFortuneOpenDTO.getCustomer().getId());
        request.setSaasId(kactivityProperties.getSaasId());
        request.setStrictCheck(false);
        return request;
    }
}
