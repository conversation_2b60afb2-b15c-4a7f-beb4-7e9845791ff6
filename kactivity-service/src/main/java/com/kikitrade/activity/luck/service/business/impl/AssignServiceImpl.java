package com.kikitrade.activity.luck.service.business.impl;

import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReleaseItemBuilder;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReleaseDTO;
import com.kikitrade.activity.luck.service.business.AssignService;
import com.kikitrade.activity.luck.service.business.RuleService;
import com.kikitrade.activity.luck.service.model.ReceiveItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.random.RandomDataGenerator;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingDeque;

/**
 *
 */
@Service
@Slf4j
public class AssignServiceImpl implements AssignService {

    @Resource
    private RuleService ruleService;
    @Resource
    private SeqGeneraterService seqGeneraterService;
    @Resource
    private LuckFortuneReleaseItemBuilder luckFortuneReleaseItemBuilder;
    @Resource
    private CustomerService customerService;

    /**
     * 构建红包
     *
     * @param luckFortuneReleaseDTO
     * @return
     */
    @Override
    public List<ReceiveItem> buildLuckFortuneReceive(LuckFortuneRule rule, LuckFortuneReleaseDTO luckFortuneReleaseDTO, String id) {
        log.info("luckFortuneReleaseDTO:{}", luckFortuneReleaseDTO);
        switch (ActivityConstant.AssignType.getType(luckFortuneReleaseDTO.getAssignType())){
            case GENERAL:
                //普通红包
                return general(id, luckFortuneReleaseDTO.getAmount(), luckFortuneReleaseDTO.getNum(), luckFortuneReleaseDTO.getCurrency(), luckFortuneReleaseDTO.getUsdCost().divide(luckFortuneReleaseDTO.getAmount(), luckFortuneReleaseDTO.getKeepDecimalForCoin(), RoundingMode.DOWN));
            default:
                //手气红包
                return random(rule ,id, luckFortuneReleaseDTO.getCustomer(), luckFortuneReleaseDTO.getAmount(), luckFortuneReleaseDTO.getNum(), luckFortuneReleaseDTO.getCurrency(), luckFortuneReleaseDTO.getKeepDecimalForCoin(), luckFortuneReleaseDTO.getUsdCost().divide(luckFortuneReleaseDTO.getAmount(), luckFortuneReleaseDTO.getKeepDecimalForCoin(), RoundingMode.DOWN));
        }
    }

    //
    private List<ReceiveItem> random(LuckFortuneRule rule, String id, ActivityCustomerDTO customer, BigDecimal releaseAmount, Integer releaseNum, String currency, int precision, BigDecimal usdCost){
        double receiveMin = rule.getReceiveMin().divide(usdCost, precision, RoundingMode.DOWN).doubleValue();
        if(rule.getReceiveMin().multiply(new BigDecimal(releaseNum)).compareTo(releaseAmount) == 0){
            return general(id, rule.getReceiveMin(), releaseNum, currency, usdCost);
        }
        //产生的红包
        Queue<ReceiveItem> queue = new LinkedBlockingDeque();
        //剩余金额
        BigDecimal remainAmount = releaseAmount;
        RandomDataGenerator generator = new RandomDataGenerator();
        for(int i = 0; i< releaseNum - 1; i++){
            double max = Math.min(remainAmount.doubleValue()/(releaseNum-i) * 2, remainAmount.doubleValue() - (receiveMin * (releaseNum - i - 1)));
            max = new BigDecimal(max).setScale(precision, BigDecimal.ROUND_HALF_UP).doubleValue();
            double receiveAmount = receiveMin == max ? max : generator.nextUniform(receiveMin, max);
            queue.add(
                    new ReceiveItem(id
                    , seqGeneraterService.next(CustomerSeqRuleBuilder.instance(luckFortuneReleaseItemBuilder.getTableName()))
                    , new BigDecimal(receiveAmount).setScale(precision, BigDecimal.ROUND_HALF_UP).toPlainString()
                    ,currency, new BigDecimal(receiveAmount).setScale(precision, BigDecimal.ROUND_HALF_UP).multiply(usdCost)));
            remainAmount = remainAmount.subtract(new BigDecimal(receiveAmount).setScale(precision, BigDecimal.ROUND_HALF_UP));
        }
        queue.add(new ReceiveItem(id,seqGeneraterService.next(CustomerSeqRuleBuilder.instance(luckFortuneReleaseItemBuilder.getTableName())) ,remainAmount.toPlainString(), currency, remainAmount.multiply(usdCost)));
        return new ArrayList<>(queue);
    }

    private List<ReceiveItem> general(String id, BigDecimal releaseAmount, Integer releaseNum, String currency, BigDecimal usdCost){
        Queue<ReceiveItem> queue = new LinkedBlockingDeque();
        for(int i = 0; i< releaseNum; i++){
            queue.add(new ReceiveItem(id, seqGeneraterService.next(CustomerSeqRuleBuilder.instance(luckFortuneReleaseItemBuilder.getTableName())) , releaseAmount.toPlainString(), currency, releaseAmount.multiply(usdCost)));
        }
        return new ArrayList<>(queue);
    }
}
