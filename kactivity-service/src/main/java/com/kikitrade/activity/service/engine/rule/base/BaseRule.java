package com.kikitrade.activity.service.engine.rule.base;


import lombok.Getter;
import lombok.Setter;
import org.jeasy.rules.annotation.Action;
import org.jeasy.rules.annotation.Priority;


/**
 * kactivity
 *
 * @Auther: dembin.deng
 * @Date: 2018/7/12 11:54
 * @Description:
 */
@Setter
@Getter
public class BaseRule {
    private int priority;

    @Action
    public void doNothing() {
    }

    @Priority
    public int getPriority() {
        return priority;
    }
}
