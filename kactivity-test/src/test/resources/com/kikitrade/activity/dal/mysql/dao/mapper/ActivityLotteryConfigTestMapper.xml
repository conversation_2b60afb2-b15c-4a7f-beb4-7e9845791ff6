<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityLotteryConfigTestDao">
    <resultMap id="BaseResultMap" type="com.kikitrade.activity.dal.mysql.model.ActivityLotteryConfig">
        <result column="vip_level" property="vipLevel"/>
        <result column="times_limit" property="timesLimit"/>
        <result column="reward_limit" property="rewardLimit"/>
    </resultMap>
    <insert id="init">
        INSERT INTO `activity_lottery_config` (`id`,`valid`,`remark`,`status`,`vip_level`,`amount`,`currency`,`times_limit`,`reward_limit`,`type`,`created`,`modified`,`saasId`) VALUES
                                              ('','2022-10','10月奖池',1,'L0,L1,L2',32.00000000,'POINT',0,200.00000000,'slot','2022-09-29 07:43:23','2022-10-11 02:12:24','kiki');
    </insert>


    <update id="createTableIfNotExist">


        CREATE TABLE `activity_lottery_config` (
                                                   `id` varbinary(64) NOT NULL,
                                                   `valid` varchar(32) DEFAULT NULL COMMENT '奖池有效期',
                                                   `remark` varchar(128) DEFAULT NULL COMMENT '奖池描述',
                                                   `status` tinyint(4) DEFAULT NULL COMMENT '奖池状态',
                                                   `vip_level` varchar(64) DEFAULT NULL COMMENT 'vip等级',
                                                   `amount` decimal(20,8) DEFAULT NULL COMMENT '抽奖消耗金额',
                                                   `currency` varchar(16) DEFAULT NULL COMMENT '抽奖消耗币种',
                                                   `times_limit` int(11) DEFAULT NULL COMMENT '抽奖次数上限',
                                                   `reward_limit` decimal(20,8) DEFAULT NULL COMMENT '抽奖奖励上限',
                                                   `type` varchar(16) DEFAULT NULL COMMENT '抽奖类型',
                                                   `created` datetime NOT NULL COMMENT '创建时间',
                                                   `modified` datetime NOT NULL COMMENT '修改时间',
                                                   `saasId` varchar(16) NOT NULL COMMENT 'saasId',
                                                   PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='抽奖活动奖池配置'
        ;


    </update>

    <select id="select" resultMap="BaseResultMap">
        select * from activity_lottery_config where valid = '2022-10';
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select * from activity_lottery_config
    </select>
</mapper>

