/*
package com.kikitrade.activity.service.engine.rule;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.mysql.model.ActivityRuleMap;
import com.kikitrade.activity.dal.tablestore.builder.ActivityRecordsBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityRecords;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.ActivityMessage;
import com.kikitrade.activity.model.constant.ActivityMessageConstant;
import com.kikitrade.activity.model.FiatDepositActivityConfig;
import com.kikitrade.activity.service.business.ActivityService;
import com.kikitrade.activity.service.business.KolExtraRewardService;
import com.kikitrade.activity.service.common.LogAlarmConstant;
import com.kikitrade.activity.service.common.LogAlarmUtil;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.service.engine.rule.base.BaseRule;
import com.kikitrade.kcustomer.api.model.CustomerInviteDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerInviteService;
import com.kikitrade.kcustomer.common.constants.CustomerReferralConstants;
import com.kikitrade.kpay.api.exception.PayException;
import com.kikitrade.kpay.api.model.OtcOrderDTO;
import com.kikitrade.kpay.api.service.RemoteOtcOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.rules.annotation.Condition;
import org.jeasy.rules.annotation.Fact;
import org.jeasy.rules.annotation.Rule;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

*/
/**
 * 邀请入金奖励活动（非首次入金，但是第一次达到奖励标准，也进行奖励）
 *
 * <AUTHOR>
 * @create 2021/8/5 11:22 上午
 * @modify
 *//*

@Slf4j
@Rule
public class InviteDepositIgnoreFirstRule extends BaseRule {

    @DubboReference(check = false)
    private RemoteCustomerInviteService remoteCustomerInviteService;

    @DubboReference(check = false)
    private RemoteOtcOrderService remoteOtcOrderService;

    @Resource
    private ActivityRecordsBuilder activityRecordsBuilder;

    @Resource
    KolExtraRewardService kolExtraRewardService;

    @Resource
    private ActivityService activityService;

    @Resource
    LogAlarmUtil logAlarmUtil;

    @Condition
    public boolean check(@Fact("request") ActivityMessage message, @Fact("ruleConfig") List<ActivityRuleMap> ruleConfig) {
        return checkNewUserReward(message, ruleConfig) && rewardCalc(message, ruleConfig);
    }

    private boolean rewardCalc(ActivityMessage msg, List<ActivityRuleMap> ruleConfig) {
        log.info("InviteDepositIgnoreFirstRule rewardCalc, customerId: {}, event: {}", msg.getCustomerId(), msg.getEvent());
        if (CollectionUtils.isEmpty(ruleConfig)) {
            log.warn("the parameter fuleConfig is empty, rule check return true");
            return true;
        }
        boolean flag = true;
        try {
            // 找到触发的活动奖励规则
            List<FiatDepositActivityConfig> configList = ruleConfig.get(0).fetchConfigList();
            String userDepositCurrency = msg.fetchParamsJson().getString("fiatCurrency");        // 用户充值的币种
            BigDecimal userDepositAmount = msg.fetchParamsJson().getBigDecimal("fiatAmount");    // 用户充值的金额

            FiatDepositActivityConfig config = findCorrectActivityConfig(configList, userDepositCurrency, userDepositAmount);
            log.info("InviteDepositIgnoreFirstRule rewardCalc：config={}, customerId={}, fiatCurrency={}, fiatAmount={}", JSONObject.toJSONString(config), msg.getCustomerId(), userDepositCurrency, userDepositAmount);

            // 计算奖励金额
            if (config.getRewardValue().compareTo(BigDecimal.ZERO) <= 0) {
                log.error("InviteDepositIgnoreFirstRule rewardCalc, invalid reward amount, skip. amount {}", config.getRewardValue());
                return true;
            }

            // 填充业务参数
            msg.putBusinessAttr(ActivityMessageConstant.BusinessAttrs.DEPOSIT_CURRENCY.getName(), userDepositCurrency);
            msg.putBusinessAttr(ActivityMessageConstant.BusinessAttrs.DEPOSIT_AMOUNT.getName(), userDepositAmount);

            // 填充奖励信息
            String customerId = msg.getCustomerId();
            CustomerInviteDTO inviteDTO = remoteCustomerInviteService.inviterByCustomerId(msg.getSaasId(), customerId);

            if (!inviteDTO.getInviterId().equals(CustomerReferralConstants.EMPTY_ID)) {
                ActivityMessage.RewardResult rewardResult = new ActivityMessage.RewardResult(ruleConfig.get(0).getActivity_id())
                        .addRewardEntity(config.getInviteeRewardCurrency(), config.getInviteeRewardValue(), config.getInviteeExchangeCurrency(), customerId)
                        .addRewardEntity(config.getInviterRewardCurrency(), config.getInviterRewardValue(), config.getInviterExchangeCurrency(), inviteDTO.getInviterId());

                // 根据邀请人KOL的配置，给被邀请人额外奖励
                Map<String, BigDecimal> extraReward = kolExtraRewardService.getByKolCustomerId(inviteDTO.getInviterId());
                if (!CollectionUtils.isEmpty(extraReward)) {
                    extraReward.entrySet().forEach(entry -> rewardResult.addExtraRewardEntity(entry.getKey(), entry.getValue(), entry.getKey(), customerId));
                }

                msg.putRewardResult(rewardResult);
            } else {
                ActivityMessage.RewardResult rewardResult = new ActivityMessage.RewardResult(ruleConfig.get(0).getActivity_id())
                        .addRewardEntity(config.getRewardCurrency(), config.getRewardValue(), config.getExchangeCurrency(), customerId);

                msg.putRewardResult(rewardResult);
            }
        } catch (Exception e) {
            log.error("InviteDepositIgnoreFirstRule rewardCalc process fail.", e);
            logAlarmUtil.alarm(LogAlarmConstant.CheckPoint.ACTIVITY_DO_CHECK_ERROR, "InviteDepositIgnoreFirstRule activity rewardCalc exception", e);
            flag = false;
        }
        return flag;
    }

    private FiatDepositActivityConfig findCorrectActivityConfig(@NotNull List<FiatDepositActivityConfig> ruleMapList, String currency, BigDecimal amount) {
        return ruleMapList.stream().filter(config -> currency.equals(config.getFiatCurrency()) && amount.compareTo(config.getFiatAmount()) >= 0)   // 币种一致，且充值金额符合条件
                .max((amount1, amount2) -> amount1.getFiatAmount().compareTo(amount2.getFiatAmount())).get();   // 触发的奖励规则中的最大值
    }

    private boolean checkNewUserReward(ActivityMessage request, List<ActivityRuleMap> ruleConfig) {
        try {
            String customerId = request.getCustomerId();
            ActivityConstant.RuleEvent ruleEvent = ActivityConstant.RuleEvent.valueOf(request.getEvent());
            if (StringUtils.isBlank(customerId) || ruleEvent != ActivityConstant.RuleEvent.KIKI_FIAT_DEPOSIT) {
                log.warn("InviteDepositIgnoreFirstRule customId[{}] is null or event not match[{}].", customerId, request.getEvent());
                return false;
            }

            // 活动检查
            JsonResult jsonResult = activityService.findById(ruleConfig.get(0).getActivity_id());
            Activity activity = null;
            if (!jsonResult.getSuccess() || (activity = (Activity) jsonResult.getObj()) == null) {
                log.error("InviteDepositIgnoreFirstRule activity not find. activityId={}.", ruleConfig.get(0).getActivity_id());
                return false;
            }

            String depositCurrency = request.fetchParamsJson().getString("fiatCurrency"); // 充值币种
            BigDecimal depositAmount = request.fetchParamsJson().getBigDecimal("fiatAmount"); // 充值金额

            // 充值金额和币种检查
            if ("HKD".equalsIgnoreCase(depositCurrency) || "USD".equalsIgnoreCase(depositCurrency)) {
                // 在 startTime 开始之前没有入金过
                if (!isAlreadyDeposit(request, activity.getStart_time(), customerId)) {
                    // 已经有过奖励的，不能再领奖
                    if (!isAlreadyRewarded(activity.getId(), activity.getExecute_type(), customerId)) {
                        List<FiatDepositActivityConfig> configList = ruleConfig.get(0).fetchConfigList(); // 活动的配置信息
                        log.info("InviteDepositIgnoreFirstRule, configList=[{}], depositCurrency=[{}], depositAmount=[{}]", JSON.toJSONString(configList), depositCurrency, depositAmount);
                        return configList.stream().anyMatch(config -> depositCurrency.equals(config.getFiatCurrency())
                                && depositAmount.compareTo(config.getFiatAmount()) >= 0);
                    }
                }
            }
        } catch (Exception e) {
            log.error("InviteDepositIgnoreFirstRule check process fail.", e);
            logAlarmUtil.alarm(LogAlarmConstant.CheckPoint.ACTIVITY_DO_CHECK_ERROR, "InviteDepositIgnoreFirstRule activity check exception", e);
        }
        return false;
    }

    */
/**
     * 判断在 fromTime 之前，是否有入金过
     *
     * @param msg
     * @param endTime
     * @param customerId
     * @return
     *//*

    private boolean isAlreadyDeposit(ActivityMessage msg, Date endTime, String customerId) throws PayException {
        List<OtcOrderDTO> list = remoteOtcOrderService.getSuccessDepositByCustomerId(msg.getSaasId(), customerId, endTime, 2);

        ActivityConstant.RuleEvent ruleEvent = ActivityConstant.RuleEvent.valueOf(msg.getEvent());

        return !CollectionUtils.isEmpty(list) && list.stream().anyMatch(otcOrder -> !StringUtils.equals(otcOrder.getCustomerId()
                + "_" + ruleEvent.code() + "_" + otcOrder.getId(), msg.getBusinessId()));
    }

    */
/**
     * 判断是否已经领取过某个活动的奖励
     *
     * @param activityId
     * @param customerId
     * @return
     *//*

    private boolean isAlreadyRewarded(Integer activityId, Integer executeType, String customerId) {
        ActivityConstant.RecordStatus[] statusArray = {ActivityConstant.RecordStatus.RECORDED, ActivityConstant.RecordStatus.PROCESSING, ActivityConstant.RecordStatus.COMPLETED};  // 需要参与统计的活动记录的状态：已登记/处理中/已完成
        List<ActivityRecords> list = activityRecordsBuilder.getByCustomerIdAndStatus(executeType, customerId, activityId, statusArray, 1);
        if (!CollectionUtils.isEmpty(list)) {
            log.info("InviteDepositIgnoreFirstRule during activity, count(activityRecord)={}, already rewarded. customerId={}", list.size(), customerId);
            return true;
        }
        return false;
    }

}
*/
