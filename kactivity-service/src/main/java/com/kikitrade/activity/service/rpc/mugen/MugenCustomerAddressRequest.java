package com.kikitrade.activity.service.rpc.mugen;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kikitrade.activity.service.common.config.MugenProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/12 11:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class MugenCustomerAddressRequest extends AccessToken {

    private MugenProperties mugenProperties;

    public MugenCustomerAddressRequest() {
    }

    public MugenCustomerAddressRequest(MugenProperties mugenProperties, AccessToken accessToken) {
        this.mugenProperties = mugenProperties;
        this.accessToken = accessToken.getAccessToken();
    }


    public List<String> execute() {
        Request request = new Request.Builder()
                .url(this.mugenProperties.getDomain() + "/v2/customer")
                .addHeader("JWT_TOKEN", this.accessToken)
                .build();

        try {
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                log.info("[MugenCustomerAddressRequest] response:{}", jsonObject);
                return Optional.ofNullable(jsonObject.getJSONObject("obj"))
                        .map(o -> o.getJSONObject("addressAll"))
                        .map(o -> o.getJSONObject("address"))
                        .map(o -> o.getJSONObject("osp_address"))
                        .map(o -> o.getJSONArray("evm"))
                        .map(o -> JSON.parseArray(o.toJSONString(), String.class)).orElse((null));
            }
            return null;
        } catch (Exception e) {
            log.error("[MugenCustomerAddressRequest] accessToken:{}, error:", accessToken, e);
            return null;
        }
    }
}
