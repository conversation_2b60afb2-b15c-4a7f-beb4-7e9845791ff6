package com.kikitrade.activity.service.rpc.discord;

import okhttp3.OkHttpClient;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/21 16:16
 */
public class HttpPoolUtil {

    static OkHttpClient client;
    static OkHttpClient nftClient;

    static {
        client = new OkHttpClient().newBuilder()
                .build();
        client.dispatcher().setMaxRequests(20);
        client.dispatcher().setMaxRequestsPerHost(20);

        nftClient = new OkHttpClient().newBuilder()
                .build();
        nftClient.dispatcher().setMaxRequests(10);
        nftClient.dispatcher().setMaxRequestsPerHost(10);
    }

    public static OkHttpClient getHttpClient(){
        if(client != null){
            return client;
        }
        client = new OkHttpClient().newBuilder()
                .build();
        client.dispatcher().setMaxRequests(20);
        client.dispatcher().setMaxRequestsPerHost(20);
        return client;
    }

    public static OkHttpClient getNftHttpClient(){
        if(nftClient != null){
            return nftClient;
        }
        nftClient = new OkHttpClient().newBuilder()
                .build();
        nftClient.dispatcher().setMaxRequests(10);
        nftClient.dispatcher().setMaxRequestsPerHost(10);
        return nftClient;
    }
}
