package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.dal.tablestore.model.ActivityRecords;

import java.util.Date;
import java.util.List;

/**
 * created by jacky. 2020/1/15 3:54 PM
 */
public interface ActivityRecordsBuilder {

    Boolean saveIfNotExist(ActivityRecords ars);


    long countByBusinessId(Integer execute_type, String business_id, Integer activity_id);

    long countByCustomerId(Integer execute_type, String customerId, Integer activity_id);

    List<ActivityRecords> getByCustomerIdAndStatus(Integer execute_type, String customerId, Integer activity_id, ActivityConstant.RecordStatus[] includeStatus, int limit);

    List<ActivityRecords> getByCustomerIdAndStatusFromTime(Integer execute_type, String customerId, Integer activity_id, Date fromTime, ActivityConstant.RecordStatus[] includeStatus, int limit);

    long countByActivityType(Integer execute_type, Date deadline, Integer status);

    long countByActivityIdAndType(int activity_id, Integer execute_type, Date deadline, Integer status);

    List<ActivityRecords> findForDispatchById(int activity_id, Integer execute_type, Date startTime, Date deadline, Integer status, Integer limit);


    List<ActivityRecords> findForDispatchByType(Integer execute_type, Date startTime, Date deadline, Integer status, Integer limit);

    boolean updateStatus(String business_id, Integer activity_id, Integer execute_type, Integer status, String remark);

    ActivityRecords findByBusinessId(Integer execute_type, String business_id, Integer activity_id, Integer status);


    List<ActivityRecords> findByCustomerAndActivity(String customerId, Integer activityId, int offset, int limit);


}
