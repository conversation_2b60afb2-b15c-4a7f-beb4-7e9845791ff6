package com.kikitrade.activity.service.job;

import com.kikitrade.activity.service.business.ActivityCompleteJobService;
import com.kikitrade.framework.elasticjob.KiKiSimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 检测是否有待导入的数据，有则调度 ActivityImportJob
 */
@Slf4j
@Component
public class ActivityCompleteJob extends KiKiSimpleJob {

    @Resource
    @Lazy
    private ActivityCompleteJobService activityCompleteJobService;

    @Override
    protected void doExecute(ShardingContext shardingContext) throws Exception {
        activityCompleteJobService.exec(shardingContext);
    }
}
