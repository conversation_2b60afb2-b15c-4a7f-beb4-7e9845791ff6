package com.kikitrade.activity.service.flow.step.processor;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.flow.AbstractDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-12 13:38
 */
@Component
@StepScope
@Slf4j
public class RewardProcessor extends AbstractDataService implements ItemProcessor<ActivityCustomReward, ActivityCustomReward> {

    @Value("#{jobParameters['batchId']}")
    private String batchId;

    @Override
    public ActivityCustomReward process(ActivityCustomReward item) throws Exception {
        ActivityBatch activityBatch = batchCache.getUnchecked(batchId);
        if(!ActivityConstant.BatchRewardStatusEnum.APPROVE.isEquals(activityBatch.getRewardStatus())
            && "Token".equalsIgnoreCase(item.getRewardType())){
            return null;
        }
        return item;
    }
}
