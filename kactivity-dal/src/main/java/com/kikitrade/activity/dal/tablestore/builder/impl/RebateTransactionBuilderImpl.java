package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.RebateTransactionBuilder;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.RebateTransaction;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static com.kikitrade.activity.dal.tablestore.model.RebateTransaction.INDEX_REFERRER_CUSTOMER;

/**
 * created by jacky. 2020/1/19 4:55 PM
 */
@Repository
public class RebateTransactionBuilderImpl extends WideColumnStoreBuilder<RebateTransaction> implements RebateTransactionBuilder {

    @PostConstruct
    public void init() {
        super.init(RebateTransaction.class);
    }

    @Override
    public Boolean insertIfNotExist(RebateTransaction rebateTransaction) {
        return putRow(rebateTransaction, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public BigDecimal getTotalReward(String customerId) {

        String aggName = "sum_agg_total_reward" + UUID.randomUUID();
        SearchQuery query = SearchQuery.newBuilder()
                .query(QueryBuilders.bool()
                        .filter(QueryBuilders.term("referrer_id", customerId))
                )
                .addAggregation(AggregationBuilders.sum(aggName, "amount")).build();
        double v = searchSum(query, INDEX_REFERRER_CUSTOMER, aggName);
        return new BigDecimal(v);
    }

    @Override
    public long getRewardCount(String referredId) {
        String aggName = "count_agg_total_reward" + UUID.randomUUID();
        SearchQuery query = SearchQuery.newBuilder()
                .query(QueryBuilders.bool()
                        .filter(QueryBuilders.term("referrer_id", referredId))
                )
                .addAggregation(AggregationBuilders.count(aggName, "business_id")).build();

        return searchCount(query, INDEX_REFERRER_CUSTOMER, aggName);
    }

    @Override
    public List<RebateTransaction> selectByRefferId(String referredId, int startIndex, int totalNum) {
        BoolQuery q = QueryBuilders.bool()
                .filter(QueryBuilders.term("referrer_id", referredId))
                .build();
        return super.search(q, null, startIndex, totalNum, INDEX_REFERRER_CUSTOMER);
    }


}
