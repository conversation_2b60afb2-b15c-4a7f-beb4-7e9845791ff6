package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.mock.RewardCsvServiceImplMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.business.impl.RewardCsvServiceImpl;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import static org.mockito.ArgumentMatchers.anyString;

@Component
public class RewardCsvServiceImplMockImpl implements RewardCsvServiceImplMock {

    @Resource
    RewardCsvServiceImpl rewardCsvService;

    @Override
    public void write() {
        Mockito.doAnswer(c->{
            BusinessOP.auditFinished = true;
            return null;
        }).when(rewardCsvService).write(anyString(), Mockito.any(ActivityRewardPageParam.class));

    }
}
