package com.kikitrade.activity.service.job;

import com.google.common.base.Splitter;
import com.kikitrade.activity.service.business.ActivityRosterImportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 将发奖名单导入到发奖表中
 */
@Slf4j
@Component
public class ActivityImportJob implements SimpleJob {

    @Autowired
    private ActivityRosterImportService activityRosterImportService;

    private static final String BATCH_KEY = "batchId";
    private static final String ACTIVITY_KEY = "activityId";

    private static final Splitter.MapSplitter splitter = Splitter.on(";").withKeyValueSeparator(":");

    /**
     * 接收到的参数有activityId、batchId
     * @param shardingContext
     */
    @Override
    public void execute(ShardingContext shardingContext) {
        log.info(shardingContext.getJobName() + " start");
        if(StringUtils.isBlank(shardingContext.getJobParameter())){
            return;
        }
        Map<String,String> jobParam = splitter.split(shardingContext.getJobParameter());
        String batchId = jobParam.get(BATCH_KEY);
        String activityId = jobParam.get(ACTIVITY_KEY);
        if(StringUtils.isBlank(batchId) || StringUtils.isBlank(activityId)){
            return;
        }
        activityRosterImportService.execute(batchId, activityId);
    }
}
