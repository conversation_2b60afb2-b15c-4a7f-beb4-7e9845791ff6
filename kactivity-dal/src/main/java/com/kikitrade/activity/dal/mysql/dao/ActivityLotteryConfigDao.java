package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.ActivityLotteryConfig;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.One;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ActivityLotteryConfigDao extends BaseDao<ActivityLotteryConfig, String> {

    @Select("select `id`,`valid`,`status`,`remark`,`vip_level` as vipLevel, `amount`, `currency`, `times_limit` as timeLimit, `reward_limit` as rewardLimit" +
            " from activity_lottery_config where valid = #{valid} and vip_level like concat('%',#{vipLevel},'%') and status != -1")
    List<ActivityLotteryConfig> findByValidAndVip(@Param("valid") String valid, @Param("vipLevel") String vipLevel);

    @Select("select `id`,`valid`,`status`,`remark`,`vip_level` as vipLevel, `amount`, `currency`, `times_limit` as timeLimit, `reward_limit` as rewardLimit" +
            " from activity_lottery_config where valid = #{valid} and vip_level like concat('%',#{vipLevel},'%') and status = 1 and type = #{type}")
    List<ActivityLotteryConfig> findByValidAndVipAndStatus(@Param("valid") String valid, @Param("vipLevel") String vipLevel, @Param("type") String type);

    @Select("<script>" +
            "select `id`,`valid`,`status`,`remark`,`vip_level` as vipLevel, `amount`, `currency`, `times_limit` as timeLimit, `reward_limit` as rewardLimit" +
            " from activity_lottery_config where <if test=\"valid!=null\"> valid = #{valid} and </if> status in(0, 1) order by valid, id desc limit #{offset},#{limit}" +
            "</script>")
    List<ActivityLotteryConfig> findByValid(@Param("valid") String valid, int offset, int limit);
}
