package com.kikitrade.activity.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.jupiter.api.Assertions;
import org.junit.platform.commons.util.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 实体类相关方法
 */
public class EntityUtils {

    /**
     * 根据字段名称存值
     *
     * @param entity    实体类
     * @param fieldName 字段名称
     * @param value     值
     */
    @Deprecated
    public static void setAttribute(Object entity, String fieldName, Object value) {
        try {
            Field f = getField(entity, fieldName);
            f.setAccessible(true);
            final Class<?> type = f.getType();
            if ("java.math.BigDecimal".equals(type.getName())) {
                f.set(entity, new BigDecimal(value.toString()));
            } else if ("java.lang.Integer".equals(type.getName())) {
                f.set(entity, new Integer(value.toString()));
            } else if ("java.util.Date".equals(type.getName())) {
                f.set(entity, new Date(value.toString()));
            } else {
                f.set(entity, value);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据字段名称取值,子类找不到，就去父类找
     *
     * @param entity    实体类
     * @param fieldName 字段名称
     */
    @Deprecated
    public static String getAttribute(Object entity, String fieldName) {
        String r = "";
        boolean find = false;
        try {
            Class<?> aClass = entity.getClass();

            while (!find) {
                List<String> collect = Arrays.stream(aClass.getDeclaredFields()).map(Field::getName).collect(Collectors.toList());
                if (collect.contains(fieldName)) {
                    Field declaredField = aClass.getDeclaredField(fieldName);
                    declaredField.setAccessible(true);
                    r = declaredField.get(entity).toString();
                    break;
                }
                Class<?> superclass = aClass.getSuperclass();
                if (superclass != Object.class) {
                    collect = Arrays.stream(superclass.getDeclaredFields()).map(Field::getName).collect(Collectors.toList());
                    if (collect.contains(fieldName)) {
                        Field declaredField = superclass.getDeclaredField(fieldName);
                        declaredField.setAccessible(true);
                        r = declaredField.get(entity).toString();
                        break;
                    }
                } else {
                    break;
                }
                aClass = superclass;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return r;
    }

    /**
     * 根据字段名称取值,子类找不到，就去父类找
     *
     * @param entity    实体类
     * @param fieldName 字段名称
     */
    public static Field getField(Object entity, String fieldName) {
        Field declaredField = null;
        boolean find = false;
        try {
            Class<?> aClass = entity.getClass();

            while (!find) {
                List<String> collect = Arrays.stream(aClass.getDeclaredFields()).map(Field::getName).collect(Collectors.toList());
                if (collect.contains(fieldName)) {
                    declaredField = aClass.getDeclaredField(fieldName);
                    break;
                }
                Class<?> superclass = aClass.getSuperclass();
                if (superclass != Object.class) {
                    collect = Arrays.stream(superclass.getDeclaredFields()).map(Field::getName).collect(Collectors.toList());
                    if (collect.contains(fieldName)) {
                        declaredField = superclass.getDeclaredField(fieldName);
                        break;
                    }
                } else {
                    break;
                }
                aClass = superclass;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return declaredField;
    }

    public static void updateEntity(Object from, Object to, List<String> fields) throws IllegalAccessException {
        if (fields == null || fields.size() == 0) {
            return;
        }

        Assertions.assertEquals(from.getClass().getName(), to.getClass().getName(), "require the same class");

        for (String _field : fields) {
            String field = StrUtils.underlineTransferSmallHump(_field);
            updateEntity(from, to, field);
        }

    }

    public static void updateEntity(Object from, Object to, List<String> fields, Boolean includeNull) throws IllegalAccessException {
        JSONObject object = JSON.parseObject(JSON.toJSONString(from, SerializerFeature.WriteMapNullValue, SerializerFeature.QuoteFieldNames));
        List<String> remove = new ArrayList();
        for (String field : fields) {
            Object value = object.get(StrUtils.underlineTransferSmallHump(field));
            if (!includeNull) {
                if (value == null) {
                    remove.add(field);
                }
            }
        }
        fields.removeAll(remove);

        if (fields.size() == 0) {
            return;
        }

        Assertions.assertEquals(from.getClass().getName(), to.getClass().getName(), "require the same class");

        for (String _field : fields) {
            String field = StrUtils.underlineTransferSmallHump(_field);
            updateEntity(from, to, field);
        }

    }

    /**
     * 将from的数据更新到to中
     *
     * @param from
     * @param to
     * @param includeNull 是否包括from中为null的数据
     * @throws IllegalAccessException
     */
    public static void updateEntity(Object from, Object to, Boolean includeNull) throws IllegalAccessException {
        List<String> fields = new ArrayList<>();
        Gson gson = new GsonBuilder().serializeNulls().create();
        String toJson = gson.toJson(from);

//        String toJSONString = JSON.toJSONString(from, SerializerFeature.WriteMapNullValue, SerializerFeature.QuoteFieldNames);
//        JSONObject object = (JSONObject) JSON.toJSON(from);
        JSONObject object = JSON.parseObject(toJson);
        Set<String> keySet = object.keySet();
        for (String field : keySet) {
            Object value = object.get(field);
            if (!includeNull) {
                if (value == null) {
                    continue;
                }
                fields.add(field);
            } else {
                fields.add(field);
            }
        }

        if (fields.size() == 0) {
            return;
        }

        Assertions.assertEquals(from.getClass().getName(), to.getClass().getName(), "require the same class");

        for (String _field : fields) {
            String field = StrUtils.underlineTransferSmallHump(_field);
            updateEntity(from, to, field);
        }

    }

    public static void updateEntity(Object from, Object to, String field) throws IllegalAccessException {
        Assertions.assertEquals(from.getClass().getName(), to.getClass().getName(), "require the same class");
        if (StringUtils.isBlank(field)) {
            return;
        }
        String _field = StrUtils.underlineTransferSmallHump(field);
        Field fromField = getField(from, _field);
        setField(to, _field, fromField, from);
    }

    /**
     * 更新entity
     *
     * @param entity    用于更新的
     * @param fieldName 字段名称
     * @param newField  新的字段
     * @param newEntity 需要更新的
     * @throws IllegalAccessException
     */
    public static void setField(Object entity, String fieldName, Field newField, Object newEntity) throws IllegalAccessException {
        Field f = getField(entity, fieldName);
        f.setAccessible(true);
        newField.setAccessible(true);
        Object newValue = newField.get(newEntity);
        f.set(entity, newValue);
    }

}



