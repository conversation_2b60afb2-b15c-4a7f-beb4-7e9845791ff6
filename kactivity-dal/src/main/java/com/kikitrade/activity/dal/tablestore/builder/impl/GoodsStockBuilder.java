package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.dipbit.dtm.client.domain.stock.StockWideColumnStoreBuilder;
import com.dipbit.dtm.client.domain.stock.constant.StockConstant;
import com.kikitrade.activity.dal.tablestore.model.GoodsStock;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/1/3 14:30
 */
@Component
public class GoodsStockBuilder extends StockWideColumnStoreBuilder<GoodsStock> {

    @PostConstruct
    public void init(){
        super.init(GoodsStock.class);
    }

    public boolean insert(GoodsStock goodsStock) {
        return super.putRow(goodsStock, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }


    public boolean incrementStock(String skuId, Integer increment) {
        GoodsStock goodsStock = new GoodsStock();
        goodsStock.setSku(skuId);
        goodsStock.setOrderId(skuId);
        goodsStock.setType(StockConstant.Type.stock.name());
        goodsStock.setChannel("default");
        return super.incrementCount(goodsStock, increment, "available");
    }


    public GoodsStock findById(String skuId) {
        GoodsStock goodsStock = new GoodsStock();
        goodsStock.setSku(skuId);
        goodsStock.setOrderId(skuId);
        goodsStock.setType(StockConstant.Type.stock.name());
        goodsStock.setChannel("default");
        return super.getRow(goodsStock);
    }
}
