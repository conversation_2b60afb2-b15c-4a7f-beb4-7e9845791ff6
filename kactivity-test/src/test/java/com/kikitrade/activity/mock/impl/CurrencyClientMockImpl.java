package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.CurrencyClientMock;
import com.kikitrade.market.client.CurrencyClient;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import static org.mockito.ArgumentMatchers.anyString;


@Component
public class CurrencyClientMockImpl implements CurrencyClientMock {
    @Resource
    CurrencyClient currencyClient;

    @Override
    public void get() {

        Mockito.doAnswer(c->{

            String currency = c.getArgument(0);

            return null;
        }).when(currencyClient).get(anyString());

    }

}
