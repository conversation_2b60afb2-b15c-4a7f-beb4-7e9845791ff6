package com.kikitrade.activity.service.common.config;

import cn.hutool.extra.spring.SpringUtil;
import com.kikitrade.activity.service.common.UploadOssUtil;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.Collections;

/**
 * Activity Configuration
 *
 * <AUTHOR>
 * @create 2021/9/7 12:47 上午
 * @modify
 */
@Configuration
public class ActivityConfiguration {

    @Value("${app.oss.endpoint}")
    private String endpoint;

    @Value("${app.oss.accesskey.id}")
    private String accessKeyId;

    @Value("${app.oss.accesskey.secret}")
    private String accessKeySecret;

    @Value("${app.oss.bucket.private}")
    private String bucketName;

    @Value("${app.oss.path.activity}")
    private String path;

    @Value("${app.oss.bucket.referer}")
    private String referer;

    @Bean
    public UploadOssUtil uploadOssUtil() {
        if(StringUtils.isBlank(referer)){
            return new UploadOssUtil(endpoint, accessKeyId, accessKeySecret, bucketName, path, null);
        }else{
            return new UploadOssUtil(endpoint, accessKeyId, accessKeySecret, bucketName, path, Collections.singletonList(referer));
        }
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean
    public RedissonClient redissonClient(RedisProperties redisProperties){
        String host = redisProperties.getHost();
        int port = redisProperties.getPort();
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://"+host+":"+port)
                .setPassword(redisProperties.getPassword())
                .setDatabase(redisProperties.getDatabase());
        return Redisson.create(config);
    }

    @Bean("okHttpClient")
    public OkHttpClient okHttpClient(KactivityProperties kactivityProperties){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        client.dispatcher().setMaxRequests(kactivityProperties.getHttpPoolSize());
        client.dispatcher().setMaxRequestsPerHost(kactivityProperties.getHttpPoolSize());
        return client;
    }
}
