package com.kikitrade.activity.api.model.activity;

import com.kikitrade.framework.common.model.Page;
import com.kikitrade.market.common.model.constant.CoinCodeConstant;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/12/13 3:13 下午
 * @modify
 */
@Data
@Builder
public class InviteRewardDTO implements Serializable {
    // 奖励记录
    private Page<RewardVO> rewards;

    // 获得总奖励
    private RewardTotalDTO rewardTotal;

    // 奖励币种
    private String currency = CoinCodeConstant.USDT.getCode();
}
