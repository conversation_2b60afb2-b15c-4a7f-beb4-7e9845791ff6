package com.kikitrade.activity.api;

import com.kikitrade.activity.api.model.response.ClaimResponse;
import com.kikitrade.activity.model.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/8 16:46
 */
public interface RemoteClaimService {

    /**
     * 兑换
     * @param address
     * @return
     */
    Result<ClaimResponse> claimItem(String businessType, String customerId, String address, String code, String saasId, List<String> addresses);

    /**
     * 是否兑换完了NFT
     * @param address
     * @return
     */
    Boolean allowClaim(String businessType, String customerId, String address, List<String> addresses);

    /**
     * 管理端-批量创建
     * @param codes 兑换码列表
     * @return
     */
    Boolean batchCreateItem(String businessType, List<String> codes);
}
