package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.kikitrade.activity.dal.tablestore.builder.CustomerQuestionSetsBuilder;
import com.kikitrade.activity.dal.tablestore.model.CustomerQuestionSets;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/29 15:26
 */
@Slf4j
@Component
public class CustomerQuestionSetsBuilderImpl extends WideColumnStoreBuilder<CustomerQuestionSets>
        implements CustomerQuestionSetsBuilder {

    @PostConstruct
    public void init(){
        super.init(CustomerQuestionSets.class);
    }

    @Override
    public boolean insert(CustomerQuestionSets customerQuestionSets) {
        return super.putRow(customerQuestionSets, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public Long incrementAvailableSets(CustomerQuestionSets customerQuestionSets, int inc) {
        return super.incrementAndReturn(customerQuestionSets, inc, "available_sets");
    }

    @Override
    public Long incrementUsedSets(CustomerQuestionSets customerQuestionSets) {
        return super.incrementAndReturn(customerQuestionSets, 1, "used_sets");
    }

    @Override
    public boolean update(CustomerQuestionSets customerQuestionSets) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(customerQuestionSets, condition);
    }

    @Override
    public CustomerQuestionSets findByUser(String saasId, String customerId) {
        CustomerQuestionSets questionSets = new CustomerQuestionSets();
        questionSets.setSaasId(saasId);
        questionSets.setCustomerId(customerId);
        return super.getRow(questionSets);
    }
}
