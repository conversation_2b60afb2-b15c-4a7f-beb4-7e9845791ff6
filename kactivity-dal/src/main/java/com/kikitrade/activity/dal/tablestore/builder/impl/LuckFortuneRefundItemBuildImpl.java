package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneRefundItemBuild;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneRefundItem;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.LuckFortuneRefundItem.INDEX_FORTUNE_REFUND_BY_STATUS;

/**
 *
 */
@Service
public class LuckFortuneRefundItemBuildImpl extends WideColumnStoreBuilder<LuckFortuneRefundItem> implements LuckFortuneRefundItemBuild {

    @Resource
    private SeqGeneraterService seqGeneraterService;

    @Override
    public String getTableName() {
        return "luck_fortune_refund_item";
    }

    @PostConstruct
    public void init() {
        init(LuckFortuneRefundItem.class);
    }
    
    /**
     * 插入一个退款的红包
     *
     * @param luckFortuneRefundItem
     * @return
     */
    @Override
    public boolean insert(LuckFortuneRefundItem luckFortuneRefundItem) {
        luckFortuneRefundItem.setRefundId(seqGeneraterService.next(CustomerSeqRuleBuilder.instance(getTableName())));
        luckFortuneRefundItem.setCreated(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        luckFortuneRefundItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        return super.putRow(luckFortuneRefundItem, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    /**
     * 更新红包到退款成功
     *
     * @param luckFortuneRefundItem
     * @return
     */
    @Override
    public boolean update(LuckFortuneRefundItem luckFortuneRefundItem) {
        luckFortuneRefundItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(luckFortuneRefundItem, condition);
    }



    /**
     * 根据状态查询退款记录
     * @param status
     * @return
     */
    @Override
    public PageResult findByStatus(Integer status, int offset, int limit) {
        BoolQuery builder = QueryBuilders.bool().must(QueryBuilders.term("status", status)).build();
        Sort sort = new Sort(Arrays.asList(new FieldSort("refund_id", SortOrder.ASC)));
        return pageSearch(builder, sort, offset, limit, INDEX_FORTUNE_REFUND_BY_STATUS);
    }

    /**
     * 查询退款记录
     *
     * @param originBusinessId
     * @param businessId
     * @return
     */
    @Override
    public LuckFortuneRefundItem findObject(String originBusinessId, String businessId) {
        LuckFortuneRefundItem item = new LuckFortuneRefundItem();
        item.setOriginBusinessId(originBusinessId);
        item.setBusinessId(businessId);
        return super.getRow(item);
    }
}
