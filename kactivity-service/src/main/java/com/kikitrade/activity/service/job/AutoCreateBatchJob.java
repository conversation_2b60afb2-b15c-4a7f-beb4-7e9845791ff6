package com.kikitrade.activity.service.job;

import com.alibaba.fastjson.JSON;
import com.aliyun.odps.Instance;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus;
import com.kikitrade.activity.facade.task.AwardTimeType;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.autoreward.AutoRewardService;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.framework.elasticjob.KiKiSimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 自动创建批次, 每小时执行一次
 */
@Slf4j
@Component
public class AutoCreateBatchJob extends KiKiSimpleJob {

    @Resource
    @Lazy
    private ActivityEntityService activityEntityService;
    @Resource
    @Lazy
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    @Lazy
    private ActivityTaskService activityTaskService;
    @Resource
    @Lazy
    private AutoRewardService autoRewardService;
    @Resource
    @Lazy
    private ActivityBatchStatusStoreBuilder activityBatchStatusStoreBuilder;
    @Resource
    @Lazy
    private RedisService redisService;

    @Override
    protected void doExecute(ShardingContext shardingContext) throws Exception {
        AtomicBoolean exception = new AtomicBoolean(false);
        activityEntityService.findByAutoCreateBatch(new Date()).forEach(activityEntity -> {
            try{
                log.info("AutoCreateBatchJob:{}", JSON.toJSONString(activityEntity));
                ActivityTaskConfig taskConfig = null;
                if(StringUtils.isNotBlank(activityEntity.getTaskId())){
                    taskConfig = activityTaskService.findTaskById(activityEntity.getTaskId());
                    log.info("AutoCreateBatchJob:{}", JSON.toJSONString(taskConfig));
                }
                Boolean isContinue = isContinue(activityEntity, taskConfig);
                if(isContinue){
                    log.info("AutoCreateBatchJob:{},{}", activityEntity.getId() ,isContinue);
                    //创建批次
                    ActivityBatch activityBatch = new ActivityBatch();
                    activityBatch.setActivityId(activityEntity.getId());
                    activityBatch.setActivityName(activityEntity.getName());
                    activityBatch.setName(String.format("%s-%s",activityEntity.getName(), TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD)));
                    activityBatch.setRemark("");
                    activityBatch.setRewardConfig(activityEntity.getRewardConfig());
                    if(!existBatch(activityEntity)){
                        Result<String> result = activityBatchNewService.save(activityBatch, activityEntity);
                        if(result.isSuccess()){
                            //执行sql
                            if(StringUtils.isNotBlank(activityEntity.getTemplateCode())){
                                Instance instance = autoRewardService.executeTradeRecord(activityEntity, result.getData());
                                log.info("AutoCreateBatchJob instance:{}", instance.getId());
                                ActivityBatch batch = activityBatchNewService.findByBatchId(result.getData());
                                batch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.ODPS_RUNNING.name());
                                batch.setInstance(instance.getId());
                                activityBatchNewService.updateBatch(batch);
                                ActivityBatchStatus activityBatchStatus = new ActivityBatchStatus();
                                activityBatchStatus.setBatchId(batch.getBatchId());
                                activityBatchStatus.setStatus(ActivityConstant.BatchRewardStatusEnum.ODPS_RUNNING.name());
                                activityBatchStatus.setCreated(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
                                activityBatchStatus.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
                                activityBatchStatusStoreBuilder.insert(activityBatchStatus);
                            }
                        }
                    }
                    Date nextTime = activityEntityService.getNextCreateTime(ActivityConstant.ActivityStatusEnum.getStatus(activityEntity.getStatus()).name(), taskConfig != null ? taskConfig.getStatus() : null
                            ,activityEntity.getNextCreateTime(), activityEntity.getCycle(), null, activityEntity.getId());
                    if(activityEntity.getId() != null && redisService.hHasKey(RedisKeyConst.ACTIVITY_CAL_LAST_KEY.getPrefix(), activityEntity.getId())){
                        redisService.hDel(RedisKeyConst.ACTIVITY_CAL_LAST_KEY.getPrefix(), activityEntity.getId());
                    }
                    activityEntity.setNextCreateTime(nextTime);
                    activityEntityService.updateNextCreateTime(activityEntity);
                }
            }catch (Exception ex){
                log.error("AutoCreateBatchJob error", ex);
                exception.set(true);
            }
        });

        if(exception.get()){
            throw new Exception();
        }
    }

    private Boolean isContinue(ActivityEntity activityEntity, ActivityTaskConfig taskConfig){
        if(StringUtils.isBlank(activityEntity.getCycle())){
            return false;
        }
        if(taskConfig == null){
            if(!ActivityConstant.ActivityStatusEnum.ACTIVE.isEquals(activityEntity.getStatus())){
                redisService.hSet(RedisKeyConst.ACTIVITY_CAL_LAST_KEY.getPrefix(), activityEntity.getId(), "true");
            }
            //未到达发奖时间
            if((activityEntity.getNextCreateTime() == null
                    || TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHH_0000).compareTo(TimeUtil.getUtcTime(activityEntity.getNextCreateTime(), TimeUtil.YYYYMMDDHH_0000)) < 0)){
                return false;
            }
            //活动未开始
            if(activityEntity.getStartTime().after(new Date())){
                return false;
            }
            //活动已经结束一个周期
            if(activityEntity.getEndTime() != null && calCycle(activityEntity.getCycle(), activityEntity.getEndTime()).before(activityEntity.getNextCreateTime())){
                return false;
            }
        }else{
            if(ActivityConstant.ActivityTaskStatus.ACTIVE.getStatus() != taskConfig.getStatus()){
                redisService.hSet(RedisKeyConst.ACTIVITY_CAL_LAST_KEY.getPrefix(), activityEntity.getId(), "true");
            }
            //未到达发奖时间
            if((activityEntity.getNextCreateTime() == null
                    || TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHH_0000).compareTo(TimeUtil.getUtcTime(activityEntity.getNextCreateTime(), TimeUtil.YYYYMMDDHH_0000)) < 0)){
                return false;
            }
            //活动未开始
            if(taskConfig.getStartTime().after(new Date())){
                return false;
            }
            //活动已经结束一个周期
            if(taskConfig.getEndTime() != null && calCycle(activityEntity.getCycle() ,taskConfig.getEndTime()).before(activityEntity.getNextCreateTime())){
                return false;
            }
            if(taskConfig.getAwardTimeType() == AwardTimeType.REALTIME.getNumber()){
                return false;
            }
        }
        return true;
    }

    private boolean existBatch(ActivityEntity activityEntity){
        ActivityBatch lastBatch = activityBatchNewService.findLastBatchByActivity(activityEntity.getId());
        return lastBatch != null && activityEntity.getNextCreateTime() != null &&
                lastBatch.getBatchId().startsWith(TimeUtil.getUtcTime(activityEntity.getNextCreateTime(), TimeUtil.YYYYMMDD));
    }

    private Date calCycle(String cycle, Date date){
        ActivityConstant.BatchCycleEnum frequency = ActivityConstant.BatchCycleEnum.valueOf(cycle);
        switch (frequency){
            case EVERY_DAY:
            case FINISH:
                return TimeUtil.parse(TimeUtil.getDataStr(TimeUtil.addDay(date, 1), TimeUtil.YYYY_MM_DD_235959));
            case EVERY_MONTH:
                //每天11点执行
                return TimeUtil.parse(TimeUtil.getDataStr(TimeUtil.addMonth(date, 1), TimeUtil.YYYY_MM_DD_235959));
            case EVERY_WEEK:
                return TimeUtil.parse(TimeUtil.getDataStr(TimeUtil.addWeek(date, 1), TimeUtil.YYYY_MM_DD_235959));
        }
        return date;
    }

}
