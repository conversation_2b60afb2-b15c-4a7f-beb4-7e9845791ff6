package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.kikitrade.activity.dal.tablestore.builder.ActivityJoinItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityJoinItem;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-12 19:47
 */
@Service
public class ActivityJoinBuilderImpl extends WideColumnStoreBuilder<ActivityJoinItem> implements ActivityJoinItemBuilder {

    @PostConstruct
    public void init(){
        init(ActivityJoinItem.class);
    }

    @Override
    public boolean insert(ActivityJoinItem activityJoinItem) {
        return putRow(activityJoinItem);
    }

    @Override
    public boolean update(ActivityJoinItem activityJoinItem) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return updateRow(activityJoinItem);
    }

    @Override
    public ActivityJoinItem queryById(String userName, String activityCode) {
        ActivityJoinItem request = new ActivityJoinItem();
        request.setUserName(userName);
        request.setActivityCode(activityCode);
        return getRow(request);
    }
}
