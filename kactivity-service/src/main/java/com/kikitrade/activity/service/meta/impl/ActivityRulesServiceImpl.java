package com.kikitrade.activity.service.meta.impl;

import com.kikitrade.activity.dal.mysql.dao.ActivityRulesDao;
import com.kikitrade.activity.dal.mysql.model.ActivityRules;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.service.meta.ActivityRulesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityRulesServiceImpl implements ActivityRulesService {


    @Resource
    private ActivityRulesDao activityRulesDao;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult save(ActivityRules activityRules) throws Exception {
        JsonResult result = new JsonResult();
        //  do some convert;
        try {
            int count = activityRulesDao.insert(activityRules);
            if (count > 0) {
                result.setObj(activityRules).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityrulesserviceimpl save process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
            throw e;
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delete(Integer id) throws Exception {
        JsonResult result = new JsonResult();
        try {
            int count = activityRulesDao.deleteById(id);
            if (count >= 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());

            }
        } catch (Exception e) {
            log.error("activityrulesserviceimpl delete process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
            throw e;
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult insert(ActivityRules activityRules) throws Exception {
        JsonResult result = new JsonResult();
        try {

            int count = activityRulesDao.insert(activityRules);
            if (count > 0) {
                result.setObj(activityRules).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityrulesserviceimpl insert process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
            throw e;
        }

        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult update(ActivityRules activityRules) throws Exception {
        JsonResult result = new JsonResult();
        if (activityRules.getId() == 0) {
            return result.setSuccess(false).setCode(ActivityExceptionType.CAN_NOT_BE_NULL.getCode()).setMsg(ActivityExceptionType.CAN_NOT_BE_NULL.getParaMsg("activityrules [id] "));
        }
        int count = 0;

        ActivityRules ar = activityRulesDao.findById(activityRules.getId());
        try {
            if (ar == null) {
                count = activityRulesDao.insert(activityRules);
            } else {
                count = activityRulesDao.update(activityRules);
            }
            if (count > 0) {
                result.setObj(activityRules).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }

        } catch (Exception e) {
            log.error("activityrulesserviceimpl update process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
            throw e;
        }

        return result;
    }


    @Override
    public JsonResult findAll() {

        JsonResult result = new JsonResult();
        try {
            List<ActivityRules> ActivityRulesList = activityRulesDao.findAll();
            if (ActivityRulesList == null || ActivityRulesList.size() == 0) {
                result.setObj(ActivityRulesList).setSuccess(true).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                result.setObj(ActivityRulesList).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityrulesserviceimpl findAll process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }


    @Override
    public JsonResult findById(Integer id) {
        JsonResult result = new JsonResult();
        try {
            ActivityRules activityRules = activityRulesDao.findById(id);
            if (activityRules == null) {
                result.setObj(activityRules).setSuccess(true).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                result.setObj(activityRules).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityrulesserviceimpl findById process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }


}
