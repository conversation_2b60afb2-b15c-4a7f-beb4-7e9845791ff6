package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.ClientExchangeServiceMock;
import com.kikitrade.market.client.service.impl.ClientExchangeService;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;

@Component
public class ClientExchangeServiceMockImpl implements ClientExchangeServiceMock {
    @Resource
    ClientExchangeService clientExchangeService;

    @Override
    public void exchangeByPrecision() {
        Mockito.doAnswer(c -> {
            BigDecimal amount = c.getArgument(0);
            String fromCurrency = c.getArgument(1) ;
            String toCurrency= c.getArgument(2);
            Integer precision= c.getArgument(3);
            RoundingMode roundingMode= c.getArgument(4);
            //BTC--->USD
            return amount.multiply(new BigDecimal("10000"));
        }).when(clientExchangeService).exchangeByPrecision(Mockito.any(BigDecimal.class), anyString(), anyString(), anyInt(), Mockito.any(RoundingMode.class));

    }
}
