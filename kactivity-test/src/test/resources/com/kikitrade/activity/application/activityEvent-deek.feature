# language: zh-CN
功能: activity-event
  #task list
  #https://docs.google.com/spreadsheets/d/1qJFappj7X8SyRKfv3OdL32JStdqdXsliJ4hNQYl5dIM/edit?gid=2034963204#gid=2034963204

  背景:
    假如task-初始化任务code配置TaskCodeConfig

    #|连续签到天数|获得point      |获得ticket |
    #|1         |200           |3         |
    #|2         |500           |3         |
    #|3         |1800          |3         |
    #|4         |3000          |3         |
    #|5         |4500          |4         |
    #|6         |8000          |5         |
    #|7         |15000         |6         |
  场景大纲: 连续签到1天
    假如task-初始化任务配置task_config<taskConfig>
    同时task-已存在主任务activity_task_item<primaryActivityTaskItem>
    同时task-已存在子任务activity_task_item<subActivityTaskItem>
    当task-系统活动事件监听<eventDTO>
    那么task-校验activity_task_item<activityTaskItem>
    那么task-校验activity_customer_reward<activityCustomReward>
    那么task-校验assetTransferInRequest<assetTransferInRequest>
    那么task-校验ActivityResponse<activityResponse>
    例子:
      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | primaryActivityTaskItem                                                                                                                                                                                                                                                                                        | subActivityTaskItem                                                                                                                                                                                                                                                       | eventDTO                                                                                                                                                                                                  | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | assetTransferInRequest                                                                                                                                                                                                                                                                                                                                                                                                                                                     | activityResponse                                                                                                                                                                                     |
    # 连续签到1天
      | [{"taskId":"412001","groupId":null,"isGroup":false,"showList":false,"title":"Consecutive check-ins","desc":"consecutive check-ins","saasId":"deek-tg","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"check_in","limitMap":{"NORMAL":-1},"rewardFrequency":7,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"POINT","amount":"200","currency":"POINT","vipLevel":"NORMAL","index":1},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":1}],"2":[{"type":"POINT","amount":"500","currency":"POINT","vipLevel":"NORMAL","index":2},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":2}],"3":[{"type":"POINT","amount":"1800","currency":"POINT","vipLevel":"NORMAL","index":3},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":3}],"4":[{"type":"POINT","amount":"3000","currency":"POINT","vipLevel":"NORMAL","index":4},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":4}],"5":[{"type":"POINT","amount":"4500","currency":"POINT","vipLevel":"NORMAL","index":5},{"type":"TICKET","amount":"4","currency":"TICKET","vipLevel":"NORMAL","index":5}],"6":[{"type":"POINT","amount":"8000","currency":"POINT","vipLevel":"NORMAL","index":6},{"type":"TICKET","amount":"5","currency":"TICKET","vipLevel":"NORMAL","index":6}],"7":[{"type":"POINT","amount":"15000","currency":"POINT","vipLevel":"NORMAL","index":7},{"type":"TICKET","amount":"6","currency":"TICKET","vipLevel":"NORMAL","index":7}]},"order":2,"domain":"social","vipLevel":"+0","btn":3,"showProgress":true,"channel":"app","position":"check_in","skipVerification":false,"callRegister":false}] | []                                                                                                                                                                                                                                                                                                             | []                                                                                                                                                                                                                                                                        | [{"eventCode":"check_in","customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"saasId":"deek-tg"}}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 07:13:59","progress":1,"status":"APPENDING","targetId":"-1","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-07 07:13:59","created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 07:13:59","status":"DONE","targetId":"20241107","taskId":"412001"}]                                                                                                                                                                                                                                                                               | [{"amount":"200.0","batchId":"19990101","businessId":"202411071536110064202411070","businessType":"activity_task","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"POINT","saasId":"deek-tg","seq":"check_in:202411071536110064202411070:POINT","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"3.0","batchId":"19990101","businessId":"202411071536110064202411070","businessType":"activity_task","currency":"TICKET","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"TICKET","saasId":"deek-tg","seq":"check_in:202411071536110064202411070:TICKET","status":"AWARD_SUCCESS","userType":"Normal"}]   | [{"amount":200.0,"businessId":"20241107074111202411071541110762202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"POINT"},{"amount":3.0,"businessId":"20241107074111202411071541110762202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"TICKET"}]   | [{"code":"SUCCESS","obj":[{"amount":"200.0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"},{"amount":"3.0","currency":"TICKET","index":1,"type":"TICKET","vipLevel":"NORMAL"}]}]   |
    # 连续签到2天
      | [{"taskId":"412001","groupId":null,"isGroup":false,"showList":false,"title":"Consecutive check-ins","desc":"consecutive check-ins","saasId":"deek-tg","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"check_in","limitMap":{"NORMAL":-1},"rewardFrequency":7,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"POINT","amount":"200","currency":"POINT","vipLevel":"NORMAL","index":1},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":1}],"2":[{"type":"POINT","amount":"500","currency":"POINT","vipLevel":"NORMAL","index":2},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":2}],"3":[{"type":"POINT","amount":"1800","currency":"POINT","vipLevel":"NORMAL","index":3},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":3}],"4":[{"type":"POINT","amount":"3000","currency":"POINT","vipLevel":"NORMAL","index":4},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":4}],"5":[{"type":"POINT","amount":"4500","currency":"POINT","vipLevel":"NORMAL","index":5},{"type":"TICKET","amount":"4","currency":"TICKET","vipLevel":"NORMAL","index":5}],"6":[{"type":"POINT","amount":"8000","currency":"POINT","vipLevel":"NORMAL","index":6},{"type":"TICKET","amount":"5","currency":"TICKET","vipLevel":"NORMAL","index":6}],"7":[{"type":"POINT","amount":"15000","currency":"POINT","vipLevel":"NORMAL","index":7},{"type":"TICKET","amount":"6","currency":"TICKET","vipLevel":"NORMAL","index":7}]},"order":2,"domain":"social","vipLevel":"+0","btn":3,"showProgress":true,"channel":"app","position":"check_in","skipVerification":false,"callRegister":false}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 07:13:59","progress":1,"status":"APPENDING","targetId":"-1","taskId":"412001"}] | [{"businessId":"202411071513590930","completeTime":"2024-11-07 07:13:59","created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 07:13:59","status":"DONE","targetId":null,"taskId":"412001"}] | [{"eventCode":"check_in","customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"saasId":"deek-tg"}}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 08:00:24","progress":2,"status":"APPENDING","targetId":"-1","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-06 16:00:24","created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-06 16:00:24","status":"DONE","targetId":"20241106","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-07 08:00:24","created":"2024-11-07 08:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 08:00:24","status":"DONE","targetId":"20241107","taskId":"412001"}] | [{"amount":"500.0","batchId":"19990101","businessId":"202411071513590930202411070","businessType":"activity_task","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"POINT","saasId":"deek-tg","seq":"check_in:202411071513590930202411070:POINT","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"3.0","batchId":"19990101","businessId":"202411071513590930202411070","businessType":"activity_task","currency":"TICKET","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"TICKET","saasId":"deek-tg","seq":"check_in:202411071513590930202411070:TICKET","status":"AWARD_SUCCESS","userType":"Normal"}]   | [{"amount":500.0,"businessId":"20241107080435202411071513590930202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"POINT"},{"amount":3.0,"businessId":"20241107080435202411071513590930202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"TICKET"}]   | [{"code":"SUCCESS","obj":[{"amount":"500.0","currency":"POINT","index":2,"type":"POINT","vipLevel":"NORMAL"},{"amount":"3.0","currency":"TICKET","index":2,"type":"TICKET","vipLevel":"NORMAL"}]}]   |
    # 连续签到3天
      | [{"taskId":"412001","groupId":null,"isGroup":false,"showList":false,"title":"Consecutive check-ins","desc":"consecutive check-ins","saasId":"deek-tg","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"check_in","limitMap":{"NORMAL":-1},"rewardFrequency":7,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"POINT","amount":"200","currency":"POINT","vipLevel":"NORMAL","index":1},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":1}],"2":[{"type":"POINT","amount":"500","currency":"POINT","vipLevel":"NORMAL","index":2},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":2}],"3":[{"type":"POINT","amount":"1800","currency":"POINT","vipLevel":"NORMAL","index":3},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":3}],"4":[{"type":"POINT","amount":"3000","currency":"POINT","vipLevel":"NORMAL","index":4},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":4}],"5":[{"type":"POINT","amount":"4500","currency":"POINT","vipLevel":"NORMAL","index":5},{"type":"TICKET","amount":"4","currency":"TICKET","vipLevel":"NORMAL","index":5}],"6":[{"type":"POINT","amount":"8000","currency":"POINT","vipLevel":"NORMAL","index":6},{"type":"TICKET","amount":"5","currency":"TICKET","vipLevel":"NORMAL","index":6}],"7":[{"type":"POINT","amount":"15000","currency":"POINT","vipLevel":"NORMAL","index":7},{"type":"TICKET","amount":"6","currency":"TICKET","vipLevel":"NORMAL","index":7}]},"order":2,"domain":"social","vipLevel":"+0","btn":3,"showProgress":true,"channel":"app","position":"check_in","skipVerification":false,"callRegister":false}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 07:13:59","progress":2,"status":"APPENDING","targetId":"-1","taskId":"412001"}] | [{"businessId":"202411071513590930","completeTime":"2024-11-07 07:13:59","created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 07:13:59","status":"DONE","targetId":null,"taskId":"412001"}] | [{"eventCode":"check_in","customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"saasId":"deek-tg"}}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 08:00:24","progress":3,"status":"APPENDING","targetId":"-1","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-06 16:00:24","created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-06 16:00:24","status":"DONE","targetId":"20241106","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-07 08:00:24","created":"2024-11-07 08:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 08:00:24","status":"DONE","targetId":"20241107","taskId":"412001"}] | [{"amount":"1800.0","batchId":"19990101","businessId":"202411071513590930202411070","businessType":"activity_task","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"POINT","saasId":"deek-tg","seq":"check_in:202411071513590930202411070:POINT","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"3.0","batchId":"19990101","businessId":"202411071513590930202411070","businessType":"activity_task","currency":"TICKET","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"TICKET","saasId":"deek-tg","seq":"check_in:202411071513590930202411070:TICKET","status":"AWARD_SUCCESS","userType":"Normal"}]  | [{"amount":1800.0,"businessId":"20241107080435202411071513590930202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"POINT"},{"amount":3.0,"businessId":"20241107080435202411071513590930202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"TICKET"}]  | [{"code":"SUCCESS","obj":[{"amount":"1800.0","currency":"POINT","index":3,"type":"POINT","vipLevel":"NORMAL"},{"amount":"3.0","currency":"TICKET","index":3,"type":"TICKET","vipLevel":"NORMAL"}]}]  |
    # 连续签到4天
      | [{"taskId":"412001","groupId":null,"isGroup":false,"showList":false,"title":"Consecutive check-ins","desc":"consecutive check-ins","saasId":"deek-tg","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"check_in","limitMap":{"NORMAL":-1},"rewardFrequency":7,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"POINT","amount":"200","currency":"POINT","vipLevel":"NORMAL","index":1},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":1}],"2":[{"type":"POINT","amount":"500","currency":"POINT","vipLevel":"NORMAL","index":2},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":2}],"3":[{"type":"POINT","amount":"1800","currency":"POINT","vipLevel":"NORMAL","index":3},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":3}],"4":[{"type":"POINT","amount":"3000","currency":"POINT","vipLevel":"NORMAL","index":4},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":4}],"5":[{"type":"POINT","amount":"4500","currency":"POINT","vipLevel":"NORMAL","index":5},{"type":"TICKET","amount":"4","currency":"TICKET","vipLevel":"NORMAL","index":5}],"6":[{"type":"POINT","amount":"8000","currency":"POINT","vipLevel":"NORMAL","index":6},{"type":"TICKET","amount":"5","currency":"TICKET","vipLevel":"NORMAL","index":6}],"7":[{"type":"POINT","amount":"15000","currency":"POINT","vipLevel":"NORMAL","index":7},{"type":"TICKET","amount":"6","currency":"TICKET","vipLevel":"NORMAL","index":7}]},"order":2,"domain":"social","vipLevel":"+0","btn":3,"showProgress":true,"channel":"app","position":"check_in","skipVerification":false,"callRegister":false}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 07:13:59","progress":3,"status":"APPENDING","targetId":"-1","taskId":"412001"}] | [{"businessId":"202411071513590930","completeTime":"2024-11-07 07:13:59","created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 07:13:59","status":"DONE","targetId":null,"taskId":"412001"}] | [{"eventCode":"check_in","customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"saasId":"deek-tg"}}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 08:00:24","progress":4,"status":"APPENDING","targetId":"-1","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-06 16:00:24","created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-06 16:00:24","status":"DONE","targetId":"20241106","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-07 08:00:24","created":"2024-11-07 08:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 08:00:24","status":"DONE","targetId":"20241107","taskId":"412001"}] | [{"amount":"3000.0","batchId":"19990101","businessId":"202411071513590930202411070","businessType":"activity_task","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"POINT","saasId":"deek-tg","seq":"check_in:202411071513590930202411070:POINT","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"3.0","batchId":"19990101","businessId":"202411071513590930202411070","businessType":"activity_task","currency":"TICKET","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"TICKET","saasId":"deek-tg","seq":"check_in:202411071513590930202411070:TICKET","status":"AWARD_SUCCESS","userType":"Normal"}]  | [{"amount":3000.0,"businessId":"20241107080435202411071513590930202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"POINT"},{"amount":3.0,"businessId":"20241107080435202411071513590930202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"TICKET"}]  | [{"code":"SUCCESS","obj":[{"amount":"3000.0","currency":"POINT","index":4,"type":"POINT","vipLevel":"NORMAL"},{"amount":"3.0","currency":"TICKET","index":4,"type":"TICKET","vipLevel":"NORMAL"}]}]  |
    # 连续签到5天
      | [{"taskId":"412001","groupId":null,"isGroup":false,"showList":false,"title":"Consecutive check-ins","desc":"consecutive check-ins","saasId":"deek-tg","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"check_in","limitMap":{"NORMAL":-1},"rewardFrequency":7,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"POINT","amount":"200","currency":"POINT","vipLevel":"NORMAL","index":1},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":1}],"2":[{"type":"POINT","amount":"500","currency":"POINT","vipLevel":"NORMAL","index":2},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":2}],"3":[{"type":"POINT","amount":"1800","currency":"POINT","vipLevel":"NORMAL","index":3},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":3}],"4":[{"type":"POINT","amount":"3000","currency":"POINT","vipLevel":"NORMAL","index":4},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":4}],"5":[{"type":"POINT","amount":"4500","currency":"POINT","vipLevel":"NORMAL","index":5},{"type":"TICKET","amount":"4","currency":"TICKET","vipLevel":"NORMAL","index":5}],"6":[{"type":"POINT","amount":"8000","currency":"POINT","vipLevel":"NORMAL","index":6},{"type":"TICKET","amount":"5","currency":"TICKET","vipLevel":"NORMAL","index":6}],"7":[{"type":"POINT","amount":"15000","currency":"POINT","vipLevel":"NORMAL","index":7},{"type":"TICKET","amount":"6","currency":"TICKET","vipLevel":"NORMAL","index":7}]},"order":2,"domain":"social","vipLevel":"+0","btn":3,"showProgress":true,"channel":"app","position":"check_in","skipVerification":false,"callRegister":false}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 07:13:59","progress":4,"status":"APPENDING","targetId":"-1","taskId":"412001"}] | [{"businessId":"202411071513590930","completeTime":"2024-11-07 07:13:59","created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 07:13:59","status":"DONE","targetId":null,"taskId":"412001"}] | [{"eventCode":"check_in","customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"saasId":"deek-tg"}}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 08:00:24","progress":5,"status":"APPENDING","targetId":"-1","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-06 16:00:24","created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-06 16:00:24","status":"DONE","targetId":"20241106","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-07 08:00:24","created":"2024-11-07 08:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 08:00:24","status":"DONE","targetId":"20241107","taskId":"412001"}] | [{"amount":"4500.0","batchId":"19990101","businessId":"202411071513590930202411070","businessType":"activity_task","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"POINT","saasId":"deek-tg","seq":"check_in:202411071513590930202411070:POINT","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"4.0","batchId":"19990101","businessId":"202411071513590930202411070","businessType":"activity_task","currency":"TICKET","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"TICKET","saasId":"deek-tg","seq":"check_in:202411071513590930202411070:TICKET","status":"AWARD_SUCCESS","userType":"Normal"}]  | [{"amount":4500.0,"businessId":"20241107083346202411071513590930202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"POINT"},{"amount":4.0,"businessId":"20241107083346202411071513590930202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"TICKET"}]  | [{"code":"SUCCESS","obj":[{"amount":"4500.0","currency":"POINT","index":5,"type":"POINT","vipLevel":"NORMAL"},{"amount":"4.0","currency":"TICKET","index":5,"type":"TICKET","vipLevel":"NORMAL"}]}]  |
    # 连续签到6天
      | [{"taskId":"412001","groupId":null,"isGroup":false,"showList":false,"title":"Consecutive check-ins","desc":"consecutive check-ins","saasId":"deek-tg","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"check_in","limitMap":{"NORMAL":-1},"rewardFrequency":7,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"POINT","amount":"200","currency":"POINT","vipLevel":"NORMAL","index":1},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":1}],"2":[{"type":"POINT","amount":"500","currency":"POINT","vipLevel":"NORMAL","index":2},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":2}],"3":[{"type":"POINT","amount":"1800","currency":"POINT","vipLevel":"NORMAL","index":3},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":3}],"4":[{"type":"POINT","amount":"3000","currency":"POINT","vipLevel":"NORMAL","index":4},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":4}],"5":[{"type":"POINT","amount":"4500","currency":"POINT","vipLevel":"NORMAL","index":5},{"type":"TICKET","amount":"4","currency":"TICKET","vipLevel":"NORMAL","index":5}],"6":[{"type":"POINT","amount":"8000","currency":"POINT","vipLevel":"NORMAL","index":6},{"type":"TICKET","amount":"5","currency":"TICKET","vipLevel":"NORMAL","index":6}],"7":[{"type":"POINT","amount":"15000","currency":"POINT","vipLevel":"NORMAL","index":7},{"type":"TICKET","amount":"6","currency":"TICKET","vipLevel":"NORMAL","index":7}]},"order":2,"domain":"social","vipLevel":"+0","btn":3,"showProgress":true,"channel":"app","position":"check_in","skipVerification":false,"callRegister":false}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 07:13:59","progress":5,"status":"APPENDING","targetId":"-1","taskId":"412001"}] | [{"businessId":"202411071513590930","completeTime":"2024-11-07 07:13:59","created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 07:13:59","status":"DONE","targetId":null,"taskId":"412001"}] | [{"eventCode":"check_in","customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"saasId":"deek-tg"}}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 08:00:24","progress":6,"status":"APPENDING","targetId":"-1","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-06 16:00:24","created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-06 16:00:24","status":"DONE","targetId":"20241106","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-07 08:00:24","created":"2024-11-07 08:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 08:00:24","status":"DONE","targetId":"20241107","taskId":"412001"}] | [{"amount":"8000.0","batchId":"19990101","businessId":"202411071513590930202411070","businessType":"activity_task","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"POINT","saasId":"deek-tg","seq":"check_in:202411071513590930202411070:POINT","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"5.0","batchId":"19990101","businessId":"202411071513590930202411070","businessType":"activity_task","currency":"TICKET","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"TICKET","saasId":"deek-tg","seq":"check_in:202411071513590930202411070:TICKET","status":"AWARD_SUCCESS","userType":"Normal"}]  | [{"amount":8000.0,"businessId":"20241107084527202411071513590930202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"POINT"},{"amount":5.0,"businessId":"20241107084527202411071513590930202411070","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"TICKET"}]  | [{"code":"SUCCESS","obj":[{"amount":"8000.0","currency":"POINT","index":6,"type":"POINT","vipLevel":"NORMAL"},{"amount":"5.0","currency":"TICKET","index":6,"type":"TICKET","vipLevel":"NORMAL"}]}]  |
    # 连续签到7天
      | [{"taskId":"412001","groupId":null,"isGroup":false,"showList":false,"title":"Consecutive check-ins","desc":"consecutive check-ins","saasId":"deek-tg","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"check_in","limitMap":{"NORMAL":-1},"rewardFrequency":7,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"POINT","amount":"200","currency":"POINT","vipLevel":"NORMAL","index":1},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":1}],"2":[{"type":"POINT","amount":"500","currency":"POINT","vipLevel":"NORMAL","index":2},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":2}],"3":[{"type":"POINT","amount":"1800","currency":"POINT","vipLevel":"NORMAL","index":3},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":3}],"4":[{"type":"POINT","amount":"3000","currency":"POINT","vipLevel":"NORMAL","index":4},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":4}],"5":[{"type":"POINT","amount":"4500","currency":"POINT","vipLevel":"NORMAL","index":5},{"type":"TICKET","amount":"4","currency":"TICKET","vipLevel":"NORMAL","index":5}],"6":[{"type":"POINT","amount":"8000","currency":"POINT","vipLevel":"NORMAL","index":6},{"type":"TICKET","amount":"5","currency":"TICKET","vipLevel":"NORMAL","index":6}],"7":[{"type":"POINT","amount":"15000","currency":"POINT","vipLevel":"NORMAL","index":7},{"type":"TICKET","amount":"6","currency":"TICKET","vipLevel":"NORMAL","index":7}]},"order":2,"domain":"social","vipLevel":"+0","btn":3,"showProgress":true,"channel":"app","position":"check_in","skipVerification":false,"callRegister":false}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 07:13:59","progress":6,"status":"APPENDING","targetId":"-1","taskId":"412001"}] | [{"businessId":"202411071513590930","completeTime":"2024-11-07 07:13:59","created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 07:13:59","status":"DONE","targetId":null,"taskId":"412001"}] | [{"eventCode":"check_in","customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"saasId":"deek-tg"}}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 08:00:24","progress":7,"status":"APPENDING","targetId":"-1","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-06 16:00:24","created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-06 16:00:24","status":"DONE","targetId":"20241106","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-07 08:00:24","created":"2024-11-07 08:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 08:00:24","status":"DONE","targetId":"20241107","taskId":"412001"}] | [{"amount":"15000.0","batchId":"19990101","businessId":"202411071513590930202411071","businessType":"activity_task","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"POINT","saasId":"deek-tg","seq":"check_in:202411071513590930202411071:POINT","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"6.0","batchId":"19990101","businessId":"202411071513590930202411071","businessType":"activity_task","currency":"TICKET","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"TICKET","saasId":"deek-tg","seq":"check_in:202411071513590930202411071:TICKET","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":15000.0,"businessId":"20241107084527202411071513590930202411071","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"POINT"},{"amount":6.0,"businessId":"20241107084527202411071513590930202411071","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"TICKET"}] | [{"code":"SUCCESS","obj":[{"amount":"15000.0","currency":"POINT","index":7,"type":"POINT","vipLevel":"NORMAL"},{"amount":"6.0","currency":"TICKET","index":7,"type":"TICKET","vipLevel":"NORMAL"}]}] |
    # 连续签到8天-同第一天签到
      | [{"taskId":"412001","groupId":null,"isGroup":false,"showList":false,"title":"Consecutive check-ins","desc":"consecutive check-ins","saasId":"deek-tg","status":"ACTIVE","startTime":1711651601000,"endTime":1748495999000,"code":"check_in","limitMap":{"NORMAL":-1},"rewardFrequency":7,"cycle":"once","progressType":"series","rewardForm":"fixed","provideType":"auto","reward":{"1":[{"type":"POINT","amount":"200","currency":"POINT","vipLevel":"NORMAL","index":1},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":1}],"2":[{"type":"POINT","amount":"500","currency":"POINT","vipLevel":"NORMAL","index":2},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":2}],"3":[{"type":"POINT","amount":"1800","currency":"POINT","vipLevel":"NORMAL","index":3},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":3}],"4":[{"type":"POINT","amount":"3000","currency":"POINT","vipLevel":"NORMAL","index":4},{"type":"TICKET","amount":"3","currency":"TICKET","vipLevel":"NORMAL","index":4}],"5":[{"type":"POINT","amount":"4500","currency":"POINT","vipLevel":"NORMAL","index":5},{"type":"TICKET","amount":"4","currency":"TICKET","vipLevel":"NORMAL","index":5}],"6":[{"type":"POINT","amount":"8000","currency":"POINT","vipLevel":"NORMAL","index":6},{"type":"TICKET","amount":"5","currency":"TICKET","vipLevel":"NORMAL","index":6}],"7":[{"type":"POINT","amount":"15000","currency":"POINT","vipLevel":"NORMAL","index":7},{"type":"TICKET","amount":"6","currency":"TICKET","vipLevel":"NORMAL","index":7}]},"order":2,"domain":"social","vipLevel":"+0","btn":3,"showProgress":true,"channel":"app","position":"check_in","skipVerification":false,"callRegister":false}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 07:13:59","progress":7,"status":"APPENDING","targetId":"-1","taskId":"412001"}] | [{"businessId":"202411071513590930","completeTime":"2024-11-07 07:13:59","created":"2024-11-07 07:13:59","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 07:13:59","status":"DONE","targetId":null,"taskId":"412001"}] | [{"eventCode":"check_in","customerId":"2024033102484135902204","globalUid":"","eventTime":1711853762073,"targetId":"","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"saasId":"deek-tg"}}] | [{"businessId":"202411071513590930","completeThreshold":2147483647,"created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","expiredTime":"30000101","modified":"2024-11-07 08:00:24","progress":8,"status":"APPENDING","targetId":"-1","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-06 16:00:24","created":"2024-11-06 16:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-06 16:00:24","status":"DONE","targetId":"20241106","taskId":"412001"},{"businessId":"202411071513590930","completeTime":"2024-11-07 08:00:24","created":"2024-11-07 08:00:24","customerId":"2024033102484135902204","cycle":"19990101","event":"check_in","modified":"2024-11-07 08:00:24","status":"DONE","targetId":"20241107","taskId":"412001"}] | [{"amount":"200.0","batchId":"19990101","businessId":"202411071513590930202411071","businessType":"activity_task","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"POINT","saasId":"deek-tg","seq":"check_in:202411071513590930202411071:POINT","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"3.0","batchId":"19990101","businessId":"202411071513590930202411071","businessType":"activity_task","currency":"TICKET","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Consecutive check-ins\"}","extendParamMap":{"desc":"Consecutive check-ins"},"rewardType":"TICKET","saasId":"deek-tg","seq":"check_in:202411071513590930202411071:TICKET","status":"AWARD_SUCCESS","userType":"Normal"}]   | [{"amount":200.0,"businessId":"20241107084527202411071513590930202411071","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"POINT"},{"amount":3.0,"businessId":"20241107084527202411071513590930202411071","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Consecutive check-ins","saasId":"deek-tg","type":"TICKET"}]   | [{"code":"SUCCESS","obj":[{"amount":"200.0","currency":"POINT","index":1,"type":"POINT","vipLevel":"NORMAL"},{"amount":"3.0","currency":"TICKET","index":1,"type":"TICKET","vipLevel":"NORMAL"}]}]   |

  #1216 beta包不对,所以beta ci此案例不通过,暂不执行

#  场景大纲: join TG channel
#    假如task-初始化任务配置task_config<taskConfig>
#    当task-系统活动事件监听<eventDTO>
#    那么task-校验activity_task_item<activityTaskItem>
#    那么task-校验activity_customer_reward<activityCustomReward>
#    那么task-校验assetTransferInRequest<assetTransferInRequest>
#    那么task-校验ActivityResponse<activityResponse>
#    例子:
#      | taskConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | eventDTO                                                                                                                                                                                                                                                     | activityTaskItem                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | activityCustomReward                                                                                                                                                                                                                                                                                                                                                                                    | assetTransferInRequest                                                                                                                                                                                                   | activityResponse                                                                                                 |
#      | [{"taskId":"412006","groupId":null,"isGroup":false,"showList":true,"titleMap":{"APP":"Join DEEK TG channel","PC":"Join DEEK TG channel"},"ledgerTitle":"Join TG channel","descMap":{"APP_NORMAL":"Join DEEK’s TG channel","PC_NORMAL":"Join DEEK’s TG channel"},"labelName":"","labelColor":"","saasId":"deek-tg","status":"ACTIVE","startTime":1730804400000,"endTime":4859769600000,"code":"join_tg_channel","limitMap":{"NORMAL":1,"L1":1},"rewardFrequency":1,"cycle":"once","progressType":"add","rewardForm":"fixed","provideType":"auto","reward":{"0":[{"type":"POINT","amount":"2000","currency":"POINT","vipLevel":"NORMAL"}]},"link":{},"order":1,"domain":"social","url":"https://t.me/joseph_partner","connectUrl":"","connectUrlPc":"","vipLevel":"+0","btn":0,"showProgress":false,"channel":"tg","position":"","attr":{},"skipVerification":false,"callRegister":false}] | [{"eventCode":"join_tg_channel","customerId":"2024033102484135902204","globalUid":"2024110702385884902000","eventTime":1711853762073,"targetId":"2024110702385884902000","verb":"","targetCustomerId":"","sourceCustomerId":"","body":{"saasId":"deek-tg"}}] | [{"businessId":"202411071722190378","completeThreshold":1,"completeTime":"2024-03-31 02:56:02","created":"2024-11-07 09:22:19","customerId":"2024033102484135902204","cycle":"19990101","event":"join_tg_channel","expiredTime":"30000101","modified":"2024-11-07 09:22:19","progress":1,"status":"DONE","targetId":"-1","taskId":"412006"},{"businessId":"202411071722190378","completeTime":"2024-11-07 09:22:19","created":"2024-11-07 09:22:19","customerId":"2024033102484135902204","cycle":"19990101","event":"join_tg_channel","modified":"2024-11-07 09:22:19","status":"DONE","targetId":"2024110702385884902000","taskId":"412006"}] | [{"amount":"2000.0","batchId":"19990101","businessId":"2024110717241802311","businessType":"activity_task","currency":"POINT","customerId":"2024033102484135902204","extendParam":"{\"desc\":\"Join TG channel\"}","extendParamMap":{"desc":"Join TG channel"},"rewardType":"POINT","saasId":"deek-tg","seq":"join_tg_channel:2024110717241802311:POINT","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":2000.0,"businessId":"202411070926122024110717261203541","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"2024033102484135902204","desc":"Join TG channel","saasId":"deek-tg","type":"POINT"}] | [{"code":"SUCCESS","obj":[{"amount":"2000.0","currency":"POINT","index":0,"type":"POINT","vipLevel":"NORMAL"}]}] |
