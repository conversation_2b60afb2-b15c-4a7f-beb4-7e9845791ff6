package com.kikitrade.activity.mockbean;

import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.member.api.RemoteMemberService;
import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RemoteMemberServiceMockBean {
//    @Bean(name = "remoteMemberServiceStub")
//    public RemoteMemberService remoteMemberService(){
//        RemoteMemberService mock = Mockito.mock(RemoteMemberService.class);
//        return mock;
//    }
}
