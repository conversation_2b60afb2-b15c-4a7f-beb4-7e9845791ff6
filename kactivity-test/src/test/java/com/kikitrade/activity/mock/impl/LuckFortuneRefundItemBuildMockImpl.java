package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneRefundItemBuild;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneRefundItem;
import com.kikitrade.activity.mock.LuckFortuneRefundItemBuildMock;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.util.DateUtil;
import com.kikitrade.activity.util.SeqUtil;
import com.kikitrade.framework.common.util.BeanUtil;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.text.SimpleDateFormat;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.kikitrade.activity.op.BusinessOP.luckFortuneRefundItemMap;
import static org.mockito.ArgumentMatchers.anyString;

@Component
public class LuckFortuneRefundItemBuildMockImpl implements LuckFortuneRefundItemBuildMock {
    @Resource
    LuckFortuneRefundItemBuild luckFortuneRefundItemBuild;

    @Override
    public void findObject() {
        Mockito.doAnswer(c -> {
            String originBusinessId = c.getArgument(0);
            String businessId = c.getArgument(1);

            if(luckFortuneRefundItemMap.isEmpty()){
                return null;
            }

            Optional<LuckFortuneRefundItem> first = luckFortuneRefundItemMap.values().stream().filter(item -> originBusinessId.equals(item.getOriginBusinessId())
                    && businessId.equals(item.getBusinessId())).findFirst();


            if(first.isPresent()){
                LuckFortuneRefundItem refundItem = first.get();
                LuckFortuneRefundItem refundItem1 = new LuckFortuneRefundItem();
                BeanUtil.copyProperties(refundItem, refundItem1);
                return refundItem1;
            }


            return null;



        }).when(luckFortuneRefundItemBuild).findObject(anyString(), anyString());
    }

    @Override
    public void insert() {
        Mockito.doAnswer(c -> {
            LuckFortuneRefundItem luckFortuneRefundItem = c.getArgument(0);
            String refundId = luckFortuneRefundItem.getRefundId();
            if(refundId == null){
                refundId = SeqUtil.getSeq();
                luckFortuneRefundItem.setRefundId(refundId);
                luckFortuneRefundItem.setCreated(DateUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
                luckFortuneRefundItem.setModified(DateUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
                luckFortuneRefundItemMap.put(refundId, luckFortuneRefundItem);
                BusinessOP.refundId = refundId;
                return true;
            }else{
                if(luckFortuneRefundItemMap.containsKey(refundId)){
                    return false;
                }
                luckFortuneRefundItemMap.put(refundId, luckFortuneRefundItem);
                return true;
            }
        }).when(luckFortuneRefundItemBuild).insert(Mockito.any(LuckFortuneRefundItem.class));
    }

    @Override
    public void update() {
        Mockito.doAnswer(c -> {


            LuckFortuneRefundItem luckFortuneRefundItem = c.getArgument(0);
            String refundId = BusinessOP.refundId;
            String businessId = luckFortuneRefundItem.getBusinessId();
            String originBusinessId = luckFortuneRefundItem.getOriginBusinessId();

            if(luckFortuneRefundItemMap.isEmpty()){
                return false;
            }

            Optional<LuckFortuneRefundItem> item1 = luckFortuneRefundItemMap.values().stream().filter(item -> refundId.equals(item.getRefundId())).findFirst();

            if(item1.isPresent()){
                LuckFortuneRefundItem luckFortuneRefundItem1 = item1.get();
                if(luckFortuneRefundItem1.getBusinessId().equals(businessId)&&luckFortuneRefundItem1.getOriginBusinessId().equals(originBusinessId)){
                    luckFortuneRefundItemMap.get(refundId).setStatus(luckFortuneRefundItem.getStatus());
                    luckFortuneRefundItemMap.get(refundId).setModified(luckFortuneRefundItem.getModified());
                    return true;
                }
            }

            return false;


        }).when(luckFortuneRefundItemBuild).update(Mockito.any(LuckFortuneRefundItem.class));

    }
}
