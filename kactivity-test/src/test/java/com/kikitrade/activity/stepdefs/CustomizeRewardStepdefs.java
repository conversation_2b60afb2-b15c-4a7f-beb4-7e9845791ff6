package com.kikitrade.activity.stepdefs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.kikitrade.activity.dal.mysql.dao.ActivityEntityTestDao;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.common.Business;
import com.kikitrade.activity.service.flow.job.BatchJobFactory;
import io.cucumber.java.zh_cn.假如;
import io.cucumber.java.zh_cn.当;
import io.cucumber.java.zh_cn.那么;
import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.Resource;
import java.util.Date;

@Slf4j
public class CustomizeRewardStepdefs {
    String batchId;



    @Resource
    ActivityBatchNewService activityBatchNewService;
    @Resource
    ActivityEntityTestDao activityEntityTestDao;
    @Resource
    BatchJobFactory batchJobFactory;


    String activityId;

    @假如("customize-reward-")
    public void autoReward() {
    }

    @假如("customize-reward-创建任务批次{}")
    public void customizeReward创建任务批次Activity_batch(String activity_batch) {
        ActivityEntity activityEntity = JSON.parseObject(activity_batch, ActivityEntity.class);

        ActivityBatch activityBatch = new ActivityBatch();
        activityBatch.setActivityId(activityEntity.getId());
        activityBatch.setActivityName(activityEntity.getName());
        activityBatch.setName(String.format("%s-%s", activityEntity.getName(), TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD)));
        activityBatch.setRemark("");
        activityBatch.setRewardConfig(activityEntity.getRewardConfig());

        activityBatchNewService.save(activityBatch, activityEntity);
        batchId = activityBatch.getBatchId();
    }


    @当("customize-reward-系统开始审核{}")
    public void customizeReward系统开始审核Audit(String audit) throws InterruptedException {
        activityBatchNewService.audit(batchId, ActivityConstant.AuditTypeEnum.valueOf(audit));

        long start = System.currentTimeMillis();
        while (true){
            System.out.println("----BusinessOP.auditFinished: " + BusinessOP.auditFinished);
            if(BusinessOP.auditFinished){
                break;
            }
            Thread.sleep(2000);
            long end = System.currentTimeMillis();
            if((end - start) > 10 * 1000){
                break;
            }
        }

    }


    @假如("customize-reward-创建活动{}")
    public void customizeReward创建活动Activity_entity(String activity_entity) {
        ActivityEntity activityEntity = JSON.parseObject(activity_entity, ActivityEntity.class);

        activityEntity.setStartTime(new Date());
        activityEntity.setEndTime(new Date());
        activityEntity.setCreated(new Date());
        activityEntity.setModified(new Date());

        activityEntityTestDao.insert(activityEntity);

        activityId = activityEntity.getId();
    }

    @假如("customize-reward-配置测试数据activityCustomReward{}")
    public void customizeReward配置测试数据activityCustomRewardReward(String reward) {
        ActivityCustomReward activityCustomReward = JSON.parseObject(reward, ActivityCustomReward.class);
        activityCustomReward.setBatchId(batchId);
        BusinessOP.activityCustomRewardList.add(activityCustomReward);
    }

    @那么("customize-reward-校验activityBatchList为{}")
    public void customizeReward校验activityBatchList为ActivityBatchList(String activityBatchList) {
        log.info("BusinessOP.activityBatchList:{}", JSON.toJSONString(BusinessOP.activityBatchList));

    }

    @那么("customize-reward-校验activityCustomRewardList为{}")
    public void customizeReward校验activityCustomRewardList为ActivityCustomRewardList(String activityCustomRewardList) {
        log.info("BusinessOP.activityCustomRewardList:{}", JSON.toJSONString(BusinessOP.activityCustomRewardList));

    }

    @当("customize-reward-执行自动发奖任务")
    public void customizeReward执行自动发奖任务() throws InterruptedException {
        batchJobFactory.runAutoMaticRewardJob(activityId, batchId);

        Thread.sleep(1000*60*60);
    }
}
