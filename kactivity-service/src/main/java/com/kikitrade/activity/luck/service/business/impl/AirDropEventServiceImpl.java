package com.kikitrade.activity.luck.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.service.business.AirDropBaseService;
import com.kikitrade.activity.luck.service.business.AirDropEventService;
import com.kikitrade.activity.luck.service.model.AirDropEventDTO;
import com.kikitrade.activity.luck.service.model.ReceiveItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.kevent.client.EventClient;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.model.EventDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-14 14:19
 */
@Service
@Slf4j
public class AirDropEventServiceImpl implements AirDropEventService {

    private static final String DEFAULT_EVENT_PRICE_CURRENCY = "USDT";
    @Lazy
    @Resource
    private OnsProducer onsProducer;
    @Resource
    private KactivityModuleProperties kactivityModuleProperties;
    @Autowired(required = false)
    private EventClient eventClient;
    @Resource
    private AirDropBaseService airDropBaseService;
    /**
     * 领取后事件Î
     *
     * @param receiveItem
     */
    @Override
    public void sendReceiveEvent(ReceiveItem receiveItem) {
        log.info("AirDropEventService receiveItem:{}", receiveItem);
        String topic = kactivityModuleProperties.getLuck().getEventTopic();
        Integer validDays = kactivityModuleProperties.getLuck().getValidMinutes();
        AirDropEventDTO dto = new AirDropEventDTO();
        dto.setEvent(ActivityConstant.AirDropEventEnum.REVERT.name());
        dto.setReleaseId(receiveItem.getReleaseId());
        dto.setReceiveId(receiveItem.getId());
        dto.setReceiveCode(receiveItem.getReceiveCode());
        SendResult result = onsProducer.send(topic, JSON.toJSONString(dto), System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(validDays));
        log.info("sendReceiveEvent{},{},{}", result.getTopic(),result.getMessageId(), receiveItem.getId());
    }

    /**
     * 发放空投后事件
     *
     * @param releaseItem
     */
    @Override
    public void sendReleaseEvent(LuckFortuneReleaseItem releaseItem) {
        log.info("AirDropEventService receiveItem:{}", releaseItem);
        String topic = kactivityModuleProperties.getLuck().getEventTopic();
        Integer validDays = releaseItem.getValidTime();
        Integer validMinutes = kactivityModuleProperties.getLuck().getValidMinutes();
        AirDropEventDTO dto = new AirDropEventDTO();
        dto.setReleaseId(releaseItem.getId());
        dto.setReleaseCustomerId(releaseItem.getCustomerId());
        dto.setEvent(ActivityConstant.AirDropEventEnum.EXPIRE.name());
        onsProducer.send(topic, JSON.toJSONString(dto), System.currentTimeMillis() + TimeUnit.DAYS.toMillis(validDays) + TimeUnit.SECONDS.toMillis(1));
        dto.setEvent(ActivityConstant.AirDropEventEnum.REFUND.name());
        onsProducer.send(topic, JSON.toJSONString(dto), System.currentTimeMillis() + TimeUnit.DAYS.toMillis(validDays) + TimeUnit.MINUTES.toMillis(validMinutes) + TimeUnit.SECONDS.toMillis(1));
    }

    @Override
    public <T> void reportEvent(EventConstants.EventName eventName, T data) {
        try {
            if (eventClient != null) {
                String customerId = "";
                String id = "";
                String currency = "";
                String amount = "";
                switch (eventName) {
                    case RELEASE_RED_POCKET:
                        LuckFortuneReleaseItem luckFortuneReleaseItem = (LuckFortuneReleaseItem) data;
                        customerId = luckFortuneReleaseItem.getCustomerId();
                        id = luckFortuneReleaseItem.getId();
                        currency = luckFortuneReleaseItem.getCurrency();
                        amount = luckFortuneReleaseItem.getAmount();
                        break;
                    case RECEIVE_RED_POCKET:
                        LuckFortuneReceiveItem luckFortuneReceiveItem = (LuckFortuneReceiveItem) data;
                        customerId = luckFortuneReceiveItem.getCustomerId();
                        id = luckFortuneReceiveItem.getReleaseId();
                        currency = luckFortuneReceiveItem.getCurrency();
                        amount = luckFortuneReceiveItem.getAmount();
                        break;
                }
                Integer keepDecimalForCoin = airDropBaseService.getKeepDecimalForCoin(currency);
                // usdt price
                BigDecimal price = airDropBaseService.getPrice(BigDecimal.ONE, currency, DEFAULT_EVENT_PRICE_CURRENCY, keepDecimalForCoin);

                JSONObject body = new JSONObject();
                body.put("price", price);

                EventDTO eventDTO = EventDTO.builder()
                        .name(eventName.getName())
                        .firstUid(customerId)
                        .time(System.currentTimeMillis())
                        .globalUid(id)
                        .customerId(customerId)
                        .amount(new BigDecimal(amount))
                        .currency(currency)
                        .body(body)
                        .build();
                log.info("reportEvent eventName {}, eventDTO {}", eventName, eventDTO);
                eventClient.push(eventDTO);
            }
        }catch (Exception e){
            log.error("reportEvent error, eventName {}, data {}", eventName, data);
        }
    }
}
