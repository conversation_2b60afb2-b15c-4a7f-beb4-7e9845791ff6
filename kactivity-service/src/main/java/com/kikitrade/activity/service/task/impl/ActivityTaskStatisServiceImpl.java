package com.kikitrade.activity.service.task.impl;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskStatisBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskStatis;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.task.ActivityTaskStatisService;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/24 11:25
 */
@Service
@Slf4j
public class ActivityTaskStatisServiceImpl implements ActivityTaskStatisService {

    @Resource
    private ActivityTaskStatisBuilder activityTaskStatisBuilder;

    /**
     * 做任务
     *
     * @param activityEventMessage
     * @param taskConfig
     * @param eventCode
     * @throws Exception
     */
    @Override
    public Long incrementProgress(TaskConfigDTO taskConfig, ActivityEventMessage activityEventMessage, TaskCodeConfig eventCode) throws Exception {
        ActivityTaskStatis taskStatis = new ActivityTaskStatis();
        taskStatis.setTargetId(activityEventMessage.getTargetId());
        taskStatis.setCustomerId(activityEventMessage.getCustomerId());
        taskStatis.setCycle(TaskCycleDomain.getCurrencyCycle(taskConfig, null));
        taskStatis.setTaskId(taskConfig.getTaskId());
        taskStatis.setSaasId(taskConfig.getSaasId());
        int incr = activityEventMessage.getInc() == null ? 1 : activityEventMessage.getInc();
        taskStatis.setProgress(incr);

        ActivityTaskStatis detail = activityTaskStatisBuilder.findDetail(taskStatis);

        try {
            if(detail != null){
                return activityTaskStatisBuilder.incrementProgress(taskStatis,incr);
            }
            boolean success = activityTaskStatisBuilder.insert(taskStatis);
            if(success){
                return taskStatis.getProgress().longValue();
            }
        }catch (Exception ex){
            log.error("ActivityTaskStatis incrementProgress error:{}", activityEventMessage, ex);
        }
        return 0L;
    }
}
