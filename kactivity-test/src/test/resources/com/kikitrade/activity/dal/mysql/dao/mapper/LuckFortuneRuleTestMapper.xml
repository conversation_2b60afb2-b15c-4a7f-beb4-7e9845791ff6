<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.LuckFortuneRuleTestDao">
    <resultMap id="BaseResultMap" type="com.kikitrade.activity.dal.mysql.model.LuckFortuneRule">
        <result column="kyc_level" property="kycLevel"/>
        <result column="user_type" property="userType"/>
        <result column="release_min" property="releaseMin"/>
        <result column="release_max" property="releaseMax"/>
        <result column="release_num_max" property="releaseNumMax"/>
        <result column="release_account_days" property="releaseAccountDays"/>
        <result column="release_account_months" property="releaseAccountMonths"/>
        <result column="receive_min" property="receiveMin"/>
        <result column="receive_max" property="receiveMax"/>
        <result column="receive_account_days" property="receiveAccountDays"/>
        <result column="receive_account_months" property="receiveAccountMonths"/>
        <result column="receive_num_days" property="receiveNumDays"/>
        <result column="receive_num_months" property="receiveNumMonths"/>
    </resultMap>

    <update id="createTableIfNotExist">

        CREATE TABLE `luck_fortune_rule` (
                                             `id` varchar(32) NOT NULL COMMENT 'pk',
                                             `kyc_level` varchar(32) NOT NULL COMMENT 'KYC等级',
                                             `user_type` varchar(32) NOT NULL COMMENT '用户类型 kyc_level + user_type 唯一',
                                             `release_min` varchar(50) NOT NULL COMMENT '发放红包最小金额',
                                             `release_max` varchar(50) NOT NULL COMMENT '发放红包最大金额',
                                             `release_num_max` int(11) NOT NULL COMMENT '单个红包最大数量',
                                             `release_account_days` varchar(50) NOT NULL COMMENT '24小时发放累积限额',
                                             `release_account_months` varchar(50) NOT NULL COMMENT '30天发放累积限额',
                                             `receive_min` varchar(32) NOT NULL COMMENT '单个红包领取最小金额',
                                             `receive_max` varchar(32) NOT NULL COMMENT '单个红包领取最大金额',
                                             `receive_account_days` varchar(32) NOT NULL COMMENT '24小时领取累积限额',
                                             `receive_account_months` varchar(32) NOT NULL COMMENT '30天领取累积限额',
                                             `receive_num_days` varchar(32) NOT NULL COMMENT '24小时领取累积数量限制',
                                             `receive_num_months` varchar(32) NOT NULL COMMENT '30天领取累积数量限制',
                                             `status` tinyint(4) NOT NULL COMMENT '活动状态',
                                             `created` datetime NOT NULL COMMENT '创建时间',
                                             `modified` datetime NOT NULL COMMENT '修改时间',
                                             `saasId` varchar(16) NOT NULL COMMENT 'saasId',
                                             PRIMARY KEY (`id`),
                                             UNIQUE KEY `uniq_kyc_user` (`kyc_level`,`user_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='红包规则'
        ;


    </update>
    <select id="selectAll" resultMap="BaseResultMap">

        select * from luck_fortune_rule;

    </select>

    <insert id="init">
        INSERT INTO `luck_fortune_rule` (`id`,`kyc_level`,`user_type`,`release_min`,`release_max`,`release_num_max`,`release_account_days`,`release_account_months`,`receive_min`,`receive_max`,`receive_account_days`,`receive_account_months`,`receive_num_days`,`receive_num_months`,`status`,`created`,`modified`,`saasId`) VALUES ('2022081609393636200200','L0','COMMON','1.5','50',50,'500','2000','1.5','50','500','50','2000','100',0,'2022-08-16 09:39:36','2022-11-01 09:11:04','kiki');
        INSERT INTO `luck_fortune_rule` (`id`,`kyc_level`,`user_type`,`release_min`,`release_max`,`release_num_max`,`release_account_days`,`release_account_months`,`receive_min`,`receive_max`,`receive_account_days`,`receive_account_months`,`receive_num_days`,`receive_num_months`,`status`,`created`,`modified`,`saasId`) VALUES ('2022081609591848500201','L1','COMMON','1','200',200,'100000000000000000','400000000000000000','1','120','300','300','250','350',0,'2022-08-16 09:59:18','2022-11-01 09:10:51','kiki');
        INSERT INTO `luck_fortune_rule` (`id`,`kyc_level`,`user_type`,`release_min`,`release_max`,`release_num_max`,`release_account_days`,`release_account_months`,`receive_min`,`receive_max`,`receive_account_days`,`receive_account_months`,`receive_num_days`,`receive_num_months`,`status`,`created`,`modified`,`saasId`) VALUES ('2022081612070386700202','L1','KOL','11','500',200,'500','800','1','200','200','600','400','800',0,'2022-08-16 12:07:03','2022-08-25 05:41:55','kiki');
        INSERT INTO `luck_fortune_rule` (`id`,`kyc_level`,`user_type`,`release_min`,`release_max`,`release_num_max`,`release_account_days`,`release_account_months`,`receive_min`,`receive_max`,`receive_account_days`,`receive_account_months`,`receive_num_days`,`receive_num_months`,`status`,`created`,`modified`,`saasId`) VALUES ('2022081612225164800400','L1','MEDIA','200','600',200,'600','800','1','300','100','200','800','1000',0,'2022-08-16 12:22:51','2022-08-24 07:25:54','kiki');
        INSERT INTO `luck_fortune_rule` (`id`,`kyc_level`,`user_type`,`release_min`,`release_max`,`release_num_max`,`release_account_days`,`release_account_months`,`receive_min`,`receive_max`,`receive_account_days`,`receive_account_months`,`receive_num_days`,`receive_num_months`,`status`,`created`,`modified`,`saasId`) VALUES ('2022081707512686500600','L1','ORGANIZATION','11','110',30,'221','1001','1','50','20','40','100','300',0,'2022-08-17 07:51:26','2022-08-24 07:24:53','kiki');
        INSERT INTO `luck_fortune_rule` (`id`,`kyc_level`,`user_type`,`release_min`,`release_max`,`release_num_max`,`release_account_days`,`release_account_months`,`receive_min`,`receive_max`,`receive_account_days`,`receive_account_months`,`receive_num_days`,`receive_num_months`,`status`,`created`,`modified`,`saasId`) VALUES ('2022082303052471300800','L2','COMMON','50','150',20,'180','200','2','8','40','50','200','300',0,'2022-08-23 03:05:24','2022-08-27 03:09:05','kiki');
        INSERT INTO `luck_fortune_rule` (`id`,`kyc_level`,`user_type`,`release_min`,`release_max`,`release_num_max`,`release_account_days`,`release_account_months`,`receive_min`,`receive_max`,`receive_account_days`,`receive_account_months`,`receive_num_days`,`receive_num_months`,`status`,`created`,`modified`,`saasId`) VALUES ('2022082303062350501000','L2','KOL','61','160',30,'18001','201000','2','8','50','100','8','16',0,'2022-08-23 03:06:23','2022-09-02 03:18:48','kiki');
        INSERT INTO `luck_fortune_rule` (`id`,`kyc_level`,`user_type`,`release_min`,`release_max`,`release_num_max`,`release_account_days`,`release_account_months`,`receive_min`,`receive_max`,`receive_account_days`,`receive_account_months`,`receive_num_days`,`receive_num_months`,`status`,`created`,`modified`,`saasId`) VALUES ('2022082303072617700801','L2','MEDIA','40','190',10,'200','250','2','9','10','20','190','250',0,'2022-08-23 03:07:26','2022-08-23 03:07:26','kiki');
        INSERT INTO `luck_fortune_rule` (`id`,`kyc_level`,`user_type`,`release_min`,`release_max`,`release_num_max`,`release_account_days`,`release_account_months`,`receive_min`,`receive_max`,`receive_account_days`,`receive_account_months`,`receive_num_days`,`receive_num_months`,`status`,`created`,`modified`,`saasId`) VALUES ('2022082303084541400802','L2','ORGANIZATION','20.5','150.1',123,'200.234','260.567','0.01','2','20','50','180','300',0,'2022-08-23 03:08:45','2022-09-23 09:06:11','kiki');
        INSERT INTO `luck_fortune_rule` (`id`,`kyc_level`,`user_type`,`release_min`,`release_max`,`release_num_max`,`release_account_days`,`release_account_months`,`receive_min`,`receive_max`,`receive_account_days`,`receive_account_months`,`receive_num_days`,`receive_num_months`,`status`,`created`,`modified`,`saasId`) VALUES ('2022092210240255101200','L0','KOL','0.0001','2.12345',1,'2.5','2.999','0.00001','2','1.123456','1','2','2',0,'2022-09-22 10:24:02','2022-09-23 06:25:22','kiki');
    </insert>

</mapper>

