package com.kikitrade.activity.service.common.mapper.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.facade.award.ActivityBatch;
import com.kikitrade.activity.facade.award.ActivitySourceEnum;
import com.kikitrade.activity.facade.award.BatchStatusEnum;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.common.mapper.ActivityBatchMapStruct;
import com.kikitrade.activity.service.model.RewardRule;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ActivityBatchMapStructImpl implements ActivityBatchMapStruct {

    @Override
    public ActivityBatch serviceToRpc(com.kikitrade.activity.dal.tablestore.model.ActivityBatch activityBatch) {
        if(activityBatch == null){
            return null;
        }
        ActivityBatch.Builder builder = ActivityBatch.newBuilder()
                .setActivityId(activityBatch.getActivityId() == null ? "" : activityBatch.getActivityId())
                .setActivityName(activityBatch.getActivityName() == null ? "" : activityBatch.getActivityName())
                .setId(activityBatch.getBatchId() == null ? "" : activityBatch.getBatchId())
                .setAmended(activityBatch.getAmended() == null ? "" : activityBatch.getAmended())
                .setAmount(activityBatch.getAmount() == null ? "" : activityBatch.getAmount())
                .setCurrency(activityBatch.getCurrency() == null ? "" : activityBatch.getCurrency())
                .setGenerateTime(activityBatch.getGeneralTime() == null ? "" : activityBatch.getGeneralTime())
                .setModified(activityBatch.getModified() == null ? "" : activityBatch.getModified())
                .setName(activityBatch.getName() == null ? "" : activityBatch.getName())
                .setOssUrl(activityBatch.getOssUrl() == null ? "" : activityBatch.getOssUrl())
                .setSourceOssUrl(activityBatch.getSourceOssUrl() == null ? "" : activityBatch.getSourceOssUrl())
                .setPrizeAmount(activityBatch.getPrizeAmount() == null ? "" : activityBatch.getPrizeAmount())
                .setRemark(activityBatch.getRemark() == null ? "" : activityBatch.getRemark())
                .setRewardType(activityBatch.getRewardType() == null ? "" : activityBatch.getRewardType())
                .setSaasId(activityBatch.getSaasId() == null ? "" : activityBatch.getSaasId())
                .setScheduled(activityBatch.getScheduled() != null && activityBatch.getScheduled())
                .setScheduledTime(activityBatch.getScheduledTime() == null ? "" : activityBatch.getScheduledTime())
                .setStatus(activityBatch.getRewardStatus() == null ? BatchStatusEnum.NOT_IMPORTED : BatchStatusEnum.forNumber(ActivityConstant.BatchRewardStatusEnum.valueOf(activityBatch.getRewardStatus()).getCode()))
                .setWinners(activityBatch.getWinners() == null ? "" : String.valueOf(activityBatch.getWinners()))
                .setActivityType(activityBatch.getActivityType() == null ? "" : String.valueOf(activityBatch.getActivityType()));

        if(StringUtils.isNotBlank(activityBatch.getRewardConfig())){
            List<com.kikitrade.activity.facade.award.RewardRule> rules
                    = JSON.parseArray(activityBatch.getRewardConfig(), RewardRule.class).stream().map(RewardRule::toFacade).collect(Collectors.toList());
            builder.addAllRewardRule(rules);
        }
        return builder.build();
    }

    @Override
    public List<ActivityBatch> serviceToRpcList(List<com.kikitrade.activity.dal.tablestore.model.ActivityBatch> activityBatchList) {
        return Optional.ofNullable(activityBatchList).orElse(new ArrayList<>()).stream().map(this::serviceToRpc).collect(Collectors.toList());
    }
}
