package com.kikitrade.activity.mockbean;


import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RemoteCustomerServiceMockBean {
    @Bean(name = "remoteCustomerServiceStub")
    public RemoteCustomerService remoteCustomerService(){
        return Mockito.mock(RemoteCustomerService.class);
    }
}
