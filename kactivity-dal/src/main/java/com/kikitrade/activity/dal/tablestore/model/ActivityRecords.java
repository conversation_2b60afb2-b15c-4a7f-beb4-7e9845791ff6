package com.kikitrade.activity.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/7/11 17:07
 */
@Getter
@Setter
@Table(name = "activity_records")
public class ActivityRecords implements Serializable {

    // 多元索引
    public static final String INDEX_COUNT = "activity_records_index_biz_count";
    public static final String INDEX_EXECUTE_TYPE_STATUS = "activity_records_execute_type_status_index";
    public static final String INDEX_ACTIVITY_CUSTOMER = "activity_records_activity_customer_index";

    // 二级索引
    public static final String INDEX_ACTIVITY_RECORDS_BY_CUSTOMER = "idx_activity_records_by_customer";

    //business_id
    @PartitionKey(value = 0, name = "business_id")
    @SearchIndex(name = {INDEX_EXECUTE_TYPE_STATUS, INDEX_COUNT}, column = "business_id")
    @Index(name = INDEX_ACTIVITY_RECORDS_BY_CUSTOMER, pkColumn = "business_id", pkValue = 3)
    private String business_id;

    //用户id
    @Column(name = "customer_id", isDefined = true)
    @SearchIndex(name = {INDEX_ACTIVITY_CUSTOMER, INDEX_COUNT}, column = "customer_id")
    @Index(name = INDEX_ACTIVITY_RECORDS_BY_CUSTOMER, pkColumn = "customer_id")
    private String customer_id;

    //创建时间

    @Column(name = "create_time", isDefined = true)
    @SearchIndex(name = {INDEX_COUNT}, column = "create_time", fieldType = FieldType.LONG)
    @Index(name = INDEX_ACTIVITY_RECORDS_BY_CUSTOMER, dfColumn = "create_time")
    private Date create_time;
    //更新时间
    @Column(name = "update_time")
    private Date update_time;

    //活动id
    @PartitionKey(value = 2, name = "activity_id")
    @Column(name = "")
    @SearchIndex(name = {INDEX_ACTIVITY_CUSTOMER, INDEX_EXECUTE_TYPE_STATUS, INDEX_COUNT}, column = "activity_id", fieldType = FieldType.LONG)
    @Index(name = INDEX_ACTIVITY_RECORDS_BY_CUSTOMER, pkColumn = "activity_id", pkValue = 1)
    private Integer activity_id;

    //活动type
    /**
     * {@link ActivityType}
     */

    @Column(name = "activity_type", isDefined = true)
    @SearchIndex(name = {INDEX_COUNT}, column = "activity_type", fieldType = FieldType.LONG)
    private Integer activity_type;

    //Mqmsg params
    @Column(name = "params")
    private String params;

    //执行类型
    //0-自动
    //1-手动
    //2-定时
    /**
     * {@link ActivityConstant.ExecuteType}
     */
    @PartitionKey(value = 1, name = "execute_type")
    @SearchIndex(name = {INDEX_EXECUTE_TYPE_STATUS, INDEX_COUNT}, column = "execute_type", fieldType = FieldType.LONG)
    @Index(name = INDEX_ACTIVITY_RECORDS_BY_CUSTOMER, pkColumn = "execute_type", pkValue = 2)
    private int execute_type;

    @Column(name = "check_result")
    private String check_result;

    //状态
    // 0-已登记
    // 1-处理中
    // 2-已完成
    // 3-已失败
    /**
     * {@link ActivityConstant.RecordStatus}
     */
    @Column(name = "status", isDefined = true)
    @SearchIndex(name = {INDEX_EXECUTE_TYPE_STATUS, INDEX_COUNT}, column = "status", fieldType = FieldType.LONG)
    @Index(name = INDEX_ACTIVITY_RECORDS_BY_CUSTOMER, dfColumn = "status")
    private Integer status;

    //备注
    @Column(name = "remark")
    private String remark;

    @Column(name = "saas_id")
    private String saasId;

    // 用户姓名
    @Column(name = "user_name")
    private String userName;

    // 业务数据的集合
    @Column(name = "business_attrs")
    private String businessAttrs;

    // 奖励的币种
    @Column(name = "reward_currency")
    private String rewardCurrency;

    // 奖励金额
    @Column(name = "reward_amount", type = Column.Type.DOUBLE)
    private BigDecimal rewardAmount;

    // 奖励实际划账的币种
    @Column(name = "exchange_currency")
    private String exchangeCurrency;

    // 奖励时间
    @Column(name = "reward_time")
    private Date rewardTime;

}
