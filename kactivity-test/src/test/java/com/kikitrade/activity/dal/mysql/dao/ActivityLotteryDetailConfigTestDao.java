package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ActivityLotteryDetailConfigTestDao {
    void createTableIfNotExist();

    void init();

    List<ActivityLotteryDetailConfig> selectAll();

    @Delete("truncate activity_lottery_detail_config")
    void truncate();

    @Select("select lottery_id as lotteryId from activity_lottery_detail_config where name = #{name}")
    String getLotteryIdByName(@Param("name") String name);

    @Update("update activity_lottery_detail_config set remain_num= remain_num - #{num} where id=#{id}")
    void updateRemainNumByCondition(@Param("num")int num, @Param("id")String id);

    @Select("select id from activity_lottery_detail_config where name = #{name}")
    String getIdByName(@Param("name") String name);

    @Update("update activity_lottery_detail_config set remain_num = #{num} where id=#{drawId}")
    void updateRemainNumByDrawId(@Param("num")int num, @Param("drawId")String drawId);


}
