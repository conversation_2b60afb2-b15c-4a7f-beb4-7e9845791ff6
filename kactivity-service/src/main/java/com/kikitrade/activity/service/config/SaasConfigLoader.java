package com.kikitrade.activity.service.config;

import cn.hutool.core.io.IoUtil;
import com.kikitrade.activity.service.util.BeanParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;
import org.yaml.snakeyaml.introspector.Property;
import org.yaml.snakeyaml.introspector.PropertyUtils;

import java.io.Serializable;
import java.io.StringReader;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/28 16:03
 */
@Slf4j
public class SaasConfigLoader implements Serializable {

    private static Map<String, SaasConfig> saasConfig;

    public static void load(String config){
        Yaml yaml = buildYamlInstance(SaasConfigDTO.class);
        SaasConfigDTO var4;
        StringReader reader = new StringReader(config);
        try {
            var4 = yaml.loadAs(reader, SaasConfigDTO.class);
            if(var4 != null){
                saasConfig = var4.getSaasConfig();
            }
            log.info("saasConfig:{}", saasConfig);
        } catch (Exception ex){
            log.error("saasConfig exception:{}", saasConfig, ex);
        } finally {
            IoUtil.close(reader);
        }
    }

    public static SaasConfig getConfig(String saasId){
        if(saasConfig != null && saasConfig.containsKey(saasId)){
            return saasConfig.get(saasId);
        }
        return new SaasConfig();
    }

    private static Yaml buildYamlInstance(Class<? extends Object> theRoot){
        Constructor c = new Constructor(theRoot);
        c.setPropertyUtils(new PropertyUtils() {
            @Override
            public Property getProperty(Class<? extends Object> type, String name){
                // 关键代码 忽略yaml中无法在类中找到属性的字段
                setSkipMissingProperties(true);
                return super.getProperty(type, name);
            }
        });
        Yaml parser = new Yaml(c);
        return parser;
    }
}
