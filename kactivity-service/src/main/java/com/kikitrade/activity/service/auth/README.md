# 认证服务重构说明

## 重构概述

本次重构使用策略模式优化了 `AuthServiceImpl` 中的 `auth` 方法，将不同平台的认证逻辑分离到各自的实现类中，提高了代码的可维护性和扩展性。

## 重构内容

### 1. 新增策略模式架构

#### 核心接口
- `PlatformAuthStrategy`: 平台认证策略接口，定义了认证流程的标准方法

#### 上下文和结果类
- `AuthContext`: 认证上下文，包含认证过程中需要的所有参数
- `AuthResult`: 认证结果，包含认证成功后的令牌和用户信息

#### 抽象基类
- `AbstractOAuth2AuthStrategy`: OAuth2认证策略抽象基类，提供通用的OAuth2认证流程实现

#### 具体策略实现
- `TwitterAuthStrategy`: Twitter认证策略实现
- `DiscordAuthStrategy`: Discord认证策略实现
- `GoogleAuthStrategy`: Google认证策略实现
- `FacebookAuthStrategy`: Facebook认证策略实现
- `LineAuthStrategy`: Line认证策略实现
- `EmailAuthStrategy`: Email认证策略实现（基于验证码，支持邮箱地址缓存）
- `TelegramAuthStrategy`: Telegram认证策略实现（直接传递用户信息）

#### 工厂类
- `PlatformAuthStrategyFactory`: 策略工厂，根据平台类型返回对应的策略实现

### 2. 重构后的优势

#### 代码结构优化
- **单一职责**: 每个策略类只负责一个平台的认证逻辑
- **开闭原则**: 新增平台时只需添加新的策略实现，无需修改现有代码
- **依赖倒置**: 依赖抽象接口而非具体实现

#### 可维护性提升
- **逻辑分离**: 不同平台的认证逻辑完全分离，互不影响
- **代码复用**: OAuth2通用逻辑提取到抽象基类中
- **易于测试**: 每个策略可以独立进行单元测试

#### 扩展性增强
- **新平台支持**: 只需实现 `PlatformAuthStrategy` 接口即可支持新平台
- **灵活配置**: 可以通过配置动态启用/禁用某些平台

### 3. 删除的无用代码

#### 删除的方法
- `buildAuthRequest()`: 移至各个策略实现中
- `buildRefreshRequest()`: 移至各个策略实现中
- `getCurrentUser()`: 移至各个策略实现中
- `authEmail()`: 移至 `EmailAuthStrategy` 中
- `authTelegram()`: 移至 `TelegramAuthStrategy` 中

#### 删除的常量
- `DC_AUTH_URL`: 移至 `DiscordAuthStrategy` 中
- `X_AUTH_URL`: 移至 `TwitterAuthStrategy` 中
- `GOOGLE_AUTH_URL`: 移至 `GoogleAuthStrategy` 中
- `FACEBOOK_AUTH_URL`: 移至 `FacebookAuthStrategy` 中
- `LINE_AUTH_URL`: 移至 `LineAuthStrategy` 中

#### 删除的依赖
- `ThreePlatformApi`: 移至各个策略实现中
- `OkHttpClient`: 移至需要的策略实现中

### 4. 保持的业务逻辑

重构过程中严格保持了原有的业务逻辑：
- 认证流程完全一致
- 错误处理机制不变
- Redis缓存逻辑保持（包括令牌存储）
- 事件发送逻辑保持
- 社交账号绑定逻辑保持

#### Redis令牌存储逻辑
重构后保持了原有的令牌存储逻辑：
- 存储字段：`rt`(refresh_token), `at`(access_token), `ex`(expire_time), `uid`(user_id), `cid`(customer_id), `uname`(username), `ctime`(create_time)
- Key格式：`ACTIVITY:AUTH:TOKEN:{authVersion}:{customerId}:{clientId}`
- 过期时间：Discord为30分钟，其他平台为30天
- 支持令牌刷新和过期检查

### 5. 使用示例

```java
// 获取策略并执行认证
PlatformAuthStrategy strategy = platformAuthStrategyFactory.getStrategy("twitter");
AuthContext context = AuthContext.builder()
    .saasId("test-saas")
    .platform("twitter")
    .code("auth-code")
    .redirectUri("redirect-uri")
    .customerId("customer-id")
    .build();
AuthResult result = strategy.authenticate(context);
```

### 6. 邮箱验证逻辑优化

#### 优化前的问题
- 发送验证码时只缓存验证码，邮箱地址通过redirectUri传递
- 验证时需要依赖前端传递正确的邮箱地址

#### 优化后的改进
- **sendVerifyCode方法**: 同时缓存验证码和邮箱地址到Redis
- **EmailAuthStrategy**: 从缓存中获取邮箱地址，确保数据一致性
- **缓存结构**: `{code: "123456", email: "<EMAIL>", timestamp: "1234567890"}`
- **安全性提升**: 避免前端篡改邮箱地址，确保验证的邮箱地址与发送验证码的邮箱地址一致

### 7. 测试

新增了多个测试类，验证重构后的功能正确性：
- `AuthServiceTest`: 主要认证服务测试
- `EmailAuthStrategyTest`: 邮箱认证策略专项测试

## 总结

本次重构通过引入策略模式，成功地将复杂的认证逻辑进行了模块化拆分，提高了代码的可读性、可维护性和可扩展性，同时保证了业务逻辑的完整性和一致性。
