package com.kikitrade.activity.luck.service.business;

import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneRelease;

import java.math.BigDecimal;

/**
 *
 */
public interface ReleaseService {

    /**
     * 历史发放的红包
     * @param customerId
     * @param type
     * @return
     */
    Page<LuckFortuneRelease> historyRelease(String customerId, String type, String startTime, String endTime, int offset, int limit);

    /**
     * 历史发放的空投数量
     * @param customerId
     * @return
     */
    long countRelease(String customerId);

    /**
     * 历史发放的空投总价值
     * @param customerId
     * @return
     */
    BigDecimal sumAmountRelease(String customerId);

    /**
     * 查询发放的红包
     * @param id
     * @return
     */
    LuckFortuneReleaseItem findById(String id);

    /**
     * 校验红包id与token是否匹配
     * @param releaseId
     * @return
     */
    boolean checkLuckFortuneToken(String releaseId, String encryptReleaseId);

    /**
     * 重新构建
     * @param releaseItem
     */
    boolean rebuildLuckFortune(LuckFortuneReleaseItem releaseItem);

    /**
     * 更新为已领完
     * @param id
     * @param releaseCustomerId
     */
    void finish(String id, String releaseCustomerId);

    /**
     * 查询releaseId对应的releaseCode
     * @param releaseId
     * @return
     */
    String encryptReleaseId(String releaseId);

    /**
     * 查询releaseCode对应的releaseId
     * @param releaseCode
     * @return
     */
    String decryptReleaseId(String releaseCode);

    /**
     * 加密空投id
     * @param releaseId
     * @return
     */
    String decodeReleaseId(String releaseId, String customerId);

    /**
     * 空投过期
     * @param releaseItem
     */
    void expire(LuckFortuneReleaseItem releaseItem);

    /**
     * 空投退款
     * @param releaseItem
     */
    void refund(LuckFortuneReleaseItem releaseItem);

    /**
     * 退款某个空投
     * @param id
     * @return
     */
    boolean refundById(String id);
}
