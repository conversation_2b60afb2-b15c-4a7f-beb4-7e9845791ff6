package com.kikitrade.activity.service.common.config;

import lombok.Data;

import java.time.DayOfWeek;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/8 14:34
 */
@Data
public class RankingProperties {

    private String saasId = "monster";
    private Integer level = 6;
    private Integer limit = 100;
    private Integer season = 0;
    private Map<String, String> badgeId;
    private Long startTime = 1715150481L;
    private Long endTime = 1715150481L;
    private Long seasonStartTime;


    private Map<String, SeasonTime> breadSeasonTime;
    private Map<String, SeasonTime> zeekSeasonTime;
    private Integer breadLevel = 6;
    private Integer zeekLevel = 6;
    private Map<String, String> breadBadgeId;
    private String zeekSaasId = "deek-app";

    private Map<String, Map<String, SeasonTime>> mappedSeasonTime = new LinkedHashMap<>();

    @Data
    public static class SeasonTime{
        private Long startTime;
        private Long endTime;
        private DayOfWeek settleWeek;
    }
}
