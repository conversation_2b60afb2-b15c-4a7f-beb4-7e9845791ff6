package com.kikitrade.activity.service.remote.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.RemoteClaimService;
import com.kikitrade.activity.api.model.response.ClaimResponse;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.business.ClaimItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/8 16:55
 */
@DubboService
@Slf4j
public class RemoteClaimServiceImpl implements RemoteClaimService {

    @Autowired
    List<ClaimItemService> claimItemServiceList;

    /**
     * 兑换NFT
     * @param businessType
     * @param customerId
     * @param address
     * @return
     */
    @Override
    public Result<ClaimResponse> claimItem(String businessType, String customerId, String address, String code, String saasId, List<String> addresses) {
        Result<ClaimResponse> response = new Result<>();
        for(ClaimItemService claimItemService : claimItemServiceList){
            if(claimItemService.isContinue(businessType)){
                try{
                    ClaimResponse claimResponse = claimItemService.claim(businessType, address, customerId, code, saasId, addresses);
                    if(claimResponse != null && claimResponse.isSuccess()){
                        response.setData(claimResponse);
                        return response;
                    }
                    response.setSuccess(false);
                    response.setData(claimResponse);
                }catch (ActivityException e){
                    log.error("[RemoteClaimService] claim ActivityException:{}! businessType:{}, customerId:{}, code:{}",
                            e.getMessage(), businessType, customerId, code);
                    response.setSuccess(false);
                    response.setCode(Integer.parseInt(e.getCode().getCode()));
                }catch (Exception e){
                    log.error("[RemoteClaimService] claim exception! businessType:{}, customerId:{}, code:{}", businessType, customerId, code, e);
                    response.setSuccess(false);
                }
                log.info("[RemoteClaimService] claim response:{}, businessType:{}, customerId:{}, code:{}",
                        JSON.toJSONString(response), businessType, customerId, code);
                return response;
            }
        }
        log.error("[RemoteClaimService] claim: businessType is invalid!, businessType:{}, customerId:{}, code:{}", businessType, customerId, code);
        response.setSuccess(false);
        return response;
    }

    /**
     * 是否能够claim
     * @param businessType
     * @param customerId
     * @return
     */
    @Override
    public Boolean allowClaim(String businessType, String customerId, String address, List<String> addresses) {
        for(ClaimItemService claimItemService : claimItemServiceList){
            if(claimItemService.isContinue(businessType)){
                return claimItemService.allowClaim(businessType, customerId, address, addresses);
            }
        }
        log.error("RemoteClaimServiceImpl allowClaim: businessType is invalid!, businessType is {}", businessType);
        return false;
    }

    /**
     * 管理端-批量创建
     *
     * @param businessType
     * @param codes        兑换码列表
     * @return
     */
    @Override
    public Boolean batchCreateItem(String businessType, List<String> codes) {
        return null;
    }

}
