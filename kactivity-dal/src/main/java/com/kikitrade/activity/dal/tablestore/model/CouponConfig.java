package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/8 11:55
 */
@Data
@Table(name = "coupon_config")
public class CouponConfig implements Serializable {

    @PartitionKey(name = "coupon_code")
    private String couponCode;

    @PartitionKey(name = "saas_id", value = 1)
    private String saasId;

    @PartitionKey(name = "business_type", value = 2)
    private String businessType;

    /**
     * 状态
     * @see ActivityConstant.CommonStatus
     */
    @Column(name = "status")
    private Integer status;

    @Column(name = "task_id")
    private String taskId;

    @Column(name = "type")
    private String type;

    @Column(name = "award_amount")
    private String awardAmount;

    @Column(name = "award_currency")
    private String awardCurrency;

}
