package com.kikitrade.activity.api.model.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/28 10:44
 */
@Data
public class GoodsDetailResponse implements Serializable {

    private String goodsId;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种类型
     */
    private String currencyType;

    /**
     * 商品描述
     */
    private String desc;

    /**
     * 商品售卖时间
     */
    private long endTime;

    /**
     * 商品图片
     */
    private Map<String, String> imageMap;

    /**
     * 标签颜色
     */
    private String labelColor;
    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 外部商品id
     */
    private String outId;

    /**
     * 其他属性
     */
    private String param;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 商品售卖时间
     */
    private long startTime;

    /**
     * 0:上架 1:下架
     */
    private Integer status;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 商品类型
     */
    private String type;

    private Integer order;

    private String blockchain;
}
