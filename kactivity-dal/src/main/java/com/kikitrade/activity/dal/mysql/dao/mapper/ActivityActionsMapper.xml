<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityActionsDao">

    <!-- 增加 -->
    <insert id="insert" parameterType="com.kikitrade.activity.dal.mysql.model.ActivityActions">
        INSERT INTO activity_actions VALUES (
            #{id}, NOW(), NOW(),
            #{activity_type},
            #{action_name},
            #{desc},
            #{priority},
            #{status},
            #{params}
        )
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.kikitrade.activity.dal.mysql.model.ActivityActions">
        update activity_actions aa
        <set>
            aa.update_time=NOW(),
            <if test="activity_type!=null">
                aa.activity_type=#{activity_type},
            </if>
            <if test="action_name!=null">
                aa.action_name=#{action_name},
            </if>
            <if test="desc!=null">
                aa.desc=#{desc},
            </if>
            <if test="priority!=null">
                aa.priority=#{priority},
            </if>
            <if test="status!=null">
                aa.status=#{status},
            </if>
            <if test="params!=null">
                aa.params=#{params}
            </if>
        </set>
        where aa.id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM activity_actions
        WHERE id = #{id}
    </delete>

    <select id="findAll" resultType="com.kikitrade.activity.dal.mysql.model.ActivityActions">
        SELECT *
        FROM activity_actions
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>


    <update id="updateStatus">
        UPDATE activity_actions
        SET update_time = NOW(), status = #{status}
        WHERE id = #{id}
    </update>

    <select id="findById" resultType="com.kikitrade.activity.dal.mysql.model.ActivityActions">
        SELECT *
        FROM activity_actions
        WHERE id = #{id}
    </select>

    <select id="findByType" resultType="com.kikitrade.activity.dal.mysql.model.ActivityActions">
        SELECT *
        FROM activity_actions
        WHERE activity_type = #{activity_type}
    </select>


</mapper>
