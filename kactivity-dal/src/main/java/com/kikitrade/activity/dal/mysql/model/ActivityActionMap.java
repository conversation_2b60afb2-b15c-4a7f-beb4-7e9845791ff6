package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/7/11 17:07
 */
@Getter
@Setter
@Table(name = "activity_action_map")
public class ActivityActionMap extends BaseModel {

    //活动id

    @Column(name = "activity_id", nullable = false)
    private Integer activity_id;

    //action id

    @Column(name = "action_id", nullable = false)
    private Integer action_id;

    //action_name for redis use
    @Column(name = "action_name", nullable = false)
    private String action_name;

    //操作优先级，哪些操作优先执行
    @Column(name = "priority", nullable = false)
    private Integer priority;

    //0-失效 1-生效
    @Column(name = "status",  nullable = false)
    private Integer status;

    //action param json
    @Column(name = "params")
    private String params;

}
