package com.kikitrade.activity.service.remote.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.exception.NacosException;
import com.kikitrade.activity.api.RemoteManageActivityConfigService;
import com.kikitrade.activity.api.model.PrecisionPoolDTO;
import com.kikitrade.activity.api.model.TaskCodeConfigDTO;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.api.model.response.TaskManageResponse;
import com.kikitrade.activity.dal.tablestore.builder.PrecisionPoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrecisionPool;
import com.kikitrade.activity.service.config.PropertiesConfigService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.framework.common.util.BeanUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/30 15:12
 */
@DubboService
@Slf4j
public class RemoteManageActivityConfigServiceImpl implements RemoteManageActivityConfigService {

    private static final String ACTIVITY_TASK_CODE = "kactivity-task-code.json";

    @Resource
    private PropertiesConfigService propertiesConfigService;
    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private PrecisionPoolBuilder precisionPoolBuilder;

    /**
     * 同步任务code
     *
     * @param codeConfigDTO
     * @return
     */
    @Override
    public TaskManageResponse syncTaskCode(TaskCodeConfigDTO codeConfigDTO) {
        TaskManageResponse response = new TaskManageResponse();
        response.setSuccess(true);
        try {
            String config = propertiesConfigService.getConfigService().getConfig(ACTIVITY_TASK_CODE, "kactivity", 1000);
            List<TaskCodeConfigDTO> taskCodeConfigDTOS = JSON.parseArray(config, TaskCodeConfigDTO.class);
            if(taskCodeConfigDTOS.stream().anyMatch(task -> task.getCode().equals(codeConfigDTO.getCode()))){
                for(int i = 0; i < taskCodeConfigDTOS.size(); i++){
                    if(taskCodeConfigDTOS.get(i).getCode().equals(codeConfigDTO.getCode())){
                        taskCodeConfigDTOS.set(i, codeConfigDTO);
                    }
                }
            }else{
                taskCodeConfigDTOS.add(codeConfigDTO);
            }
            log.info("syncTaskCode config:{}", taskCodeConfigDTOS);
            propertiesConfigService.getConfigService().publishConfig(ACTIVITY_TASK_CODE, "kactivity", JSON.toJSONString(taskCodeConfigDTOS), ConfigType.JSON.getType());
            return response;
        } catch (NacosException e) {
            log.error("syncTaskCode error:{}", codeConfigDTO, e);
            response.setSuccess(false);
            return response;
        }
    }

    public TaskManageResponse syncTaskConfig(TaskConfigDTO taskConfigDTO, Boolean isGray) throws Exception {
        log.info("syncTaskConfig taskConfigDTO:{}, isGray:{}", JSON.toJSONString(taskConfigDTO), isGray);
        TaskManageResponse response = new TaskManageResponse();
        response.setSuccess(true);
        try{
            taskConfigService.upsert(taskConfigDTO, isGray);
        }catch (Exception ex){
            log.error("syncTaskConfig error:{}", taskConfigDTO, ex);
            response.setSuccess(false);
        }
        return response;
    }

    @Override
    public TaskManageResponse delete(String id) {
        TaskManageResponse response = new TaskManageResponse();
        response.setSuccess(true);
        try {
            taskConfigService.delete(id);
        }catch (Exception ex){
            log.error("delete error:{}", id, ex);
            response.setSuccess(false);
        }
        return response;
    }

    @Override
    public TaskManageResponse syncPrecisionPool(PrecisionPoolDTO precisionPoolDTO) {
        TaskManageResponse response = new TaskManageResponse();
        response.setSuccess(true);
        try {
            PrecisionPool precisionPool = BeanUtil.copyProperties(precisionPoolDTO, new PrecisionPool());
            PrecisionPool pool = precisionPoolBuilder.findById(precisionPool.getId());
            if(pool != null){
                precisionPoolBuilder.update(precisionPool);
                return response;
            }
            precisionPoolBuilder.insert(precisionPool);
            return response;
        }catch (Exception ex){
            log.error("syncPrecisionPool error:{}", precisionPoolDTO, ex);
            response.setSuccess(false);
            return response;
        }
    }
}
