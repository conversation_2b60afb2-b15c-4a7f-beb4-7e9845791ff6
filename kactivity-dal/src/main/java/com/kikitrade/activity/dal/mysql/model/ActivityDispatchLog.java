package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/8/21 21:37
 */
@Getter
@Setter
@Table(name = "activity_dispatch_log")
public class ActivityDispatchLog extends BaseModel {


    //创建时间yyyyMMdd
    @Column(name = "tran_date")
    private String tranDate;

    //分发状态
    @Column(name = "dispatch_status")
    private Integer dispatchStatus;

    //活动ID
    @Column(name = "activity_id")
    private Integer activityId;


    //活动类型
    @Column(name = "execute_type")
    private Integer executeType;

    //状态
    @Column(name = "status")
    private Integer status;

    //单次分片笔数
    @Column(name = "limit")
    private Integer limit;

    //分片查询截止时间
    @Column(name = "deadline")
    private Date deadline;

    //分片通知节点Url
    @Column(name = "notifyUrl")
    private String notifyUrl;


}
