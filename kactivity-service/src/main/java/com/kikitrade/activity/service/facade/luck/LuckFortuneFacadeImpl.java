package com.kikitrade.activity.service.facade.luck;

import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.dao.LuckFortuneRuleDao;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.facade.luck.LuckCommonResponse;
import com.kikitrade.activity.facade.luck.LuckFortuneFacadeGrpc;
import com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO;
import com.kikitrade.activity.luck.service.business.RuleService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.math.BigDecimal;

@Component
@Slf4j
public class LuckFortuneFacadeImpl extends LuckFortuneFacadeGrpc.LuckFortuneFacadeImplBase {

    
    @Resource
    private RuleService ruleService;
    @Resource
    private SeqGeneraterService seqGeneraterService;
    @Resource
    private LuckFortuneRuleDao luckFortuneRuleDao;

    /**
     * 保存红包规则
     * @param request
     * @param responseObserver
     */
    @Override
    public void save(LuckFortuneRuleDTO request, StreamObserver<LuckCommonResponse> responseObserver) {
        try{
            log.info("luckRequest:{}",request);
            String id;
            if(StringUtils.isNotBlank(request.getId())){
                LuckFortuneRule luckFortuneRule = luckFortuneRuleDao.selectByPrimaryKey(request.getId());
                toEntity(request, luckFortuneRule);
                id = ruleService.tryUpdateRuleV1(luckFortuneRule);
            }else{
                id = seqGeneraterService.next(CustomerSeqRuleBuilder.instance("luck_fortune_rule"));
                LuckFortuneRule luckFortuneRule = new LuckFortuneRule();
                toEntity(request, luckFortuneRule);
                ruleService.trySaveRuleV1(luckFortuneRule, id);
            }
            responseObserver.onNext(LuckCommonResponse.newBuilder().setSuccess(StringUtils.isNotBlank(id)).setMessage("success").setId(id).build());
            responseObserver.onCompleted();
        }catch (Exception ex){
            log.error("luck rule save, exception", ex);
            responseObserver.onError(ex);
        }
    }

    private void toEntity(LuckFortuneRuleDTO luckFortuneRuleDTO, LuckFortuneRule luckFortuneRule){
        luckFortuneRule.setId(luckFortuneRuleDTO.getId());
        luckFortuneRule.setKycLevel(luckFortuneRuleDTO.getKycLevel().name());
        luckFortuneRule.setUserType(luckFortuneRuleDTO.getUserType().name());
        if(StringUtils.isNotBlank(luckFortuneRuleDTO.getReleaseMin())){
            luckFortuneRule.setReleaseMin(new BigDecimal(luckFortuneRuleDTO.getReleaseMin()));
        }
        if(StringUtils.isNotBlank(luckFortuneRuleDTO.getReleaseMax())){
            luckFortuneRule.setReleaseMax(new BigDecimal(luckFortuneRuleDTO.getReleaseMax()));
        }
        luckFortuneRule.setReleaseNumMax(luckFortuneRuleDTO.getReleaseNumMax());
        if(StringUtils.isNotBlank(luckFortuneRuleDTO.getReleaseAccountDays())){
            luckFortuneRule.setReleaseAccountDays(new BigDecimal(luckFortuneRuleDTO.getReleaseAccountDays()));
        }
        if(StringUtils.isNotBlank(luckFortuneRuleDTO.getReleaseAccountMonths())){
            luckFortuneRule.setReleaseAccountMonths(new BigDecimal(luckFortuneRuleDTO.getReleaseAccountMonths()));
        }
        if(StringUtils.isNotBlank(luckFortuneRuleDTO.getReceiveMin())){
            luckFortuneRule.setReceiveMin(new BigDecimal(luckFortuneRuleDTO.getReceiveMin()));
        }
        if(StringUtils.isNotBlank(luckFortuneRuleDTO.getReceiveMax())){
            luckFortuneRule.setReceiveMax(new BigDecimal(luckFortuneRuleDTO.getReceiveMax()));
        }
        if(StringUtils.isNotBlank(luckFortuneRuleDTO.getReceiveAccountDays())){
            luckFortuneRule.setReceiveAccountDays(new BigDecimal(luckFortuneRuleDTO.getReceiveAccountDays()));
        }
        if(StringUtils.isNotBlank(luckFortuneRuleDTO.getReceiveAccountMonths())){
            luckFortuneRule.setReceiveAccountMonths(new BigDecimal(luckFortuneRuleDTO.getReceiveAccountMonths()));
        }
        luckFortuneRule.setReceiveNumDays(luckFortuneRuleDTO.getReceiveNumDays());
        luckFortuneRule.setReceiveNumMonths(luckFortuneRuleDTO.getReceiveNumMonths());
    }
}
