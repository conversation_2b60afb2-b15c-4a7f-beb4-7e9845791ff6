package com.kikitrade.activity.dal.tablestore.model;

import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/26 14:40
 */
@Data
@Table(name = "goods")
public class Goods implements Serializable {

    public static final String INDEX_GOODS = "index_goods";

    @Column(name = "saas_id")
    @SearchIndex(name = INDEX_GOODS, column = "saas_id")
    private String saasId;

    /**
     * 商品id
     */
    @PartitionKey(name = "goods_id")
    @SearchIndex(name = INDEX_GOODS, column = "goods_id")
    private String goodsId;

    /**
     * 币种
     */
    @Column(name = "currency")
    private String currency;
    /**
     * 币种类型
     */
    @Column(name = "currency_type")
    private String currencyType;
    /**
     * 商品描述
     */
    @Column(name = "desc")
    private String desc;
    /**
     * 商品售卖时间
     */
    @Column(name = "end_time")
    @SearchIndex(name = INDEX_GOODS, column = "end_time")
    private long endTime;
    /**
     * 商品图片
     */
    @Column(name = "image")
    private String image;
    /**
     * 标签颜色
     */
    @Column(name = "label_color")
    private String labelColor;
    /**
     * 标签名称
     */
    @Column(name = "label_name")
    private String labelName;
    /**
     * 商品名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 外部商品id
     */
    @Column(name = "out_id")
    private String outId;
    /**
     * 其他属性
     */
    @Column(name = "param")
    private String param;
    /**
     * 商品价格
     */
    @Column(name = "price", type = Column.Type.DOUBLE)
    private BigDecimal price;
    /**
     * 商品售卖时间
     */
    @Column(name = "start_time")
    private long startTime;
    /**
     * 0:上架 1:下架
     */
    @Column(name = "status", type = Column.Type.INTEGER)
    @SearchIndex(name = INDEX_GOODS, column = "status", fieldType = FieldType.LONG)
    private Integer status;
    /**
     * 库存
     */
    @Column(name = "stock", type = Column.Type.INTEGER)
    private Integer stock;
    /**
     * 商品类型
     */
    @Column(name = "type")
    private String type;

    @Column(name = "blockchain")
    private String blockchain;

    @Column(name = "order")
    @SearchIndex(name = INDEX_GOODS, column = "order")
    private Integer order;

    public Map<String, String> getImageMap(){
        if(StringUtils.isBlank(this.image)){
            return new HashMap<>();
        }
        return JSON.parseObject(this.image, Map.class);
    }

    public Map<String, String> getParamMap(){
        if(StringUtils.isBlank(this.param)){
            return new HashMap<>();
        }
        return JSON.parseObject(this.param, Map.class);
    }
}
