package com.kikitrade.activity.api;

import com.kikitrade.activity.api.model.ActivityTaskConfigDTO;
import com.kikitrade.activity.api.model.PrecisionPoolDTO;
import com.kikitrade.activity.api.model.TaskCodeConfigDTO;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.api.model.response.TaskManageResponse;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/30 15:06
 */
public interface RemoteManageActivityConfigService {

    /**
     * 同步任务code
     * @param codeConfigDTO
     * @return
     */
    TaskManageResponse syncTaskCode(TaskCodeConfigDTO codeConfigDTO);

    /**
     * 同步任务配置
     * @param taskConfigDTO
     * @param isGray
     * @return
     * @throws Exception
     */
    TaskManageResponse syncTaskConfig(TaskConfigDTO taskConfigDTO, Boolean isGray) throws Exception;

    /**
     * 根据id删除任务配置
     * @param id
     * @return
     */
    TaskManageResponse delete(String id);

    /**
     * 保存aura池子
     * @param precisionPoolDTO
     * @return
     */
    TaskManageResponse syncPrecisionPool(PrecisionPoolDTO precisionPoolDTO);

    
}
