package com.kikitrade.activity.service.rpc.twitter;

import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.twitter.clientlib.TwitterCredentialsOAuth2;
import com.twitter.clientlib.api.TwitterApi;
import com.twitter.clientlib.model.Tweet;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/23 10:26
 */
@Data
@Slf4j
public class CommentPostRequest extends AccessToken {
    private TwitterProperties twitterProperties;

    private String userName;
    private String tweetId;

    public CommentPostRequest(){}

    public CommentPostRequest(TwitterProperties twitterProperties, AccessToken accessToken){
        super(accessToken);
        this.twitterProperties = twitterProperties;
    }

    public CommentPostRequest buildCommentPostRequest(String userName, String tweetId){
        this.userName = userName;
        this.tweetId = tweetId;
        return this;
    }

    public List<Tweet> execute() {
        try{
            String query = String.format("from:%s is:reply in_reply_to_tweet_id:%s", this.userName, this.tweetId);
            log.info("verifyCommentPost request:{}", query);
            return getApi(this.accessToken, this.refreshToken, this.twitterProperties).tweets().tweetsRecentSearch(query).execute().getData();
        }catch (Exception ex){
            log.error("verifyCommentPost error:{},{}",this.userName, this.tweetId, ex);
            return null;
        }
    }
}