package com.kikitrade.activity.service.job;

import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.NoticeService;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.common.config.RankingProperties;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.elasticjob.KiKiSimpleJob;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.model.QuestsLadderDTO;
import com.kikitrade.member.model.request.QuestLadderRequest;
import com.kikitrade.member.model.request.QuestLadderTokenPageRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.Period;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/10 21:22
 */
@Component
@Slf4j
public class BreadMemberRankingJob extends KiKiSimpleJob {

    @DubboReference(check = false)
    @Lazy
    private RemoteMemberService remoteMemberService;
    @Resource
    @Lazy
    private KactivityModuleProperties kactivityModuleProperties;
    @Resource
    @Lazy
    private RewardService rewardService;
    @Resource
    @Lazy
    private RedisService redisService;
    @Resource
    @Lazy
    private NoticeService noticeService;

    private static final String SAAS_ID = "bread";

    private static final String LEADERBOARD = "Leaderboard";

    private static final String DINGTALK_URL = "https://oapi.dingtalk.com/robot/send?access_token=53f9e144057599dc640203a13f385167e12a757fdee7c0b45ec0a871c16eff52";

    @Override
    protected void doExecute(ShardingContext shardingContext) throws Exception {
        ex();
    }

    public void ex(){
        try{
            log.info("BreadMemberRankingJob start");
            Map<String, RankingProperties.SeasonTime> seasonTime = kactivityModuleProperties.getRank().getBreadSeasonTime();
            Optional<Map.Entry<String, RankingProperties.SeasonTime>> entryOptional = seasonTime.entrySet().stream().filter(s -> s.getValue().getStartTime() <= OffsetDateTime.now().toEpochSecond() && s.getValue().getEndTime() > OffsetDateTime.now().toEpochSecond()).findFirst();
            if(entryOptional.isEmpty()){
                log.info("BreadMemberRankingJob not to season time");
                return;
            }
            Map.Entry<String, RankingProperties.SeasonTime> seasonTimeEntry = entryOptional.get();
            LocalDateTime seasonStartTime = new Date(seasonTimeEntry.getValue().getStartTime() * 1000).toInstant().atOffset(ZoneOffset.UTC).toLocalDate().atTime(0, 0, 0);
            //判断与season1间隔几个月，得知当前第几赛季
            Long season1StartTime = seasonTime.get("season1").getStartTime() * 1000;
            LocalDate season1LocalDate = new Date(season1StartTime).toInstant().atOffset(ZoneOffset.UTC).toLocalDate();
            int seasonIdx = Period.between(season1LocalDate, seasonStartTime.toLocalDate()).getYears() * 12 + Period.between(season1LocalDate, seasonStartTime.toLocalDate()).getMonths() + 1;
            long firstLadderTime = Date.from(seasonStartTime.with(TemporalAdjusters.next(DayOfWeek.WEDNESDAY)).toInstant(ZoneOffset.UTC)).getTime();
            if(OffsetDateTime.now().toEpochSecond() * 1000L < firstLadderTime + 10 * 60 * 1000){
                log.info("BreadMemberRankingJob not to time");
                return;
            }
            //计算获取cycle
            long c = 1;
            if(OffsetDateTime.now().toEpochSecond() * 1000L >= firstLadderTime){
                c = ((OffsetDateTime.now().toEpochSecond() * 1000L  - firstLadderTime)/1000 / 60 / 60 / 24 / 7) + 2;
            }

            String cycle = String.format("%02d", c);
            QuestLadderTokenPageRequest request = new QuestLadderTokenPageRequest();
            request.setSaasId(SAAS_ID);
            request.setSeason(seasonTimeEntry.getKey());
            request.setLimit(kactivityModuleProperties.getRank().getLimit());
            request.setCycle(cycle);

            if(redisService.hHasKey(RedisKeyConst.ACTIVITY_BREAD_MEMBER_RANK.getPrefix(), request.getSeason() + request.getCycle())){
                log.info("BreadMemberRankingJob, badge already reward,{}", request.getSeason() + request.getCycle());
                return;
            }
            for(int level = kactivityModuleProperties.getRank().getBreadLevel(); level >= 2; level--){
                request.setLevel(level);
                TokenPage<QuestsLadderDTO> pageLadderResults = null;
                String nextToken = null;
                do{
                    request.setNextToken(nextToken);
                    pageLadderResults = remoteMemberService.pageLaddersByLevel(request);
                    if(CollectionUtils.isEmpty(pageLadderResults.getRows())){
                        break;
                    }
                    if(nextToken == null){
                        redisService.hSet(RedisKeyConst.ACTIVITY_BREAD_MEMBER_RANK.getPrefix(), request.getSeason() + request.getCycle(), true);
                        String key = String.format("%s%s%s",RedisKeyConst.ACTIVITY_BREAD_MEMBER_RANK.getPrefix(), request.getSeason(), request.getCycle());
                        noticeService.noticeDingTalk(key, DINGTALK_URL, "bnb 徽章开始发放");
                    }
                    nextToken = pageLadderResults.getNextToken();
                    reward(pageLadderResults.getRows(), seasonIdx, seasonTimeEntry, c, cycle);
                }while (pageLadderResults.getNextToken() != null);
                String key = String.format("%s%s%s%s", request.getSaasId(), request.getSeason(), request.getCycle(), level);
                noticeService.noticeDingTalk(key, DINGTALK_URL, "bnb " + level + " 徽章发放完成");
            }
        }catch (Exception ex){
            log.error("BreadMemberRankingJob", ex);
        }
    }

    private void reward(List<QuestsLadderDTO> ladderDTOS, int seasonIdx, Map.Entry<String, RankingProperties.SeasonTime> seasonTimeEntry, long cycle, String cycleStr) {
        if(CollectionUtils.isEmpty(ladderDTOS)){
            return;
        }
        for(QuestsLadderDTO dto : ladderDTOS){
            if(dto.getCustomerId().startsWith("0x")){
                continue;
            }
            // 第一赛季 seasonId传1、level 2~6 代表白银～王者
            String badgeInfo = seasonIdx + "_" + dto.getLevel() + "_" + LEADERBOARD;
            RewardRequest rewardRequest = RewardRequest.builder()
                    .customerId(dto.getCustomerId())
                    .rewardId(String.format("%s%03d%s", dto.getCustomerId(), Integer.parseInt(seasonTimeEntry.getKey().substring(seasonTimeEntry.getKey().length() - 1)), cycleStr))
                    .amount(BigDecimal.ONE)
                    .currency(badgeInfo)
                    .type(ActivityConstant.AwardTypeEnum.BADGE.name())
                    .businessType(AssetBusinessType.ACTIVITY_TASK.getCodeDesc())
                    .receiveEndTime(seasonTimeEntry.getValue().getEndTime())
                    .desc("S"+ (Integer.parseInt(seasonTimeEntry.getKey().substring(seasonTimeEntry.getKey().length() - 1))) +" week " + (cycle - 1))
                    .saasId(SAAS_ID)
                    .build();
            try{
                log.info("[reward] ranking badge:{}", rewardRequest);
                rewardService.reward(rewardRequest);
            }catch (Exception ex){
                log.error("[reward] ranking badge:{}", rewardRequest, ex);
            }
        }
    }
}
