//package com.kikitrade.activity.stepdefs;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.kikitrade.accounting.api.model.account.AccountException;
//import com.kikitrade.accounting.api.model.account.request.AccountTransferRequest;
//import com.kikitrade.accounting.api.model.productaccount.ProductAccountParam;
//import com.kikitrade.activity.api.RemoteLotteryService;
//import com.kikitrade.activity.api.model.LotteryResponse;
//import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryConfigTestDao;
//import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryDetailConfigTestDao;
//import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryPoolConfigTestDao;
//import com.kikitrade.activity.dal.mysql.dao.ActivityLotteryStoreLogTestDao;
//import com.kikitrade.activity.dal.mysql.model.ActivityLotteryConfig;
//import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
//import com.kikitrade.activity.dal.mysql.model.ActivityLotteryPoolConfig;
//import com.kikitrade.activity.dal.mysql.model.ActivityLotteryStoreLog;
//import com.kikitrade.activity.dal.redis.RedisKeyConst;
//import com.kikitrade.activity.dal.redis.RedisService;
//import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
//import com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem;
//import com.kikitrade.activity.mock.*;
//import com.kikitrade.activity.op.BusinessOP;
//import com.kikitrade.activity.service.config.KactivityLotteryConfig;
//import com.kikitrade.activity.service.facade.domain.LotteryFacadeDTO;
//import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
//import com.kikitrade.activity.service.lottery.LotteryService;
//import com.kikitrade.activity.service.model.StoreInfo;
//import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
//import com.kikitrade.activity.util.CheckUtils;
//import com.kikitrade.activity.util.DateUtil;
//import com.kikitrade.asset.model.request.AssetTransferInRequest;
//import com.kikitrade.asset.model.request.AssetTransferOutRequest;
//import com.kikitrade.quota.api.model.request.QuotaConfigDTO;
//import com.kikitrade.quota.api.model.request.QuotaDeductDTO;
//import com.kikitrade.quota.api.model.request.QuotaFreezeDTO;
//import com.kikitrade.quota.api.model.request.QuotaUnfreezeDTO;
//import io.cucumber.datatable.DataTable;
//import io.cucumber.java.zh_cn.并且;
//import io.cucumber.java.zh_cn.当;
//import io.cucumber.java.zh_cn.那么;
//import io.grpc.stub.StreamObserver;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//
//import static com.kikitrade.activity.op.BusinessOP.*;
//import static org.junit.jupiter.api.Assertions.*;
//import static org.junit.jupiter.api.Assertions.assertEquals;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//
//import jakarta.annotation.Resource;
//
//import java.io.File;
//import java.io.FileWriter;
//import java.io.IOException;
//import java.util.*;
//import java.util.concurrent.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//@Slf4j
//public class SlotLotteryStepdefs {
//    //    @Resource
//    //    LotteryService slotLotteryService;
//    @Autowired
//    @Qualifier("slotLotteryService")
//    LotteryService slotLotteryService;
//    @Resource
//    RedisService redisService;
//
//    @Resource
//    ActivityLotteryDetailConfigTestDao activityLotteryDetailConfigTestDao;
//    @Resource
//    ActivityLotteryConfigTestDao activityLotteryConfigTestDao;
//    @Resource
//    RemoteLotteryService remoteLotteryService;
//    @Resource
//    ActivityLotteryStoreLogTestDao activityLotteryStoreLogTestDao;
//    @Resource
//    LotteryPoolService lotteryPoolService;
//    @Resource
//    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;
//    @Resource
//    private ActivityLotteryConfigService activityLotteryConfigService;
//    @Resource
//    private ActivityLotteryPoolConfigTestDao activityLotteryPoolConfigTestDao;
//    @Resource
//    KactivityLotteryConfig kactivityLotteryConfig;
//    @Resource
//    ActivityLotteryItemBuilderMock activityLotteryItemBuilderMock;
//    @Resource
//    RemoteAccountingOperateServiceDubboMock remoteAccountingOperateServiceDubboMock;
////    @Resource
////    LotteryService lotteryService;
//    @Resource
//    SeqGeneraterServiceMock seqGeneraterServiceMock;
//    @Resource
//    RemoteBlockBusinessServiceDubboMock remoteBlockBusinessServiceDubboMock;
//    @Resource
//    RemoteQuotaServiceDubboMock remoteQuotaServiceDubboMock;
//
//
//    @当("slot-lottery-奖品配置为{},奖池配置为{},初始化奖池")
//    public void slotLottery奖品配置为PrizeConfig奖池配置为PoolConfig初始化奖池(String activityLotteryDetailConfigJson, String activityLotteryConfigJsonList) {
//        List<ActivityLotteryDetailConfig> configList = JSON.parseArray(activityLotteryDetailConfigJson, ActivityLotteryDetailConfig.class);
//        configList.forEach(c -> {
//            c.setCreated(new Date());
//            c.setModified(new Date());
//            c.setSaasId("kiki");
//        });
//        ActivityLotteryConfig activityLotteryConfig = JSON.parseObject(activityLotteryConfigJsonList, ActivityLotteryConfig.class);
//        activityLotteryConfig.setCreated(new Date());
//        activityLotteryConfig.setModified(new Date());
//        activityLotteryConfig.setSaasId("kiki");
//
//        lotteryPoolService.initPool(activityLotteryConfig, configList);
//    }
//
//    @那么("slot-lottery-校验初始化奖池结果")
//    public void slotLottery校验初始化奖池结果() {
//        log.info("---校验初始化奖池");
//        Set<String> keys = redisService.keys(RedisKeyConst.LOTTERY_POOL_KEY.getPrefix());
//        for (String key : keys) {
//            Map<String, Integer> map = redisService.hGetAllMap(key);
//            log.info("-----------奖池号==>数量 {}", map);
//        }
//    }
//
//    @那么("slot-lottery-不满足抽奖规则要求返回{}")
//    public void slotLottery不满足抽奖规则要求返回异常(String exception) {
//        log.info("-----exceptions:{}", BusinessOP.exceptions);
//        if (BusinessOP.exceptions.size() == 0) {
//            assertEquals("null", exceptions);
//            return;
//        }
//        assertTrue(BusinessOP.exceptions.get(0).contains(exception));
//    }
//
//    @当("slot-lottery-配置remoteQuotaService.check异常")
//    public void slotLottery配置remoteQuotaServicecheck异常() throws ExecutionException, InterruptedException, TimeoutException {
//        remoteQuotaServiceDubboMock.checkError();
//    }
//
//
//    /**
//     * @param awardNum {"20220930145600":20}
//     * @param poolNum  {"ACTIVITY:LOTTERY:POOL:L1:20220930145600":15}
//     */
//    @那么("slot-lottery-校验,奖品数量为{},奖池数量为{}")
//    public void slotLottery校验奖品数量为AwardNum奖池数量为PoolNum(String awardNum, String poolNum) {
//
//
//        //奖池
//        Map<String, Integer> pool = JSON.parseObject(poolNum, Map.class);
//        //奖品
//        Map<String, Integer> award = JSON.parseObject(awardNum, Map.class);
//
//        log.info("---校验初始化奖池");
//        Set<String> keys = redisService.keys(RedisKeyConst.LOTTERY_POOL_KEY.getPrefix());
//        for (String key : keys) {
//            int number = pool.get(key);
//            Map<String, Integer> map = redisService.hGetAllMap(key);
//            assertEquals(number, map.size());
//
//            //总奖品数量
//            List<Integer> collect = map.values().stream().collect(Collectors.toList());
//            int sum = collect.stream().mapToInt(Integer::intValue).sum();
//            String[] split = key.split(":");
//            int length = split.length;
//            String id = split[length - 1];
//            int num = award.get(id);
//            assertEquals(num, sum);
//
//            log.info("-----------奖池号==>数量 {}", map);
//        }
//
//    }
//
///*    @并且("slot-lottery-开始抽奖,抽中{}")
//    public void slotLottery开始抽奖抽中LotteryNum(int lotteryNum) throws Exception {
//        BusinessOP.lotteryNum = lotteryNum;
//
//        ActivityLotteryDetailConfig draw = slotLotteryService.draw(System.currentTimeMillis(), "20220930145600", "L1", "slot");
//        log.info("-----draw:{}", draw);
//
//        BusinessOP.draw = draw;
//
//    }*/
//
//    @那么("slot-lottery-校验,奖品是{}")
//    public void slotLottery校验奖品是Prize(String respDraw) {
//        ActivityLotteryDetailConfig rd = JSON.parseObject(respDraw, ActivityLotteryDetailConfig.class);
//        System.out.println("Expect----draw:{}" + JSON.toJSONString(rd));
//
//        ActivityLotteryDetailConfig draw = BusinessOP.draw;
//        System.out.println("Actual-----draw:{}" + JSON.toJSONString(draw));
//
//        //assertEquals(rd.getId(), draw.getId());
//        //assertEquals(rd.getLotteryId(), draw.getLotteryId());
//        assertEquals(rd.getName(), draw.getName());
//        assertEquals(rd.getCurrency(), draw.getCurrency());
//        assertEquals(rd.getAmount(), draw.getAmount());
//        assertEquals(rd.getNum(), draw.getNum());
//        assertEquals(rd.getPercent(), draw.getPercent());
//        assertEquals(rd.getIsLow(), draw.getIsLow());
//        assertEquals(rd.getRemainNum(), draw.getRemainNum());
//    }
//
//    @并且("slot-lottery-VIP{}开始抽奖{},抽中{}")
//    public void slotLotteryVIPVip开始抽奖Slot抽中LotteryNum(String vip, String type, int lotteryNum) throws Exception {
//        BusinessOP.lotteryNum = lotteryNum;
//
//        ActivityLotteryDetailConfig draw = slotLotteryService.draw(1665726100709L, "20220930145600", vip, type, "kiki");
//        System.out.println("-----draw:" + JSON.toJSONString(draw));
//
//        BusinessOP.draw = draw;
//    }
//
//    @当("slot-lottery-按照数据库配置初始化奖池")
//    public void slotLottery按照数据库配置初始化奖池() {
//        ActivityLotteryConfig activityLotteryConfig = activityLotteryConfigTestDao.select();
//        List<ActivityLotteryDetailConfig> configList = activityLotteryDetailConfigTestDao.selectAll();
//
//        //清空表
//        activityLotteryConfigTestDao.truncate();
//        activityLotteryDetailConfigTestDao.truncate();
//
//        lotteryPoolService.initPool(activityLotteryConfig, configList);
//
//
//        log.info("-----奖池已初始化完成");
//    }
//
//    @并且("slot-lottery-全流程-VIP{}开始抽奖{},抽中{}")
//    public void slotLottery全流程VIPVip开始抽奖Type抽中LotteryNum(String vip, String type, int lotteryNum) throws Exception {
//        BusinessOP.lotteryNum = lotteryNum;
//        BusinessOP.vip = vip;
//
//        LotteryResponse lotteryResponse = remoteLotteryService.lottery(Long.parseLong(DateUtil.dateToStamp("2022-10-14 09:00:00")), "20220930145600", type, "kiki");
//        log.info("-----lotteryResponse:{}", JSON.toJSONString(lotteryResponse));
//    }
//
//
//    @并且("slot-lottery-mock风控决策失败")
//    public void slotLotteryslotmock风控决策失败() {
//        remoteBlockBusinessServiceDubboMock.verify_failed();
//    }
//
//    @那么("slot-lottery-校验activityCustomRewardMap为{}")
//    public void slotLottery校验activityCustomRewardMap为ActivityCustomRewardMap(String rewardMap) {
//
//
//        /*Map<String, ActivityCustomReward> realMap = BusinessOP.activityCustomRewardMap;
//        ActivityCustomReward real = realMap.get("2022-10");*/
//
//        if (BusinessOP.activityCustomRewardList.size() == 0) {
//            assertEquals("{}", rewardMap);
//            return;
//        }
//
//        ActivityCustomReward real = BusinessOP.activityCustomRewardList.get(0);
//        log.info("real:{}", JSON.toJSONString(real));
//
//        ActivityCustomReward expe = JSON.parseObject(rewardMap, ActivityCustomReward.class);
//        log.info("-----ActivityCustomReward real:{}", JSON.toJSONString(expe));
//
//        assertEquals(expe.getBatchId(), real.getBatchId());
//        assertEquals(expe.getCustomerId(), real.getCustomerId());
//        //assertEquals(expe.getSeq(), real.getSeq());
//        assertEquals(expe.getStatus(), real.getStatus());
//        assertEquals(expe.getActivityId(), real.getActivityId());
//        assertEquals(expe.getUserName(), real.getUserName());
//        assertEquals(expe.getNickName(), real.getNickName());
//        assertEquals(expe.getCurrency(), real.getCurrency());
//        assertEquals(expe.getAmount(), real.getAmount());
//        assertEquals(expe.getPhone(), real.getPhone());
//        assertEquals(expe.getEmail(), real.getEmail());
//        assertEquals(expe.getShard(), real.getShard());
//        assertEquals(expe.getMessage(), real.getMessage());
//        //assertEquals(expe.getBusinessId(), real.getBusinessId());
//        assertEquals(expe.getRewardType(), real.getRewardType());
//        assertEquals(expe.getSide(), real.getSide());
//        assertEquals(expe.getUserType(), real.getUserType());
//        assertEquals(expe.getScope(), real.getScope());
//        assertEquals(expe.getLevel(), real.getLevel());
//        assertEquals(expe.getReferId(), real.getReferId());
//        assertEquals(expe.getActivityName(), real.getActivityName());
//        assertEquals(expe.getCost(), real.getCost());
//
//    }
//
//    @那么("slot-lottery-校验activityLotteryItemMap为{}")
//    public void slotLottery校验activityLotteryItemMap为ActivityLotteryItemMap(String itemMap) {
//        Map<String, ActivityLotteryItem> realMap = BusinessOP.activityLotteryItemMap;
//
//        if (realMap.isEmpty()) {
//            assertEquals("{}", itemMap);
//            return;
//        }
//
//        ActivityLotteryItem real = realMap.values().stream().findFirst().get();
//        log.info("-----ActivityLotteryItem real:{}", JSON.toJSONString(real));
//
//        ActivityLotteryItem item = JSON.parseObject(itemMap, ActivityLotteryItem.class);
//        log.info("-----ActivityLotteryItem exp:{}", JSON.toJSONString(item));
//
//        //assertEquals(item.getId(), real.getId());
//        assertEquals(item.getCustomerId(), real.getCustomerId());
//        assertEquals(item.getPoolId(), real.getPoolId());
//        //assertEquals(item.getNickName(), real.getNickName());
//        //assertEquals(item.getSourceDrewId(), real.getSourceDrewId());
//        //assertEquals(item.getDrewId(), real.getDrewId());
//        assertEquals(item.getDrewName(), real.getDrewName());
//        assertEquals(item.getAmount(), real.getAmount());
//        assertEquals(item.getCurrency(), real.getCurrency());
//        //assertEquals(item.getDrewTime(), real.getDrewTime());
//        assertEquals(item.getMsg(), real.getMsg());
//        assertEquals(item.getStatus(), real.getStatus());
//        assertEquals(item.getUseAmount(), real.getUseAmount());
//        assertEquals(item.getUseCurrency(), real.getUseCurrency());
//    }
//
//    @那么("slot-lottery-校验activity_lottery_store_log为{}")
//    public void slotLottery校验activity_lottery_store_log为Activity_lottery_store_log(String activity_lottery_store_log) {
//        List<ActivityLotteryStoreLog> logs = activityLotteryStoreLogTestDao.selectAll();
//        System.out.println("-----List<ActivityLotteryStoreLog>:" + JSON.toJSONString(logs));
//
//        List<ActivityLotteryStoreLog> logs1 = JSON.parseArray(activity_lottery_store_log, ActivityLotteryStoreLog.class);
//
//        assertEquals(logs.size(), logs1.size());
//
//
//        for (int i = 0; i < logs.size(); i++) {
//            ActivityLotteryStoreLog res = logs1.get(i);
//            ActivityLotteryStoreLog rel = logs.get(i);
//
//            //assertEquals(res.getId(), rel.getId());
//            //assertEquals(res.getTime(), rel.getTime());
//            assertEquals(res.getStatus(), rel.getStatus());
//        }
//    }
//
//    @并且("slot-lottery-设置用户首次抽奖")
//    public void slotLottery设置用户首次抽奖() {
//        BusinessOP.count = 0;
//    }
//
//    @并且("slot-lottery-配置当前命中奖池无奖品")
//    public void slotLottery配置当前命中奖池无奖品() {
//        /*String sku = "2022101102122413904200";
//        String storeNo = "14";
//        redisService.hSet(RedisKeyConst.LOTTERY_POOL_KEY.getKey(sku), storeNo, 0);*/
//
//
//    }
//
//    @并且("slot-lottery-系统开始发奖")
//    public void slotLottery系统开始发奖() throws Exception {
//
//        LauncherParameter launcherParameter = new LauncherParameter();
//        launcherParameter.setActivityCustomReward(BusinessOP.reward);
//
//        activityRealTimeRewardTccService.reward(launcherParameter);
//
//    }
//
//    @当("slot-lottery-管理端配置{}保存和初始化奖池")
//    public void slotLottery管理端配置LotteryConfig保存和初始化奖池(String lotteryConfig) throws Exception {
//        StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse> responseObserver = new StreamObserver<com.kikitrade.activity.facade.award.LotteryResponse>() {
//            @Override
//            public void onNext(com.kikitrade.activity.facade.award.LotteryResponse lotteryResponse) {
//            }
//
//            @Override
//            public void onError(Throwable throwable) {
//            }
//
//            @Override
//            public void onCompleted() {
//            }
//        };
//
//        LotteryFacadeDTO lotteryFacadeDTO = JSON.parseObject(lotteryConfig, LotteryFacadeDTO.class);
//        log.info(JSON.toJSONString(lotteryFacadeDTO));
//
//        String saveOrUpdate = activityLotteryConfigService.saveOrUpdate(lotteryFacadeDTO);
//        System.out.println(("-----保存奖池：" + saveOrUpdate));
//    }
//
//    @那么("slot-lottery-校验activity_lottery_detail_config为{}")
//    public void slotLottery校验activity_lottery_detail_config为Activity_lottery_detail_config(String activity_lottery_detail_config) {
//        List<ActivityLotteryDetailConfig> activityLotteryDetailConfigs = activityLotteryDetailConfigTestDao.selectAll();
//        log.info(">>Actual--List<ActivityTaskItem>{}", JSON.toJSONString(activityLotteryDetailConfigs));
//
//
//        List<ActivityLotteryDetailConfig> activityLotteryDetailConfigs1 = JSON.parseArray(activity_lottery_detail_config, ActivityLotteryDetailConfig.class);
//        log.info(">>Expect--List<ActivityTaskItem>{}", JSON.toJSONString(activityLotteryDetailConfigs1));
//
//        assertEquals(activityLotteryDetailConfigs1.size(), activityLotteryDetailConfigs.size());
//
//        for (int i = 0; i < activityLotteryDetailConfigs.size(); i++) {
//            ActivityLotteryDetailConfig res = activityLotteryDetailConfigs1.get(i);
//            ActivityLotteryDetailConfig rel = activityLotteryDetailConfigs.get(i);
//
//            //assertEquals(res.getId(), rel.getId());
//            //assertEquals(res.getLotteryId(), rel.getLotteryId());
//            assertEquals(res.getName(), rel.getName());
//            assertEquals(res.getCurrency(), rel.getCurrency());
//            assertEquals(res.getAmount(), rel.getAmount());
//            assertEquals(res.getNum(), rel.getNum());
//            assertEquals(res.getPercent(), rel.getPercent());
//            assertEquals(res.getIsLow(), rel.getIsLow());
//            assertEquals(res.getRemainNum(), rel.getRemainNum());
//
//        }
//
//    }
//
//    @那么("slot-lottery-校验activity_lottery_pool_config")
//    public void slotLottery校验activity_lottery_pool_config() {
//        List<ActivityLotteryPoolConfig> activityLotteryPoolConfigs = activityLotteryPoolConfigTestDao.selectAll();
//        System.out.println("-----List<ActivityLotteryPoolConfig>:" + JSON.toJSONString(activityLotteryPoolConfigs));
//
//        //校验
//        //1.一等奖20，二等奖40，三等奖60，四等奖100
//        //2.一等奖数量1或2，二等奖数量1或2，三等奖数量1或2，四等奖数量3或4
//        //3.一等奖奖池15 二等奖奖池31，三等奖奖池31，四等奖奖池31
//        //4.首抽
//
//        //校验一等奖
//        String first_prize = activityLotteryDetailConfigTestDao.getIdByName("first prize");
//        List<ActivityLotteryPoolConfig> firstPoolConfigs = activityLotteryPoolConfigTestDao.selectByDrawId(first_prize);
//
//        int sum = 0;
//        for (ActivityLotteryPoolConfig firstPoolConfig : firstPoolConfigs) {
//            Integer remainNum = firstPoolConfig.getRemainNum();
//            sum = sum + remainNum;
//            assertTrue(remainNum >= 1 && remainNum <= 2);
//        }
//        assertEquals(20, sum);
//        assertEquals(15, firstPoolConfigs.size());
//
//        //校验二等奖
//        String second_prize = activityLotteryDetailConfigTestDao.getIdByName("second prize");
//        List<ActivityLotteryPoolConfig> secondPoolConfigs = activityLotteryPoolConfigTestDao.selectByDrawId(second_prize);
//
//        sum = 0;
//        for (ActivityLotteryPoolConfig secondPoolConfig : secondPoolConfigs) {
//            Integer remainNum = secondPoolConfig.getRemainNum();
//            sum = sum + remainNum;
//            assertTrue(remainNum >= 1 && remainNum <= 2);
//        }
//        assertEquals(40, sum);
//        assertEquals(31, secondPoolConfigs.size());
//
//        //校验三等奖
//        String third_prize = activityLotteryDetailConfigTestDao.getIdByName("third prize");
//        List<ActivityLotteryPoolConfig> thirdPoolConfigs = activityLotteryPoolConfigTestDao.selectByDrawId(third_prize);
//
//        sum = 0;
//        for (ActivityLotteryPoolConfig thirdPoolConfig : thirdPoolConfigs) {
//            Integer remainNum = thirdPoolConfig.getRemainNum();
//            sum = sum + remainNum;
//            assertTrue(remainNum >= 1 && remainNum <= 2);
//        }
//        assertEquals(60, sum);
//        assertEquals(31, thirdPoolConfigs.size());
//
//        //校验四等奖
//        String fourth_prize = activityLotteryDetailConfigTestDao.getIdByName("fourth prize");
//        List<ActivityLotteryPoolConfig> fourthPoolConfigs = activityLotteryPoolConfigTestDao.selectByDrawId(fourth_prize);
//
//        sum = 0;
//        for (ActivityLotteryPoolConfig fourthPoolConfig : fourthPoolConfigs) {
//            Integer remainNum = fourthPoolConfig.getRemainNum();
//            sum = sum + remainNum;
//            assertTrue(remainNum >= 3 && remainNum <= 4);
//        }
//        assertEquals(100, sum);
//        assertEquals(31, fourthPoolConfigs.size());
//
//
//    }
//
//    @那么("slot-lottery-校验activity_lottery_config为{}")
//    public void slotLottery校验activity_lottery_config为Activity_lottery_config(String activity_lottery_config) {
//        List<ActivityLotteryConfig> activityLotteryConfigs = activityLotteryConfigTestDao.selectAll();
//        System.out.println("-----List<ActivityLotteryConfig>:" + JSON.toJSONString(activityLotteryConfigs));
//
//        //期望
//        List<ActivityLotteryConfig> configList = JSON.parseArray(activity_lottery_config, ActivityLotteryConfig.class);
//
//
//        for (int i = 0; i < configList.size(); i++) {
//
//            ActivityLotteryConfig res = configList.get(i);
//            ActivityLotteryConfig rel = activityLotteryConfigs.get(i);
//
//            assertEquals(res.getValid(), rel.getValid());
//            assertEquals(res.getRemark(), rel.getRemark());
//            assertEquals(res.getStatus(), rel.getStatus());
//            assertEquals(res.getVipLevel(), rel.getVipLevel());
//            assertEquals(res.getAmount(), rel.getAmount());
//            assertEquals(res.getCurrency(), rel.getCurrency());
//            assertEquals(res.getTimesLimit(), rel.getTimesLimit());
//            assertEquals(res.getRewardLimit(), rel.getRewardLimit());
//            assertEquals(res.getType(), rel.getType());
//            //assertEquals(res.getId(), rel.getId());
//
//        }
//
//
//    }
//
//    @当("slot-lottery-配置指定奖品{}")
//    public void slotLottery配置指定奖品AssignDraw(String assignDraw) {
//        kactivityLotteryConfig.load(assignDraw);
//        System.out.println("-----指定奖品已配置");
//
//
//    }
//
//
//    @并且("slot-lottery-配置奖池无奖品{}")
//    public void slotLottery配置奖池无奖品NoRewardPool(String noRewardPool) {
//
//        JSONArray array = JSON.parseArray(noRewardPool);
//        Integer first = (Integer) array.get(0);
//        Integer end = (Integer) array.get(1);
//        Integer[] poolNos = toPoolNos(first, end);
//
//        String lotteryId = activityLotteryDetailConfigTestDao.getLotteryIdByName("first prize");
//
//        String id = activityLotteryDetailConfigTestDao.getIdByName("first prize");
//
//        //remain_num的和
//        int num = activityLotteryPoolConfigTestDao.getSumRemainNumByCondition(id, poolNos);
//
//        //修改 activityLotteryPoolConfig remain_num
//        activityLotteryPoolConfigTestDao.updateRemainNumByCondition(id, poolNos);
//
//        //修改 activityLotteryDetailConfig remain_num
//        activityLotteryDetailConfigTestDao.updateRemainNumByCondition(num, id);
//
//
//    }
//
//    private Integer[] toPoolNos(Integer first, Integer end) {
//        List<Integer> pools = new ArrayList<>();
//
//
//        for (int i = first; i <= end; i++) {
//            pools.add(i);
//        }
//
//        int size = pools.size();
//
//        return pools.toArray(new Integer[size]);
//    }
//
//    @并且("slot-lottery-配置首抽奖池无奖品")
//    public void slotLottery配置首抽奖池无奖品() {
//        String id = activityLotteryDetailConfigTestDao.getIdByName("assign_slot_L1_1");
//
//        activityLotteryDetailConfigTestDao.updateRemainNumByDrawId(0, id);
//
//        activityLotteryPoolConfigTestDao.updateRemainNumByDrawId(0, "assign_slot_L1_1");
//    }
//
//    @并且("slot-lottery-定点爆破{}")
//    public void slotLottery定点爆破PointBlast(String pointBlast) {
//        BusinessOP.pointBlast = pointBlast;
//    }
//
//    @并且("slot-lottery-回滚-VIP{}开始抽奖{},抽中{}")
//    public void slotLottery回滚VIPVip开始抽奖Type抽中LotteryNum(String vip, String type, int lotteryNum) {
//
//        BusinessOP.lotteryNum = lotteryNum;
//        BusinessOP.vip = vip;
//
//        try {
//            LotteryResponse lotteryResponse = remoteLotteryService.lottery(1665726100709L, "20220930145600", type, "kiki");
//            log.info("-----lotteryResponse:{}", JSON.toJSONString(lotteryResponse));
//        } catch (Exception e) {
//
//            String s = JSON.toJSONString(e);
//            log.info("-----捕获到异常:{}", s);
//            BusinessOP.exceptions.add(s);
//
//        }
//    }
//
//    @并且("slot-lottery-校验exceptions为{}")
//    public void slotLottery校验exceptions为Exceptions(String exceptions) {
//        log.info("-----exceptions:{}", BusinessOP.exceptions);
//        if (BusinessOP.exceptions.size() == 0) {
//            assertEquals("null", exceptions);
//            return;
//        }
//
//        assertTrue(BusinessOP.exceptions.get(0).contains(exceptions));
//    }
//
//    @并且("slot-lottery-配置activityLotteryItemBuilder.insert抛异常")
//    public void slotLottery配置activityLotteryItemBuilderInsert抛异常() {
//        activityLotteryItemBuilderMock.insertErr();
//    }
//
//    @并且("slot-lottery-回滚-系统开始发奖")
//    public void slotLottery回滚系统开始发奖() {
//        LauncherParameter launcherParameter = new LauncherParameter();
//        launcherParameter.setActivityCustomReward(BusinessOP.reward);
//
//        try {
//            activityRealTimeRewardTccService.reward(launcherParameter);
//        } catch (Exception e) {
//            String s = JSON.toJSONString(e);
//            log.info("-----捕获到异常:{}", s);
//            BusinessOP.exceptions.add(s);
//        }
//    }
//
//    @并且("slot-lottery-配置remoteAccountingOperateService.transfer抛异常")
//    public void slotLottery配置remoteAccountingOperateServiceTransfer抛异常() throws AccountException {
//        remoteAccountingOperateServiceDubboMock.transferErr();
//    }
//
//    @并且("slot-lottery-配置remoteAccountingOperateService.transfer正常")
//    public void slotLottery配置remoteAccountingOperateServiceTransfer正常() throws AccountException {
//        remoteAccountingOperateServiceDubboMock.transfer();
//    }
//
//    @并且("slot-lottery-并发-VIP{}开始抽奖{},抽中{}")
//    public void slotLottery并发VIPVip开始抽奖Type抽中LotteryNum(String vip, String type, int lotteryNum) throws Exception {
//        BusinessOP.lotteryNum = lotteryNum;
//        BusinessOP.vip = vip;
//
//        int len = 10;
//        final CountDownLatch endLatch = new CountDownLatch(len);
//        final CountDownLatch mainLatch = new CountDownLatch(len);
//
//        for (int i = 0; i < len; i++) {
//            new Thread(new Runnable() {
//                @SneakyThrows
//                @Override
//                public void run() {
//                    try {
//                        endLatch.countDown();
//                        endLatch.await();
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                    LotteryResponse lotteryResponse = remoteLotteryService.lottery(1665726100709L, "20220930145600", type, "kiki");
//                    log.info("-----lotteryResponse:{}", JSON.toJSONString(lotteryResponse));
//                    mainLatch.countDown();
//                }
//            }).start();
//        }
//        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
//        try {
//            mainLatch.await();
//            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @当("slot-lottery-使用抽奖产生{}次随机数")
//    public void slotLottery使用抽奖产生Times次随机数(int times) {
//
//        //不使用切面
//        BusinessOP.randomFlag = false;
//
//        List<Integer> randoms = new ArrayList<>();
//
//        for (int i = 0; i < times; i++) {
////            int random = lotteryService.getRandom();
//            int random = slotLotteryService.getRandom();
//            randoms.add(random);
//        }
//
//        int count = 0;
//        int intervals = 100;
//        int max = 10000;
//        long m = 0;
//
//
//        List<Long> l = new ArrayList<>();
//
//        while (count < 100) {
//            m = m + 100;
//            long finalM = m;
//            long res = randoms.stream().filter(a -> (a > (finalM - intervals) && a < finalM)).count();
//            l.add(res);
//            count++;
//        }
//
//        System.out.println("------命中区间次数" + l);
//
//        BusinessOP.interval = l;
//    }
//
//    @那么("slot-lottery-校验命中区间次数")
//    public void slotLottery校验命中区间次数() {
//        List<Long> interval = BusinessOP.interval;
//        long count = interval.stream().filter(a -> a < 900 || a > 1100).count();
//        assertEquals(0L, count);
//
//    }
//
//    @并且("slot-lottery-抽奖日期与奖池映射-日期{}VIP{}开始抽奖{},抽中{}")
//    public void slotLottery抽奖日期与奖池映射日期DateVIPVip开始抽奖Type抽中LotteryNum(String date, String vip, String type, int lotteryNum) throws Exception {
//        BusinessOP.lotteryNum = lotteryNum;
//        BusinessOP.vip = vip;
//        long toStamp = Long.parseLong(DateUtil.dateToStamp(date));
//        LotteryResponse lotteryResponse = remoteLotteryService.lottery(toStamp, "20220930145600", type, "kiki");
//        log.info("-----lotteryResponse:{}", JSON.toJSONString(lotteryResponse));
//    }
//
//    @那么("slot-lottery-校验,奖池是{}")
//    public void slotLottery校验奖池是StoreNo(String storeNo) {
//        StoreInfo storeInfo = BusinessOP.storeInfo;
//        assertEquals(storeNo, storeInfo.getStoreNo());
//    }
//
//    @那么("slot-lottery-校验activity_lottery_pool_config扣减{}")
//    public void slotLottery校验activity_lottery_pool_config扣减num(int num) {
//        //对应奖池库存的扣减
//        assertEquals(num, BusinessOP.remainNumBeforeLottery - BusinessOP.remainNumAfterLottery);
//    }
//
//    @那么("slot-lottery-校验assetTransferInRequestList{}")
//    public void slotLottery校验assetTransferInRequestList(String assetTransferInRequestList) {
//        List<AssetTransferInRequest> requestsEXP = JSON.parseArray(assetTransferInRequestList, AssetTransferInRequest.class);
//        log.info(">>Expect--List<AssetTransferInRequest>{}", requestsEXP);
//        List<AssetTransferInRequest> requestsACT = BusinessOP.assetTransferInRequestList;
//        log.info(">>Actual--List<AssetTransferInRequest>{}", BusinessOP.assetTransferInRequestList);
//        CheckUtils.compare(requestsEXP, requestsACT);
//    }
//
//    @那么("slot-lottery-校验accountTransferRequest{}")
//    public void slotLottery校验accountTransferRequest(String accountTransferRequests) {
//        List<AccountTransferRequest> requestsEXP = JSON.parseArray(accountTransferRequests, AccountTransferRequest.class);
//        log.info(">>Expect--List<AccountTransferRequest>{}", requestsEXP);
//        log.info(">>Actual--List<AccountTransferRequest>{}", accountTransferRequestList);
//        CheckUtils.compare(requestsEXP, accountTransferRequestList);
//    }
//
//    @当("slot-lottery-VIP用户{}开始抽奖{}")
//    public void slotLotteryVIP用户开始抽奖不满足抽奖(String vip, String type) {
//        BusinessOP.vip = vip;
//        try {
//            LotteryResponse lotteryResponse = remoteLotteryService.lottery(Long.parseLong(DateUtil.dateToStamp("2022-10-14 09:00:00")), "202209301456001111", type, "kiki");
//            log.info("-----lotteryResponse:{}", JSON.toJSONString(lotteryResponse));
//        } catch (Exception e) {
//            log.error("-----lotteryResponse:{}", e.getMessage());
//            BusinessOP.exceptions.add(JSON.toJSONString(e));
//        }
//
//    }
//
//    @并且("slot-lottery-获取抽奖前remain_num{}")
//    public void slotLottery获取抽奖前remain_numName(String name) {
//        String n = name.split(",")[0];
//        String i = name.split(",")[1];
//
//        if ("low prize".equals(n) || "thanks".equals(n)) {
//            BusinessOP.remainNumBeforeLottery = 99999;
//            return;
//        }
//
//
//        //获取抽奖前库存
//        String id = activityLotteryDetailConfigTestDao.getIdByName(n);
//        BusinessOP.remainNumBeforeLottery = activityLotteryPoolConfigTestDao.getRemainNumByCondition(id, i);
//        log.info("-----抽奖前库存:{}", BusinessOP.remainNumBeforeLottery);
//    }
//
//    @并且("slot-lottery-获取抽奖后remain_num{}")
//    public void slotLottery获取抽奖后remain_numName(String name) {
//        String n = name.split(",")[0];
//        String i = name.split(",")[1];
//
//        if ("low prize".equals(n) || "thanks".equals(n)) {
//            BusinessOP.remainNumAfterLottery = 99999;
//            return;
//        }
//
//        //获取抽奖后库存
//        String id = activityLotteryDetailConfigTestDao.getIdByName(n);
//        BusinessOP.remainNumAfterLottery = activityLotteryPoolConfigTestDao.getRemainNumByCondition(id, i);
//        log.info("-----抽奖后库存:{}", BusinessOP.remainNumAfterLottery);
//    }
//
//    @并且("slot-lottery-配置SEQ")
//    public void slotLottery配置SEQ() {
//        seqGeneraterServiceMock.next2();
//    }
//
//    @并且("slot-lottery-配置奖池一等奖的数量为{}")
//    public void slotLottery配置奖池一等奖的数量为Num(int num) {
//        String first_prize = activityLotteryDetailConfigTestDao.getIdByName("first prize");
//        activityLotteryPoolConfigTestDao.updateRemainNumByDrawId(num, first_prize);
//    }
//
//    @并且("slot-lottery-配置activityLotteryItemBuilder.update抛异常")
//    public void slotLottery配置activityLotteryItemBuilderUpdate抛异常() {
//
//        activityLotteryItemBuilderMock.updateErr();
//
//    }
//
//    @那么("slot-lottery-并发-校验activity_lottery_detail_config为{}")
//    public void slotLottery并发校验activity_lottery_detail_config为Activity_lottery_detail_config(String activity_lottery_detail_config) {
//
//    }
//
//    @那么("slot-lottery-校验产品帐冻结{}")
//    public void slotLottery校验产品帐冻结Freeze(String freeze) {
//        ProductAccountParam real = BusinessOP.productAccountParam;
//
//        log.info("-----ProductAccountParam:{}", JSON.toJSONString(real));
//
//        //期望
//        ProductAccountParam resp = JSON.parseObject(freeze, ProductAccountParam.class);
//
//        assertEquals(resp.getCustomerId(), real.getCustomerId());
//        assertEquals(resp.getProductLimitCode(), real.getProductLimitCode());
//        assertEquals(resp.getAmount(), real.getAmount());
//
//    }
//
//    /**
//     * 账务重构kquota
//     */
//    @那么("slot-lottery-校验产品帐创建请求为{}")
//    public void slotlottery校验产品帐创建请求为(String quotaConfigDTO) {
//        List<QuotaConfigDTO> expConfigs = JSON.parseArray(quotaConfigDTO, QuotaConfigDTO.class);
//        log.info(">>Expect--List<QuotaConfigDTO>:{}", JSON.toJSONString(expConfigs));
//
//        List<QuotaConfigDTO> actConfigs = quotaConfigDTOList;
//        log.info(">>Actual--List<QuotaConfigDTO>:{}", JSON.toJSONString(actConfigs));
//        CheckUtils.compare(expConfigs, actConfigs);
//    }
//
//    @那么("slot-lottery-校验产品帐quota冻结请求为{}")
//    public void slotlottery校验产品帐quota冻结请求为(String quotaFreezeDTO) {
//        List<QuotaFreezeDTO> expConfigs = JSON.parseArray(quotaFreezeDTO, QuotaFreezeDTO.class);
//        log.info(">>Expect--List<QuotaFreezeDTO>:{}", JSON.toJSONString(expConfigs));
//
//        List<QuotaFreezeDTO> actConfigs = quotaFreezeDTOList;
//        log.info(">>Actual--List<QuotaFreezeDTO>:{}", JSON.toJSONString(actConfigs));
//        CheckUtils.compare(expConfigs, actConfigs, new String[]{"bizId"});
//    }
//
//    @那么("slot-lottery-校验产品帐quota冻结扣减请求为{}")
//    public void slotlottery校验产品帐quota冻结扣减请求为(String quotaUnfreezeDeduct) {
//        List<QuotaUnfreezeDTO> expConfigs = JSON.parseArray(quotaUnfreezeDeduct, QuotaUnfreezeDTO.class);
//        log.info(">>Expect--List<QuotaUnfreezeDTO>:{}", JSON.toJSONString(expConfigs));
//
//        List<QuotaUnfreezeDTO> actConfigs = quotaUnfreezeDeductList;
//        log.info(">>Actual--List<QuotaUnfreezeDTO>:{}", JSON.toJSONString(actConfigs));
//        CheckUtils.compare(expConfigs, actConfigs, new String[]{"bizId"});
//    }
//
//    @那么("slot-lottery-校验产品帐quota扣减请求为{}")
//    public void slotlottery校验产品帐quota扣减请求为(String quotaDeduct) {
//        List<QuotaDeductDTO> expQuotaDeducts = JSON.parseArray(quotaDeduct, QuotaDeductDTO.class);
//        log.info(">>Expect--List<QuotaDeductDTO>:{}", JSON.toJSONString(expQuotaDeducts));
//
//        List<QuotaDeductDTO> actQuotaDeducts = quotaDeductDTOList;
//        log.info(">>Actual--List<QuotaDeductDTO>:{}", JSON.toJSONString(actQuotaDeducts));
//        CheckUtils.compare(expQuotaDeducts, actQuotaDeducts, new String[]{"bizId"});
//    }
//
//    @那么("slot-lottery-校验产品帐quota解冻请求为{}")
//    public void slotlottery校验产品帐quota解冻请求为(String quotaUnfreezeDTO) {
//        List<QuotaUnfreezeDTO> expConfigs = JSON.parseArray(quotaUnfreezeDTO, QuotaUnfreezeDTO.class);
//        log.info(">>Expect--List<QuotaUnfreezeDTO>:{}", JSON.toJSONString(expConfigs));
//
//        List<QuotaUnfreezeDTO> actConfigs = quotaUnfreezeDTOList;
//        log.info(">>Actual--List<QuotaUnfreezeDTO>:{}", JSON.toJSONString(actConfigs));
//        CheckUtils.compare(expConfigs, actConfigs);
//    }
//
//    @并且("slot-校验扣减燃料{}")
//    public void slot校验扣减燃料PointTransfer(String pointTransfer) {
//        AssetTransferOutRequest real = BusinessOP.assetTransferOutRequest;
//
//        log.info("-----BusinessOP.assetTransferOutRequest:{}", JSON.toJSONString(real));
//
//        AssetTransferOutRequest resp = JSON.parseObject(pointTransfer, AssetTransferOutRequest.class);
//
//
//        assertEquals(resp.getCustomerId(), real.getCustomerId());
//        assertEquals(resp.getBusinessType(), real.getBusinessType());
//        //assertEquals(resp.getBusinessId(), real.getBusinessId());
//        assertEquals(resp.getType(), real.getType());
//        assertEquals(resp.getCategory(), real.getCategory());
//        assertEquals(resp.getAmount(), real.getAmount());
//        assertEquals(resp.getSaasId(), real.getSaasId());
//
//    }
//
//    @并且("slot-校验派奖转账{}")
//    public void slot校验派奖转账RewardTransfer(String rewardTransfer) {
//
//        AccountTransferRequest real = accountTransferRequestList.get(0);
//
//        log.info("-----BusinessOP.accountTransferRequest:{}", JSON.toJSONString(real));
//
//        AccountTransferRequest resp = JSON.parseObject(rewardTransfer, AccountTransferRequest.class);
//
//        //assertEquals(resp.getBusinessId(), real.getBusinessId());
//        assertEquals(resp.getType(), real.getType());
//        assertEquals(resp.getFromCustomerId(), real.getFromCustomerId());
//        assertEquals(resp.getToCustomerId(), real.getToCustomerId());
//        assertEquals(resp.getFromCategory(), real.getFromCategory());
//        assertEquals(resp.getToCategory(), real.getToCategory());
//        assertEquals(resp.getCurrency(), real.getCurrency());
//        assertEquals(resp.getAmount(), real.getAmount());
//        assertEquals(resp.getFee(), real.getFee());
//        assertEquals(resp.getFeeCustomer(), real.getFeeCustomer());
//        assertEquals(resp.isStrictCheck(), real.isStrictCheck());
//        assertEquals(resp.getSaasId(), real.getSaasId());
//
//
//    }
//
//    @当("slot-lottery-使用draw抽奖产生{}次随机数")
//    public void slotLottery使用draw抽奖产生Times次随机数(double times) throws Exception {
//        //不使用切面
//        BusinessOP.randomFlag = false;
//
//        List<String> randoms = new ArrayList<>();
//
//        for (int i = 0; i < times; i++) {
//            ActivityLotteryDetailConfig draw = slotLotteryService.draw(1665726100709L, "20220930145600", "L1", "slot", "kiki");
//            randoms.add(draw.getName());
//        }
//
//
//        Map<String, Long> resMap = randoms.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
//
//
//        System.out.println("一共抽了" + times + "次");
//        System.out.println("配置：一等奖5%、二等奖10%、三等奖15%、四等奖20%、兜底奖品20%、谢谢惠顾30%");
//        System.out.println("一等奖:" + resMap.get("first prize") + "次, 概率统计:" + resMap.get("first prize") / times);
//        System.out.println("二等奖:" + resMap.get("second prize") + "次, 概率统计:" + resMap.get("second prize") / times);
//        System.out.println("三等奖:" + resMap.get("third prize") + "次, 概率统计:" + resMap.get("third prize") / times);
//        System.out.println("四等奖:" + resMap.get("fourth prize") + "次, 概率统计:" + resMap.get("fourth prize") / times);
//        System.out.println("兜底奖:" + resMap.get("low prize") + "次, 概率统计:" + resMap.get("low prize") / times);
//        System.out.println("谢谢惠顾:" + resMap.get("thanks") + "次, 概率统计:" + resMap.get("thanks") / times);
//        System.out.println(resMap);
//
//
//        int count = 0;
//        int intervals = 100;
//        int max = 10000;
//        long m = 0;
//
//
////        List<Long> l = new ArrayList<>();
//
////        while (count < 100) {
////            m = m + 100;
////            long finalM = m;
////            long res = randoms.stream().filter(a -> (a > (finalM - intervals) && a < finalM)).count();
////            l.add(res);
////            count++;
////        }
////
////        System.out.println("------命中区间次数" + l);
//
////        BusinessOP.interval = l;
//    }
//
//    @并且("slot-lottery-使用draw抽奖在规定日期产生次随机数")
//    public void slotLottery使用draw抽奖在规定日期产生次随机数(DataTable dataTable) throws Exception {
//
//
//        //不使用切面
//        BusinessOP.randomFlag = false;
//
//        List<String> randoms = new ArrayList<>();
//
//        double times = 1000;
//
//        String name = "test_appendfile_" + System.currentTimeMillis() + ".txt";
//
//        List<String> rows = dataTable.asList();
//
//        for (String date : rows) {
//            long toStamp = Long.parseLong(DateUtil.dateToStamp(date));
//            randoms.clear();
//
//
//            for (int i = 0; i < times; i++) {
//                LotteryResponse lotteryResponse = remoteLotteryService.lottery(toStamp, "20220930145600", "slot", "kiki");
//                log.info("-----lotteryResponse:{}", JSON.toJSONString(lotteryResponse));
//                randoms.add(lotteryResponse.getCurrency() + "_" + lotteryResponse.getReward() + "_" + lotteryResponse.getWin());
//            }
//
//
//            Map<String, Long> resMap = randoms.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
//
//
//            String c1 = "抽奖时间:" + date + ", 一共抽了" + times + "次";
//            String c2 = "配置：一等奖0.1%(BTC,0.1,5份)、二等奖1%(BTC,0.05,40份)、三等奖10%(BTC,0.02，60份)、四等奖20%(BTC,0.02,100份)、兜底奖品20%(POINT, 50)";
//            String c3 = "一等奖:" + resMap.getOrDefault("BTC_0.10000000_true", 0L) + "次, 概率统计:" + resMap.getOrDefault("BTC_0.10000000_true", 0L) / times;
//            String c4 = "二等奖:" + resMap.getOrDefault("BTC_0.05000000_true", 0L) + "次, 概率统计:" + resMap.getOrDefault("BTC_0.05000000_true", 0L) / times;
//            String c5 = "三等奖:" + resMap.getOrDefault("BTC_0.02000000_true", 0L) + "次, 概率统计:" + resMap.getOrDefault("BTC_0.02000000_true", 0L) / times;
//            String c6 = "四等奖:" + resMap.getOrDefault("BTC_0.01000000_true", 0L) + "次, 概率统计:" + resMap.getOrDefault("BTC_0.01000000_true", 0L) / times;
//            String c7 = "兜底奖:" + resMap.getOrDefault("POINT_50.00000000_true", 0L) + "次, 概率统计:" + resMap.getOrDefault("POINT_50.00000000_true", 0L) / times;
//            String c8 = "谢谢惠顾:" + resMap.getOrDefault("null_null_false", 0L) + "次, 概率统计:" + resMap.getOrDefault("null_null_false", 0L) / times;
//
//
//            System.out.println(c1);
//            System.out.println(c2);
//            System.out.println(c3);
//            System.out.println(c4);
//            System.out.println(c5);
//            System.out.println(c6);
//            System.out.println(c7);
//            System.out.println(c8);
//
//            System.out.println(resMap);
//
//            try {
//
//                String content = "A cat?will append to the end of the file";
//
//
//                File file = new File(name);
//
//                if (!file.exists()) {
//                    file.createNewFile();
//                }
//
//                //使用true，即进行append file
//
//                FileWriter fileWritter = new FileWriter(file.getName(), true);
//
//
//                fileWritter.write(c1);
//                fileWritter.write("\n");
//
//                fileWritter.write(c2);
//                fileWritter.write("\n");
//
//                fileWritter.write(c3);
//                fileWritter.write("\n");
//
//                fileWritter.write(c4);
//                fileWritter.write("\n");
//
//                fileWritter.write(c5);
//                fileWritter.write("\n");
//
//                fileWritter.write(c6);
//                fileWritter.write("\n");
//
//                fileWritter.write(c7);
//                fileWritter.write("\n");
//
//                fileWritter.write(c8);
//                fileWritter.write("\n");
//                fileWritter.write("\n");
//
//                fileWritter.close();
//
//                System.out.println("finish");
//
//            } catch (IOException e) {
//                e.printStackTrace();
//
//            }
//
//        }
//
//
//    }
//
//    @并且("slot-lottery-奖池累积-使用draw抽奖在规定日期产生次随机数")
//    public void slotLottery奖池累积使用draw抽奖在规定日期产生次随机数(DataTable dataTable) throws Exception {
//        //不使用切面
//        BusinessOP.randomFlag = false;
//
//        List<String> randoms = new ArrayList<>();
//
//        //double times = 1000;
//
//        String name = "test_appendfile_" + System.currentTimeMillis() + ".txt";
//
//        List<List<String>> lists = dataTable.asLists();
//
//        for (List<String> list : lists) {
//            long toStamp = Long.parseLong(DateUtil.dateToStamp(list.get(0)));
//
//            String date = list.get(0);
//            double times = Double.parseDouble(list.get(1));
//
//            randoms.clear();
//
//
//            for (int i = 0; i < times; i++) {
//                LotteryResponse lotteryResponse = remoteLotteryService.lottery(toStamp, "20220930145600", "slot", "kiki");
//                log.info("-----lotteryResponse:{}", JSON.toJSONString(lotteryResponse));
//                randoms.add(lotteryResponse.getCurrency() + "_" + lotteryResponse.getReward() + "_" + lotteryResponse.getWin());
//            }
//
//
//            Map<String, Long> resMap = randoms.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
//
//
//            String c1 = "抽奖时间:" + date + ", 一共抽了" + times + "次";
//            String c2 = "配置：一等奖0.1%(BTC,0.1,5份)、二等奖1%(BTC,0.05,40份)、三等奖10%(BTC,0.02，60份)、四等奖20%(BTC,0.02,100份)、兜底奖品20%(POINT, 50)";
//            String c3 = "一等奖:" + resMap.getOrDefault("BTC_0.10000000_true", 0L) + "次, 概率统计:" + resMap.getOrDefault("BTC_0.10000000_true", 0L) / times;
//            String c4 = "二等奖:" + resMap.getOrDefault("BTC_0.05000000_true", 0L) + "次, 概率统计:" + resMap.getOrDefault("BTC_0.05000000_true", 0L) / times;
//            String c5 = "三等奖:" + resMap.getOrDefault("BTC_0.02000000_true", 0L) + "次, 概率统计:" + resMap.getOrDefault("BTC_0.02000000_true", 0L) / times;
//            String c6 = "四等奖:" + resMap.getOrDefault("BTC_0.01000000_true", 0L) + "次, 概率统计:" + resMap.getOrDefault("BTC_0.01000000_true", 0L) / times;
//            String c7 = "兜底奖:" + resMap.getOrDefault("POINT_50.00000000_true", 0L) + "次, 概率统计:" + resMap.getOrDefault("POINT_50.00000000_true", 0L) / times;
//            String c8 = "谢谢惠顾:" + resMap.getOrDefault("null_null_false", 0L) + "次, 概率统计:" + resMap.getOrDefault("null_null_false", 0L) / times;
//
//
//            System.out.println(c1);
//            System.out.println(c2);
//            System.out.println(c3);
//            System.out.println(c4);
//            System.out.println(c5);
//            System.out.println(c6);
//            System.out.println(c7);
//            System.out.println(c8);
//
//            System.out.println(resMap);
//
//            try {
//
//                String content = "A cat?will append to the end of the file";
//
//
//                File file = new File(name);
//
//                if (!file.exists()) {
//                    file.createNewFile();
//                }
//
//                //使用true，即进行append file
//
//                FileWriter fileWritter = new FileWriter(file.getName(), true);
//
//
//                fileWritter.write(c1);
//                fileWritter.write("\n");
//
//                fileWritter.write(c2);
//                fileWritter.write("\n");
//
//                fileWritter.write(c3);
//                fileWritter.write("\n");
//
//                fileWritter.write(c4);
//                fileWritter.write("\n");
//
//                fileWritter.write(c5);
//                fileWritter.write("\n");
//
//                fileWritter.write(c6);
//                fileWritter.write("\n");
//
//                fileWritter.write(c7);
//                fileWritter.write("\n");
//
//                fileWritter.write(c8);
//                fileWritter.write("\n");
//                fileWritter.write("\n");
//
//                fileWritter.close();
//
//                System.out.println("finish");
//
//            } catch (IOException e) {
//                e.printStackTrace();
//
//            }
//
//        }
//    }
//}
//
