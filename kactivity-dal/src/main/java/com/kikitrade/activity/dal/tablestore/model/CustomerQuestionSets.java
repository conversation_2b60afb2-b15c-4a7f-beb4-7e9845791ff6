package com.kikitrade.activity.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/29 14:40
 */
@Data
@Table(name = "customer_question_sets")
public class CustomerQuestionSets implements Serializable {

    public static final String INDEX_CUSTOMER_QUESTION_SETS = "index_customer_question_sets";

    @PartitionKey(name = "saas_id")
    @SearchIndex(name = INDEX_CUSTOMER_QUESTION_SETS, column = "saas_id")
    private String saasId;

    @PartitionKey(name = "customer_id")
    @SearchIndex(name = INDEX_CUSTOMER_QUESTION_SETS, column = "customer_id")
    private String customerId;

    @Column(name = "used_sets")
    @SearchIndex(name = INDEX_CUSTOMER_QUESTION_SETS, column = "used_sets", fieldType = FieldType.LONG)
    private Integer usedSets = 0;

    @Column(name = "available_sets")
    @SearchIndex(name = INDEX_CUSTOMER_QUESTION_SETS, column = "available_sets", fieldType = FieldType.LONG)
    private Integer availableSets = 0;

    /**
     * 开启奖励时间
     */
    @Column(name = "begin_reward_time")
    @SearchIndex(name = INDEX_CUSTOMER_QUESTION_SETS, column = "begin_reward_time", fieldType = FieldType.LONG)
    private Long beginRewardTime = 0L;

    /**
     * 奖励剩余时间
     */
    @Column(name = "reward_remain_time")
    @SearchIndex(name = INDEX_CUSTOMER_QUESTION_SETS, column = "reward_remain_time", fieldType = FieldType.LONG)
    private Long rewardRemainTime = 0L;

    @Column(name = "created")
    @SearchIndex(name = INDEX_CUSTOMER_QUESTION_SETS, column = "created", fieldType = FieldType.LONG)
    private Long created;

    @Column(name = "modified")
    @SearchIndex(name = INDEX_CUSTOMER_QUESTION_SETS, column = "modified", fieldType = FieldType.LONG)
    private Long modified;

}
