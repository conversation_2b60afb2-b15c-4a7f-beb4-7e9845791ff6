//package com.kikitrade.activity.mock.impl;
//
//import com.aliyun.openservices.ons.api.ConsumeContext;
//import com.aliyun.openservices.ons.api.Message;
//import com.kikitrade.activity.mock.RewardDivideListenerMock;
//import com.kikitrade.activity.op.BusinessOP;
//import com.kikitrade.activity.service.config.SaasConfig;
//import com.kikitrade.activity.service.mq.RewardDivideListener;
//
//import com.kikitrade.activity.util.ReflectionUtils;
//import jakarta.annotation.Resource;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//
//@Component
//public class RewardDivideListenerMockImpl implements RewardDivideListenerMock {
//    @Resource
//    RewardDivideListener rewardDivideListener;
//
//    public RewardDivideListener rewardDivideListenerSpy;
//
//    @PostConstruct
//    public void init() {
//        rewardDivideListenerSpy = Mockito.spy(rewardDivideListener);
//    }
//
//
//    @Override
//    public void getInviters() {
//        Mockito.doAnswer(c -> {
//            SaasConfig saasConfig = c.getArgument(0);
//            String ownerId = c.getArgument(1);
//            String saasId = c.getArgument(2);
//
//            return BusinessOP.inviterList;
//
//        }).when(rewardDivideListenerSpy).getInviters(Mockito.any(SaasConfig.class), Mockito.anyString(), Mockito.anyString());
//    }
//}
