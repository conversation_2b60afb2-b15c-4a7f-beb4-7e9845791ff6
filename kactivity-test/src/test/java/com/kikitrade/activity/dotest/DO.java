package com.kikitrade.activity.dotest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.util.CheckUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DO {


    public static void main(String[] args) {
//        String s1 = "{\"value\":\"0.1\"}";
//        String s2 = "{\"value\":\"0.100000000\"}";
//
//        JSONObject object1 = JSON.parseObject(s1);
//
//        BigDecimal a = new BigDecimal("-0.1");
//        BigDecimal b = new BigDecimal("-0.10000");
//
//
//        Assert.assertEquals(a.compareTo(b), 0);

        //System.out.println(a);

        //canTransfer2BigDecimal(String)

        String s1 = "{\"customerId\":\"20220930145600\",\"detail\":\"[{\\\"amount\\\":\\\"2\\\",\\\"cost\\\":2.00000000,\\\"currency\\\":\\\"USD\\\",\\\"id\\\":\\\"279417468019666780\\\",\\\"releaseId\\\":\\\"252412077505532365\\\"}]\"}";
        String s2 = "{\"customerId\":\"20220930145600\",\"detail\":\"[{\\\"amount\\\":\\\"2\\\",\\\"cost\\\":2,\\\"currency\\\":\\\"USD\\\",\\\"id\\\":\\\"279417468019666781\\\",\\\"releaseId\\\":\\\"252412077505532365\\\"}]\"}";
//        String s1 = "{\"amount\":11,\"detail\":\"[1,3,4]\"}";
//        String s2 = "{\"amount\":11,\"detail\":\"[1,1010,4]\"}";
//
//
        CheckUtils.compare(s1, s2);

//        Date date = new Date();



        //String string = "\"{\\\"amount\\\":11,\\\"detail\\\":\\\"[{\\\\\\\"amount\\\\\\\":11}]\\\"}\"";

        //JSONObject jsonObject = JSON.parseObject(string);

//        String JSONString = "{\"amount\":11,\"detail\":\"[{\\\"cost\\\":1}]\"}";
        //String JSONString = "{\"amount\":11,\"detail\":[{\"cost\":1}]}";
//
//        System.out.println(JSONString);
//
//        String temp;

//        List<String> regs = Arrays.asList("\\\\", "\"\\{", "\\}\"", "\"\\[", "\\]\"");
//        List<String> replaces = Arrays.asList("", "{", "}", "[", "]");

        //List<String> regs = Arrays.asList("\\\\", "\"\\{", "\"\\[", "\\]\"");
//        List<String> regs = Arrays.asList("\\\\", "\"\\{","\"}", "\"\\[", "]\"");
//        List<String> replaces = Arrays.asList("", "{","}", "[", "]");


//        for (int i = 0; i < regs.size(); i++) {
//            boolean rePattern = true;
//            while (rePattern) {
//                temp = JSONString;
//                //System.out.println("temp: {}"+ temp);
//                Pattern pattern = Pattern.compile(regs.get(i));
//                Matcher matcher = pattern.matcher(JSONString);
//                JSONString = matcher.replaceAll(replaces.get(i));
//                //System.out.println(JSONString);
//                rePattern = !JSONString.equals(temp);
//            }
//        }







        /**
         *
         *
         *   "{\"amount\":11,\"detail\":\"[{\\\"amount\\\":11}]\"}"
         */








        //CheckUtils.compare(s1, s2, new String[]{"businessId"});
//
//        JSONObject jsonObject = JSON.parseObject(s1);
//
//        String detail = jsonObject.getString("detail");
//
//        System.out.println(detail);
//
//        JSONArray array = JSON.parseArray(detail);
//
//        JSONObject jsonObject1 = array.getJSONObject(0);
//        System.out.println(jsonObject1);

    }


}
