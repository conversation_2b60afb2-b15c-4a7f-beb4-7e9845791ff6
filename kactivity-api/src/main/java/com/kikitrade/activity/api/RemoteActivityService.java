package com.kikitrade.activity.api;

import com.kikitrade.activity.api.model.ActivityRequest;
import com.kikitrade.activity.api.model.ActivityResponse;
import com.kikitrade.activity.api.model.RewardsRequest;
import com.kikitrade.activity.api.model.activity.ActivityInfoVO;
import com.kikitrade.activity.api.model.activity.RewardVO;
import com.kikitrade.activity.api.model.request.InviteRewardsRequest;
import com.kikitrade.activity.api.model.request.InviteRewardsSumRequest;
import com.kikitrade.activity.api.model.request.InviteRewardsTotalRequest;
import com.kikitrade.activity.api.model.request.PrecisionRecordRequest;
import com.kikitrade.activity.api.model.response.InviteRewardsResponse;
import com.kikitrade.activity.api.model.response.InviteRewardsSumResponse;
import com.kikitrade.activity.api.model.response.InviteRewardsTotalResponse;
import com.kikitrade.activity.api.model.response.PrecisionRecordResponse;
import com.kikitrade.activity.model.ActivityType;
import com.kikitrade.activity.model.Result;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.model.PageResult;
import java.math.BigDecimal;

public interface RemoteActivityService {

    /**
     * findActiveByTypeAndLocale
     *
     * @param type
     * @param locale
     * @return
     */
    ActivityResponse getActivity(Integer type, String locale);

    /**
     * reward total count
     *
     * @param customerId
     * @param currency
     * @param type
     * @return
     */
    Long rewardTotalCount(String customerId, String currency, ActivityType type);

    /**
     * reward total amount
     *
     * @param customerId
     * @param currency
     * @param type
     * @return
     */
    BigDecimal rewardTotalAmount(String customerId, String currency, ActivityType type);

    /**
     * rewards
     *
     * rows refer to RewardDTO
     *
     * @param customerId
     * @param type
     * @param offset
     * @param limit
     * @return
     */
    PageResult rewards(String customerId, ActivityType type, int offset, int limit);

    /**
     * util
     *
     * @return
     */
    ActivityResponse reloadKolExtraRewardConfig();

    /**
     * 获取活动信息
     * @param activityRequest
     * @return
     */
    Result<ActivityInfoVO> getActivityInfo(ActivityRequest activityRequest);

    /**
     * 邀请活动奖励
     * @param customerId
     * @param offset
     * @param limit
     * @return
     */
    PageResult inviteRewards(String saasId, String customerId, int offset, int limit);

    /**
     * 邀请人获取的总奖励
     * @param customerId
     * @return
     */
    BigDecimal inviteRewardSum(String saasId, String customerId);

    /**
     * 汇总新手任务上线之后的邀请类活动的奖励
     * @param customerId
     * @return
     */
    BigDecimal inviteRewardSumAfterTime(String saasId, String customerId);

    /**
     * 奖励列表
     * @param rewardsRequest
     * @param offset
     * @param limit
     * @return
     */
    Page<RewardVO> rewards(RewardsRequest rewardsRequest, String saasId, int offset, int limit);

    /**
     *参加活动
     * @param username
     * @param businessCode
     * @param businessType
     * @return
     */
    Boolean joinActivity(String username, String businessCode, String businessType);

    /**
     * 查询邀请奖励总览
     * @param request
     * @return
     */
    InviteRewardsTotalResponse inviteRewardsTotal(InviteRewardsTotalRequest request);

    /**
     * 按奖励类型查询邀请奖励明细
     * @param request
     * @return
     */
    InviteRewardsResponse inviteRewards(InviteRewardsRequest request);

    /**
     * 按奖励类型查询奖励的总额
     * @param request
     * @return
     */
    InviteRewardsSumResponse inviteRewardsSumByType(InviteRewardsSumRequest request);

    /**
     * 保存打分记录信息
     * @param request
     * @return
     */
    PrecisionRecordResponse savePrecisionRecord(PrecisionRecordRequest request);
}
