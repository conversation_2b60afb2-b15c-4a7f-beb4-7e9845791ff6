package com.kikitrade.activity.dal.mysql.model;

import com.kikitrade.activity.dal.IdCustomer;
import lombok.Getter;
import lombok.Setter;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@Table(name = "activity_lottery_store_log")
public class ActivityLotteryStoreLog extends BaseModel {

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @KeySql(genId = IdCustomer.class)
    private String id;

    @Column(name = "time")
    private Long time;

    @Column(name = "status")
    private Integer status;
}
