package com.kikitrade.activity.service.engine;

import com.kikitrade.activity.service.engine.rule.base.BaseRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @create 2021/9/7 12:57 上午
 * @modify
 */
@Configuration
@Slf4j
public class EngineConfig {

    @Bean
    public GlobalEventRuleEngine globalEventRuleEngine(@Autowired List<BaseRule> ruleList) {
        GlobalEventRuleEngine globalEventRuleEngine = new GlobalEventRuleEngine();
        globalEventRuleEngine.setRuleList(ruleList);
        return globalEventRuleEngine;
    }
}
