package com.kikitrade.activity.service.rpc.osp;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.kikitrade.activity.service.common.config.OspProperties;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/12 14:34
 */
@Data
@Slf4j
public class OspProfileRequest {

    private OspProperties ospProperties;

    private String address;

    private String osChainId;

    private String appId;

    public OspProfileRequest() {
    }

    public OspProfileRequest(OspProperties ospProperties) {
        this.ospProperties = ospProperties;
    }

    public OspProfileRequest build(String address, String osChainId, String appId) {
        this.address = address;
        this.osChainId = osChainId;
        this.appId = appId;
        return this;
    }


    public String execute() {
        try {
            String profileId = getProfileByEoaAddress(this.address, this.osChainId, this.appId);
            if (StringUtils.isNotBlank(profileId)) {
                return profileId;
            }
            return null;
        } catch (Exception e) {
            log.error("[OspProfileRequest] execute exception, address:{}, error:", address, e);
            return null;
        }
    }

    private String getProfileByEoaAddress(String ospAddress, String osChainId, String appId) {
        try {
            HttpUrl.Builder urlBuilder = HttpUrl.parse(this.ospProperties.getDomain() + "/v2/profiles").newBuilder();
            urlBuilder.addQueryParameter("address", ospAddress);
            urlBuilder.addQueryParameter("limit", "1");
            Headers.Builder headerBuilder = new Headers.Builder()
                    .add("Content-Type", "application/json");
            headerBuilder.add("os-app-id", StringUtils.isNotBlank(appId)? appId : ospProperties.getAppId());
            if (StringUtils.isNotBlank(osChainId)) {
                headerBuilder.add("OS-Chain-Id", osChainId);
            }
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .headers(headerBuilder.build())
                    .build();
            log.info("[OspProfileRequest] getProfileByEoaAddress request:{}", request);
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                log.info("[OspProfileRequest] getProfileByEoaAddress response:{}", jsonObject);
                Optional<JSONObject> data = Optional.ofNullable(jsonObject.getJSONObject("data"));
                if (data.isPresent() && data.get().containsKey("rows")) {
                    JSONArray rows = data.get().getJSONArray("rows");
                    if (CollectionUtils.isNotEmpty(rows)) {
                        JSONObject object = rows.getJSONObject(0);
                        return Objects.nonNull(object) ? object.getString("profile_id") : null;
                    }
                }
            }
            log.info("[OspProfileRequest] getProfileByEoaAddress failed. eoa_address:{}, response:{}", ospAddress, response);
            return null;
        } catch (Exception e) {
            log.error("[OspProfileRequest] getProfileByEoaAddress Exception:{}, error:", ospAddress, e);
            return null;
        }
    }
}
