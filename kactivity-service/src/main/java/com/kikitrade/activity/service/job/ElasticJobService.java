package com.kikitrade.activity.service.job;

import com.google.common.base.Joiner;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.dal.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.shardingsphere.elasticjob.api.JobConfiguration;
import org.apache.shardingsphere.elasticjob.lite.api.bootstrap.impl.ScheduleJobBootstrap;
import org.apache.shardingsphere.elasticjob.lite.lifecycle.api.JobAPIFactory;
import org.apache.shardingsphere.elasticjob.reg.zookeeper.ZookeeperRegistryCenter;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ElasticJobService {

    @Resource
    private ZookeeperRegistryCenter zookeeperRegistryCenter;
    @Resource
    private RedisService redisService;
    @Resource
    private ActivityBatchBuilder activityBatchBuilder;
    @Resource
    @Lazy
    private ActivityRewardJob activityRewardJob;
    @Resource
    @Lazy
    private ActivityImportJob activityImportJob;

    @Value("${elasticjob.reg-center.namespace}")
    private String namespace;
    @Value("${elasticjob.reg-center.server-lists}")
    private String serverList;

    private static final String IMPORT_JOB_NAME = "import";
    private static final String REWARD_JOB_NAME = "reward";
    private static final String CRON_KEY = "cron_";
    private static final String SHARD_KEY = "shard_";

    private static final int DEFAULT_SHARD_TOTAL_COUNT = 1;

    /**
     * 创建定时任务
     *
     * @param simpleJob
     * @param jobName
     * @param cron
     * @param params
     * @return
     */
    public boolean createJob(SimpleJob simpleJob, String jobName, String cron, Integer shardCount, Map<String, String> params) {
        try {
            JobConfiguration.Builder builder = JobConfiguration.newBuilder(jobName, shardCount == null ? DEFAULT_SHARD_TOTAL_COUNT : shardCount);
            builder.cron(cron);
            builder.failover(true);
            builder.overwrite(true);
            builder.misfire(true);
            builder.monitorExecution(true);
            if (MapUtils.isNotEmpty(params)) {
                String param = Joiner.on(";").withKeyValueSeparator(":").join(params);
                builder.jobParameter(param);
            }
            JobConfiguration build = builder.build();
            ScheduleJobBootstrap scheduleJobBootstrap = new ScheduleJobBootstrap(zookeeperRegistryCenter, simpleJob, build);
            scheduleJobBootstrap.schedule();
            params.put(CRON_KEY, cron);
            params.put(SHARD_KEY, String.valueOf(shardCount == null ? DEFAULT_SHARD_TOTAL_COUNT : shardCount));
            redisService.hSetAll(jobName, params);
            return true;
        } catch (Exception ex) {
            log.error("ElasticJobService createJob exception jobName:{}", jobName, ex);
            return false;
        }
    }

    /**
     * 删除定时任务
     *
     * @param jobName
     * @return
     */
    public boolean removeJob(String jobName) {
        try {
            JobAPIFactory.createJobOperateAPI(serverList, namespace, null).remove(jobName, null);
            redisService.del(jobName);
            return true;
        } catch (Exception ex) {
            log.error("ElasticJobService removeJob exception jobName:{}", jobName, ex);
            return false;
        }
    }

    /**
     * 触发定时任务
     * @param jobName
     * @return
     */
    public boolean tiggerJob(String jobName){
        try{
            JobAPIFactory.createJobOperateAPI(serverList, namespace, null).trigger(jobName);
            return true;
        }catch (Exception ex){
            log.error("ElasticJobService tiggerJob exception jobName:{}", jobName, ex);
            return false;
        }
    }

    public String getJobNameForImport(String batchId) {
        return String.format("%s_%s", batchId, IMPORT_JOB_NAME);
    }

    public String getJobNameForReward(String batchId) {
        return String.format("%s_%s", batchId, REWARD_JOB_NAME);
    }

    @PostConstruct
    public void initJob() {
        initImporting();
        initImporFail();
        initAwarding();
        initAwardFailed();
    }

    public void initImporting() {
        List<ActivityBatch> activityBatches = activityBatchBuilder.queryListByStatus(ActivityConstant.BatchRewardStatusEnum.IMPORTING.name());
        for (ActivityBatch activityBatch : activityBatches) {
            Map map = redisService.hGetAllMap(getJobNameForImport(activityBatch.getBatchId()));
            if (MapUtils.isNotEmpty(map)) {
                String shardCount = String.valueOf(map.get(SHARD_KEY) == null ? 1 : map.get(SHARD_KEY));
                createJob(activityImportJob, getJobNameForImport(activityBatch.getBatchId()),
                        String.valueOf(map.get(CRON_KEY)), Integer.parseInt(shardCount), map);
            }
        }
    }

    public void initImporFail() {
        List<ActivityBatch> activityBatches = activityBatchBuilder.queryListByStatus(ActivityConstant.BatchRewardStatusEnum.IMPORT_FAILED.name());
        for (ActivityBatch activityBatch : activityBatches) {
            Map map = redisService.hGetAllMap(getJobNameForImport(activityBatch.getBatchId()));
            if (MapUtils.isNotEmpty(map)) {
                String shardCount = String.valueOf(map.get(SHARD_KEY) == null ? 1 : map.get(SHARD_KEY));
                createJob(activityImportJob, getJobNameForImport(activityBatch.getBatchId()),
                        String.valueOf(map.get(CRON_KEY)), Integer.parseInt(shardCount), map);
            }
        }
    }

    public void initAwarding() {
        List<ActivityBatch> activityBatches = activityBatchBuilder.queryListByStatus(ActivityConstant.BatchRewardStatusEnum.AWARDING.name());
        for (ActivityBatch activityBatch : activityBatches) {
            Map map = redisService.hGetAllMap(getJobNameForReward(activityBatch.getBatchId()));
            if (MapUtils.isNotEmpty(map)) {
                String shardCount = String.valueOf(map.get(SHARD_KEY) == null ? 1 : map.get(SHARD_KEY));
                createJob(activityRewardJob, getJobNameForReward(activityBatch.getBatchId()),
                        String.valueOf(map.get(CRON_KEY)), Integer.parseInt(shardCount), map);
            }
        }
    }

    public void initAwardFailed() {
        List<ActivityBatch> activityBatches = activityBatchBuilder.queryListByStatus(ActivityConstant.BatchRewardStatusEnum.AWARD_FAILED.name());
        for (ActivityBatch activityBatch : activityBatches) {
            Map map = redisService.hGetAllMap(getJobNameForReward(activityBatch.getBatchId()));
            if (MapUtils.isNotEmpty(map)) {
                String shardCount = String.valueOf(map.get(SHARD_KEY) == null ? 1 : map.get(SHARD_KEY));
                createJob(activityRewardJob, getJobNameForReward(activityBatch.getBatchId()),
                        String.valueOf(map.get(CRON_KEY)), Integer.parseInt(shardCount), map);
            }
        }
    }
}
