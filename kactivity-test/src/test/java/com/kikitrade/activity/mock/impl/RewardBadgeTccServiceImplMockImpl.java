package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RewardBadgeTccServiceImplMock;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.service.reward.impl.RewardBadgeTccServiceImpl;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import jakarta.annotation.Resource;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

@Component
public class RewardBadgeTccServiceImplMockImpl implements RewardBadgeTccServiceImplMock {
    @Resource
    RewardBadgeTccServiceImpl rewardBadgeTccServiceImpl;

    @Override
    public void tryReward() throws Exception {
        Mockito.doAnswer(c ->{
            RewardRequest request = c.getArgument(0);
            BusinessOP.rewardRequestList.add(request);
            return null;
        }).when(rewardBadgeTccServiceImpl).tryReward(Mockito.any(RewardRequest.class));
    }
}
