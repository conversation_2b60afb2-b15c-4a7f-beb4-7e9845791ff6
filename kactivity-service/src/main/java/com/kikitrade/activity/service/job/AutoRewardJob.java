package com.kikitrade.activity.service.job;

import com.alibaba.fastjson.JSON;
import com.aliyun.odps.Instance;
import com.aliyun.odps.data.Record;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.business.NoticeService;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.flow.job.BatchConstantName;
import com.kikitrade.activity.service.flow.job.BatchJobFactory;
import com.kikitrade.activity.service.meta.ActivityBatchInfoService;
import com.kikitrade.framework.elasticjob.KiKiSimpleJob;
import com.kikitrade.frameworks.odps.OdpsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-08 10:41
 */
@Component
@Slf4j
public class AutoRewardJob extends KiKiSimpleJob {

    @Resource
    @Lazy
    private ActivityBatchBuilder activityBatchBuilder;
    @Resource
    @Lazy
    private OdpsTemplate odpsTemplate;
    @Resource
    @Lazy
    private BatchJobFactory batchJobFactory;
    @Resource
    @Lazy
    private ActivityEntityService activityEntityService;
    @Resource
    @Lazy
    private NoticeService noticeService;
    @Resource
    @Lazy
    private KactivityModuleProperties kactivityModuleProperties;

    @Override
    protected void doExecute(ShardingContext shardingContext) throws Exception {
        //拉取odps执行中的批次
        List<ActivityBatch> activityBatches = activityBatchBuilder.queryListByStatus(ActivityConstant.BatchRewardStatusEnum.ODPS_RUNNING.name());
        for(ActivityBatch activityBatch : activityBatches){
            String instanceId = activityBatch.getInstance();
            Instance instance = odpsTemplate.getInstance(instanceId);
            log.info("instance:{},{}", JSON.toJSONString(instance), activityBatch);
            if(instance == null || Instance.Status.TERMINATED != instance.getStatus()){
                continue;
            }
            for(Map.Entry<String, Instance.TaskStatus> entry : instance.getTaskStatus().entrySet()){
                if(Instance.TaskStatus.Status.FAILED == entry.getValue().getStatus()){
                    activityBatch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.ODPS_FAILED.name());
                    activityBatchBuilder.update(activityBatch);
                    break;
                }
                if(Instance.TaskStatus.Status.SUCCESS != entry.getValue().getStatus()){
                    break;
                }
            }
            activityBatch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.ODPS_COMPLETE.name());
            activityBatchBuilder.update(activityBatch);
            ActivityEntity entity = activityEntityService.findById(activityBatch.getActivityId());
            log.info("ActivityEntity:{}", JSON.toJSONString(entity));
            if(entity != null && entity.getAutoApprove()){
                batchJobFactory.runAutoMaticRewardJob(activityBatch.getActivityId(), activityBatch.getBatchId());
            }else{
                batchJobFactory.runSemiAutoMaticRewardJob(activityBatch.getActivityId(), activityBatch.getBatchId());
            }
        }
    }
}
