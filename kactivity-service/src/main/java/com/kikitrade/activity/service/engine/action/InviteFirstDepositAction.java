package com.kikitrade.activity.service.engine.action;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.tablestore.model.CustomerReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.ActivityMessage;
import com.kikitrade.activity.service.common.LogAlarmConstant;
import com.kikitrade.activity.service.common.LogAlarmUtil;
import com.kikitrade.activity.service.engine.action.base.AbstractBaseAction;
import com.kikitrade.activity.service.reward.ActivityRewardService;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.model.CustomerInviteDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerInviteService;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.kcustomer.common.constants.CustomerReferralConstants;
import com.kikitrade.kfinancing.api.service.RemoteFinancingService;
import com.kikitrade.trade.api.RemoteTradingService;
import com.kikitrade.trade.api.model.response.OrderIdResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Setter
@Getter
@Component
public class InviteFirstDepositAction extends AbstractBaseAction {

    @Resource
    private LogAlarmUtil logAlarmUtil;

    @DubboReference
    private RemoteCustomerInviteService remoteCustomerInviteService;

    @DubboReference
    private RemoteCustomerService remoteCustomerService;

    @DubboReference
    private RemoteFinancingService remoteFinancingService;

    @Resource
    private ActivityRewardService activityRewardService;

    @DubboReference
    private RemoteTradingService remoteTradingService;

    @Override
    public boolean doAction(ActivityMessage msg, Activity activity) throws Exception {
        ActivityMessage.RewardResult rewardResult = msg.fetchRewardResult(activity.getId());
        // 校验之前计算的奖励结果
        if (rewardResult != null && !CollectionUtils.isEmpty(rewardResult.getRewardEntities())) {
            try {
                List<CustomerReward> rewardList = new ArrayList<>();

                //获取邀请者信息
                CustomerInviteDTO inviteInfo = getInviterInfo(msg.getSaasId(), msg.getCustomerId());

                CustomerDTO invitor = remoteCustomerService.getById(msg.getSaasId(), inviteInfo.getInviterId(), true);
                CustomerDTO invitee = remoteCustomerService.getById(msg.getSaasId(), inviteInfo.getInviteeId(), true);


                //被邀请人奖励信息填写
                rewardResult.getRewardByCustomerId(inviteInfo.getInviteeId()).forEach(rewardEntity -> {
                    if (!StringUtils.isAnyEmpty(rewardEntity.getRewardCurrency(), rewardEntity.getExchangeCurrency()) && rewardEntity.getRewardMoney() != null && rewardEntity.getRewardMoney().compareTo(BigDecimal.ZERO) > 0) {
                        CustomerReward inviteeReward = toCustomerReward(msg, rewardEntity.getRewardCurrency(), rewardEntity.getRewardMoney(), activity.getType());
                        inviteeReward.setInvitee(inviteInfo.getInviteeId());//受邀者奖励设置受邀者为邀请记录的受邀者id
                        inviteeReward.setInviteeUserName(invitee.getUserName());//受邀者奖励记录的受邀者用户名设置为邀请记录的username
                        inviteeReward.setCustomerId(CustomerReferralConstants.EMPTY_ID);
                        inviteeReward.setUserName("");
                        inviteeReward.setOrderNum(getOrderId(inviteeReward.getInvitee()));
                        // 额外奖励不转入理财账户
                        if (rewardEntity.isExtraReward()) {
                            inviteeReward.setSecondStep(ActivityConstant.SecondStep.SKIP.name());
                        }
                        log.info("fiatDepositReward reward ...invitee:{}", JSON.toJSONString(inviteeReward));
                        rewardList.add(inviteeReward);
                    }
                });

                //邀请人奖励记录填写
                if (!inviteInfo.getInviterId().equals(CustomerReferralConstants.EMPTY_ID)) {
                    rewardResult.getRewardByCustomerId(inviteInfo.getInviterId()).forEach(rewardEntity -> {
                        if (StringUtils.isNotBlank(rewardEntity.getRewardCurrency()) && rewardEntity.getRewardMoney().compareTo(BigDecimal.ZERO) > 0) {
                            CustomerReward inviterReward = toCustomerReward(msg, rewardEntity.getRewardCurrency(), rewardEntity.getRewardMoney(), activity.getType());
                            inviterReward.setCustomerId(invitor.getId());//邀请者奖励记录customerid设置为邀请记录的推介人
                            inviterReward.setUserName(invitor.getUserName());//邀请者奖励记录的用户名设置邀请记录的推介人的用户名
                            inviterReward.setInvitee(invitee.getId());//邀请者奖励记录的受邀者设置为邀请记录的customerId
                            inviterReward.setInviteeUserName(invitee.getUserName());//邀请者奖励记录的受邀者用户名设置为邀请记录的用户名
                            inviterReward.setOrderNum(getOrderId(inviteInfo.getInviterId()));
                            // 额外奖励不转入理财账户
                            if (rewardEntity.isExtraReward()) {
                                inviterReward.setSecondStep(ActivityConstant.SecondStep.SKIP.name());
                            }
                            log.info("fiatDepositReward reward ...inviter:{}", JSON.toJSONString(inviterReward));
                            rewardList.add(inviterReward);
                        } else {
                            log.error("InviteFirstDepositAction doAction, invalid inviter rewardResult, skip. rewardResult:{}", JSONObject.toJSONString(rewardResult));
                        }
                    });
                }

                log.info("fiatDepositReward reward ...rewardList:{}", JSON.toJSONString(rewardList));
                return inviteFirstFiatReward(rewardList);
            } catch (Exception e) {
                logAlarmUtil.alarm(LogAlarmConstant.CheckPoint.ACTIVITY_FIAT_REWARD_ERROR, "activityMessage doAction process fail.", e);
            }
        } else {
            log.error("InviteFirstDepositAction doAction, invalid rewardResult, skip. rewardResult:{}", JSONObject.toJSONString(rewardResult));
        }
        return false;
    }

    /**
     * 奖励邀请者和受邀者
     *
     * @param rewardList
     * @return
     */
    private boolean inviteFirstFiatReward(List<CustomerReward> rewardList) {
        return activityRewardService.fiatReward(rewardList).isSuccess();
    }

    /**
     * 查询邀请者
     *
     * @param customerId 被邀请者id
     * @return
     */
    private CustomerInviteDTO getInviterInfo(String saasId, String customerId) {
        return remoteCustomerInviteService.inviterByCustomerId(saasId, customerId);
    }

    private String getOrderId(String customerId) {
        OrderIdResponse orderIdResponse = remoteTradingService.acquireOrderId(customerId);
        if (orderIdResponse == null || !orderIdResponse.getCode().isSuccess() || StringUtils.isBlank(orderIdResponse.getOrderId())) {
            logAlarmUtil.alarm(LogAlarmConstant.CheckPoint.ACTIVITY_DO_ACTION_ERROR, String.format("InviteFirstDepositAction getOrderId fail. orderIdResponse=%s", JSON.toJSONString(orderIdResponse)));
            return null;
        }
        return orderIdResponse.getOrderId();
    }
}
