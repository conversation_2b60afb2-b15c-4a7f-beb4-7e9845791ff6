package com.kikitrade.activity.service.reward;

import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import com.kikitrade.activity.service.reward.model.CustomerMiscDTO;
import com.kikitrade.kcustomer.api.model.block.SecurityVerifyResponse;

import java.util.List;

public interface CustomerService {

    /**
     * 查询用户信息
     * @param customId
     * @return
     */
    CustomerCacheDTO getById(String customId);


    /**
     * 查询用户信息
     * @param customIds
     * @return
     */
    List<CustomerCacheDTO> getByIds(List<String> customIds);

    /**
     * 查询用户邀请信息
     * @param customerId
     * @return
     */
    String getInviteInfo(String customerId);

    /**
     * 查询用户信息
     * @param phone
     * @return
     */
    CustomerCacheDTO getByPhoneOrEmail(String phone);

    /**
     * 查询社区信息
     * @param customerId
     * @return
     */
    CustomerMiscDTO getMiscByCustomerId(String customerId);

    /**
     * 获取kol身份
     * @param customerId
     * @return
     */
    String getKolType(String customerId);

    /**
     * 获取vip等级
     * @param customerId
     * @return
     */
    String getVipLevel(String customerId);

    /**
     * 检查决策结果
     * @param customerId
     * @return
     */
    SecurityVerifyResponse verify(String customerId);
}
