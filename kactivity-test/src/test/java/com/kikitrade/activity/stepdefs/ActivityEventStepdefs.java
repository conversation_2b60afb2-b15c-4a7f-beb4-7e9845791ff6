//package com.kikitrade.activity.stepdefs;
//
//import com.alibaba.fastjson.JSON;
//import com.aliyun.openservices.ons.api.ConsumeContext;
//import com.aliyun.openservices.ons.api.Message;
//import com.kikitrade.activity.dal.mysql.dao.ActivityTaskConfigTestDao;
//import com.kikitrade.activity.dal.mysql.model.ActivityTaskConfig;
//import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
//import com.kikitrade.activity.luck.api.model.request.LuckFortuneReleaseDTO;
//import com.kikitrade.activity.mock.RemoteAssetOperateServiceDubboMock;
//import com.kikitrade.activity.op.BusinessOP;
//import com.kikitrade.activity.service.mq.ActivityEventListener;
//import com.kikitrade.activity.service.mq.ActivityEventMessage;
//import com.kikitrade.activity.util.CheckUtils;
//import com.kikitrade.asset.model.exception.AssetException;
//import com.kikitrade.asset.model.request.AssetTransferInRequest;
//import io.cucumber.java.zh_cn.假如;
//import io.cucumber.java.zh_cn.并且;
//import io.cucumber.java.zh_cn.当;
//import io.cucumber.java.zh_cn.那么;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeanUtils;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//import jakarta.annotation.Resource;
//import java.nio.charset.StandardCharsets;
//import java.util.Date;
//import java.util.List;
//import java.util.concurrent.CountDownLatch;
//
//@Slf4j
//public class ActivityEventStepdefs {
//    @Resource
//    ActivityEventListener activityEventListener;
//    @Resource
//    ActivityTaskConfigTestDao activityTaskConfigTestDao;
//    @Resource
//    RemoteAssetOperateServiceDubboMock remoteAssetOperateServiceDubboMock;
//
//    @当("activity-event-系统接收到活动事件消息{}")
//    public void activityEvent系统接收到活动事件消息ActivityEventMessage(String eventMessage) {
//        ActivityEventMessage activityEventMessage = JSON.parseObject(eventMessage, ActivityEventMessage.class);
//        activityEventMessage.setEventTime(System.currentTimeMillis());
//        Message message = new Message();
//        String body = JSON.toJSONString(activityEventMessage);
//        message.setBody(body.getBytes(StandardCharsets.UTF_8));
//        activityEventListener.consume(message, new ConsumeContext());
//
//
//        //{"eventCode":"","customerId":"","globalUid":"","eventTime":"","targetId":"","verb":""}
//    }
//
//    @假如("activity-event-配置用户vip等级{}")
//    public void activityEvent配置用户vip等级VipLevel(String vipLevel) {
//        BusinessOP.vip = vipLevel;
//    }
//
//    @那么("activity-event-校验activityCustomRewardMap{}")
//    public void activityEvent校验activityCustomRewardMapActivityCustomRewardMap(String activityCustomRewardMap) {
//        log.info("BusinessOP.activityCustomRewardMap:{}", JSON.toJSONString(BusinessOP.activityCustomRewardMap));
//
//    }
//
//    @那么("activity-event-校验assetTransferInRequestList{}")
//    public void activityEvent校验assetTransferInRequestListAssetTransferInRequestList(String assetTransferInRequestList) {
//        System.out.println("BusinessOP.assetTransferInRequestList:{}"+ JSON.toJSONString(BusinessOP.assetTransferInRequestList));
//        //CheckUtils.compare(assetTransferInRequestList, JSON.toJSONString(BusinessOP.assetTransferInRequestList), new String[]{"businessId", "desc"});
//
//
//        List<AssetTransferInRequest> respRequests = JSON.parseArray(assetTransferInRequestList, AssetTransferInRequest.class);
//
//        assertEquals(respRequests.size(), BusinessOP.assetTransferInRequestList.size());
//
//        for (int i = 0; i < respRequests.size(); i++) {
//            AssetTransferInRequest resp = respRequests.get(i);
//            AssetTransferInRequest actual = BusinessOP.assetTransferInRequestList.get(i);
//
//            assertEquals(resp.getAmount(), actual.getAmount());
//            assertEquals(resp.getBusinessType(), actual.getBusinessType());
//            assertEquals(resp.getCategory(), actual.getCategory());
//            assertEquals(resp.getType(), actual.getType());
//
//        }
//
//
//    }
//
//
//    @那么("activity-event-校验activityTaskItemList{}")
//    public void activityEvent校验activityTaskItemListActivityTaskItemList(String activityTaskItemList) {
//        log.info("BusinessOP.activityTaskItemList:{}", JSON.toJSONString(BusinessOP.activityTaskItemList));
//        CheckUtils.compare(activityTaskItemList, JSON.toJSONString(BusinessOP.activityTaskItemList), new String[]{"cycle", "expiredTime", "completeTime","created", "modified","businessId"}, null);
//
//    }
//
//    @那么("activity-event-校验activityTaskDetailList{}")
//    public void activityEvent校验activityTaskDetailListActivityTaskDetailList(String activityTaskDetailList) {
//        log.info("BusinessOP.activityTaskDetailList:{}", JSON.toJSONString(BusinessOP.activityTaskDetailList));
//        CheckUtils.compare(activityTaskDetailList, JSON.toJSONString(BusinessOP.activityTaskDetailList), new String[]{"completeTime", "taskId"}, null);
//
//    }
//
//    @那么("activity-event-校验activityCustomRewardList{}")
//    public void activityEvent校验activityCustomRewardListActivityCustomRewardList(String activityCustomRewardList) {
//        log.info("activityCustomRewards actual:{}",JSON.toJSONString(BusinessOP.activityCustomRewardList));
//        //CheckUtils.compare(activityCustomRewardList, JSON.toJSONString(BusinessOP.activityCustomRewardList), new String[]{"batchId", "seq", "businessId","extendParam","extendParamMap"}, null);
//
//        List<ActivityCustomReward> activityCustomRewards = JSON.parseArray(activityCustomRewardList, ActivityCustomReward.class);
//        log.info("activityCustomRewards expect:{}",JSON.toJSONString(activityCustomRewards));
//
//        assertEquals(activityCustomRewards.size(), BusinessOP.activityCustomRewardList.size());
//
//        for (int i = 0; i < activityCustomRewards.size(); i++) {
//            ActivityCustomReward respReward = activityCustomRewards.get(i);
//            ActivityCustomReward actualReward = BusinessOP.activityCustomRewardList.get(i);
//
//            assertEquals(respReward.getAmount(), actualReward.getAmount());
//            assertEquals(respReward.getBusinessType(), actualReward.getBusinessType());
//            assertEquals(respReward.getCustomerId(), actualReward.getCustomerId());
//            assertEquals(respReward.getRewardType(), actualReward.getRewardType());
//            assertEquals(respReward.getStatus(), actualReward.getStatus());
//            assertEquals(respReward.getUserType(), actualReward.getUserType());
//        }
//
//
//
//
//
//    }
//
//    @假如("activity-event-配置默认activityTaskConfig")
//    public void activityEvent配置默认activityTaskConfig() {
//        activityTaskConfigTestDao.init();
//        ActivityTaskConfig activityTaskConfig = new ActivityTaskConfig();
//        activityTaskConfig.setId("2022082405582497101000");
//        Date date = new Date();
//        date.setTime(System.currentTimeMillis() + 1000 * 60 * 60 * 24);
//        activityTaskConfig.setEndTime(date);
//        activityTaskConfigTestDao.update(activityTaskConfig);
//    }
//
//    @假如("activity-event-配置activityTaskConfig{}")
//    public void activityEvent配置activityTaskConfigConfig(String config) {
//        ActivityTaskConfig activityTaskConfig = JSON.parseObject(config, ActivityTaskConfig.class);
//        activityTaskConfig.setId("2022082405582497101000");
//        activityTaskConfigTestDao.update(activityTaskConfig);
//
//    }
//
//    @当("activity-event-并发-系统接收到活动事件消息{}")
//    public void activityEvent并发系统接收到活动事件消息EventMessage(String eventMessage) {
//        ActivityEventMessage activityEventMessage = JSON.parseObject(eventMessage, ActivityEventMessage.class);
//        activityEventMessage.setEventTime(System.currentTimeMillis());
//        Message message = new Message();
//        String body = JSON.toJSONString(activityEventMessage);
//        message.setBody(body.getBytes(StandardCharsets.UTF_8));
//
//
//        int len = 2;
//        final CountDownLatch endLatch = new CountDownLatch(len);
//        final CountDownLatch downLatch = new CountDownLatch(1);
//
//        for (int i = 0; i < len; i++) {
//            new Thread(new Runnable() {
//                @SneakyThrows
//                @Override
//                public void run() {
//                    try {
//                        downLatch.await();
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                    activityEventListener.consume(message, new ConsumeContext());
//                    endLatch.countDown();
//                }
//            }).start();
//        }
//
//        downLatch.countDown();
//        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
//        try {
//            endLatch.await();
//            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @当("activity-event-并发-系统接收到不同活动事件消息{}")
//    public void activityEvent并发系统接收到不同活动事件消息EventMessage(String eventMessage) {
//        ActivityEventMessage activityEventMessage = JSON.parseObject(eventMessage, ActivityEventMessage.class);
//        activityEventMessage.setEventTime(System.currentTimeMillis());
//        Message message = new Message();
//        String body = JSON.toJSONString(activityEventMessage);
//        message.setBody(body.getBytes(StandardCharsets.UTF_8));
//
//        ActivityEventMessage activityEventMessage2 = new ActivityEventMessage();
//        BeanUtils.copyProperties(activityEventMessage, activityEventMessage2);
//        activityEventMessage2.setTargetId("test_target_id_2");
//        activityEventMessage2.setGlobalUid("test_global_uid_2");
//        Message message2 = new Message();
//        String body2 = JSON.toJSONString(activityEventMessage2);
//        message2.setBody(body2.getBytes(StandardCharsets.UTF_8));
//
//        ActivityEventMessage activityEventMessage3 = new ActivityEventMessage();
//        BeanUtils.copyProperties(activityEventMessage, activityEventMessage3);
//        activityEventMessage3.setTargetId("test_target_id_3");
//        activityEventMessage3.setGlobalUid("test_global_uid_3");
//        Message message3 = new Message();
//        String body3 = JSON.toJSONString(activityEventMessage3);
//        message3.setBody(body3.getBytes(StandardCharsets.UTF_8));
//
//        int len = 3;
//        final CountDownLatch endLatch = new CountDownLatch(len);
//        final CountDownLatch downLatch = new CountDownLatch(1);
//
//
//        new Thread(new Runnable() {
//            @SneakyThrows
//            @Override
//            public void run() {
//                try {
//                    downLatch.await();
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//                activityEventListener.consume(message, new ConsumeContext());
//                endLatch.countDown();
//            }
//        }).start();
//
//        new Thread(new Runnable() {
//            @SneakyThrows
//            @Override
//            public void run() {
//                try {
//                    downLatch.await();
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//                activityEventListener.consume(message2, new ConsumeContext());
//                endLatch.countDown();
//            }
//        }).start();
//
//        new Thread(new Runnable() {
//            @SneakyThrows
//            @Override
//            public void run() {
//                try {
//                    downLatch.await();
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//                activityEventListener.consume(message3, new ConsumeContext());
//                endLatch.countDown();
//            }
//        }).start();
//
//
//        downLatch.countDown();
//        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
//        try {
//            endLatch.await();
//            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @当("activity-event-并发-系统接收到相同活动事件消息{}")
//    public void activityEvent并发系统接收到相同活动事件消息EventMessage(String eventMessage) {
//        ActivityEventMessage activityEventMessage = JSON.parseObject(eventMessage, ActivityEventMessage.class);
//        activityEventMessage.setEventTime(System.currentTimeMillis());
//        Message message = new Message();
//        String body = JSON.toJSONString(activityEventMessage);
//        message.setBody(body.getBytes(StandardCharsets.UTF_8));
//
//
//        int len = 3;
//        final CountDownLatch endLatch = new CountDownLatch(len);
//        final CountDownLatch latch = new CountDownLatch(len);
//        final CountDownLatch downLatch = new CountDownLatch(1);
//
//        for (int i = 0; i < len; i++) {
//            new Thread(new Runnable() {
//                @SneakyThrows
//                @Override
//                public void run() {
//                    try {
//                        latch.countDown();
//                        downLatch.await();
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                    activityEventListener.consume(message, new ConsumeContext());
//                    endLatch.countDown();
//                }
//            }).start();
//        }
//
//        try {
//            latch.await();
//            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        downLatch.countDown();
//        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
//        try {
//            endLatch.await();
//            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @假如("activity-event-配置remoteAssetOperateService.transferIn异常")
//    public void activityEvent配置remoteAssetOperateServiceTransferIn异常() throws AssetException {
//        remoteAssetOperateServiceDubboMock.transferInError();
//    }
//
//    @假如("activity-event-配置remoteAssetOperateService.transferIn正常")
//    public void activityEvent配置remoteAssetOperateServiceTransferIn正常() throws AssetException {
//        remoteAssetOperateServiceDubboMock.transferIn();
//    }
//
//    @并且("activity-event-配置清除redis缓存")
//    public void activityEvent配置清除redis缓存() {
//
//    }
//}
