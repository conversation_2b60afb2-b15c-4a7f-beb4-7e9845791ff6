package com.kikitrade.activity.service.meta;

import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.dal.mysql.model.ActivityActionMap;

import java.util.List;

public interface ActivityActionMapService {

    JsonResult save(ActivityActionMap activityActionMap);

    JsonResult delete(Integer activityId);

    JsonResult batchInsert(List<ActivityActionMap> activityActionMapList);

    JsonResult update(ActivityActionMap activityActionMap);

    JsonResult update(List<ActivityActionMap> activityActionMapList);

    List<ActivityActionMap> findByActivityId(Integer activityId);

    JsonResult findAll(Integer offset, Integer limit);

}
