package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.condition.ColumnCondition;
import com.alicloud.openservices.tablestore.model.condition.SingleColumnValueCondition;
import com.alicloud.openservices.tablestore.model.filter.SingleColumnValueFilter;
import com.kikitrade.activity.dal.tablestore.builder.ActivityUserTotalDataBuilder;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.RowChange;
import com.kikitrade.framework.ots.UpdateRowChange;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.dal.tablestore.model.ActivityUserTotalData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/01/19 17:55
 **/
@Component
@Slf4j
public class ActivityUserTotalDataBuilderImpl extends WideColumnStoreBuilder<ActivityUserTotalData> implements ActivityUserTotalDataBuilder {

    @PostConstruct
    public void init() {
        init(ActivityUserTotalData.class);
    }

    @Override
    public RangeResult<ActivityUserTotalData> list(Date batchNo, Integer activityId, String currency, ActivityConstant.RecordStatus status, PrimaryKey nextToken, int limit) {

        List<RangeQueryParameter> ps = Arrays.asList(
                new RangeQueryParameter("batch_no", PrimaryKeyValue.fromLong(batchNo.getTime()), PrimaryKeyValue.fromLong(batchNo.getTime())),
                new RangeQueryParameter("activity_id", PrimaryKeyValue.fromLong(activityId), PrimaryKeyValue.fromLong(activityId)),
                new RangeQueryParameter("currency", PrimaryKeyValue.fromString(currency), PrimaryKeyValue.fromString(currency)),
                new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));

        SingleColumnValueFilter filter = new SingleColumnValueFilter("status",
                SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromLong(status.getCode()));

        return super.rangeQueryWithNextToken(mapping.getTableName(), ps, filter, Direction.FORWARD, limit, nextToken);
    }


    public boolean batchUpdate(List<ActivityUserTotalData> records) {
        List<RowChange<ActivityUserTotalData>> batchWriteRowRequest = new ArrayList<>();
        // 构造rowUpdateChange
        records.stream().forEach(
                record -> {
                    ActivityUserTotalData data = new ActivityUserTotalData();
                    data.setBatchNo(record.getBatchNo());
                    data.setActivityId(record.getActivityId());
                    data.setCurrency(record.getCurrency());
                    data.setCustomerId(record.getCustomerId());
                    data.setModified(record.getModified());

                    if (record.getStatus().intValue() != record.getOldStatus().intValue()) {
                        data.setStatus(record.getStatus());
                    }

                    if (StringUtils.isNotBlank(record.getRemark())) {
                        data.setRemark(record.getRemark());
                    }
                    Condition condition = new Condition(RowExistenceExpectation.EXPECT_EXIST);
                    ColumnCondition statusCondition = new SingleColumnValueCondition("status", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(record.getOldStatus()));
                    condition.setColumnCondition(statusCondition);

                    RowChange<ActivityUserTotalData> rowChange = new UpdateRowChange<>(data, null, null, condition);

                    batchWriteRowRequest.add(rowChange);
                }
        );
        return super.batchRowChange(batchWriteRowRequest);
    }


}
