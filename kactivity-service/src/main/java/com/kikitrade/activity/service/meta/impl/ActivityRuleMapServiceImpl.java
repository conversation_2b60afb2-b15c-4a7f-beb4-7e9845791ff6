package com.kikitrade.activity.service.meta.impl;

import com.kikitrade.activity.dal.mysql.dao.ActivityRuleMapDao;
import com.kikitrade.activity.dal.mysql.model.ActivityRuleMap;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.service.meta.ActivityRuleMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityRuleMapServiceImpl implements ActivityRuleMapService {

    @Resource
    private ActivityRuleMapDao activityRuleMapDao;


    @Override
    public JsonResult save(ActivityRuleMap ActivityRuleMap) {
        JsonResult result = new JsonResult();
        //  do some convert;
        try {
            int count = activityRuleMapDao.insert(ActivityRuleMap);
            if (count > 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityrulemapserviceimpl save process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }

    @Override
    public JsonResult delete(Integer activityId) {
        JsonResult result = new JsonResult();
        try {
            int count = activityRuleMapDao.deleteById(activityId);
            if (count >= 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());

            }
        } catch (Exception e) {
            log.error("activityrulemapserviceimpl delete process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }

    @Override
    public JsonResult batchInsert(List<ActivityRuleMap> ActivityRuleMapList) {
        JsonResult result = new JsonResult();
        try {
            //TODO batchInsert
            //int count = ActivityRuleMapDAO.batchInsert(ActivityRuleMapList);
            int count = 0;
            for (ActivityRuleMap activityRuleMap : ActivityRuleMapList) {
                count += activityRuleMapDao.insert(activityRuleMap);
            }
            if (count > 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());

            }
        } catch (Exception e) {
            log.error("activityrulemapserviceimpl batchInsert process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }

        return result;
    }


    @Override
    public JsonResult update(ActivityRuleMap ActivityRuleMap) {
        JsonResult result = new JsonResult();

        try {
            //  delete first
            activityRuleMapDao.deleteById(ActivityRuleMap.getActivity_id());
            // then insert
            int count = activityRuleMapDao.insert(ActivityRuleMap);
            if (count > 0) {
                result.setSuccess(true).setObj(ActivityRuleMap).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityrulemapserviceimpl update process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }


    @Override
    public JsonResult findAll(Integer offset, Integer limit) {

        JsonResult result = new JsonResult();
        try {
            List<ActivityRuleMap> activityRuleMapList = activityRuleMapDao.findAll(offset, limit);
            if (activityRuleMapList == null || activityRuleMapList.size() == 0) {
                result.setObj(activityRuleMapList).setSuccess(true).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                result.setObj(activityRuleMapList).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityrulemapserviceimpl findAll process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }


    @Override
    public List<ActivityRuleMap> findByActivityId(Integer id) {
        JsonResult result = new JsonResult();
        try {
             return activityRuleMapDao.findByActivityId(id);
        } catch (Exception e) {
            log.error("activityrulemapserviceimpl findByActivityId process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return null;
    }

}
