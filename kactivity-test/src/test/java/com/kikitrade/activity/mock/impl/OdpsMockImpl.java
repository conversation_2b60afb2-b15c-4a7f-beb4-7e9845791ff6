package com.kikitrade.activity.mock.impl;

import com.aliyun.odps.Odps;
import com.kikitrade.activity.mock.OdpsMock;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class OdpsMockImpl implements OdpsMock {
    @Resource
    Odps odps;

    @Override
    public void getDefaultProject() {

        Mockito.doAnswer(c->{
            return "testProject";
        }).when(odps).getDefaultProject();

    }
}
