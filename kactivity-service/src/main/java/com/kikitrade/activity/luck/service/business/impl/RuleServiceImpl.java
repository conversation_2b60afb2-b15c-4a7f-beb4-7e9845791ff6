package com.kikitrade.activity.luck.service.business.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.accounting.api.RemoteProductAccountService;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountConfigDTO;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountConstant;
import com.kikitrade.activity.dal.mysql.dao.LuckFortuneRuleDao;
import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisScriptConstant;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.facade.luck.KycLevel;
import com.kikitrade.activity.facade.luck.LuckFortuneRuleDTO;
import com.kikitrade.activity.facade.luck.UserType;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.config.KactivityTextConfig;
import com.kikitrade.activity.service.config.PropertiesConfigService;
import com.kikitrade.activity.service.exception.BussinessException;
import com.kikitrade.kcustomer.common.constants.CustomerConstants;
import com.kikitrade.activity.luck.service.business.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;

/**
 *
 */
@Service
@Slf4j
public class RuleServiceImpl implements RuleService {

    @Resource
    private LuckFortuneRuleDao luckFortuneRuleDao;
    @Resource
    private KactivityProperties kactivityProperties;
    @Resource
    private PropertiesConfigService propertiesConfigService;
    @DubboReference
    private RemoteProductAccountService remoteProductAccountService;
    @Resource
    private RedisService redisService;

    @Override
    @Compensation(confirmMethod = "confirmSaveRule")
    public String trySaveRule(LuckFortuneRuleDTO luckFortuneRule, String id) throws Exception{
        return null;
    }

    /**
     * 修改
     *
     * @param luckFortuneRule
     * @return
     * @throws Exception
     */
    @Override
    @Compensation(confirmMethod = "confirmUpdateRule")
    public String tryUpdateRule(LuckFortuneRuleDTO luckFortuneRule) throws Exception {
        return null;
    }

    public void confirmSaveRule(LuckFortuneRuleDTO luckFortuneRuleDTO, String id){}

    public void confirmUpdateRule(LuckFortuneRuleDTO luckFortuneRuleDTO){}

    private void confirm(LuckFortuneRule luckFortuneRule, String id){
        if(StringUtils.isNotBlank(id)){
            LuckFortuneRule rule = luckFortuneRuleDao.selectByPrimaryKey(id);
            if(rule != null){
                //发放24小时金额限制
                createReleaseAmountConfigDays(luckFortuneRule);
                //发放30天金额限制
                createReleaseAmountConfigMonths(luckFortuneRule);
                //领取24小时金额限制
                createReceiveAmountConfigDays(luckFortuneRule);
                //领取30天金额限制
                createReceiveAmountConfigMonths(luckFortuneRule);
                //领取24小时次数限制
                createReceiveTimesConfigDays(luckFortuneRule);
                //领取30日次数限制
                createReceiveTimesConfigMonths(luckFortuneRule);
            }
        }

    }

    /**
     * 24小时发放限额
     * @param luckFortuneRule
     */
    private void createReleaseAmountConfigDays(LuckFortuneRule luckFortuneRule){
        ProductAccountConfigDTO productAccountConfig = remoteProductAccountService.getProductAccountConfig(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RELEASE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType()),
                ProductAccountConstant.LimitAccountType.LIMIT_ACCOUNT_TYPE_PERSON.getLimitAccountType(),
                ProductAccountConstant.LimitType.LIMIT_TYPE_AMOUNT.getLimitType(),
                ProductAccountConstant.PeriodType.PERIOD_TYPE_DAY_TOTAL.getPeriodType());
        if(productAccountConfig == null){
            ProductAccountConfigDTO configDTO = buildDTO(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RELEASE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType())
                    ,"空投发放金额限制-日", ProductAccountConstant.LimitType.LIMIT_TYPE_AMOUNT.getLimitType(), ProductAccountConstant.PeriodType.PERIOD_TYPE_DAY_TOTAL.getPeriodType()
                    , luckFortuneRule.getReleaseAccountDays(), null);
            remoteProductAccountService.createConfig(configDTO);
        }else {
            productAccountConfig.setLimitMinAmount(BigDecimal.ZERO);
            productAccountConfig.setLimitMaxAmount(luckFortuneRule.getReleaseAccountDays());
            productAccountConfig.setProLimitName("空投发放金额限制-日");
            productAccountConfig.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            productAccountConfig.setSaasId(kactivityProperties.getSaasId());
            productAccountConfig.setEffectType(ProductAccountConstant.EffectType.CONFIG_EFFECT.getCode());

            log.info("createReleaseAmountConfigDays:{}", productAccountConfig);
            remoteProductAccountService.updateProductAccountConfig(productAccountConfig);
        }
    }

    /**
     * 30天发放限额
     * @param luckFortuneRule
     */
    private void createReleaseAmountConfigMonths(LuckFortuneRule luckFortuneRule){
        ProductAccountConfigDTO productAccountConfig = remoteProductAccountService.getProductAccountConfig(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RELEASE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType()),
                ProductAccountConstant.LimitAccountType.LIMIT_ACCOUNT_TYPE_PERSON.getLimitAccountType(),
                ProductAccountConstant.LimitType.LIMIT_TYPE_AMOUNT.getLimitType(),
                ProductAccountConstant.PeriodType.PERIOD_TYPE_MONTH_TOTAL.getPeriodType());
        if(productAccountConfig == null){
            ProductAccountConfigDTO configDTO = buildDTO(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RELEASE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType())
                    ,"空投发放金额限制-月", ProductAccountConstant.LimitType.LIMIT_TYPE_AMOUNT.getLimitType(), ProductAccountConstant.PeriodType.PERIOD_TYPE_MONTH_TOTAL.getPeriodType()
                    , luckFortuneRule.getReleaseAccountMonths(), null);
            remoteProductAccountService.createConfig(configDTO);
        }else {
            productAccountConfig.setLimitMinAmount(BigDecimal.ZERO);
            productAccountConfig.setLimitMaxAmount(luckFortuneRule.getReleaseAccountMonths());
            productAccountConfig.setProLimitName("空投发放金额限制-月");
            productAccountConfig.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            productAccountConfig.setEffectType(ProductAccountConstant.EffectType.CONFIG_EFFECT.getCode());

            log.info("createReleaseAmountConfigMonths:{}", productAccountConfig);
            remoteProductAccountService.updateProductAccountConfig(productAccountConfig);
        }
    }

    /**
     * 24小时领取限额
     * @param luckFortuneRule
     */
    private void createReceiveAmountConfigDays(LuckFortuneRule luckFortuneRule){
        if(luckFortuneRule.getReceiveAccountDays() == null){
            return;
        }
        ProductAccountConfigDTO productAccountConfig = remoteProductAccountService.getProductAccountConfig(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType()),
                ProductAccountConstant.LimitAccountType.LIMIT_ACCOUNT_TYPE_PERSON.getLimitAccountType(),
                ProductAccountConstant.LimitType.LIMIT_TYPE_AMOUNT.getLimitType(),
                ProductAccountConstant.PeriodType.PERIOD_TYPE_DAY_TOTAL.getPeriodType());
        if(productAccountConfig == null){
            ProductAccountConfigDTO configDTO = buildDTO(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType())
                    ,"空投领取金额限制-日", ProductAccountConstant.LimitType.LIMIT_TYPE_AMOUNT.getLimitType(), ProductAccountConstant.PeriodType.PERIOD_TYPE_DAY_TOTAL.getPeriodType()
                    , luckFortuneRule.getReceiveAccountDays(), null);
            remoteProductAccountService.createConfig(configDTO);
        }else {
            productAccountConfig.setLimitMinAmount(BigDecimal.ZERO);
            productAccountConfig.setLimitMaxAmount(luckFortuneRule.getReceiveAccountDays());
            productAccountConfig.setProLimitName("空投领取金额限制-日");
            productAccountConfig.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            productAccountConfig.setEffectType(ProductAccountConstant.EffectType.CONFIG_EFFECT.getCode());

            log.info("createReceiveAmountConfigDays:{}", productAccountConfig);
            remoteProductAccountService.updateProductAccountConfig(productAccountConfig);
        }
    }

    /**
     * 30天领取限额
     * @param luckFortuneRule
     */
    private void createReceiveAmountConfigMonths(LuckFortuneRule luckFortuneRule){
        if(luckFortuneRule.getReceiveAccountMonths() == null){
            return;
        }
        ProductAccountConfigDTO productAccountConfig = remoteProductAccountService.getProductAccountConfig(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType()),
                ProductAccountConstant.LimitAccountType.LIMIT_ACCOUNT_TYPE_PERSON.getLimitAccountType(),
                ProductAccountConstant.LimitType.LIMIT_TYPE_AMOUNT.getLimitType(),
                ProductAccountConstant.PeriodType.PERIOD_TYPE_MONTH_TOTAL.getPeriodType());
        if(productAccountConfig == null){
            ProductAccountConfigDTO configDTO = buildDTO(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType())
                    ,"空投领取金额限制-日", ProductAccountConstant.LimitType.LIMIT_TYPE_AMOUNT.getLimitType(), ProductAccountConstant.PeriodType.PERIOD_TYPE_MONTH_TOTAL.getPeriodType()
                    , luckFortuneRule.getReceiveAccountMonths(), null);
            remoteProductAccountService.createConfig(configDTO);
        }else {
            productAccountConfig.setLimitMinAmount(BigDecimal.ZERO);
            productAccountConfig.setLimitMaxAmount(luckFortuneRule.getReceiveAccountMonths());
            productAccountConfig.setProLimitName("空投领取金额限制-月");
            productAccountConfig.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            productAccountConfig.setEffectType(ProductAccountConstant.EffectType.CONFIG_EFFECT.getCode());
            log.info("createReceiveAmountConfigMonths:{}", productAccountConfig);
            remoteProductAccountService.updateProductAccountConfig(productAccountConfig);
        }
    }

    /**
     * 24小时次数限制
     * @param luckFortuneRule
     */
    private void createReceiveTimesConfigDays(LuckFortuneRule luckFortuneRule){
        if(luckFortuneRule.getReceiveNumDays() <= 0){
            return;
        }
        ProductAccountConfigDTO productAccountConfig = remoteProductAccountService.getProductAccountConfig(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType()),
                ProductAccountConstant.LimitAccountType.LIMIT_ACCOUNT_TYPE_PERSON.getLimitAccountType(),
                ProductAccountConstant.LimitType.LIMIT_TYPE_TIMES.getLimitType(),
                ProductAccountConstant.PeriodType.PERIOD_TYPE_DAY_TOTAL.getPeriodType());
        if(productAccountConfig == null){
            ProductAccountConfigDTO configDTO = buildDTO(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType())
                    ,"空投领取次数限制-日", ProductAccountConstant.LimitType.LIMIT_TYPE_TIMES.getLimitType(), ProductAccountConstant.PeriodType.PERIOD_TYPE_DAY_TOTAL.getPeriodType()
                    , null, (long)luckFortuneRule.getReceiveNumDays());
            remoteProductAccountService.createConfig(configDTO);
        }else {
            productAccountConfig.setLimitMinTimes(0L);
            productAccountConfig.setLimitMaxTimes((long)luckFortuneRule.getReceiveNumDays());
            productAccountConfig.setProLimitName("空投领取次数限制-日");
            productAccountConfig.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            productAccountConfig.setEffectType(ProductAccountConstant.EffectType.CONFIG_EFFECT.getCode());
            log.info("createReceiveTimesConfigDays:{}", productAccountConfig);
            remoteProductAccountService.updateProductAccountConfig(productAccountConfig);
        }
    }

    /**
     * 30天领取次数限制
     * @param luckFortuneRule
     */
    private void createReceiveTimesConfigMonths(LuckFortuneRule luckFortuneRule){
        if(luckFortuneRule.getReceiveNumMonths() <= 0){
            return;
        }
        ProductAccountConfigDTO productAccountConfig = remoteProductAccountService.getProductAccountConfig(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType()),
                ProductAccountConstant.LimitAccountType.LIMIT_ACCOUNT_TYPE_PERSON.getLimitAccountType(),
                ProductAccountConstant.LimitType.LIMIT_TYPE_TIMES.getLimitType(),
                ProductAccountConstant.PeriodType.PERIOD_TYPE_MONTH_TOTAL.getPeriodType());
        if(productAccountConfig == null){
            ProductAccountConfigDTO configDTO = buildDTO(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RECEIVE_LIMIT.name(), luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType())
                    ,"空投领取次数限制-月", ProductAccountConstant.LimitType.LIMIT_TYPE_TIMES.getLimitType(), ProductAccountConstant.PeriodType.PERIOD_TYPE_MONTH_TOTAL.getPeriodType()
                    , null, (long)luckFortuneRule.getReceiveNumMonths());
            remoteProductAccountService.createConfig(configDTO);
        }else {
            productAccountConfig.setLimitMinTimes(0L);
            productAccountConfig.setLimitMaxTimes((long)luckFortuneRule.getReceiveNumMonths());
            productAccountConfig.setProLimitName("空投领取次数限制-月");
            productAccountConfig.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
            productAccountConfig.setEffectType(ProductAccountConstant.EffectType.CONFIG_EFFECT.getCode());
            log.info("createReceiveTimesConfigMonths:{}", productAccountConfig);
            remoteProductAccountService.updateProductAccountConfig(productAccountConfig);
        }
    }

    private ProductAccountConfigDTO buildDTO(String proLimitCode, String proLimitName, String limitType, String periodType,
                                             BigDecimal limitMaxAmount, Long limitMaxTimes){
        ProductAccountConfigDTO configDTO = new ProductAccountConfigDTO();
        configDTO.setProLimitCode(proLimitCode);
        configDTO.setProLimitName(proLimitName);
        configDTO.setLimitAccountType(ProductAccountConstant.LimitAccountType.LIMIT_ACCOUNT_TYPE_PERSON.getLimitAccountType());
        configDTO.setLimitType(limitType);
        configDTO.setPeriodType(periodType);
        configDTO.setLimitMinAmount(BigDecimal.ZERO);
        configDTO.setLimitMaxAmount(limitMaxAmount);
        configDTO.setLimitMinTimes(0L);
        configDTO.setLimitMaxTimes(limitMaxTimes);
        configDTO.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        configDTO.setSaasId(kactivityProperties.getSaasId());
        return configDTO;
    }

    @Override
    @Compensation(confirmMethod = "confirmSaveRuleV1")
    public String trySaveRuleV1(LuckFortuneRule luckFortuneRule, String id) throws Exception {
        try{
            LuckFortuneRule rule = save(luckFortuneRule, id);
            redisService.save(RedisKeyConst.LUCK_FORTUNE_RULE_KEY.getKey(String.format("%s:%s", luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType())), JSON.toJSONString(rule));
            return id;
        }catch (DuplicateKeyException ex){
            log.error("duplicateKeyException", ex);
            throw new BussinessException(String.format("%s + %s已存在", luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType()));
        }catch (Exception ex){
            log.error("saveOrUpdate_luck_rule_error:{}",luckFortuneRule,ex);
            throw ex;
        }
    }

    public void confirmSaveRuleV1(LuckFortuneRule luckFortuneRule, String id){
        if(StringUtils.isNotBlank(id)){
            confirm(luckFortuneRule, id);
        }
    }

    @Override
    @Compensation(confirmMethod = "confirmUpdateRuleV1")
    public String tryUpdateRuleV1(LuckFortuneRule luckFortuneRule) throws Exception {
        try{
            log.info("tryUpdateRule dto:{}", luckFortuneRule);
            LuckFortuneRule rule = update(luckFortuneRule);
            log.info("tryUpdateRule update result:{}", rule);
            redisService.save(RedisKeyConst.LUCK_FORTUNE_RULE_KEY.getKey(String.format("%s:%s", luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType())), JSON.toJSONString(rule));
            return rule.getId();
        }catch (DuplicateKeyException ex){
            log.error("duplicateKeyException", ex);
            throw new BussinessException(String.format("%s + %s已存在", luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType()));
        }catch (Exception ex){
            log.error("saveOrUpdate_luck_rule_error:{}",luckFortuneRule,ex);
            throw ex;
        }
    }

    public void confirmUpdateRuleV1(LuckFortuneRule luckFortuneRule){
        if(StringUtils.isNotBlank(luckFortuneRule.getId())){
            confirm(luckFortuneRule, luckFortuneRule.getId());
        }
    }

    /**
     * 查询配置
     *
     * @param kycLevel
     * @param userType
     * @return
     */
    @Override
    public LuckFortuneRule findObject(String kycLevel, String userType) {
        String obj = redisService.get(RedisKeyConst.LUCK_FORTUNE_RULE_KEY.getKey(String.format("%s:%s", kycLevel, userType)));
        LuckFortuneRule luckFortune = JSON.parseObject(obj, LuckFortuneRule.class);
        if(luckFortune == null){
            LuckFortuneRule param = new LuckFortuneRule();
            param.setKycLevel(kycLevel);
            param.setUserType(userType);
            param.setStatus(ActivityConstant.ValidStatus.VALID.getStatus());
            LuckFortuneRule luckFortuneRule = luckFortuneRuleDao.selectOne(param);
            if(luckFortuneRule == null){
                String normal = redisService.get(RedisKeyConst.LUCK_FORTUNE_RULE_KEY.getKey(String.format("%s:%s", KycLevel.L0.name(), UserType.COMMON.name())));
                return JSON.parseObject(normal, LuckFortuneRule.class);
            }
            return luckFortuneRule;
        }
        return luckFortune;
    }

    private LuckFortuneRule save(LuckFortuneRule luckFortuneRule, String id){
        luckFortuneRule.setId(id);
        luckFortuneRule.setCreated(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        luckFortuneRule.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        luckFortuneRule.setStatus(ActivityConstant.ValidStatus.VALID.getStatus());
        luckFortuneRule.setSaasId(kactivityProperties.getSaasId());
        luckFortuneRuleDao.insert(luckFortuneRule);
        return luckFortuneRule;
    }
    
    private LuckFortuneRule update(LuckFortuneRule luckFortuneRule){
        luckFortuneRule.setModified(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)));
        luckFortuneRuleDao.updateByPrimaryKey(luckFortuneRule);
        return luckFortuneRule;
    }
}
