package com.kikitrade.activity.service.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 16:27
 */
@Configuration
@ConfigurationProperties(prefix = "three")
@Data
public class ThreePlatformProperties {

    @NestedConfigurationProperty
    private TwitterProperties twitter;
    @NestedConfigurationProperty
    private DiscordProperties discord;
    @NestedConfigurationProperty
    private GameProperties game;
    @NestedConfigurationProperty
    private SoMonProperties somon;
    @NestedConfigurationProperty
    private OspProperties osp;
    @NestedConfigurationProperty
    private MugenProperties mugen;

    @NestedConfigurationProperty
    private TGProperties tg;

    @NestedConfigurationProperty
    private TelePulseProperties telePulse;

    @NestedConfigurationProperty
    private GoogleProperties google;

    @NestedConfigurationProperty
    private FacebookProperties facebook;

    @NestedConfigurationProperty
    private LineProperties line;

}
