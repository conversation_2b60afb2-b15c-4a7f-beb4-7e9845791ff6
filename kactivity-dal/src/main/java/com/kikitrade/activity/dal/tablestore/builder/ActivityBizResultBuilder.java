package com.kikitrade.activity.dal.tablestore.builder;

import com.kikitrade.activity.dal.tablestore.model.ActivityBizResult;
import com.kikitrade.framework.common.model.PageResult;

import java.util.Date;
import java.util.List;

/**
 * created by jacky. 2020/2/14 10:20 AM
 */
public interface ActivityBizResultBuilder {

    boolean insertIfNotExist(ActivityBizResult reward);

    PageResult findByCustomer(String customerId, int offset, int limit);

    long countByCustomer(String customerId);

    PageResult findByCustomerAndActivity(String customerId, Integer activityId, int offset, int limit);

    long countByCustomerAndActivity(String customerId, Integer activityId);

    PageResult findByCustomerAndActivityAndStatus(String customerId, Integer activityId, String status, int offset, int limit);

    long countByCustomerAndActivityAndStatus(String customerId, Integer activityId, String status);


    List<ActivityBizResult> findByCustomerAndActivityDaily(String customerId, Integer activityId, Date date);

}
