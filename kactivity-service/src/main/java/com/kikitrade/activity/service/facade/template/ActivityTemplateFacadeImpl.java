package com.kikitrade.activity.service.facade.template;

import com.kikitrade.activity.facade.template.*;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityMaterialService;
import com.kikitrade.activity.service.model.MaterialVO;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class ActivityTemplateFacadeImpl extends ActivityTemplateFacadeGrpc.ActivityTemplateFacadeImplBase {

    @Resource
    private ActivityMaterialService activityMaterialService;

    /**
     * 保存物料
     * @param request
     * @param responseObserver
     */
    @Override
    public void saveMaterial(ActivityMaterialDTO request, StreamObserver<CommonResponse> responseObserver) {
        try{
            List<ActivityMaterial> materialList = request.getMaterialList();
            List<MaterialVO> activityMaterialList = new ArrayList<>();
            for(ActivityMaterial material : materialList){
                MaterialVO activityMaterial = new MaterialVO();
                activityMaterial.setCode(material.getCode());
                activityMaterial.setValue(material.getValue());
                activityMaterialList.add(activityMaterial);
            }
            com.kikitrade.activity.service.model.ActivityMaterialVO activityMaterialVO = new com.kikitrade.activity.service.model.ActivityMaterialVO();
            activityMaterialVO.setActivityId(request.getActivityId());
            activityMaterialVO.setTemplateStatus(request.getTemplateStatus());
            activityMaterialVO.setMaterialVOList(activityMaterialList);
            Result result = activityMaterialService.save(activityMaterialVO);
            responseObserver.onNext(CommonResponse.newBuilder().setSuccess(result.isSuccess()).setMessage(result.getMessage()).build());
            responseObserver.onCompleted();
        }catch (Exception ex){
            log.error("grpc save material error",ex);
            responseObserver.onError(ex);
        }
    }
}
