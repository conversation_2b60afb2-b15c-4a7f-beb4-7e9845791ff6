package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Direction;
import com.alicloud.openservices.tablestore.model.PrimaryKey;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster.INDEX_ACTIVITY_BATCH_ROSTER_DEFAULT_INDEX;
import static com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster.INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX;

@Slf4j
@Component
public class ActivityBatchRewardRosterStoreBuilderImpl extends WideColumnStoreBuilder<ActivityBatchRewardRoster> implements ActivityBatchRewardRosterStoreBuilder {

    @PostConstruct
    public void init() {
        init(ActivityBatchRewardRoster.class);
    }

    @Override
    public String tableName() {
        return "activity_batch_roster";
    }

    @Override
    public boolean batchInsert(List<ActivityBatchRewardRoster> activityBatchRewardRosters) {
        return batchPutRow(activityBatchRewardRosters, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public PageResult findByBatchIdAndStatus(String batchId, String status, int offset, int limit) {

        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("batch_id", batchId));
        if(StringUtils.isNotBlank(status)){
            boolQuery.must(QueryBuilders.term("status", status));
        }
        return pageSearch(boolQuery.build(), null, offset, limit, INDEX_ACTIVITY_BATCH_ROSTER_DEFAULT_INDEX);
    }

    @Override
    public boolean updateStatus(ActivityBatchRewardRoster activityBatchRewardRoster) {
        return updateRow(activityBatchRewardRoster);
    }

    @Override
    public boolean existByBatchIdAndStatus(String batchId, String rewardStatus) {

        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("status", PrimaryKeyValue.fromString(rewardStatus), PrimaryKeyValue.fromString(rewardStatus)));
        queryList.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.fromString(batchId), PrimaryKeyValue.fromString(batchId)));
        queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        queryList.add(new RangeQueryParameter("seq", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));

        return rangeQueryOne(INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, queryList) != null;
    }

    @Override
    public RangeResult<ActivityBatchRewardRoster> findForPage(String batchId, String rewardStatus, int limit, PrimaryKey primaryKey) {
        List<RangeQueryParameter> queryList = new ArrayList<>();
        queryList.add(new RangeQueryParameter("status", PrimaryKeyValue.fromString(rewardStatus), PrimaryKeyValue.fromString(rewardStatus)));
        queryList.add(new RangeQueryParameter("batch_id", PrimaryKeyValue.fromString(batchId), PrimaryKeyValue.fromString(batchId)));
        queryList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        queryList.add(new RangeQueryParameter("seq", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return super.rangeQueryWithNextToken(INDEX_ACTIVITY_BATCH_ROSTER_STATUS_INDEX, queryList, Direction.FORWARD, limit, primaryKey);
    }

    @Override
    public long count(String batchId){
        String aggName = "count_agg_roster_by_batch_id";
        SearchQuery query = SearchQuery.newBuilder().query(QueryBuilders.term("batch_id", batchId))
                .addAggregation(AggregationBuilders.count(aggName, "batch_id")).build();

        return searchCount(query, INDEX_ACTIVITY_BATCH_ROSTER_DEFAULT_INDEX, aggName);
    }

    @Override
    public ActivityBatchRewardRoster findById(String batchId, String customerId, String seq) {
        ActivityBatchRewardRoster reward = new ActivityBatchRewardRoster();
        reward.setBatchId(batchId);
        reward.setCustomerId(customerId);
        reward.setSeq(seq);
        return super.getRow(reward);
    }
}
