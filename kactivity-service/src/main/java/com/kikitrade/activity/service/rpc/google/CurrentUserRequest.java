package com.kikitrade.activity.service.rpc.google;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.service.common.config.GoogleProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.json.JSONObject;

import java.time.OffsetDateTime;

/**
*   <AUTHOR>
*   @date 2025/7/18 18:12
*   @description: 
*/
@Data
@Slf4j
public class CurrentUserRequest extends AccessToken {
    public CurrentUserRequest(){}

    public CurrentUserRequest(GoogleProperties googleProperties, String accessToken){
        this.accessToken = accessToken;
    }



    public SocialUserInfo execute() {
        SocialUserInfo cache = GoogleCache.get(this.accessToken, SocialUserInfo.class);
        if(cache != null){
            return cache;
        }
        Request request = new Request.Builder()
            .url("https://www.googleapis.com/oauth2/v3/userinfo")
            .addHeader("Authorization", "Bearer " + this.getAccessToken())
            .build();

        try {
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("[google] currentUser:{}", jsonObject);

            String id = jsonObject.getString("sub");
            String username = jsonObject.getString("name");
            String email = jsonObject.optString("email"); // 获取email字段
            String profileImage = jsonObject.getString("picture");

            SocialUserInfo userInfo = SocialUserInfo.builder()
                .userId(id)
                .userName(username)
                .email(email)
                .profileImageUrl(profileImage)
                .createdAt(OffsetDateTime.now().toEpochSecond())
                .build();
            GoogleCache.put(this.accessToken, JSON.toJSONString(userInfo));
            return userInfo;
        } catch (Exception e) {
            log.error("[discord] currentUser error:{}", accessToken ,e);
            return null;
        }
    }
}
