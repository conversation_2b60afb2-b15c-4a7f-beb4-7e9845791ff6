package com.kikitrade.activity.op;

import com.kikitrade.accounting.api.domain.activity.model.AccountActivityRequest;
import com.kikitrade.accounting.api.model.account.request.AccountFreezeRequest;
import com.kikitrade.accounting.api.model.account.request.AccountTransferRequest;
import com.kikitrade.accounting.api.model.account.request.AccountUnfreezeRequest;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountConfigDTO;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountParam;
import com.kikitrade.activity.api.model.response.*;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.model.*;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.service.model.StoreInfo;
import com.kikitrade.activity.service.mq.RewardDivideListener;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.asset.model.request.AssetTransferInRequest;
import com.kikitrade.asset.model.request.AssetTransferOutRequest;
import com.kikitrade.ksocial.common.constants.CustomerConstants;
import com.kikitrade.quota.api.model.request.QuotaConfigDTO;
import com.kikitrade.quota.api.model.request.QuotaDeductDTO;
import com.kikitrade.quota.api.model.request.QuotaFreezeDTO;
import com.kikitrade.quota.api.model.request.QuotaUnfreezeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BusinessOP {

    //抽奖号码
    public static int lotteryNum = 10000;
    //抽奖次数,默认第三抽
    public static int count = 2;
    //奖品
    public static ActivityLotteryDetailConfig draw = null;
    //vip等级
    public static String vip = "L1";
    public static Map<String, ActivityCustomReward> activityCustomRewardMap = new HashMap<>();
    public static List<ActivityCustomReward> activityCustomRewardList = new ArrayList<>();
    public static Map<String, ActivityLotteryItem> activityLotteryItemMap = new HashMap<>();

    //奖品
    public static ActivityCustomReward reward;

    public static ProductAccountConfigDTO productAccountConfigDTO;

    //定点爆破
    public static String pointBlast = null;

    public static List<String> exceptions = new ArrayList<>();

    public static Boolean randomFlag = true;

    //命中区间次数
    public static List<Long> interval = new ArrayList<>();

    public static StoreInfo storeInfo;

    public static int remainNumBeforeLottery;
    public static int remainNumAfterLottery;

    //业务帐
    public static ProductAccountParam productAccountParam;
    public static List<ProductAccountParam> productAccountParamList = new ArrayList<>();


    public static List<AccountTransferRequest> accountTransferRequestList = new ArrayList<>();

    public static AssetTransferOutRequest assetTransferOutRequest;
    public static List<AssetTransferInRequest> assetTransferInRequestList = new ArrayList<>();

    public static Map<String, LuckFortuneReleaseItem> luckFortuneReleaseItemMap = new HashMap<>();
    public static Map<String, LuckFortuneReceiveItem> luckFortuneReceiveItemMap = new HashMap<>();
    public static Map<String, LuckFortuneRefundItem> luckFortuneRefundItemMap = new HashMap<>();


    //账务帐冻结
    public static AccountFreezeRequest accountFreezeRequest;
    public static List<AccountFreezeRequest> accountFreezeRequestList = new ArrayList<>();
    public static List<AccountUnfreezeRequest> accountUnfreezeRequestList = new ArrayList<>();

    // 公用key
    public static String releaseId;

    // 默认的kolRole
    public static CustomerConstants.KolRole kolRole = CustomerConstants.KolRole.KOL;

    // 默认可用余额
    public static BigDecimal available = new BigDecimal(100000L);

    public static String releaseCode;

    public static List<String> receiveIds = new ArrayList<>();

    public static List<AccountActivityRequest> accountActivityRequests = new ArrayList<>();

    public static String refundId;
    public static String detailId;
    public static String expireEvent;
    public static String revertEvent;
    public static String refundEvent;

    public static List<ActivityTaskItem> activityTaskItemList = new ArrayList<>();
    public static List<ActivityTaskDetail> activityTaskDetailList = new ArrayList<>();

    public static List<ActivityBatch> activityBatchList = new ArrayList<>();

    public static boolean auditFinished = false;
    public static List<QuotaConfigDTO> quotaConfigDTOList = new ArrayList<>();
    public static List<QuotaFreezeDTO> quotaFreezeDTOList = new ArrayList<>();
    public static List<QuotaUnfreezeDTO> quotaUnfreezeDeductList = new ArrayList<>();
    public static List<QuotaUnfreezeDTO> quotaUnfreezeDTOList = new ArrayList<>();
    public static List<QuotaDeductDTO> quotaDeductDTOList = new ArrayList<>();
    public static Map<String, TaskConfig> taskConfigMap = new HashMap<>();
    public static List<ActivityResponse> activityResponseList = new ArrayList<>();
    public static List<RewardRequest> rewardRequestList = new ArrayList<>();
    public static Map<String, String> mqMessageMap = new HashMap<>();
    public static List<RewardDivideListener.OwnerInvite> inviterList = new ArrayList<>();
    public static List<CustomerQuestionSets> customerQuestionSetsList = new ArrayList<>();
    public static List<ActivityTaskStatis> activityTaskStatisList = new ArrayList<>();
    public static List<VerifyResponse> verifyResponseList = new ArrayList<>();
    public static List<TaskListResponse> taskListResponseList = new ArrayList<>();
    public static List<TaskPopResponse> taskPopResponseList = new ArrayList<>();
    public static List<TaskDetailResponse> taskDetailResponseList = new ArrayList<>();
    public static List<TaskProgressResponse> taskProgressResponseList = new ArrayList<>();

    /**
     * 初始化容器
     */
    public void initContainer() {
        lotteryNum = 10000;
        count = 2;
        draw = null;
        activityCustomRewardMap.clear();
        activityCustomRewardList.clear();
        activityLotteryItemMap.clear();
        vip = "L1";
        reward = null;
        productAccountConfigDTO = null;
        pointBlast = null;
        exceptions.clear();
        randomFlag = true;
        interval.clear();
        storeInfo = null;
        remainNumBeforeLottery = 0;
        remainNumAfterLottery = 0;
        productAccountParam = null;
        productAccountParamList.clear();
        accountTransferRequestList.clear();
        assetTransferOutRequest = null;
        luckFortuneReleaseItemMap.clear();
        luckFortuneReceiveItemMap.clear();
        luckFortuneRefundItemMap.clear();
        accountFreezeRequest = null;
        accountFreezeRequestList.clear();
        accountUnfreezeRequestList.clear();
        releaseId = null;
        kolRole = CustomerConstants.KolRole.KOL;
        available = new BigDecimal(100000L);
        releaseCode = null;
        receiveIds.clear();
        accountActivityRequests.clear();
        refundId = null;
        detailId = null;
        expireEvent = null;
        revertEvent = null;
        refundEvent = null;
        assetTransferInRequestList.clear();
        activityTaskItemList.clear();
        activityTaskDetailList.clear();
        activityBatchList.clear();
        auditFinished = false;
        quotaConfigDTOList.clear();
        quotaFreezeDTOList.clear();
        quotaUnfreezeDTOList.clear();
        quotaUnfreezeDeductList.clear();
        quotaDeductDTOList.clear();
        taskConfigMap.clear();
        activityResponseList.clear();
        rewardRequestList.clear();
        mqMessageMap.clear();
        inviterList.clear();
        customerQuestionSetsList.clear();
        activityTaskStatisList.clear();
        verifyResponseList.clear();
        taskListResponseList.clear();
        taskPopResponseList.clear();
        taskDetailResponseList.clear();
        taskProgressResponseList.clear();
    }

}
