package com.kikitrade.activity.processor;

import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.dao.ActivityEntityDao;
import com.kikitrade.activity.dal.tablestore.builder.impl.ActivityBatchBuildImpl;
import com.kikitrade.activity.dal.tablestore.builder.impl.ActivityCustomRewardStoreBuilderImpl;
import com.kikitrade.activity.service.business.impl.NoticeServiceImpl;
import com.kikitrade.activity.service.reward.impl.CustomerServiceImpl;
import com.kikitrade.activity.util.ReflectionUtils;
import com.kikitrade.activity.util.SpringBootBeanUtils;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.kcustomer.api.service.RemoteNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.mockito.MockSettings;
import org.mockito.Mockito;
import org.mockito.internal.creation.MockSettingsImpl;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;



import jakarta.annotation.Resource;

@Component
@Slf4j
public class ProjectPostProcessor implements BeanPostProcessor {
    @Resource
    ApplicationContext applicationContext;
    @Resource(name = "remoteCustomerServiceStub")
    RemoteCustomerService remoteCustomerService;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {

        if(beanName.equals("customerServiceImpl")){
            //System.out.println(beanName+" "+bean);
            ReflectionUtils.setField(CustomerServiceImpl.class, "remoteCustomerService", bean, remoteCustomerService);
            return bean;
        }

        //System.out.println(beanName+" "+bean);
//        if(beanName.equals("exchangeService")){
//            //System.out.println(beanName+" "+bean);
//            SpringBootBeanUtils.removeBean((ConfigurableApplicationContext) applicationContext, beanName);
//            return null;
//        }

        if(beanName.equals("activityBatchBuildImpl")){
            //System.out.println(beanName+" "+bean);
            ActivityBatchBuildImpl mock = (ActivityBatchBuildImpl) Mockito.mock(bean.getClass());
            SeqGeneraterService seqGeneraterService = applicationContext.getBean(SeqGeneraterService.class);
            ActivityEntityDao activityEntityDao = applicationContext.getBean(ActivityEntityDao.class);
            ReflectionUtils.setField(ActivityBatchBuildImpl.class, "seqGeneraterService", mock, seqGeneraterService);
            ReflectionUtils.setField(ActivityBatchBuildImpl.class, "activityEntityDao", mock, activityEntityDao);
            return mock;
        }

//        if(beanName.equals("activityCustomRewardStoreBuilderImpl")){
//            System.out.println(beanName+" "+bean);
//        }

        if(beanName.equals("noticeServiceImpl")){
            //System.out.println(beanName+" "+bean);
//            RemoteNotificationService stub = (RemoteNotificationService) applicationContext.getBean("remoteNotificationServiceStub");
//            ReflectionUtils.setField(NoticeServiceImpl.class, "remoteNotificationService", bean, stub);


        }

        if(beanName.equals("clientWorker")){
            //System.out.println(beanName+" "+bean);

        }



        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {

        if(beanName.equals("clientWorker")){
            System.out.println(beanName+" "+bean);

        }
        return bean;
    }
}
