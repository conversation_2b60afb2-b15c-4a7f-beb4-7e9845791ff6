############### ignite éç½® ###############
kiki.ignite.namespace=kikitrade
kiki.ignite.application-name=kactivity
kiki.ignite.local=true
kiki.ignite.environment-namespace=kikitrade-dev
kiki.ignite.client=false

###########################zookeeper###################################
zookeeper.url=localhost

############### dubbo éç½® ###############
dubbo.application.name=kactivity
dubbo.application.id=kactivity
dubbo.application.version=1.0.0
dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=20882
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=500
dubbo.protocol.queues=1000
dubbo.registry.address=zookeeper://127.0.0.1:2181
dubbo.provider.timeout=5000
dubbo.provider.retries=1
dubbo.consumer.group=kktd
dubbo.provider.group=kktd
dubbo.reference.check=false
dubbo.consumer.check=false
dubbo.registry.check=false
dubbo.consumer.filter=dtm
dubbo.provider.filter=dtm


################ MySQL Spring Config ##############
spring.datasource.name=x_shard_01
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.url=****************************************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.username=user
spring.datasource.password=password
spring.datasource.druid.initial-size=10
spring.datasource.druid.max-active=20
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-wait=10000

########################### dtm config ###################################
kiki.dtm.participant=kactivity
kiki.dtm.server-address=api.dev.dipbit.xyz
kiki.dtm.server-port=20880
kiki.dtm.local=true

########################### tablestore æ¯è´¦æ·çº§å«çak skï¼ ä¸ossï¼åªåå«kikiçossææåè½ï¼ å¬ç¨ak sk ###################################

#kiki.ots.endpoint=https://KikiTradeTest.ap-southeast-1.ots.aliyuncs.com
#kiki.ots.instanceName=KikiTradeTest
#kiki.ots.retry-pause-in-millis=5000
#kiki.ots.retry-times=1
#kiki.ots.skipCheckTableChange=true

####################### producer manage ##########################
# ons
kiki.ons.producer.enable=false
kiki.ons.consumer.enable=false
kiki.ons.address=http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
kiki.ons.env=_DEV
kiki.ons.tag=dev
kiki.ons.group-id=GID_ACTIVITY_KIKI_DEV
ons.topic.activity=T_ACTIVITY_KIKI_DEV
ons.topic.phase.two=T_PAY_PHASETWO_KIKI_DEV
ons.topic.customer.event=T_EVENT_ACTIVITY_KIKI_DEV
ons.topic.reward=T_ACTIVITY_REWARD_KIKI_DEV
ons.topic.activity.event=T_EVENT_ACTIVITY_KIKI_DEV
ons.topic.activity.customer=T_CUSTOMER_EVENT_KIKI_DEV


#################elasticjob config ##################
#elasticjob.enabled=false
elasticjob.reg-center.server-lists=127.0.0.1:2181
elasticjob.reg-center.namespace=kactivity/jobs
elasticjob.reg-center.base-sleep-time-milliseconds=1000
elasticjob.reg-center.max-sleep-time-milliseconds=3000
elasticjob.reg-center.max-retries=5
elasticjob.reg-center.session-timeout-milliseconds=10000
elasticjob.reg-center.connection-timeout-milliseconds=10000


########################### ActivityAsyncEventBus ###################################
#æ´»å¨åå¥åçè°åº¦é»è®¤ä¸æ¬¡è°åº¦ç¬æ°ï¼åæºæ¨¡å¼ï¼
activity.dispatch.limit=100
#æ´»å¨åå¥åçè°åº¦ååurl
activity.dispatch.notifyurl=http://x-activity.beta.svc.kiki.local:8080/activity/jobexecute/
#å®æ¶ä»»å¡åæºï¼éç¾¤æ å¿
#activity.schedule.single=false
#ååç¬æ°ï¼éè¦èèHttpgetè¯·æ±æ¥æé¿åº¦ï¼è°åº¦èç¹æ¯æ¬¡åæ§è¡èç¹åéç¬æ°ï¼
#activity.page.num=@activity.page.num@
#æ´»å¨å®æ¶åå¥è°ç¨æ¶é´ï¼åæºæ¨¡å¼ï¼
#activity.time.reward.time=@activity.time.reward.time@
kactivity.luck.event-topic=test_topic
# kactivity.luck.operate-customer=test_customer

########################### OSS ###################################
app.oss.rolename=ECSDipbitRoleBeta
app.oss.endpoint=http://oss-ap-southeast-1.aliyuncs.com
app.oss.bucket.private=kiki-dev
app.oss.path.activity=activity
app.oss.activity.kolExtraReward.key=KolExtraReward.csv
app.oss.accesskey.id=LTAI5t61acrmFKw5SnkGQyYg
app.oss.accesskey.secret=******************************
app.oss.bucket.referer=http://admin.dev.dipbit.xyz/

###########################redis###################################
spring.data.redis.host=127.0.0.1
spring.data.redis.port=6379
#spring.redis.password=
#spring.redis.database=5
spring.data.redis.lettuce.pool.max-active=32

########################### mybatis ###################################
mybatis.mapper-locations=classpath:com/kikitrade/activity/dal/mysql/dao/mapper/*.xml
#### tk mapper ####
mapper.mappers=tk.mybatis.mapper.common.Mapper
mapper.notEmpty=true

################################ springbatch ####################################
spring.batch.job.enabled=false
spring.batch.jdbc.initialize-schema=always
spring.batch.jdbc.table-prefix=kactivity_
spring.batch.jdbc.schema=classpath:com/kikitrade/activity/dal/mysql/schema-mysql.sql

################################ KactivityProperties ####################################
batch.reward-shard=1
batch.csv.path=/Users/<USER>/csv/
batch.csv.head=businessId,customerId,phone,email,amount,currency,reward_time,reward_type,status,message
batch.csv.show-head=Award ID,Customer ID,Phone Number,Email,Award Amount,Award,Award Time,Award Type,Award Status,Reason
batch.reward.retry-job-count=10
batch.inteface-limiter=rewardService:20;remoteCustomerService:100;rewardAssert:100;rewardToken:100;
batch.import.max-job-count=5

###########################notice#########################
reward.notice-switch=true
activity.area-sort=HK,EN,CN
material.type-properties=
material.type-properties-base=titleCN,titleHK,titleEN,descCN,descHK,descEN,awardRuleCN,awardRuleHK,awardEN,button

##################################odps################################
app.odps.accessId=LTAIZr2b7AyF0bNC
app.odps.accessKey=******************************
app.odps.endPoint=http://service.ap-southeast-1.maxcompute.aliyun-inc.com/api
app.odps.project=dbit_beta

##################################nacos################################
#spring.cloud.nacos.config.namespace=d35ed129-2c19-4870-ab9c-25ad7f149440
#spring.cloud.nacos.config.server-addr=127.0.0.1:8848
spring.cloud.nacos.config.enabled=false


################################## å¶ä»éç½®################################
saas-id=kiki
manage.grpc.port=30880
#spring çº¿ä¸ç¯å¢å¯å¨, é»è®¤ä¸ºçº¿ä¸ç¯å¢ï¼ éåéç½®èªå¨æ¿æ¢çæ¹å¼
debug=true
spring.servlet.multipart.max-file-size=10MB
server.port=8082
spring.zipkin.baseUrl=http://zipkin.kikitrade-dev.svc.kiki.local:9411
spring.sleuth.sampler.percentage=1.0
zipkin.host=jaeger-collector.beta.svc.kiki.local
log.alarm.owner.config={"***********":"spot_trade,margin_trade,copy_trade,activity,member", "***********":"market,trader"}
spring.aop.auto=true



###########################dubbo.mock config###############################
dubbo.mock.enable=true
dubbo.mock.interfaceName[0]=com.kikitrade.ksocial.api.service.RemoteSocialService
dubbo.mock.interfaceName[1]=com.kikitrade.member.api.RemoteMemberService
dubbo.mock.interfaceName[2]=com.kikitrade.accounting.api.RemoteProductAccountService
dubbo.mock.interfaceName[3]=com.kikitrade.accounting.api.RemoteAccountingOperateService
dubbo.mock.interfaceName[4]=com.kikitrade.kcustomer.api.service.RemoteCustomerExtraService
dubbo.mock.interfaceName[5]=com.kikitrade.asset.api.RemoteAssetOperateService
dubbo.mock.interfaceName[6]=com.kikitrade.trade.api.RemoteTradingService
dubbo.mock.interfaceName[7]=com.kikitrade.accounting.api.domain.activity.RemoteAccountingActivityService
dubbo.mock.interfaceName[8]=com.kikitrade.kcustomer.api.service.RemoteCustomerService
dubbo.mock.interfaceName[9]=com.kikitrade.kcustomer.api.service.RemoteNotificationService
dubbo.mock.interfaceName[10]=com.kikitrade.kcustomer.api.service.RemoteBlockBusinessService
dubbo.mock.interfaceName[11]=com.kikitrade.quota.api.RemoteQuotaConfigService
dubbo.mock.interfaceName[12]=com.kikitrade.quota.api.RemoteQuotaService
dubbo.mock.interfaceName[13]=com.kikitrade.kcustomer.api.service.RemoteCustomerBindService

###########################kactivity config###############################
kactivity.luck.valid-minutes=15
kactivity.reward.ding-talk-url=test_ding_url
activity.nft.chain=test_chain
#kactivity.rank.badge-id=2,3,4,5,6
#kactivity.rank.start-time=**********
#kactivity.rank.end-time=**********
kactivity.rank.saasId=monster-test

kactivity.rank.season-start-time=*************


kactivity.rank.bread-season-time.season1.start-time=**********
kactivity.rank.bread-season-time.season1.end-time=1757560598
kactivity.rank.bread-badge-id.season1=26,27,28,29,30

activity.quests.dingtalk.url=https://oapi.dingtalk.com/robot/send?access_token=3040029b5caf9d5ad25cae709065efeadc2e4c6bae97e6e5e378d2cb8ac22b30


#?????id??
task-white-properties=2024071906435660016061

#?????????
task.white.date=5

activity.precision.metrics.tunnelId=https://otsnext.console.aliyun.com/ap-southeast-1/QuestsBeta/precision_metrics/tunnelService














