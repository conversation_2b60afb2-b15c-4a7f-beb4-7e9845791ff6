package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.SchedLog;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

public interface SchedLogDao extends BaseDao<SchedLog, String> {

    SchedLog findByBatch(@Param("batch_pt") String batch_pt, @Param("job_nm") String job_nm);

    SchedLog findByLatestBatch( @Param("job_nm") String job_nm);

}
