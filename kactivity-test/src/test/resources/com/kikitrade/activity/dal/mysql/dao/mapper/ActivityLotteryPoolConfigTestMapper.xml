<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityLotteryPoolConfigTestDao">
    <resultMap id="BaseResultMap" type="com.kikitrade.activity.dal.mysql.model.ActivityLotteryPoolConfig">

        <result column="lottery_id" property="lotteryId"/>
        <result column="draw_id" property="drawId"/>
        <result column="pool_no" property="poolNo"/>
        <result column="remain_num" property="remainNum"/>


    </resultMap>

    <update id="createTableIfNotExist">
        CREATE TABLE `activity_lottery_pool_config`
        (
            `id`         varbinary(64) NOT NULL,
            `lottery_id` varchar(32)  DEFAULT NULL COMMENT '奖池id',
            `draw_id`    varchar(32)  DEFAULT NULL COMMENT '奖品id',
            `pool_no`    varchar(128) DEFAULT NULL COMMENT '奖品名称',
            `remain_num` int(11) DEFAULT NULL COMMENT '剩余数量',
            `created`    datetime    NOT NULL COMMENT '创建时间',
            `modified`   datetime    NOT NULL COMMENT '修改时间',
            `saasId`     varchar(16) NOT NULL COMMENT 'saasId',
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='抽奖活动子奖池配置'
        ;
    </update>

    <update id="updateRemainNumByCondition">
        update activity_lottery_pool_config set remain_num = 0 where draw_id = #{drawId} and pool_no in
        <foreach
                collection="poolNos" item="poolNo" separator="," open="(" close=")">
            #{poolNo}
        </foreach>
    </update>

    <select id="selectAll" resultMap="BaseResultMap">
        select *
        from activity_lottery_pool_config
    </select>

    <select id="getSumRemainNumByCondition" resultType="java.lang.Integer">
        select sum(remain_num) from activity_lottery_pool_config where draw_id = #{drawId} and pool_no in
        <foreach
                collection="poolNos" item="poolNo" separator="," open="(" close=")">
            #{poolNo}
        </foreach>

    </select>

    <select id="selectByDrawId" resultMap="BaseResultMap">
        select *
        from activity_lottery_pool_config
        where draw_id = #{drawId};
    </select>

    <select id="getRemainNumByCondition" resultType="java.lang.Integer">
        select remain_num
        from activity_lottery_pool_config
        where draw_id = #{drawId}
          and pool_no = #{poolNo}
    </select>

</mapper>