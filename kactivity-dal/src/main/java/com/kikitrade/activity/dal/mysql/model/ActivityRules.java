package com.kikitrade.activity.dal.mysql.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/7/11 17:07
 */
@Getter
@Setter
@Table(name = "activity_rules")
public class ActivityRules extends BaseModel {

    @Id
    //规则id（主键）
    //@GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Integer id;  //id

    //创建时间
    @Column(name = "create_time", nullable = false)
    private Date create_time;
    //更新时间
    @Column(name = "update_time", nullable = false)
    private Date update_time;

    //rule_name for redis use
    @Column(name = "rule_name", nullable = false)
    private String rule_name;

    //规则描述，用于管理端新建活动时展示
    @Column(name = "desc", nullable = false)
    private String desc;

    //规则优先级，哪些规则优先执行
    @Column(name = "priority", nullable = false)
    private Integer priority;

    //0-失效 1-生效
    @Column(name = "status",  nullable = false)
    private Integer status;

    //规则参数，用于个性化配置规则执行，前期设计Json存储
    @Column(name = "params")
    private String params;





}
