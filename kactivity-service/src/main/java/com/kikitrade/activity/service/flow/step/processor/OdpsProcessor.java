package com.kikitrade.activity.service.flow.step.processor;

import com.alibaba.fastjson.JSON;
import com.aliyun.odps.Column;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.data.Record;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchRewardRoster;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.flow.AbstractDataService;
import com.kikitrade.activity.service.importing.roster.RewardImportingProcess;
import com.kikitrade.activity.service.importing.roster.impl.RewardSeq;
import com.kikitrade.kseq.api.SeqAcquireService;
import com.kikitrade.market.client.CurrencyClient;
import com.kikitrade.market.client.TickerClient;
import com.kikitrade.market.common.model.CurrencyDTO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-05 14:30
 */
@Component
@StepScope
@Slf4j
public class OdpsProcessor extends AbstractDataService implements ItemProcessor<Record, ActivityBatchRewardRoster> {

    @Value("#{jobParameters['batchId']}")
    private String batchId;
    @Value("#{jobParameters['activityId']}")
    private String activityId;
    @Resource
    private RewardSeq rewardSeq;
    @Resource
    private CurrencyClient currencyClient;
    @Resource
    private ActivityBatchNewService activityBatchNewService;

    @Override
    public ActivityBatchRewardRoster process(@NotNull Record record) throws Exception {
        ActivityBatchRewardRoster roster = new ActivityBatchRewardRoster();
        roster.setBatchId(batchId);
        ActivityEntity activityEntity = activityCache.getUnchecked(activityId);
        roster.setSeq(getSeq(activityEntity == null ? ActivityConstant.ActivityTypeEnum.CUSTOMIZE.name() : activityEntity.getType() ,batchId));
        List<String> names = Arrays.stream(record.getColumns()).map(Column::getName).collect(Collectors.toList());
        if(names.contains(ColumnName.CUSTOMERID)){
            roster.setCustomerId(getValue(ColumnName.CUSTOMERID, record));
        }
        if(names.contains(ColumnName.VIPLEVEL)){
            roster.setVipLevel(getValue(ColumnName.VIPLEVEL, record));
        }
        if(names.contains(ColumnName.SCORE)){
            roster.setScope(getValue(ColumnName.SCORE, record));
        }
        if(names.contains(ColumnName.AMOUNT)){
            roster.setAmount(getValue(ColumnName.AMOUNT, record));
        }
        if(names.contains(ColumnName.REWARDTYPE)){
            roster.setRewardType(getValue(ColumnName.REWARDTYPE, record));
            if(!isUpdateRewardType){
                ActivityBatch activityBatch = activityBatchNewService.findByBatchId(batchId);
                activityBatch.setRewardType(getValue(ColumnName.REWARDTYPE, record));
                activityBatchNewService.updateBatch(activityBatch);
                isUpdateRewardType = true;
            }
        }
        if(names.contains(ColumnName.CURRENCY)){
            roster.setCurrency(getValue(ColumnName.CURRENCY, record));
        }
        if(names.contains(ColumnName.SIDE)){
            roster.setSide(getValue(ColumnName.SIDE, record));
        }
        if(names.contains(ColumnName.AMOUNT) && names.contains(ColumnName.REWARDTYPE) && names.contains(ColumnName.CURRENCY)){
            CurrencyDTO currencyDTO = currencyClient.get(roster.getCurrency());
            if(currencyDTO == null){
                currencyDTO = new CurrencyDTO();
                currencyDTO.setKeepDecimalForCoin(8);
            }
            Integer keepDecimalForCoin = currencyDTO.getKeepDecimalForCoin();
            BigDecimal decimal = new BigDecimal(roster.getAmount()).setScale(keepDecimalForCoin, RoundingMode.DOWN);
            roster.setAmount(decimal.toPlainString());
        }
        if(names.contains(ColumnName.REFERID)){
            roster.setReferId(getValue(ColumnName.REFERID, record));
        }
        if(names.contains(ColumnName.NICKNAME)){
            roster.setNickName(getValue(ColumnName.NICKNAME, record));
        }
        roster.setStatus(ActivityConstant.ImportStatusEnum.NOT_IMPORTED.name());
        return roster;
    }

    private String getValue(String name, Record record){
        Optional<Column> column = Arrays.stream(record.getColumns()).filter(c -> c.getName().equalsIgnoreCase(name)).findFirst();
        if(!column.isPresent()){
            return null;
        }
        switch (column.get().getTypeInfo().getOdpsType()){
            case DECIMAL:
                return String.valueOf(record.getDecimal(name));
            default:
                return record.getString(name);
        }
    }

    private String getSeq(String type, String batchId){
        switch (ActivityConstant.ActivityTypeEnum.value(type)){
            case INVITE:
                return rewardSeq.of(ActivityConstant.SeqPrefix.INVITE_SEQ, batchId).getValue();
            case HIERARCHY:
                return rewardSeq.of(ActivityConstant.SeqPrefix.HIERARCHY_SEQ, batchId).getValue();
            case NORMAL:
                return rewardSeq.of(ActivityConstant.SeqPrefix.NORMAL_SEQ, batchId).getValue();
            default:
                return rewardSeq.of(ActivityConstant.SeqPrefix.CUSTOMIZE_SEQ, batchId).getValue();
        }
    }


    public static class ColumnName{
        private static final String CUSTOMERID = "customerid";
        private static final String VIPLEVEL = "viplevel";
        private static final String SCORE = "score";
        private static final String AMOUNT = "amount";
        private static final String REWARDTYPE = "rewardtype";
        private static final String CURRENCY = "currency";
        private static final String SIDE = "side";
        private static final String REFERID = "referid";
        private static final String NICKNAME = "nickname";
    }
}
