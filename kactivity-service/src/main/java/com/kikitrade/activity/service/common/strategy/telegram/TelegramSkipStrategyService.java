package com.kikitrade.activity.service.common.strategy.telegram;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/22 18:40
 * @description: Telegram跳过策略服务
 */
@Component
@Slf4j
public class TelegramSkipStrategyService implements TelegramSaasStrategyService{

    @Override
    public String strategy() {
        return SaasStrategyConstant.TelegramStrategyEnum.SKIP.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        log.info("TelegramSkipStrategyService execute, request: {}", request);
        // 跳过策略，不执行任何操作
    }
}
