package com.kikitrade.activity.dal.tablestore.model;

import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import com.kikitrade.framework.ots.annotations.Transient;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@Table(name = "activity_user_total_data")
public class ActivityUserTotalData {

    //币种
    @PartitionKey(name = "batch_no")
    private Date batchNo;

    //活动ID
    @PartitionKey(value = 1, name = "activity_id")
    private Integer activityId;

    //币种
    @PartitionKey(value = 2, name = "currency")
    private String currency;

    //用户ID
    @PartitionKey(value = 3, name = "customer_id")
    private String customerId;

    //活动类型
    @Column(name = "activity_type", isDefined = true)
    private Integer activityType;

    //交易总笔数
    @Column(name = "total_num", isDefined = true)
    private Integer totalNum;

    //手续费总金额
    @Column(name = "total_amount", isDefined = true)
    private BigDecimal totalAmount;


    @Column(name = "status", isDefined = true)
    private Integer status;

    @Column(name = "remark", type = Column.Type.STRING)
    private String remark;

    @Column(name = "created")
    private Date created;


    @Column(name = "modified")
    private Date modified;

    @Column(name = "saas_id")
    private String saasId;

    @Transient
    private Integer oldStatus;
}
