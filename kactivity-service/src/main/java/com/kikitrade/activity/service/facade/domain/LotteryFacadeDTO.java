package com.kikitrade.activity.service.facade.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LotteryFacadeDTO implements Serializable {

    //奖池id
    private String id;
    //valid
    private String valid;
    //remark
    private String remark;
    //status
    private String status;
    //规则vip等级
    private String vipLevel;
    //消耗燃料（次）
    private String amount;
    //参与次数
    private String timesLimit;
    //奖励金额上限
    private String rewardLimit;
    //奖品url
    private List<LotteryItemFacadeDTO> itemList;
}
