package com.kikitrade.activity.dal.mysql.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;

@MappedSuperclass
@Getter
@Setter
public class BaseModel implements Serializable {
    private static final long serialVersionUID = -4825890686624512635L;
    /**
     * SassId
     */
    @Column(name = "saasId")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    private String saasId;

    /**
     * 创建时间
     */
    @Column(name = "created")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    private Date created;
    /**
     * 修改时间
     */
    @Column(name = "modified")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    private Date modified;

}
