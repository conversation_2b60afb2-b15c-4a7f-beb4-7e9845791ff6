<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kikitrade.activity.dal.mysql.dao.ActivityActionMapDao">


    <!-- 增加 -->
    <insert id="insert" parameterType="com.kikitrade.activity.dal.mysql.model.ActivityActionMap">
        INSERT INTO activity_action_map VALUES (
            #{activity_id},
            #{action_id},
            #{action_name},
            #{priority},
            #{status},
            #{params}
        )
    </insert>

    <delete id="deleteById">
        DELETE FROM activity_action_map
        WHERE activity_id = #{activity_id}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">

        INSERT INTO activity_action_map (activity_id, action_id)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">

            (
            #{item.activity_id,jdbcType=INTEGER},
            #{item.action_id,jdbcType=INTEGER},
            #{item.params,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>


    <select id="findAll" resultType="com.kikitrade.activity.dal.mysql.model.ActivityActionMap">
        SELECT *
        FROM activity_action_map
        ORDER BY activity_id DESC
        LIMIT #{offset}, #{limit}
    </select>


    <select id="findByActivityId" resultType="com.kikitrade.activity.dal.mysql.model.ActivityActionMap">
        SELECT *
        FROM activity_action_map
        WHERE activity_id = #{activity_id}

    </select>

    <!--<insert id="insertBatch">-->
    <!--INSERT INTO t_user-->
    <!--(id, name, del_flag)-->
    <!--VALUES-->
    <!--<foreach collection ="list" item="user" separator =",">-->
    <!--(#{user.id}, #{user.name}, #{user.delFlag})-->
    <!--</foreach >-->
    <!--</insert>-->


</mapper>
