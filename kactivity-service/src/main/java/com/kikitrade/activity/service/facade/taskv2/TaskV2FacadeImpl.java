package com.kikitrade.activity.service.facade.taskv2;

import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.facade.taskv2.CodeVO;
import com.kikitrade.activity.facade.taskv2.CodeVOResponse;
import com.kikitrade.activity.facade.taskv2.DubboTaskFacadeTriple;
import com.kikitrade.activity.facade.taskv2.EmptyRequest;
import com.kikitrade.activity.facade.taskv2.IdVOResponse;
import com.kikitrade.activity.facade.taskv2.RewardDTO;
import com.kikitrade.activity.facade.taskv2.SubTaskDTO;
import com.kikitrade.activity.facade.taskv2.TaskDTO;
import com.kikitrade.activity.facade.taskv2.TaskFacadeGrpc;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.task.TaskConfigService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.config.annotation.DubboService;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/27 15:02
 */
@DubboService
public class TaskV2FacadeImpl extends DubboTaskFacadeTriple.TaskFacadeImplBase {

    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private KactivityModuleProperties kactivityModuleProperties;

    @Override
    public void save(TaskDTO request, StreamObserver<IdVOResponse> responseObserver) {
        try{
            IdVOResponse.Builder response = IdVOResponse.newBuilder();
            TaskConfigDTO configDTO = buildMainConfig(request);
            response.setId(configDTO.getTaskId());
            List<TaskConfigDTO> configDTOS = buildSubConfig(request, configDTO.getTaskId());
            for (TaskConfigDTO config : configDTOS){
                response.addSubId(config.getTaskId());
                taskConfigService.upsert(config, false);
            }
            taskConfigService.upsert(configDTO, false);
            response.setSuccess(true);
            response.setMessage("success");
            responseObserver.onNext(response.build());
        }catch (Exception ex){
            responseObserver.onError(ex);
        }
        responseObserver.onCompleted();
    }

    private TaskConfigDTO buildMainConfig(TaskDTO request){
        TaskConfigDTO taskConfigDTO = new TaskConfigDTO();
        if(StringUtils.isBlank(request.getId())){
            taskConfigDTO.setTaskId(taskConfigService.nextId(false));
        }else{
            taskConfigDTO.setTaskId(request.getId());
        }
        taskConfigDTO.setIsGroup(true);
        taskConfigDTO.setTitle(request.getTitle());
        taskConfigDTO.setDesc(request.getDesc());
        taskConfigDTO.setLabelName(request.getLabelContent());
        taskConfigDTO.setLabelColor(request.getLabelColor());

        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("detail", request.getDetailImage());
        imageMap.put("list", request.getListImage());
        imageMap.put("share", request.getShareImage());
        taskConfigDTO.setImage(imageMap);

        taskConfigDTO.setSaasId(request.getSaasId());
        taskConfigDTO.setStatus(ActivityConstant.CommonStatus.valueOf(request.getStatus().name()));
        taskConfigDTO.setStartTime(TimeUtil.parse(request.getStartTime()).getTime());
        if(StringUtils.isNotBlank(request.getEndTime())){
            taskConfigDTO.setEndTime(TimeUtil.parse(request.getEndTime()).getTime());
        }

        Map<String, String> attrMap = new HashMap<>();
        attrMap.put("twitter-subject", request.getTwitterSubject());
        attrMap.put("twitter-to", request.getTwitterTo());
        taskConfigDTO.setAttr(attrMap);

        if(request.getSubTaskDTOCount() <= 0){
            Map<String, Integer> limitMap = new HashMap<>();
            limitMap.put("NORMAL", request.getLimit());
            limitMap.put("L1", request.getLimitVip());
            taskConfigDTO.setLimitMap(limitMap);

            taskConfigDTO.setCode(request.getCode());
            taskConfigDTO.setRewardFrequency(request.getRewardFrequency());
            taskConfigDTO.setCycle(ActivityTaskConstant.TaskCycleEnum.valueOf(request.getCycle().name()));
            taskConfigDTO.setProgressType(ActivityTaskConstant.ProgressTypeEnum.valueOf(request.getProgressType().name()));
            taskConfigDTO.setRewardForm(ActivityTaskConstant.RewardForm.valueOf(request.getRewardForm().name()));
            taskConfigDTO.setProvideType(ActivityTaskConstant.ProvideType.valueOf(request.getProvideType().name()));
            List<RewardDTO> rewardDTOList = request.getRewardDTOList();
            Map<String, List<Award>> rewardMap = new HashMap<>();
            for(RewardDTO rewardDTO : rewardDTOList){
                Award award = new Award();
                award.setAmount(rewardDTO.getAmount());
                award.setCurrency(rewardDTO.getCurrency());
                award.setType(rewardDTO.getType().name());
                award.setVipLevel(rewardDTO.getVipLevel().name());
                if(rewardMap.containsKey(String.valueOf(rewardDTO.getConsecutiveTimes()))){
                    List<Award> awards = rewardMap.get(String.valueOf(rewardDTO.getConsecutiveTimes()));
                    award.setIndex(awards.size() + 1);
                    awards.add(award);
                    rewardMap.put(String.valueOf(rewardDTO.getConsecutiveTimes()), awards);
                }else{
                    List<Award> awards = new ArrayList<>();
                    award.setIndex(1);
                    awards.add(award);
                    rewardMap.put(String.valueOf(rewardDTO.getConsecutiveTimes()), awards);
                }
            }
            taskConfigDTO.setReward(rewardMap);
        }
        return taskConfigDTO;
    }

    private List<TaskConfigDTO> buildSubConfig(TaskDTO request, String groupId) throws MalformedURLException {
        List<TaskConfigDTO> configs = new ArrayList<>();
        for(SubTaskDTO config : request.getSubTaskDTOList()){
            TaskConfigDTO taskConfigDTO = new TaskConfigDTO();
            if(StringUtils.isBlank(config.getId())){
                taskConfigDTO.setTaskId(taskConfigService.nextId(true));
            }else{
                taskConfigDTO.setTaskId(config.getId());
            }
            taskConfigDTO.setGroupId(groupId);
            taskConfigDTO.setIsGroup(false);
            taskConfigDTO.setTitle(config.getTitle());
            taskConfigDTO.setSaasId(request.getSaasId());
            taskConfigDTO.setStatus(ActivityConstant.CommonStatus.valueOf(request.getStatus().name()));
            taskConfigDTO.setStartTime(TimeUtil.parse(request.getStartTime()).getTime());
            if(StringUtils.isNotBlank(request.getEndTime())){
                taskConfigDTO.setEndTime(TimeUtil.parse(request.getEndTime()).getTime());
            }
            taskConfigDTO.setCode(config.getCode());
            Map<String, Integer> limitMap = new HashMap<>();
            limitMap.put("NORMAL", request.getLimit());
            limitMap.put("L1", request.getLimitVip());
            taskConfigDTO.setLimitMap(limitMap);

            taskConfigDTO.setRewardFrequency(request.getRewardFrequency());
            taskConfigDTO.setCycle(ActivityTaskConstant.TaskCycleEnum.valueOf(request.getCycle().name()));
            taskConfigDTO.setProgressType(ActivityTaskConstant.ProgressTypeEnum.valueOf(request.getProgressType().name()));
            taskConfigDTO.setRewardForm(ActivityTaskConstant.RewardForm.valueOf(request.getRewardForm().name()));
            taskConfigDTO.setProvideType(ActivityTaskConstant.ProvideType.valueOf(request.getProvideType().name()));
            taskConfigDTO.setUrl(config.getUrl());
            URL url = new URL(config.getUrl());
            taskConfigDTO.setDomain(getDomain(url.getHost()));
            taskConfigDTO.setConnectUrl(getConnectUrl(taskConfigDTO.getSaasId(), taskConfigDTO.getDomain()));
            taskConfigDTO.setImage(new HashMap<>(){{put("icon", config.getIcon());}});
            taskConfigDTO.setOrder(config.getOrder());
            configs.add(taskConfigDTO);
        }
        return configs;
    }

    @Override
    public void getCode(EmptyRequest request, StreamObserver<CodeVOResponse> responseObserver) {
        Map<String, TaskCodeConfig> all = TaskCodeConfig.getAll();
        List<CodeVO> list = new ArrayList<>();
        for(Map.Entry<String, TaskCodeConfig> entry : all.entrySet()){
            list.add(CodeVO.newBuilder()
                    .setCode(entry.getValue().getCode())
                    .setDesc(entry.getValue().getDesc())
                    .build());
        }
        CodeVOResponse response = CodeVOResponse.newBuilder()
                .setSuccess(true)
                .addAllData(list)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    private String getConnectUrl(String saasId, String domain){
        if("twitter".equals(domain)){
            return kactivityModuleProperties.getReward().getTwitterConnectUrl().get(saasId);
        }else if("discord".equals(domain)){
            return kactivityModuleProperties.getReward().getDiscordConnectUrl().get(saasId);
        }
        return null;
    }

    private String getDomain(String host){
        if(host.contains("twitter")){
            return "twitter";
        }else if(host.contains("discord")){
            return "discord";
        }
        return null;
    }
}
