package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCumulateItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus;
import com.kikitrade.activity.dal.tablestore.model.ActivityCumulateItem;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/22 20:05
 */
@Service
@Slf4j
public class ActivityCumulateItemBuilderImpl extends WideColumnStoreBuilder<ActivityCumulateItem> implements ActivityCumulateItemBuilder {

    @PostConstruct
    public void init() {
        init(ActivityCumulateItem.class);
    }

    @Override
    public String getTableName() {
        return "activity_cumulate_item";
    }

    @Override
    public boolean insert(ActivityCumulateItem activityCumulateItem) {
        return super.putRow(activityCumulateItem, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean update(ActivityCumulateItem activityCumulateItem) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(activityCumulateItem, condition);
    }
}
