package com.kikitrade.activity.service.reward.impl;

import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.asset.api.RemoteLimitedTimeAssetService;
import com.kikitrade.asset.model.ExtraBoostDTO;
import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/11/20 20:34
 * @description: 领取额外加成
 */
@Slf4j
@Service("rewardExtraBoostTccService")
public class RewardExtraBoostTccServiceImpl extends AbstractRewardTccService implements RewardTccService {

    @DubboReference
    private RemoteLimitedTimeAssetService remoteLimitedTimeAssetService;
    @Resource
    private SeqClient seqClient;

    @Override
    public void tryReward(RewardRequest request) throws Exception {
        log.info("tryReward request: {}", JSON.toJSONString(request));
        ExtraBoostDTO extraBoostDTO = new ExtraBoostDTO();
        extraBoostDTO.setId(seqClient.next(new SeqRule("IDX_LOTTERY_EXTRA_BOOST", 1, "yyyyMMddHHmmss", null)));
        extraBoostDTO.setSaasId(request.getSaasId());
        extraBoostDTO.setCustomerId(request.getCustomerId());
        extraBoostDTO.setBoostValue(request.getAmount());
        extraBoostDTO.setExpireTime(request.getReceiveEndTime());
        boolean res = remoteLimitedTimeAssetService.saveExtraBoost(extraBoostDTO);
        if (!res) {
            log.error("tryReward saveExtraBoost failed!");
        }
    }

}
