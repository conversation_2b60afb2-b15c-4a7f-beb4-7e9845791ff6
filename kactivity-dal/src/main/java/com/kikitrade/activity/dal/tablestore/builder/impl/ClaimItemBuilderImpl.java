package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilder;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.kikitrade.activity.dal.tablestore.builder.ClaimItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ClaimItem;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/9 11:27
 */
@Component
public class ClaimItemBuilderImpl extends WideColumnStoreBuilder<ClaimItem> implements ClaimItemBuilder {

    @PostConstruct
    public void init() {
        super.init(ClaimItem.class);
    }

    @Override
    public String tableName() {
        return "claim_item";
    }

    @Override
    public Boolean batchInsert(List<ClaimItem> claimItems) {
        return super.batchPutRow(claimItems, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public Boolean insert(ClaimItem claimItems) {
        return super.putRow(claimItems, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public ClaimItem getById(String businessType, String code, String created) {
        ClaimItem claimItem = new ClaimItem();
        claimItem.setCode(code);
        claimItem.setCreated(created);
        return getRow(claimItem);
    }

    @Override
    public Boolean exist(String businessType, String customerId, String created) {
        BoolQuery query = QueryBuilders.bool()
                .must(QueryBuilders.term("business_type", businessType))
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("created", created))
                .build();

        return super.searchOne(query, ClaimItem.SEARCH_CUSTOMER) != null;
    }

    @Override
    public long countByIds(String businessType, List<String> codes, String created) {

        String aggName = "claim_item_count_code_by_code";
        BoolQuery.Builder must = QueryBuilders.bool()
                .must(QueryBuilders.term("business_type", businessType))
                .must(QueryBuilders.term("created", created))
                .must(QueryBuilders.terms("code").terms(codes.toArray()));

        SearchQuery query = SearchQuery.newBuilder().query(must).addAggregation(AggregationBuilders.count(aggName, "code")).build();

        return searchCount(query, ClaimItem.SEARCH_CUSTOMER, aggName);
    }
}
