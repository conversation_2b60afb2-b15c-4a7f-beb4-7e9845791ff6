package com.kikitrade.activity.service.reward.impl;

import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.service.common.config.EarnallianceProperties;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("rewardEarnallianceService")
@Slf4j
public class RewardEarnallianceServiceImpl extends AbstractRewardTccService implements RewardTccService {

    @Resource
    private KactivityModuleProperties kactivityModuleProperties;

    @Override
    public void tryReward(RewardRequest request) throws Exception {
        //发送游戏事件
        sendGameEvent(request);
    }

    private void sendGameEvent(RewardRequest request){
        // 发送游戏事件
        try{
            EarnallianceProperties properties = EarnallianceUtil.getProperties(kactivityModuleProperties.getEarnalliance());
            Map<String, Object> event = new HashMap<>();
            event.put("userId", request.getCustomerId());
            event.put("event", "PLAY_GAME");
            event.put("value", request.getAmount().intValue());
            event.put("time", new Date());

            Map<String, Object> map = new HashMap<>();
            map.put("gameId", properties.getGameId());
            map.put("events", List.of(event));

            long now = new Date().getTime();
            String signature = EarnallianceUtil.getSignature(properties.getClientId(), properties.getClientSecret(), now, map);

            Headers.Builder headerBuilder = new Headers.Builder()
                    .add("x-client-id", properties.getClientId())
                    .add("x-timestamp", String.valueOf(now))
                    .add("x-signature", signature);

            HttpUrl.Builder urlBuilder = HttpUrl.parse(properties.getUrl() + "custom-events").newBuilder();

            Request httpRequest = new Request.Builder()
                    .url(urlBuilder.build())
                    .headers(headerBuilder.build())
                    .post(RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(map)))
                    .build();
            log.info("sendGameEvent request:{}", request);
            Response response = HttpPoolUtil.getHttpClient().newCall(httpRequest).execute();
            log.info("sendGameEvent response:{}", response.body().string());
        }catch (Exception e) {
            log.error("sendGameEvent error", e);
        }
    }
}
