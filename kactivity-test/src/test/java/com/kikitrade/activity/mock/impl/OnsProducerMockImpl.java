package com.kikitrade.activity.mock.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.activity.luck.service.model.AirDropEventDTO;
import com.kikitrade.activity.mock.OnsProducerMock;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.framework.ons.OnsProducer;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import static com.kikitrade.activity.model.constant.ActivityConstant.AirDropEventEnum.*;
import static com.kikitrade.activity.op.BusinessOP.mqMessageMap;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;

@Component
public class OnsProducerMockImpl implements OnsProducerMock {
    @Resource
    OnsProducer onsProducer;

    @Override
    public void send() {


        Mockito.doAnswer(c -> {
            String topic = c.getArgument(0);
            String event = c.getArgument(1);
            AirDropEventDTO airDropEventDTO = JSON.parseObject(event, AirDropEventDTO.class);
            switch (ActivityConstant.AirDropEventEnum.valueOf(airDropEventDTO.getEvent())) {
                case EXPIRE:
                    BusinessOP.expireEvent = event;
                    break;
                case REVERT:
                    BusinessOP.revertEvent = event;
                    break;
                case REFUND:
                    BusinessOP.refundEvent = event;
                    break;
                default:
                    break;
            }

            SendResult sendResult = new SendResult();
            sendResult.setTopic(topic);


            return sendResult;
        }).when(onsProducer).send(anyString(), anyString(), anyLong());


        Mockito.doAnswer(c -> {
            String topic = c.getArgument(0);
            String event = c.getArgument(1);

            mqMessageMap.put(topic, event);

            SendResult sendResult = new SendResult();
            sendResult.setTopic(topic);
            return sendResult;
        }).when(onsProducer).send(anyString(), anyString());
    }
}
