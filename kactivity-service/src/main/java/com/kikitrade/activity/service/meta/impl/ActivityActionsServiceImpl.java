package com.kikitrade.activity.service.meta.impl;

import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.dal.mysql.dao.ActivityActionsDao;
import com.kikitrade.activity.dal.mysql.model.ActivityActions;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.service.meta.ActivityActionsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ActivityActionsServiceImpl implements ActivityActionsService {

    @Resource
    private ActivityActionsDao activityActionsDao;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult save(ActivityActions activityActions) throws Exception {
        JsonResult result = new JsonResult();
        //  do some convert;
        try {
            int count = activityActionsDao.insert(activityActions);
            if (count > 0) {
                result.setObj(activityActions).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityactionsserviceimpl save process failed.", e);
            result.setObj(activityActions).setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
            throw e;
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult delete(Integer id) throws Exception {
        JsonResult result = new JsonResult();
        try {
            int count = activityActionsDao.deleteById(id);
            if (count >= 0) {
                result.setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());

            }
        } catch (Exception e) {
            log.error("activityactionsserviceimpl delete process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
            throw e;
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult insert(ActivityActions activityActions) throws Exception {
        JsonResult result = new JsonResult();
        try {

            int count = activityActionsDao.insert(activityActions);

            if (count > 0) {
                result.setObj(activityActions).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());

            }
        } catch (Exception e) {
            log.error("activityactionsserviceimpl insert process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
            throw e;
        }

        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult update(ActivityActions activityActions) throws Exception {
        JsonResult result = new JsonResult();
        if (activityActions.getId() == 0) {
            log.info("action id must be not empty -->{}", JSONObject.toJSONString(activityActions));
            return result.setSuccess(false).setCode(ActivityExceptionType.CAN_NOT_BE_NULL.getCode()).setMsg(ActivityExceptionType.CAN_NOT_BE_NULL.getParaMsg("activityactions [id] "));
        }
        int count = 0;
        ActivityActions ar = activityActionsDao.findById(activityActions.getId());
        try {
            if (ar == null) {
                count = activityActionsDao.insert(activityActions);
            } else {
                count = activityActionsDao.update(activityActions);
            }
            if (count > 0) {
                result.setObj(activityActions).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }

        } catch (Exception e) {
            log.error("activityactionsserviceimpl update process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
            throw e;
        }

        return result;
    }


    @Override
    public JsonResult findAll(Integer offset, Integer limit) {

        JsonResult result = new JsonResult();
        try {
            List<ActivityActions> activityActionsList = activityActionsDao.findAll(offset, limit);
            if (activityActionsList == null || activityActionsList.size() == 0) {
                result.setObj(activityActionsList).setSuccess(true).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                result.setObj(activityActionsList).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityactionsserviceimpl findAll process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }


    @Override
    public JsonResult findById(Integer id) {
        JsonResult result = new JsonResult();
        try {
            ActivityActions activityActions = activityActionsDao.findById(id);
            if (activityActions == null) {
                result.setObj(activityActions).setSuccess(true).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                result.setObj(activityActions).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityactionsserviceimpl findById process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }

    @Override
    public JsonResult findByType(Integer id) {
        JsonResult result = new JsonResult();
        try {
            List<ActivityActions> activityActions = activityActionsDao.findByType(id);
            if (activityActions == null || activityActions.size() == 0) {
                result.setObj(activityActions).setSuccess(true).setCode(ActivityExceptionType.NO_DATA_FOUND.getCode()).setMsg(ActivityExceptionType.NO_DATA_FOUND.getMessage());
            } else {
                result.setObj(activityActions).setSuccess(true).setCode(ActivityExceptionType.EXECUTION_SUCCEED.getCode()).setMsg(ActivityExceptionType.EXECUTION_SUCCEED.getMessage());
            }
        } catch (Exception e) {
            log.error("activityactionsserviceimpl findByType process failed.", e);
            result.setSuccess(false).setCode(ActivityExceptionType.UNKNOWN_REASON.getCode()).setMsg(ActivityExceptionType.UNKNOWN_REASON.getMessage());
        }
        return result;
    }

}
