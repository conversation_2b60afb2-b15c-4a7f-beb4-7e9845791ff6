package com.kikitrade.activity.service.reward.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSONObject;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.accounting.api.model.account.common.TransferType;
import com.kikitrade.activity.dal.tablestore.builder.CustomerRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.CustomerReward;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.service.mq.PhaseTwoMessageService;
import com.kikitrade.activity.service.reward.KikiActivityGiftTccService;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.kcustomer.api.constants.FirebaseTemplateType;
import com.kikitrade.kcustomer.api.model.FirebaseMessageDTO;
import com.kikitrade.kcustomer.api.service.RemoteNotificationService;
import com.kikitrade.kcustomer.common.constants.CustomerReferralConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.math.BigDecimal;

@Slf4j
@Component
public class KikiActivityGiftTccServiceImpl implements KikiActivityGiftTccService {

    @DubboReference
    private RemoteNotificationService remoteNotificationService;

    @Resource
    private RewardService rewardService;

    @Resource
    private CustomerRewardStoreBuilder customerRewardStoreBuilder;
    @Resource
    private PhaseTwoMessageService phaseTwoMessageService;


    @Override
    @Compensation(confirmMethod = "confirmFiatReward")
    public boolean tryFiatReward(CustomerReward reward) throws Exception {
        log.info("KikiActivityGiftTccServiceImpl tryFiatReward begin reward ...{}", JSONObject.toJSONString(reward));

        String currency = reward.isAutoExchange() ? reward.getQuoteCurrency() : reward.getCurrency();
        BigDecimal amount = reward.isAutoExchange() ? reward.getQuoteQuantity() : reward.getAmount();

        String businessId = reward.getOrderNum();
        // String optId = remoteVirtualAccountService.getUniqueOpIdByBusinessId(businessId, VirtualAccountConstants.BusinessType.FIAT_DEPOSIT.getCode());
        String customerId = reward.getCustomerId().equals(CustomerReferralConstants.EMPTY_ID) ? reward.getInvitee() : reward.getCustomerId();

        RewardRequest rewardRequest = RewardRequest.builder().rewardId(businessId).currency(currency).customerId(customerId).type(TransferType.REWARD.name()).build();

        ActivityResponse result = rewardService.reward(rewardRequest);

        log.info("KikiActivityGiftTccServiceImpl tryFiatReward result ...{}", JSONObject.toJSONString(result));
        if (!result.getCode().isSuccess()) {
            throw new IllegalStateException("fiatReward process fail");
        }

        return true;
    }

    public boolean confirmFiatReward(CustomerReward reward) throws Exception {
        log.info("KikiActivityGiftTccServiceImpl confirmFiatReward begin  ...{}", JSONObject.toJSONString(reward));
        customerRewardStoreBuilder.insertIfNotExist(reward);
        String currency = reward.isAutoExchange() ? reward.getQuoteCurrency() : reward.getCurrency();
        BigDecimal amount = reward.isAutoExchange() ? reward.getQuoteQuantity() : reward.getAmount();

        notify(reward, currency, amount);

        switch (ActivityConstant.SecondStep.valueOf(reward.getSecondStep())) {
            case SKIP:
                log.warn("KikiActivityGiftTccServiceImpl confirmFiatReward second step skip");
                break;
            case REWARD_INVITER:
                phaseTwoMessageService.sendSyncPhaseTwoMessage("KIKI_ACTIVITY_INVITE_FIRST_DEPOSIT", reward);
                break;
            case AUTO_EXCHANGE:
                phaseTwoMessageService.sendSyncPhaseTwoMessage("KIKI_ACTIVITY_GIFT", reward);
                break;
            default:
                log.warn("KikiActivityGiftTccServiceImpl confirmFiatReward second step is not defined");
                break;
        }
        log.info("KikiActivityGiftTccServiceImpl confirmFiatReward end  ...");
        return true;
    }

    /**
     * 发送app通知
     *
     * @param reward
     * @param currency
     * @param amount
     */
    private void notify(CustomerReward reward, String currency, BigDecimal amount) {
        JSONObject activityRewardPush = new JSONObject();
        activityRewardPush.put("customerId", reward.getCustomerId());
        activityRewardPush.put("currency", currency);
        activityRewardPush.put("amount", amount.toPlainString());
        activityRewardPush.put("orderNum", reward.getBusinessId());

        FirebaseMessageDTO firebaseMessageDTO = FirebaseMessageDTO.builder()
                .customerId(reward.getCustomerId())
                .templateType(FirebaseTemplateType.activity_reward)
                .params(activityRewardPush)
                .build();
        remoteNotificationService.send(firebaseMessageDTO);
    }

}
