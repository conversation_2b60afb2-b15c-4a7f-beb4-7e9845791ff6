package com.kikitrade.activity.service.strategy;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.common.strategy.email.EmailSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.facebook.FacebookSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.line.LineSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.telegram.TelegramSaasStrategyService;
import com.kikitrade.activity.service.common.strategy.wallet.WalletSaasStrategyService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import jakarta.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2025/7/22 19:00
 * @description: 平台策略测试
 */
@SpringBootTest
@TestPropertySource(locations = "classpath:application-junit.properties")
public class PlatformStrategyTest {

    @Resource
    private FacebookSaasStrategyService facebookAuthRejectStrategy;

    @Resource
    private LineSaasStrategyService lineAuthRejectStrategy;

    @Resource
    private EmailSaasStrategyService emailAuthRejectStrategy;

    @Resource
    private TelegramSaasStrategyService telegramAuthRejectStrategy;

    @Resource
    private WalletSaasStrategyService walletAuthRejectStrategy;

    @Test
    public void testFacebookRejectStrategy() {
        // 测试Facebook拒绝策略
        assertEquals(SaasStrategyConstant.FacebookStrategyEnum.REJECT.name(), 
                    facebookAuthRejectStrategy.strategy());
        
        OpenStrategyAuthRequest request = OpenStrategyAuthRequest.builder()
                .saasId("test")
                .customerId("customer123")
                .authId("facebook123")
                .authHandleName("testUser")
                .build();
        
        // 这里应该不会抛出异常，因为没有绑定的账号
        assertDoesNotThrow(() -> facebookAuthRejectStrategy.execute(request));
    }

    @Test
    public void testLineRejectStrategy() {
        // 测试Line拒绝策略
        assertEquals(SaasStrategyConstant.LineStrategyEnum.REJECT.name(), 
                    lineAuthRejectStrategy.strategy());
        
        OpenStrategyAuthRequest request = OpenStrategyAuthRequest.builder()
                .saasId("test")
                .customerId("customer123")
                .authId("line123")
                .authHandleName("testUser")
                .build();
        
        assertDoesNotThrow(() -> lineAuthRejectStrategy.execute(request));
    }

    @Test
    public void testEmailRejectStrategy() {
        // 测试Email拒绝策略
        assertEquals(SaasStrategyConstant.EmailStrategyEnum.REJECT.name(), 
                    emailAuthRejectStrategy.strategy());
        
        OpenStrategyAuthRequest request = OpenStrategyAuthRequest.builder()
                .saasId("test")
                .customerId("customer123")
                .authId("email123")
                .authHandleName("testUser")
                .build();
        
        assertDoesNotThrow(() -> emailAuthRejectStrategy.execute(request));
    }

    @Test
    public void testTelegramRejectStrategy() {
        // 测试Telegram拒绝策略
        assertEquals(SaasStrategyConstant.TelegramStrategyEnum.REJECT.name(), 
                    telegramAuthRejectStrategy.strategy());
        
        OpenStrategyAuthRequest request = OpenStrategyAuthRequest.builder()
                .saasId("test")
                .customerId("customer123")
                .authId("telegram123")
                .authHandleName("testUser")
                .build();
        
        assertDoesNotThrow(() -> telegramAuthRejectStrategy.execute(request));
    }

    @Test
    public void testWalletRejectStrategy() {
        // 测试Wallet拒绝策略
        assertEquals(SaasStrategyConstant.WalletStrategyEnum.REJECT.name(), 
                    walletAuthRejectStrategy.strategy());
        
        OpenStrategyAuthRequest request = OpenStrategyAuthRequest.builder()
                .saasId("test")
                .customerId("customer123")
                .authId("wallet123")
                .authHandleName("testUser")
                .build();
        
        assertDoesNotThrow(() -> walletAuthRejectStrategy.execute(request));
    }
}
