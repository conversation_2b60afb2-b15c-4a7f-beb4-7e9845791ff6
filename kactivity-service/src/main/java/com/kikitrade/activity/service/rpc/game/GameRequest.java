package com.kikitrade.activity.service.rpc.game;

import cn.hutool.core.util.URLUtil;
import com.kikitrade.activity.service.common.config.GameProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.discord.HttpPoolUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.json.JSONObject;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/1/11 11:10
 */
@Data
@Slf4j
public class GameRequest extends AccessToken {

    private GameProperties gameProperties;

    public GameRequest(GameProperties gameProperties, AccessToken token){
        this.userId = token.getUserId();
        this.gameProperties = gameProperties;
    }

    public VerifyGameRequest buildJoinGameRequest(String outTaskId){
        VerifyGameRequest request = new VerifyGameRequest();
        request.setUrl(this.gameProperties.getVerifyUrl());
        request.setAddress(this.userId);
        request.setOutTaskId(outTaskId);
        return request;
    }

    @Data
    public class VerifyGameRequest {

        private String url;
        private String address;
        private String outTaskId;

        public Boolean execute(){
            Map<String, Object> param = new HashMap<>();
            param.put("verifyId", this.outTaskId);
            param.put("verifyAddress", this.address);
            String query = URLUtil.buildQuery(param, Charset.defaultCharset());

            Request request = new Request.Builder()
                    .url(String.format("%s?%s",url, query))
                    .build();
            try {
                Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
                String responseBody = response.body().string();
                JSONObject jsonObject = new JSONObject(responseBody);
                JSONObject data = jsonObject.getJSONObject("data");
                log.info("JoinGameRequest:{}", jsonObject);
                return !data.getBoolean("result");
            } catch (Exception e) {
                log.error("JoinGameRequest error:{},{}", this.outTaskId, this.address, e);
                return false;
            }
        }
    }
}
