package com.kikitrade.activity.service.goods;

import com.dipbit.dtm.client.domain.stock.StockOrderRequest;
import com.dipbit.dtm.client.domain.stock.StockOrderTccService;
import com.dipbit.dtm.client.domain.stock.StockWideColumnStoreBuilder;
import com.dipbit.dtm.client.domain.stock.constant.StockConstant;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.activity.api.model.request.GoodsOrderRequest;
import com.kikitrade.activity.dal.tablestore.builder.impl.GoodsStockBuilder;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.activity.dal.tablestore.model.GoodsStock;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.reward.RewardTccService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.asset.api.RemoteAssetOperateService;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.asset.model.request.AssetTransferOutRequest;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/1/3 14:59
 */
@Service
public class GoodsStockOrderTccService extends StockOrderTccService<GoodsStock> {

    @Resource
    private GoodsStockBuilder goodsStockBuilder;
    @Resource
    private RemoteAssetOperateService remoteAssetOperateService;
    @Resource
    private RewardTccService rewardTccService;
    @Resource
    private GoodsService goodsService;

    @Override
    protected StockWideColumnStoreBuilder stockBuilder() {
        return goodsStockBuilder;
    }

    @Override
    protected GoodsStock instance(String sku, String orderId, BigDecimal amount, StockConstant.Type type) {
        GoodsStock goodsStock = super.instance(sku, orderId, amount, type);
        goodsStock.setChannel("default");
        Goods goods = goodsService.findById(sku);
        goodsStock.setSkuName(goods.getName());
        return goodsStock;
    }

    @Compensation
    public void redeem(GoodsOrderRequest orderRequest, Goods goods, String address, String orderId) throws Exception{
        //扣积分
        AssetTransferOutRequest operation = new AssetTransferOutRequest();
        operation.setCustomerId(orderRequest.getCustomerId());
        operation.setBusinessType(AssetBusinessType.SHOP);
        operation.setAmount(orderRequest.getTotalPrice());
        operation.setType(AssetType.POINT);
        operation.setBusinessId(orderId);
        operation.setCategory(AssetCategory.NORMAL);
        operation.setDesc("buy " + goods.getName());
        remoteAssetOperateService.transferOut(operation);

        //创建订单
        StockOrderRequest request = new StockOrderRequest();
        request.setSku(orderRequest.getGoodsId());
        request.setOrderId(orderId);
        request.setAmount(orderRequest.getTotalPrice());
        request.setChannel("default");
        super.tryFreeze(request);

        if("nft".equals(goods.getType())){
            RewardRequest rewardRequest = RewardRequest.builder()
                    .address(address)
                    .currency(goods.getOutId())
                    .amount(new BigDecimal(orderRequest.getQuality()))
                    .rewardId(orderId)
                    .build();
            rewardTccService.getService(ActivityConstant.AwardTypeEnum.NFT.name())
                    .tryReward(rewardRequest);
        }else if("game_item".equals(goods.getType())){
            RewardRequest rewardRequest = RewardRequest.builder()
                    .address(address)
                    .goodsId(goods.getOutId())
                    .amount(new BigDecimal(orderRequest.getQuality()))
                    .build();
            rewardTccService.getService(ActivityConstant.AwardTypeEnum.GAME_ITEM.name())
                    .tryReward(rewardRequest);
        }
    }
}
