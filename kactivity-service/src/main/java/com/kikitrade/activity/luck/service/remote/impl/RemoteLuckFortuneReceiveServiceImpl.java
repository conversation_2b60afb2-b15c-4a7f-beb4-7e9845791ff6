package com.kikitrade.activity.luck.service.remote.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReceiveItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.service.business.ReceiveTccService;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.business.ActivityJoinItemService;
import com.kikitrade.activity.service.common.config.KactivityModuleProperties;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.model.CustomerMiscDTO;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.activity.luck.api.RemoteLuckFortuneReceiveService;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneOpenDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReceiveDTO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneHistoryReceiveVO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceive;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceiveResponse;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceiveVO;
import com.kikitrade.activity.luck.service.business.ReceiveService;
import com.kikitrade.activity.luck.service.business.ReleaseService;
import com.kikitrade.activity.luck.service.model.ReceiveItem;
import com.kikitrade.activity.luck.api.exception.LuckFortuneException;
import com.kikitrade.activity.luck.api.exception.LuckFortuneExceptionType;
import com.kikitrade.ksocial.common.constants.CustomerConstants;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 */
@DubboService
@Slf4j
public class RemoteLuckFortuneReceiveServiceImpl implements RemoteLuckFortuneReceiveService {

    @Resource
    private ReleaseService releaseService;
    @Resource
    private ReceiveService receiveService;
    @Resource
    private ReceiveTccService receiveTccService;
    @Resource
    private RedisService redisService;
    @Resource
    private SeqGeneraterService seqGeneraterService;
    @Resource
    private LuckFortuneReceiveItemBuilder luckFortuneReceiveItemBuilder;
    @Resource
    private CustomerService customerService;
    @Resource
    private KactivityModuleProperties kactivityModuleProperties;
    @Resource
    private ActivityJoinItemService activityJoinItemService;

    /**
     * 领取红包
     *
     * @param luckFortuneReceiveDTO
     */
    @Override
    @LogRecord(success = "【{{#luckFortuneReceiveDTO.receiveCode}}】success_receive【{{#luckFortuneReceiveDTO.releaseId}}】", type = "AIRDROP", subType = "RECEIVE", bizNo = "{{#_ret.data.receiveId}}", successCondition = "{{#_ret.success}}"
            , fail = "【{{#luckFortuneReceiveDTO.receiveCode}}】fail_receive【{{#luckFortuneReceiveDTO.releaseId}}】result：{{#_errorMsg}}")
    public Result<LuckFortuneReceiveResponse> receiveLuckFortune(LuckFortuneReceiveDTO luckFortuneReceiveDTO) throws Exception {
        log.info("receiveLuckFortune request:{}", JSON.toJSONString(luckFortuneReceiveDTO));
        if(StringUtils.isAnyBlank(luckFortuneReceiveDTO.getReleaseId(), luckFortuneReceiveDTO.getReceiveCode())){
            //必要参数为空
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_PARAM_NULL);
        }
        String releaseId = releaseService.decryptReleaseId(luckFortuneReceiveDTO.getReleaseId());
        boolean success = releaseService.checkLuckFortuneToken(releaseId, luckFortuneReceiveDTO.getReleaseId());
        if(!success){
            //红包id与token不匹配
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_INVALID);
        }
        if(activityJoinItemService.isContainsActivityCode(luckFortuneReceiveDTO.getReleaseId()).getLeft()){
            boolean done = activityJoinItemService.done(luckFortuneReceiveDTO.getReceiveCode(), luckFortuneReceiveDTO.getReleaseId());
            if(!done){
                //未参与新人空投活动
                throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_ACTIVITY_INVALID);
            }
        }
        luckFortuneReceiveDTO.setReleaseId(releaseId);
        return receiveService.receive(luckFortuneReceiveDTO);
    }

    /**
     * 开启红包
     *
     * @param luckFortuneOpenDTO
     */
    @Override
    @LogRecord(success = "【{{#luckFortuneOpenDTO.customer.id}}】success_open【{{#luckFortuneOpenDTO.id}}】", type = "AIRDROP", subType = "OPEN", bizNo = "{{#luckFortuneOpenDTO.id}}", successCondition = "{{#_ret.success}}"
            , fail = "【{{#luckFortuneOpenDTO.customer.id}}】fail_open【{{#luckFortuneOpenDTO.id}}】result：{{#_errorMsg}}")
    public Result openLuckFortune(LuckFortuneOpenDTO luckFortuneOpenDTO) throws Exception {
        log.info("open airdrop request:{}", luckFortuneOpenDTO);
        //是否存在过期空投
        redisService.hDel(RedisKeyConst.LUCK_FORTUNE_RECEIVE_EXPIRE_KEY.getKey(luckFortuneOpenDTO.getCustomer().getUserName()), luckFortuneOpenDTO.getId());
        Object o = redisService.hGet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(luckFortuneOpenDTO.getCustomer().getPhone()), luckFortuneOpenDTO.getId());
        ReceiveItem receiveItem;
        if(o != null){
            receiveItem = JSON.parseObject(String.valueOf(o), ReceiveItem.class);
            Date now = TimeUtil.addMinute(new Date(), -kactivityModuleProperties.getLuck().getValidMinutes());
            if(TimeUtil.getUtcTime(now, TimeUtil.YYYYMMDDHHMMSS).compareTo(receiveItem.getReceiveTime()) > 0){
                redisService.hDel(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(luckFortuneOpenDTO.getCustomer().getPhone()), luckFortuneOpenDTO.getId());
                log.warn("open airdrop luck.fortune.expired:{}", luckFortuneOpenDTO.getId());
                throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_EXPIRED);
            }
        } else {
            o = redisService.hGet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(luckFortuneOpenDTO.getCustomer().getEmail()), luckFortuneOpenDTO.getId());
            if(o == null){
                log.warn("open airdrop luck.fortune.expired:{}", luckFortuneOpenDTO.getId());
                throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_EXPIRED);
            }
            receiveItem = JSON.parseObject(String.valueOf(o), ReceiveItem.class);
            Date now = TimeUtil.addMinute(new Date(), -kactivityModuleProperties.getLuck().getValidMinutes());
            if (TimeUtil.getUtcTime(now, TimeUtil.YYYYMMDDHHMMSS).compareTo(receiveItem.getReceiveTime()) > 0) {
                redisService.hDel(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(luckFortuneOpenDTO.getCustomer().getEmail()), luckFortuneOpenDTO.getId());
                log.warn("open airdrop luck.fortune.expired:{}", luckFortuneOpenDTO.getId());
                throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_EXPIRED);
            }
        }

        LuckFortuneReleaseItem releaseItem = releaseService.findById(receiveItem.getReleaseId());
        LuckFortuneReceiveItem luckFortuneReceiveItem = toEntity(luckFortuneOpenDTO.getCustomer().getId(), releaseItem, receiveItem);
        try{
            //customerId + releaseId 做主键
            LuckFortuneReceiveItem item = receiveService.findItemByCustomerIdAndReleaseId(luckFortuneOpenDTO.getCustomer().getId(), receiveItem.getReleaseId());
            if(item != null){
                if(item.getStatus().equals(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_FAIL.getCode())){
                    if(!luckFortuneReceiveItemBuilder.update(luckFortuneReceiveItem)){
                        log.warn("open airdrop luck.fortune.repeat:{}", luckFortuneOpenDTO.getId());
                        throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_REPEAT_ERROR);
                    }
                }else{
                    log.warn("open airdrop luck.fortune.repeat:{}", luckFortuneOpenDTO.getId());
                    throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_REPEAT_ERROR);
                }
            }else{
                if(!luckFortuneReceiveItemBuilder.insert(luckFortuneReceiveItem)){
                    log.warn("open airdrop luck.fortune.repeat:{}", luckFortuneOpenDTO.getId());
                    throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_REPEAT_ERROR);
                }
            }
        }catch (Exception ex){
            log.warn("open airdrop luck.fortune.repeat:{}", luckFortuneOpenDTO.getId());
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_REPEAT_ERROR);
        }
        receiveTccService.tryOpen(luckFortuneOpenDTO, releaseItem, luckFortuneReceiveItem);
        return Result.of(Result.ResultCode.SUCCESS);
    }

    /**
     * 最近收到的红包
     * @param customer
     * @return
     */
    @Override
    public List<LuckFortuneReceive> lastReceiveLuckFortune(ActivityCustomerDTO customer) {
        return receiveService.lastReceiveLuckFortune(customer);
    }

    /**
     * @param customer
     */
    @Override
    public LuckFortuneHistoryReceiveVO historyReceiveLuckFortune(ActivityCustomerDTO customer, String type, Long startTime, Long endTime, int offset, int limit) {
        Page<LuckFortuneReceive> page = receiveService.historyReceiveLuckFortune(customer.getId(), type, startTime == null || startTime == 0 ? null : TimeUtil.getUtcTime(TimeUtil.parseUnittime(startTime), TimeUtil.YYYYMMDDHHMMSS),
                endTime == null || endTime == 0  ? null : TimeUtil.getUtcTime(TimeUtil.parseUnittime(endTime), TimeUtil.YYYYMMDDHHMMSS), offset, limit);
        if(page == null || CollectionUtils.isEmpty(page.getRows())){
            return null;
        }
        long num = receiveService.countReceive(customer.getId());
        BigDecimal amount = receiveService.sumAmountReceive(customer.getId());
        return new LuckFortuneHistoryReceiveVO(offset, limit, page.getTotalCount(), num, amount.setScale(2, BigDecimal.ROUND_DOWN), page.getRows());
    }

    /**
     * 查询我领取的某个红包
     *
     * @param paramId
     * @param receiveId
     * @return
     */
    @Override
    public LuckFortuneReceiveVO findByReceiveId(String paramId, String receiveId, String refer) throws Exception{
        String id = releaseService.decryptReleaseId(paramId);
        boolean success = releaseService.checkLuckFortuneToken(id, paramId);
        if(!success){
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_INVALID);
        }
        LuckFortuneReceiveItem receiveItem = receiveService.findById(receiveId);
        if(receiveItem == null || !receiveItem.getReleaseId().equals(id)){
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_INVALID);
        }
        LuckFortuneReleaseItem releaseItem = releaseService.findById(id);
        return toVO(receiveItem, releaseItem, refer);
    }

    /**
     * 检测能否开启空投
     *
     * @param luckFortuneOpenDTO
     * @return
     * @throws Exception
     */
    @Override
    public Result check(LuckFortuneOpenDTO luckFortuneOpenDTO) throws Exception {
        log.info("receiveOpenCheck request:{}", luckFortuneOpenDTO);
        ActivityCustomerDTO customer = luckFortuneOpenDTO.getCustomer();
        Object obj = redisService.hGet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(customer.getEmail()), luckFortuneOpenDTO.getId());
        if(obj == null){
            obj = redisService.hGet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(customer.getPhone()), luckFortuneOpenDTO.getId());
        }
        if(obj == null){
            log.info("receiveOpenCheck luck.fortune.invalid:{}", luckFortuneOpenDTO);
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_INVALID);
        }
        ReceiveItem receiveItem = JSON.parseObject(String.valueOf(obj), ReceiveItem.class);
        //customerId + releaseId 做主键
        LuckFortuneReceiveItem item = receiveService.findItemByCustomerIdAndReleaseId(luckFortuneOpenDTO.getCustomer().getId(), receiveItem.getReleaseId());
        if(item != null && !item.getStatus().equals(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATE_FAIL.getCode())){
            log.info("receiveOpenCheck luck.fortune.repeat:{}", luckFortuneOpenDTO);
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RECEIVE_REPEAT_ERROR);
        }
        return receiveService.check(receiveItem, luckFortuneOpenDTO.getCustomer());
    }

    private LuckFortuneReceiveVO toVO(LuckFortuneReceiveItem luckFortuneReceiveItem, LuckFortuneReleaseItem releaseItem, String refer){
        LuckFortuneReceiveVO receiveVO = new LuckFortuneReceiveVO();

        receiveVO.setExpiredTime(StringUtils.isNotBlank(luckFortuneReceiveItem.getExpiredTime()) ? TimeUtil.parse(luckFortuneReceiveItem.getExpiredTime()).getTime() : null);
        receiveVO.setId(releaseItem.getId());
        receiveVO.setAssignType(releaseItem.getAssignType());
        receiveVO.setNum(releaseItem.getNum());
        receiveVO.setAmount(luckFortuneReceiveItem.getAmount());
        receiveVO.setCurrency(luckFortuneReceiveItem.getCurrency());
        receiveVO.setCover(releaseItem.getCover());
        receiveVO.setGreeting(releaseItem.getGreeting());
        receiveVO.setAvatar(releaseItem.getAvatar());
        receiveVO.setNickName(releaseItem.getNickName());
        List<LuckFortuneReceive> releaseItemList = receiveService.findItemByReleaseId(releaseItem, refer);
        receiveVO.setReceiveNum(releaseItemList.size());
        receiveVO.setReceiveList(releaseItemList);
        receiveVO.setReleaseCustomerId(luckFortuneReceiveItem.getReleaseCustomerId());
        return receiveVO;
    }

    /**
     * 是否正在领取
     * @param luckFortuneReceiveDTO
     * @return
     */
    private String isDrawing(LuckFortuneReceiveDTO luckFortuneReceiveDTO){
        return redisService.onceLock(RedisKeyConst.LUCK_FORTUNE_RECEIVE_CUSTOMER_LOCK_KEY.getKey(luckFortuneReceiveDTO.getReceiveCode()), 1);
    }

    private LuckFortuneReceiveItem toEntity(String customerId, LuckFortuneReleaseItem releaseItem, ReceiveItem receiveItem){
        LuckFortuneReceiveItem item = new LuckFortuneReceiveItem();

        item.setCustomerId(customerId);
        item.setReleaseId(releaseItem.getId());

        item.setId(receiveItem.getId());
        item.setAmount(receiveItem.getAmount());
        item.setCurrency(releaseItem.getCurrency());
        item.setCost(receiveItem.getCost() != null ? receiveItem.getCost().doubleValue() : 0.0d);
        item.setCover(releaseItem.getCover());
        item.setGreeting(releaseItem.getGreeting());
        item.setType(releaseItem.getType());
        item.setAssignType(releaseItem.getAssignType());
        item.setAvatar(releaseItem.getAvatar());
        item.setReleaseCustomerId(releaseItem.getCustomerId());
        item.setReleaseNickName(releaseItem.getNickName());
        CustomerMiscDTO misc = customerService.getMiscByCustomerId(customerId);
        item.setNickName(misc.getNickName());
        item.setReceiveAvatar(misc.getAvatar());
        item.setNickName(customerService.getMiscByCustomerId(customerId).getNickName());

        item.setStatus(ActivityConstant.LuckFortuneReceiveStatus.ACTIVATING.getCode());
        item.setBusinessId(receiveItem.getBusinessId());
        item.setReceiveTime(receiveItem.getReceiveTime());
        item.setExpiredTime(TimeUtil.getUtcTime(TimeUtil.addDay(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS)), 3),TimeUtil.YYYYMMDDHHMMSS));
        item.setReceiveCode(receiveItem.getReceiveCode());
        return item;
    }
}