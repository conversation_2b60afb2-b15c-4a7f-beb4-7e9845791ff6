package com.kikitrade.activity.service.facade;

import com.kikitrade.activity.service.facade.luck.LuckFortuneFacadeImpl;
import com.kikitrade.activity.service.facade.award.ActivityFacadeImpl;
import com.kikitrade.activity.service.facade.reward.ActivityRewardFacadeImpl;
import com.kikitrade.activity.service.facade.task.ActivityTaskFacadeImpl;
import com.kikitrade.activity.service.facade.template.ActivityTemplateFacadeImpl;
import io.grpc.Server;
import io.grpc.ServerBuilder;
import io.grpc.ServerInterceptors;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.IOException;

/**
 * FacadeServerConfiguration
 *
 * <AUTHOR>
 * @create 2021/10/26 10:04 上午
 * @modify
 */
//@Component
public class ActivityFacadeServerConfiguration implements DisposableBean {
    @Resource
    private ActivityRewardFacadeImpl activityRewardFacade;
    @Resource
    private ActivityFacadeImpl activityFacade;
    @Resource
    private ActivityTemplateFacadeImpl activityTemplateFacade;
    @Resource
    private LuckFortuneFacadeImpl luckFortuneFacade;
    @Resource
    private ActivityTaskFacadeImpl activityTaskFacade;
    @Resource
    SaasIdInterceptor saasIdInterceptor;

    @Value("${manage.grpc.port}")
    int manageGrpcPort = 30880;

    private Server server;

    @Bean
    Server grpcServer() throws IOException {
        server = ServerBuilder
                .forPort(manageGrpcPort)
                .addService(activityRewardFacade)
                .addService(activityFacade)
                .addService(activityTemplateFacade)
                .addService(luckFortuneFacade)
                .addService(activityTaskFacade)
                .addService(ServerInterceptors.intercept(activityRewardFacade, saasIdInterceptor))
                .addService(ServerInterceptors.intercept(activityFacade, saasIdInterceptor))
                .addService(ServerInterceptors.intercept(activityTemplateFacade, saasIdInterceptor))
                .addService(ServerInterceptors.intercept(luckFortuneFacade, saasIdInterceptor))
                .addService(ServerInterceptors.intercept(activityTaskFacade, saasIdInterceptor))
                .build();
        server.start();
        return server;
    }

    @Override
    public void destroy() throws Exception {
        if (server != null) {
            server.shutdown();
        }
    }
}
