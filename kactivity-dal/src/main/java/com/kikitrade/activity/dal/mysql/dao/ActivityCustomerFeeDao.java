package com.kikitrade.activity.dal.mysql.dao;


import com.kikitrade.activity.dal.mysql.model.ActivityCustomerFee;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityCustomerFeeDao extends BaseDao<ActivityCustomerFee, String> {

    List<ActivityCustomerFee> findAll(@Param("tran_date") String tran_date, @Param("offset") Integer offset, @Param("limit") Integer limit, @Param("type")Integer type);
    List<ActivityCustomerFee> findByDate(@Param("tran_date") String tran_date, @Param("offset") int offset, @Param("limit") int limit);
    List<ActivityCustomerFee> findByCurrencyDate(@Param("currency")String currency, @Param("tran_date")String date, @Param("offset")int offset,@Param("limit") int limit);

    int updateStatus(@Param("tran_date") String tran_date, @Param("customer_id") String customer_id, @Param("currency") String currency, @Param("status") Integer status, @Param("type")Integer type);

    long countByTypeAndTye(@Param("tran_date") String tran_date, @Param("type") int type, @Param("status") int status);

    List<ActivityCustomerFee> findByDateAndStatus(@Param("tran_date") String tran_date,  @Param("activity_type")Integer activityType, @Param("status") Integer status, @Param("offset") int offset, @Param("limit") int limit);
}
