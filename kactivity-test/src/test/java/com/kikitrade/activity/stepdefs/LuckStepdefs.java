package com.kikitrade.activity.stepdefs;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.accounting.api.domain.activity.model.AccountActivityRequest;
import com.kikitrade.accounting.api.model.account.AccountException;
import com.kikitrade.accounting.api.model.account.request.AccountFreezeRequest;
import com.kikitrade.accounting.api.model.account.request.AccountUnfreezeRequest;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountParam;
import com.kikitrade.activity.dal.mysql.dao.LuckFortuneRuleTestDao;
import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneRefundItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.api.RemoteLuckFortuneReceiveService;
import com.kikitrade.activity.luck.api.RemoteLuckFortuneReleaseService;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneOpenDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReceiveDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReleaseDTO;
import com.kikitrade.activity.luck.api.model.response.LuckFortuneReceiveResponse;
import com.kikitrade.activity.luck.service.model.ReceiveItem;
import com.kikitrade.activity.luck.service.mq.AirDropEventListener;
import com.kikitrade.activity.mock.*;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.op.BusinessOP;
import com.kikitrade.activity.op.Check;
import com.kikitrade.activity.service.job.ActivityRefundJob;
import com.kikitrade.activity.service.job.ActivitySignExpiredJob;
import com.kikitrade.activity.service.reward.impl.CustomerCacheDTO;
import com.kikitrade.activity.util.CheckUtils;
import com.kikitrade.activity.util.DateUtil;
import com.kikitrade.ksocial.common.constants.CustomerConstants;
import io.cucumber.java.zh_cn.假如;
import io.cucumber.java.zh_cn.并且;
import io.cucumber.java.zh_cn.当;
import io.cucumber.java.zh_cn.那么;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import static org.junit.jupiter.api.Assertions.*;

import jakarta.annotation.Resource;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.kikitrade.activity.op.BusinessOP.*;
import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class LuckStepdefs {

    @Resource
    RemoteLuckFortuneReleaseService remoteLuckFortuneReleaseService;
    @Resource
    LuckFortuneRuleTestDao luckFortuneRuleTestDao;
    @Resource
    RedisService redisService;
    @Resource
    RemoteAccountingOperateServiceDubboMock remoteAccountingOperateServiceDubboMock;
    @Resource
    RemoteProductAccountServiceDubboMock remoteProductAccountServiceDubboMock;
    @Resource
    SeqGeneraterServiceMock seqGeneraterServiceMock;
    @Resource
    RemoteLuckFortuneReceiveService remoteLuckFortuneReceiveService;
    @Resource
    RemoteAccountingActivityServiceDubboMock remoteAccountingActivityServiceDubboMock;
    @Resource
    ActivityRefundJob activityRefundJob;
    @Resource
    ActivitySignExpiredJob activitySignExpiredJob;
    @Resource
    LuckFortuneReleaseItemBuilderMock luckFortuneReleaseItemBuilderMock;
    @Resource
    AirDropEventListener airDropEventListener;

    static String cusId;


    @当("luck-用户发红包{}")
    public void luck用户发红包Luck(String luck) throws Exception {

        LuckFortuneReleaseDTO luckFortuneReleaseDTO = JSON.parseObject(luck, LuckFortuneReleaseDTO.class);
        //发红包
        remoteLuckFortuneReleaseService.releaseLuckFortune(luckFortuneReleaseDTO);

    }

    @假如("redis缓存数据已经初始化完成")
    public void redis缓存数据已经初始化完成() throws ParseException {
        //初始化luck_fortune_rule redis
        List<LuckFortuneRule> luckFortuneRules = luckFortuneRuleTestDao.selectAll();
        for (LuckFortuneRule luckFortuneRule : luckFortuneRules) {
            String key = RedisKeyConst.LUCK_FORTUNE_RULE_KEY.getKey(String.format("%s:%s", luckFortuneRule.getKycLevel(), luckFortuneRule.getUserType()));
            String value = JSON.toJSONString(luckFortuneRule);
            redisService.save(key, value);
        }

        //初始化customerCacheDto
        CustomerCacheDTO cacheDTO = new CustomerCacheDTO();
        cacheDTO.setId("**************");
        cacheDTO.setRegisterTime(DateUtil.string2Date("2022-01-01"));
        redisService.save(RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_CUSTOMER_KEY.getPrefix(), "kiki" + "**************"), JSON.toJSONString(cacheDTO));
    }

    @那么("luck-校验账务帐冻结{}")
    public void luck校验账务帐冻结AccountFreezeRequest(String accountFreezeRequest) {
        log.info("---账务帐冻结:{}", JSON.toJSONString(BusinessOP.accountFreezeRequest));
        AccountFreezeRequest freezeRequest = JSON.parseObject(accountFreezeRequest, AccountFreezeRequest.class);
        CheckUtils.compare(freezeRequest, BusinessOP.accountFreezeRequest, new String[]{"businessId"});
    }

    @并且("luck-校验业务帐冻结{}")
    public void luck校验业务帐冻结ProductAccountParam(String productAccountParam) {
        log.info("---业务帐冻结:{}", JSON.toJSONString(BusinessOP.productAccountParam));
        ProductAccountParam param = JSON.parseObject(productAccountParam, ProductAccountParam.class);
        CheckUtils.compare(param, BusinessOP.productAccountParam, new String[]{"bizId"});

    }

    @并且("luck-校验luckFortuneReleaseItem{}")
    public void luck校验luckFortuneReleaseItemLuckFortuneReleaseItem(String luckFortuneReleaseItem) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        LuckFortuneReleaseItem actual = BusinessOP.luckFortuneReleaseItemMap.get(BusinessOP.releaseId);
        log.info("---luckFortuneReleaseItem:{}", JSON.toJSONString(actual));
        CheckUtils.compare(luckFortuneReleaseItem, JSON.toJSONString(actual), new String[]{"avatar", "nickName", "created", "releaseTime", "expiredTime", "id", "releaseId", "releaseCode", "receiveTime"}, null);
    }

    @假如("luck-用户的KolRole为{}")
    public void luck用户的KolRole为KolRole(String kolRole) {
        BusinessOP.kolRole = CustomerConstants.KolRole.valueOf(kolRole);
    }

    @当("luck-异常-用户发红包{}")
    public void luck异常用户发红包Luck(String luck) {
        LuckFortuneReleaseDTO luckFortuneReleaseDTO = JSON.parseObject(luck, LuckFortuneReleaseDTO.class);
        //发红包
        try {
            remoteLuckFortuneReleaseService.releaseLuckFortune(luckFortuneReleaseDTO);
        } catch (Exception e) {
            e.printStackTrace();
            BusinessOP.exceptions.add(JSON.toJSONString(e));
        }
    }

    @那么("luck-校验exceptions为{}")
    public void luck校验exceptions为Exceptions(String exceptions) {
        Check.checkException(exceptions);
    }

    @假如("luck-用户可用余额为{}")
    public void luck用户可用余额为Available(double available) {
        BusinessOP.available = BigDecimal.valueOf(available);
    }

    @假如("luck-kaccounting异常，无法冻结账务")
    public void luckKaccounting异常无法冻结账务() throws AccountException {
        remoteAccountingOperateServiceDubboMock.freezeError();
    }

    @假如("luck-kaccounting异常，无法冻结产品帐")
    public void luckKaccounting异常无法冻结产品帐() throws Exception {
        remoteProductAccountServiceDubboMock.freezeError();
    }

    @当("luck-并发-用户发红包{}")
    public void luck并发用户发红包Luck(String luck) throws Exception {
        LuckFortuneReleaseDTO luckFortuneReleaseDTO = JSON.parseObject(luck, LuckFortuneReleaseDTO.class);

        int len = 10;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch downLatch = new CountDownLatch(1);

        for (int i = 0; i < len; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        downLatch.await();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    remoteLuckFortuneReleaseService.releaseLuckFortune(luckFortuneReleaseDTO);
                    endLatch.countDown();
                }
            }).start();
        }

        downLatch.countDown();
        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
        try {
            endLatch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @并且("seq的规则更改为next2")
    public void seq的规则更改为next() {
        seqGeneraterServiceMock.next2();

    }

    @那么("luck-new-校验账务帐冻结{}")
    public void luckNew校验账务帐冻结AccountFreezeRequestList(String accountFreezeRequestList) {
        log.info("---accountFreezeRequestList:{}", BusinessOP.accountFreezeRequestList);
    }

    @那么("luck-并发-校验账务帐冻结")
    public void luck并发校验账务帐冻结() {
        assertEquals(BusinessOP.accountFreezeRequestList.size(), 10,"Size");
    }

    @并且("luck-并发-校验业务帐冻结")
    public void luck并发校验业务帐冻结() {
        assertEquals(BusinessOP.productAccountParamList.size(), 10, "size");
    }

    @并且("luck-并发-校验luckFortuneReleaseItem")
    public void luck并发校验luckFortuneReleaseItem() {
        log.info("BusinessOP.luckFortuneReleaseItemMap:{}", JSON.toJSONString(luckFortuneReleaseItemMap));
        assertEquals(10, BusinessOP.luckFortuneReleaseItemMap.size(), "size");
    }

    @并且("luck-用户{}抢红包")
    public void luck用户抢红包(String customerId) throws Exception {
        cusId = customerId;

        LuckFortuneReceiveDTO luckFortuneReceiveDTO = new LuckFortuneReceiveDTO();
        luckFortuneReceiveDTO.setReceiveCode(customerId);
        luckFortuneReceiveDTO.setReleaseId(BusinessOP.releaseCode);
        luckFortuneReceiveDTO.setToken("");
        remoteLuckFortuneReceiveService.receiveLuckFortune(luckFortuneReceiveDTO);

        Map<String, String> map = redisService.getMap(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(cusId));

        Optional<String> first = map.keySet().stream().findFirst();

        first.ifPresent(s -> BusinessOP.detailId = s);


    }

    @并且("luck-校验redis-LUCK_FORTUNE_RELEASE_STATUS_KEY{}")
    public void luck校验redisLUCK_FORTUNE_RELEASE_STATUS_KEYStatus(String status) {
        String s = redisService.get(RedisKeyConst.LUCK_FORTUNE_RELEASE_STATUS_KEY.getKey(BusinessOP.releaseId));
        System.out.println(s);
        CheckUtils.compare(status, s);
    }

    @并且("luck-校验redis-LUCK_FORTUNE_RELEASE_INFO_KEY{}")
    public void luck校验redisLUCK_FORTUNE_RELEASE_INFO_KEYInfo(String info) {
        String s = redisService.get(RedisKeyConst.LUCK_FORTUNE_RELEASE_INFO_KEY.getKey(BusinessOP.releaseId));
        System.out.println(s);
        CheckUtils.compare(info, s, new String[]{"created", "id", "releaseId", "expiredTime", "modified", "releaseCode", "releaseTime"});


    }

    @并且("luck-校验redis-LUCK_FORTUNE_RELEASE_NOT_DRAW_KEY{}")
    public void luck校验redisLUCK_FORTUNE_RELEASE_NOT_DRAW_KEYNot_draw(String not_draw) {
        List<String> lrange = redisService.lrange(RedisKeyConst.LUCK_FORTUNE_RELEASE_NOT_DRAW_KEY.getMiddleKey(BusinessOP.releaseId));
        System.out.println("---" + lrange.toString());

        CheckUtils.compare(not_draw, lrange.toString());
    }

    @那么("luck-校验redis-LUCK_FORTUNE_RECEIVE_DETAIL_KEY{}")
    public void luck校验redisLUCK_FORTUNE_RECEIVE_DETAIL_KEYDetail(String detail) {
        if (detail.equals("null")) {
            detail = null;
        }
        Object hGet = redisService.hGet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_DETAIL_KEY.getMiddleKey(BusinessOP.releaseId), cusId);
        String s = (String) hGet;
        System.out.println(s);
        CheckUtils.compare(detail, s, new String[]{"id", "releaseId", "expiredTime", "receiveTime"});
    }

    @那么("luck-校验redis-LUCK_FORTUNE_RELEASE_DREW_KEY{}")
    public void luck校验redisLUCK_FORTUNE_RELEASE_DREW_KEYDrew(String drew) {
        List<String> lrange = redisService.lrange(RedisKeyConst.LUCK_FORTUNE_RELEASE_DREW_KEY.getMiddleKey(BusinessOP.releaseId));
        System.out.println(lrange.toString());
        CheckUtils.compare(drew, lrange.toString());

    }

    @那么("luck-校验redis-LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY{}")
    public void luck校验redisLUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEYNot_active(String not_active) {
        List list = redisService.hGetAllList(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(cusId));
        System.out.println(list);

        CheckUtils.compare(not_active, list.toString(), new String[]{"businessId", "id", "releaseId", "receiveTime", "expiredTime"});

    }

    @并且("luck-配置未领红包过期时间为{}")
    public void luck配置未领红包过期时间为ExpireTime(String expireTime) {
        String releaseItemStr = redisService.get(RedisKeyConst.LUCK_FORTUNE_RELEASE_INFO_KEY.getKey(BusinessOP.releaseId));
        System.out.println(releaseItemStr);

        LuckFortuneReleaseItem luckFortuneReleaseItem = JSON.parseObject(releaseItemStr, LuckFortuneReleaseItem.class);
        System.out.println(luckFortuneReleaseItem.getExpiredTime());

        luckFortuneReleaseItem.setExpiredTime(expireTime);

        redisService.save(RedisKeyConst.LUCK_FORTUNE_RELEASE_INFO_KEY.getKey(BusinessOP.releaseId), JSON.toJSONString(luckFortuneReleaseItem));
    }

    @并且("luck-异常-用户{}抢红包")
    public void luck异常用户CustomerId抢红包(String customerId) {
        cusId = customerId;

        LuckFortuneReceiveDTO luckFortuneReceiveDTO = new LuckFortuneReceiveDTO();
        luckFortuneReceiveDTO.setReceiveCode(customerId);
        luckFortuneReceiveDTO.setReleaseId(BusinessOP.releaseCode);
        luckFortuneReceiveDTO.setToken("");
        try {
            remoteLuckFortuneReceiveService.receiveLuckFortune(luckFortuneReceiveDTO);
        } catch (Exception e) {
            e.printStackTrace();
            BusinessOP.exceptions.add(JSON.toJSONString(e));
        }
    }

    @并且("luck-配置红包releaseId为{}")
    public void luck配置红包releaseId为ReleaseId(String releaseId) {
        BusinessOP.releaseCode = releaseId;
    }

    @并且("luck-配置红包为已抢状态")
    public void luck配置红包为已抢状态() {
        redisService.hSet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_DETAIL_KEY.getMiddleKey(BusinessOP.releaseId), "**************", "");
    }

    @并且("luck-配置红包为已被抢空")
    public void luck配置红包为已被抢空() {
        redisService.del(RedisKeyConst.LUCK_FORTUNE_RELEASE_NOT_DRAW_KEY.getMiddleKey(BusinessOP.releaseId));
    }

    @并且("luck-并发-多个用户同时抢红包")
    public void luck并发多个用户同时抢红包() {
        String customerId1 = "**************";
        String customerId2 = "20220930145602";

        LuckFortuneReceiveDTO luckFortuneReceiveDTO = new LuckFortuneReceiveDTO();
        luckFortuneReceiveDTO.setReceiveCode(customerId1);
        luckFortuneReceiveDTO.setReleaseId(BusinessOP.releaseCode);
        luckFortuneReceiveDTO.setToken("");

        LuckFortuneReceiveDTO luckFortuneReceiveDTO2 = new LuckFortuneReceiveDTO();
        luckFortuneReceiveDTO2.setReceiveCode(customerId2);
        luckFortuneReceiveDTO2.setReleaseId(BusinessOP.releaseCode);
        luckFortuneReceiveDTO2.setToken("");

        int len = 2;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch mainLatch = new CountDownLatch(len);

        //线程1
        new Thread(() -> {
            try {
                endLatch.countDown();
                endLatch.await();
                Result<LuckFortuneReceiveResponse> result = remoteLuckFortuneReceiveService.receiveLuckFortune(luckFortuneReceiveDTO);
                log.info("---Result<LuckFortuneReceiveResponse>1:{}", JSON.toJSONString(result));
            } catch (Exception e) {
                e.printStackTrace();
                BusinessOP.exceptions.add(JSON.toJSONString(e));
            } finally {
                mainLatch.countDown();
            }
        }).start();

        //线程2
        new Thread(() -> {
            try {
                endLatch.countDown();
                endLatch.await();
                Result<LuckFortuneReceiveResponse> result = remoteLuckFortuneReceiveService.receiveLuckFortune(luckFortuneReceiveDTO2);
                log.info("---Result<LuckFortuneReceiveResponse>2:{}", JSON.toJSONString(result));
            } catch (Exception e) {
                e.printStackTrace();
                BusinessOP.exceptions.add(JSON.toJSONString(e));
            } finally {
                mainLatch.countDown();
            }
        }).start();

        //主线程
        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
        try {
            mainLatch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @并且("luck-用户开启红包")
    public void luck用户开启红包() throws Exception {
        ActivityCustomerDTO activityCustomerDTO = new ActivityCustomerDTO();
        activityCustomerDTO.setPhone(cusId);
        activityCustomerDTO.setId(cusId);
        activityCustomerDTO.setEmail(cusId);

        LuckFortuneOpenDTO luckFortuneOpenDTO = new LuckFortuneOpenDTO();
        luckFortuneOpenDTO.setCustomer(activityCustomerDTO);
        luckFortuneOpenDTO.setId(BusinessOP.detailId);

        remoteLuckFortuneReceiveService.openLuckFortune(luckFortuneOpenDTO);
    }

    @那么("luck-校验accountActivityRequestList账务帐操作{}")
    public void luck校验accountActivityRequestList账务帐操作AccountActivityRequestList(String accountActivityRequestList) {
        List<AccountActivityRequest> activityRequestList = BusinessOP.accountActivityRequests;
        System.out.println("---" + JSON.toJSONString(activityRequestList));

        List<AccountActivityRequest> requests = JSON.parseArray(accountActivityRequestList, AccountActivityRequest.class);
        CheckUtils.compare(requests, activityRequestList, new String[]{"businessId", "originalBusinessId"});
    }

    @并且("luck-校验productAccountParamList业务帐冻结{}")
    public void luck校验productAccountParamList业务帐冻结ProductAccountParamList(String productAccountParamList) {
        List<ProductAccountParam> accountParamList = BusinessOP.productAccountParamList;
        System.out.println("---" + JSON.toJSONString(accountParamList));

        List<ProductAccountParam> params = JSON.parseArray(productAccountParamList, ProductAccountParam.class);
        CheckUtils.compare(params, accountParamList, new String[]{"bizId"});


    }

    @并且("luck-校验luckFortuneReceiveItemMap{}")
    public void luck校验luckFortuneReceiveItemMapLuckFortuneReceiveItemMap(String luckFortuneReceiveItemMap) {
        Map<String, LuckFortuneReceiveItem> receiveItemMap = BusinessOP.luckFortuneReceiveItemMap;

        LuckFortuneReceiveItem luckFortuneReceiveItem = receiveItemMap.values().stream().findFirst().get();

        System.out.println("---" + JSON.toJSONString(luckFortuneReceiveItem));

        LuckFortuneReceiveItem item = JSON.parseObject(luckFortuneReceiveItemMap, LuckFortuneReceiveItem.class);

        CheckUtils.compare(item, luckFortuneReceiveItem, new String[]{"avatar", "receiveAvatar", "releaseNickName", "nickName", "businessId", "expiredTime", "receiveTime", "releaseId", "id"}, null);


    }

    @那么("luck-校验accountUnfreezeRequestList账务帐操作{}")
    public void luck校验accountUnfreezeRequestList账务帐操作AccountUnfreezeRequestList(String accountUnfreezeRequestList) {

        List<AccountUnfreezeRequest> list = BusinessOP.accountUnfreezeRequestList;
        System.out.println("---" + JSON.toJSONString(list));

        List<AccountUnfreezeRequest> accountUnfreezeRequests = JSON.parseArray(accountUnfreezeRequestList, AccountUnfreezeRequest.class);

        CheckUtils.compare(accountUnfreezeRequests, list, new String[]{"businessId", "originalBusinessId"});
    }

    @并且("luck-异常-用户开启红包")
    public void luck异常用户开启红包() {
        ActivityCustomerDTO activityCustomerDTO = new ActivityCustomerDTO();
        activityCustomerDTO.setPhone(cusId);
        activityCustomerDTO.setId(cusId);

        LuckFortuneOpenDTO luckFortuneOpenDTO = new LuckFortuneOpenDTO();
        luckFortuneOpenDTO.setCustomer(activityCustomerDTO);
        luckFortuneOpenDTO.setId(BusinessOP.receiveIds.get(0));
        try {
            remoteLuckFortuneReceiveService.openLuckFortune(luckFortuneOpenDTO);
        } catch (Exception e) {
            e.printStackTrace();
            BusinessOP.exceptions.add(JSON.toJSONString(e));
        }
    }

    @并且("luck-配置红包过期时间{}")
    public void luck配置红包过期时间ExpiredTime(String expiredTime) {
        Object hGet = redisService.hGet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(cusId), BusinessOP.receiveIds.get(0));

        ReceiveItem item = JSON.parseObject(String.valueOf(hGet), ReceiveItem.class);

        item.setReceiveTime(expiredTime);

        redisService.hSet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(cusId), BusinessOP.receiveIds.get(0), JSON.toJSONString(item));
    }

    @并且("luck-配置红包无效")
    public void luck配置红包无效() {
        redisService.delete(RedisKeyConst.LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY.getKey(cusId));
    }

    @并且("luck-配置红包已被领取")
    public void luck配置红包已被领取() {

        LuckFortuneReceiveItem item = new LuckFortuneReceiveItem();
        item.setCustomerId("293857380569940201");
        item.setReleaseId(BusinessOP.releaseId);

        luckFortuneReceiveItemMap.put(BusinessOP.receiveIds.get(0), item);
    }

    @并且("luck-kaccounting异常，无法解冻账务")
    public void luckKaccounting异常无法解冻账务() throws AccountException {
        remoteAccountingOperateServiceDubboMock.unfreezeError();
    }

    @并且("luck-kaccounting异常，无法转账")
    public void luckKaccounting异常无法转账() throws AccountException {
        remoteAccountingActivityServiceDubboMock.transferError();
    }

    @并且("luck-并发-用户开启红包")
    public void luck并发用户开启红包() {
        ActivityCustomerDTO activityCustomerDTO = new ActivityCustomerDTO();
        activityCustomerDTO.setPhone(cusId);
        activityCustomerDTO.setId(cusId);

        LuckFortuneOpenDTO luckFortuneOpenDTO = new LuckFortuneOpenDTO();
        luckFortuneOpenDTO.setCustomer(activityCustomerDTO);
        luckFortuneOpenDTO.setId(BusinessOP.receiveIds.get(0));


        int len = 2;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch mainLatch = new CountDownLatch(len);

        for (int i = 0; i < len; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        endLatch.countDown();
                        endLatch.await();
                        Result result = remoteLuckFortuneReceiveService.openLuckFortune(luckFortuneOpenDTO);
                        log.info("-----Result:{}", JSON.toJSONString(result));
                    } catch (Exception e) {
                        e.printStackTrace();
                        BusinessOP.exceptions.add(JSON.toJSONString(e));
                    }
                    mainLatch.countDown();
                }
            }).start();
        }
        System.out.println("-----" + Thread.currentThread().getName() + ": waiting...");
        try {
            mainLatch.await();
            System.out.println("-----" + Thread.currentThread().getName() + ": start running...");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @并且("luck-kaccounting恢复正常，可以转账")
    public void luckKaccounting恢复正常可以转账() throws AccountException {
        remoteAccountingActivityServiceDubboMock.transfer();
    }

    @并且("luck-系统执行未领红包退款任务")
    public void luck系统执行未领红包退款任务() {
        activityRefundJob.execute(new ShardingContext("", "", 0, "", 0, ""));
    }

    @并且("luck-系统执行ActivitySignExpiredJob注册超时任务")
    public void luck系统执行ActivitySignExpiredJob注册超时任务() {
        activitySignExpiredJob.execute(new ShardingContext("", "", 0, "", 0, ""));

    }

    @并且("luck-配置数据库红包过期时间{}")
    public void luck配置数据库红包过期时间ExpireTime(String expireTime) {
        luckFortuneReleaseItemMap.values().stream().findFirst().get().setExpiredTime(expireTime);
    }

    @并且("luck-校验luckFortuneRefundItem{}")
    public void luck校验luckFortuneRefundItemLuckFortuneRefundItem(String luckFortuneRefundItem) {
        Map<String, LuckFortuneRefundItem> refundItemMap = luckFortuneRefundItemMap;
        List<LuckFortuneRefundItem> collect = refundItemMap.values().stream().sorted((a, b) -> DateUtil.parseYYYYMMDDHHMMSS(a.getCreated()).compareTo(DateUtil.parseYYYYMMDDHHMMSS(b.getCreated()))).collect(Collectors.toList());
        System.out.println("---" + JSON.toJSONString(collect));

        List<LuckFortuneRefundItem> refundItems = JSON.parseArray(luckFortuneRefundItem, LuckFortuneRefundItem.class);
        CheckUtils.compare(refundItems, collect, new String[]{"created", "businessId", "originBusinessId", "refundId", "refundTime"});


    }

    @并且("luck-配置luckFortuneReleaseItemBuilder.update异常")
    public void luck配置luckFortuneReleaseItemBuilderUpdate异常() {
        luckFortuneReleaseItemBuilderMock.updateError();


    }

    @并且("luck-配置remoteAccountingOperateService.unfreeze异常")
    public void luck配置remoteAccountingOperateServiceUnfreeze异常() throws AccountException {
        remoteAccountingOperateServiceDubboMock.unfreezeError();
    }

    @并且("luck-配置remoteAccountingOperateService.unfreeze正常")
    public void luck配置remoteAccountingOperateServiceUnfreeze正常() throws AccountException {
        remoteAccountingOperateServiceDubboMock.unfreeze();
    }

    @并且("luck-校验总金额")
    public void luck校验总金额() {

        double sum1 = luckFortuneReceiveItemMap.values().stream().mapToDouble(LuckFortuneReceiveItem::getCost).sum();

        double sum = 0.0;
        for (LuckFortuneRefundItem luckFortuneRefundItem : luckFortuneRefundItemMap.values()) {
            double amount = Double.parseDouble(luckFortuneRefundItem.getAmount());
            sum += amount;
        }

        double act = sum + sum1;

        double res = 100;


        //(res, act);


    }

    @并且("luck-增加忽略校验-校验luckFortuneReleaseItem{}")
    public void luck增加忽略校验校验luckFortuneReleaseItemLuckFortuneReleaseItem(String luckFortuneReleaseItem) {
        LuckFortuneReleaseItem actual = BusinessOP.luckFortuneReleaseItemMap.get(BusinessOP.releaseId);
        System.out.println("---luckFortuneReleaseItem:{}" + JSON.toJSONString(actual));
        CheckUtils.compare(luckFortuneReleaseItem, JSON.toJSONString(actual), new String[]{"amount", "cost",
                "refundAmount", "avatar", "nickName", "releaseTime", "id", "releaseId", "created", "releaseCode",
                "receiveCode", "expiredTime", "receiveTime"}, null);
    }

    @那么("luck-增加忽略校验-校验accountUnfreezeRequestList账务帐操作{}")
    public void luck增加忽略校验校验accountUnfreezeRequestList账务帐操作AccountUnfreezeRequestList(String accountUnfreezeRequestList) {
        List<AccountUnfreezeRequest> list = BusinessOP.accountUnfreezeRequestList;
        System.out.println("---" + JSON.toJSONString(list));

        List<AccountUnfreezeRequest> accountUnfreezeRequests = JSON.parseArray(accountUnfreezeRequestList, AccountUnfreezeRequest.class);

        CheckUtils.compare(accountUnfreezeRequests, list, new String[]{"amount", "cost", "businessId", "originalBusinessId"}, null);
    }

    @并且("luck-增加忽略校验-校验luckFortuneRefundItem{}")
    public void luck增加忽略校验校验luckFortuneRefundItemLuckFortuneRefundItem(String luckFortuneRefundItem) {
        Map<String, LuckFortuneRefundItem> refundItemMap = luckFortuneRefundItemMap;
        List<LuckFortuneRefundItem> collect = refundItemMap.values().stream().sorted((a, b) -> DateUtil.parseYYYYMMDDHHMMSS(a.getCreated()).compareTo(DateUtil.parseYYYYMMDDHHMMSS(b.getCreated()))).collect(Collectors.toList());
        System.out.println("---" + JSON.toJSONString(collect));

        List<LuckFortuneRefundItem> refundItems = JSON.parseArray(luckFortuneRefundItem, LuckFortuneRefundItem.class);
        CheckUtils.compare(refundItems, collect, new String[]{"amount", "cost", "refundAmount", "created", "originBusinessId", "refundId", "refundTime", "businessId"}, null);
    }

    @当("luck-系统接收到延迟mq消息{}")
    public void luck系统接收到延迟mq消息Event(String event) {
        Message message = new Message();

        switch (ActivityConstant.AirDropEventEnum.valueOf(event)) {
            case EXPIRE:
                message.setBody(expireEvent.getBytes(StandardCharsets.UTF_8));
                break;
            case REVERT:
                message.setBody(revertEvent.getBytes(StandardCharsets.UTF_8));
                break;
            case REFUND:
                message.setBody(refundEvent.getBytes(StandardCharsets.UTF_8));
                break;
            default:
                break;
        }

        airDropEventListener.consume(message, new ConsumeContext());
    }

    @那么("luck-校验redis-LUCK_FORTUNE_RECEIVE_EXPIRE_KEY{}")
    public void luck校验redisLUCK_FORTUNE_RECEIVE_EXPIRE_KEYReceive_expire(String receive_expire) {
        String hGet = (String) redisService.hGet(RedisKeyConst.LUCK_FORTUNE_RECEIVE_EXPIRE_KEY.getKey(cusId), receiveIds.get(0));
        System.out.println(hGet);
        CheckUtils.compare(receive_expire, hGet, new String[]{"releaseNickName", "businessId", "expiredTime", "id", "receiveCode", "receiveTime", "releaseId"}, null);
    }
}
