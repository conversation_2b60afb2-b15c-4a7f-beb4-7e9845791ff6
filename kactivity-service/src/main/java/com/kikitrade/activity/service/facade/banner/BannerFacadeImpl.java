package com.kikitrade.activity.service.facade.banner;

import com.kikitrade.activity.dal.tablestore.builder.BannerBuilder;
import com.kikitrade.activity.dal.tablestore.model.Banner;
import com.kikitrade.activity.facade.banner.BannerDTO;
import com.kikitrade.activity.facade.banner.BannerFacade;
import com.kikitrade.activity.facade.banner.BannerSaveResponse;
import com.kikitrade.activity.facade.banner.DubboBannerFacadeTriple;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.stream.StreamObserver;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/3/1 14:42
 */
@DubboService(interfaceClass = BannerFacade.class)
public class BannerFacadeImpl extends DubboBannerFacadeTriple.BannerFacadeImplBase {

    @Resource
    private BannerBuilder bannerBuilder;

    @Override
    public void save(BannerDTO request, StreamObserver<BannerSaveResponse> responseObserver) {
        Banner banner = new Banner();
        banner.setName(request.getName());
        banner.setDesc(request.getDesc());
        banner.setLocal(request.getLocal());
        banner.setImage(request.getImage());
        banner.setChannel(request.getChannel().name());
        banner.setOrder(request.getOrder());
        banner.setUrl(request.getUrl());
        banner.setSource(request.getSource().name());
        banner.setSaasId(request.getSaasId());
        banner.setSourceId(request.getSourceId());
        try{
            if(StringUtils.isNotBlank(request.getId())){
                banner.setId(request.getId());
                bannerBuilder.update(banner);
            }else{
                banner.setId(bannerBuilder.nextId());
                bannerBuilder.insert(banner);
            }

            BannerSaveResponse response = BannerSaveResponse.newBuilder()
                    .setSuccess(true)
                    .setMessage("success")
                    .setId(banner.getId())
                    .build();
            responseObserver.onNext(response);
        }catch (Exception ex){
            responseObserver.onError(ex);
        }
        responseObserver.onCompleted();
    }
}
