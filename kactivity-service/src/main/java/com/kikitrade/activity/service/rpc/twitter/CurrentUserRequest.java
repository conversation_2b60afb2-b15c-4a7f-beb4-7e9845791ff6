package com.kikitrade.activity.service.rpc.twitter;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.service.common.config.TwitterProperties;
import com.kikitrade.activity.service.rpc.AccessToken;
import com.kikitrade.activity.service.rpc.SocialUserInfo;
import com.twitter.clientlib.model.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/22 16:12
 */
@Data
@Slf4j
public class CurrentUserRequest extends AccessToken {
    private TwitterProperties twitterProperties;
    private boolean cache = true;

    public CurrentUserRequest(){}

    public CurrentUserRequest(TwitterProperties twitterProperties, AccessToken accessToken){
        super(accessToken);
        this.twitterProperties = twitterProperties;
    }

    public CurrentUserRequest build(boolean cache){
        this.cache = cache;
        return this;
    }

    public SocialUserInfo execute() {
        try{
            SocialUserInfo userCache = TwitterCache.TokenCache.get(this.accessToken, SocialUserInfo.class);
            if(userCache != null && this.cache){
                return userCache;
            }
            User user = getApi(this.accessToken, this.refreshToken, this.twitterProperties)
                    .users()
                    .findMyUser()
                    .userFields(Set.of("created_at"))
                    .execute().getData();

            if (user == null) {
                log.error("Failed to get Twitter user info");
                return null;
            }

            log.info("twitter current user:{}", user);
            SocialUserInfo userInfo =
                SocialUserInfo.builder()
                    .userId(user.getId())
                    .userName(user.getUsername())
                    .profileImageUrl(null)
                    .createdAt(user.getCreatedAt() != null ? user.getCreatedAt().toEpochSecond() : null)
                    .build();
            TwitterCache.TokenCache.put(this.accessToken, JSON.toJSONString(userInfo));
            return userInfo;
        }catch (Exception ex){
            log.error("twitter CurrentUserRequest error, properties:{}, accessToken:{}, refreshToken:{}", this.twitterProperties, this.accessToken, this.refreshToken, ex);
            return null;
        }
    }
}
