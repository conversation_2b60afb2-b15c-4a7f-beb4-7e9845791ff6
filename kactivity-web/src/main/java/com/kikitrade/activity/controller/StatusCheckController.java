package com.kikitrade.activity.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Slf4j
@Deprecated
public class StatusCheckController {

    @ResponseBody
    @RequestMapping(value = "/_health_check", method = {RequestMethod.GET})
    public String check() {
        log.info("/_health_check invoked");
        return "success";
    }
}
