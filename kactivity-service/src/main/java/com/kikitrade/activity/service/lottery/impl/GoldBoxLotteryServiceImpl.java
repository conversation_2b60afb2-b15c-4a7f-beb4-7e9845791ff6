package com.kikitrade.activity.service.lottery.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.model.LotteryItem;
import com.kikitrade.activity.api.model.LotteryResponse;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.activity.dal.tablestore.builder.ActivityLotteryItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.ActivityLotteryItem;
import com.kikitrade.activity.dal.tablestore.model.LotteryAward;
import com.kikitrade.activity.dal.tablestore.model.LotteryConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.kseq.api.SeqClient;
import com.kikitrade.kseq.api.model.SeqRule;
import com.kikitrade.order.api.RemoteOrderService;
import com.kikitrade.order.model.constant.OrderEventEnum;
import com.kikitrade.order.model.exception.OrderException;
import com.kikitrade.order.model.request.PlaceOrderRequest;
import com.kikitrade.order.model.response.PlaceOrderResponse;
import com.kikitrade.quota.api.model.response.RpcResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/20 20:20
 */
@Service("goldBoxLotteryService")
@Slf4j
public class GoldBoxLotteryServiceImpl extends AbstractLotteryService{

    @Resource
    private ActivityLotteryItemBuilder activityLotteryItemBuilder;
    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;
    @Resource
    private SeqClient seqClient;
    @DubboReference
    private RemoteOrderService remoteOrderService;

    @Override
    public LotteryAward draw(String customerId, LotteryConfig lotteryConfig) throws Exception {
        LotteryAward award = JSON.parseObject(lotteryConfig.getAwards(), LotteryAward.class);
        if(award.getAmount().contains("-")){
            //奖励积分包含 "-" 表示随机
            award.setAmount(String.valueOf(RandomUtil.randomInt(Integer.parseInt(award.getAmount().split("-")[0].trim()), Integer.parseInt(award.getAmount().split("-")[1].trim()))));
        }
        return award;
    }

    @Override
    public LotteryResponse postAction(ActivityLotteryItem lotteryItem) {

        PlaceOrderRequest orderRequest = new PlaceOrderRequest();
        orderRequest.setEvent(OrderEventEnum.lottery);
        orderRequest.setSaasId(lotteryItem.getSaasId());
        orderRequest.setCustomerId(lotteryItem.getCustomerId());
        orderRequest.setParam(JSON.toJSONString(lotteryItem));

        //生成订单
        try {
            PlaceOrderResponse orderResponse = remoteOrderService.placeOrder(orderRequest);
            lotteryItem.setStatus(ActivityConstant.LotteryStatus.NO_PAID.name());
            activityLotteryItemBuilder.update(lotteryItem);
            LotteryResponse lotteryResponse = new LotteryResponse();
            lotteryResponse.setOrderId(orderResponse.getOrderId());
            return lotteryResponse;
        } catch (OrderException e) {
            throw new RuntimeException(e);
        }
    }
}
