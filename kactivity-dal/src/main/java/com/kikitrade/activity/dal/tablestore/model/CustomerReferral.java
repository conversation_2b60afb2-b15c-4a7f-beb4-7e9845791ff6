package com.kikitrade.activity.dal.tablestore.model;


import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "customer_referral")
public class CustomerReferral implements Serializable {

    private static final long serialVersionUID = -1847009177261737664L;

    public static final String INDEX_CUSTOMER_REFERRAL_DEFAULT_INDEX = "idx_customer_referral_default_index";

    public static final String IDX_CUSTOMER_REFERRAL_BY_CID = "idx_customer_referral_by_cid";

    @SearchIndex(name = INDEX_CUSTOMER_REFERRAL_DEFAULT_INDEX, column = "referrer_id")
    @Index(name = IDX_CUSTOMER_REFERRAL_BY_CID, pkColumn = "referrer_id", pkValue = 1)
    @PartitionKey(name = "referrer_id")
    private String referrerId = "NULL";

    @SearchIndex(name = INDEX_CUSTOMER_REFERRAL_DEFAULT_INDEX, column = "register_time", fieldType = FieldType.LONG)
    @Index(name = IDX_CUSTOMER_REFERRAL_BY_CID, pkColumn = "register_time", pkValue = 2)
    @PartitionKey(value = 1, name = "register_time", type = PartitionKey.Type.INTEGER)
    private Date registerTime;

    @SearchIndex(name = INDEX_CUSTOMER_REFERRAL_DEFAULT_INDEX, column = "customer_id")
    @Index(name = IDX_CUSTOMER_REFERRAL_BY_CID, pkColumn = "customer_id")
    @PartitionKey(value = 2, name = "customer_id")
    private String customerId;

    @Index(name = IDX_CUSTOMER_REFERRAL_BY_CID, dfColumn = "email")
    @Column(name = "email", isDefined = true)
    private String userName = "NULL";

    @Index(name = IDX_CUSTOMER_REFERRAL_BY_CID, dfColumn = "code")
    @SearchIndex(name = INDEX_CUSTOMER_REFERRAL_DEFAULT_INDEX, column = "code")
    @Column(name = "code", isDefined = true)
    private String invitationCode;

    @SearchIndex(name = INDEX_CUSTOMER_REFERRAL_DEFAULT_INDEX, column = "referrer_email")
    @Index(name = IDX_CUSTOMER_REFERRAL_BY_CID, dfColumn = "referrer_email")
    @Column(name = "referrer_email", isDefined = true)
    private String referrerUserName;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Index(name = IDX_CUSTOMER_REFERRAL_BY_CID, dfColumn = "root_referrer_id")
    @SearchIndex(name = INDEX_CUSTOMER_REFERRAL_DEFAULT_INDEX, column = "root_referrer_id")
    @Column(name = "root_referrer_id", isDefined = true)
    private String rootReferrerId = "NULL";

    @Column(name = "root_referrer_user_name")
    private String rootReferrerUserName = "NULL";

    @Index(name = IDX_CUSTOMER_REFERRAL_BY_CID, dfColumn = "partner_ship")
    @Column(name = "partner_ship", isDefined = true)
    private Boolean partnerShip;

    /**
     * 入伙时间
     */
    @Index(name = IDX_CUSTOMER_REFERRAL_BY_CID, dfColumn = "partner_time")
    @Column(name = "partner_time", isDefined = true)
    private Date partnerTime;

    @Index(name = IDX_CUSTOMER_REFERRAL_BY_CID, dfColumn = "saas_id")
    @Column(name = "saas_id", isDefined = true)
    private String saasId;

    @SearchIndex(name = INDEX_CUSTOMER_REFERRAL_DEFAULT_INDEX, column = "activity_type", fieldType = FieldType.LONG)
    @Column(name = "activity_type", isDefined = true)
    private Integer activityType;

    @Transient
    private Boolean isPhoneCertified = false;
    @Transient
    private Boolean isGoogleCertified = false;
    @Transient
    private Boolean isBoundPhone = false;

}
