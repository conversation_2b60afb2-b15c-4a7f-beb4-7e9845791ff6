package com.kikitrade.activity.mock.impl;//package com.kikitrade.activity.mock.impl;
//
//import com.kikitrade.activity.dal.tablestore.builder.CustomerMiscBuilder;
//import com.kikitrade.activity.mock.CustomerMiscBuilderMock;
//import org.mockito.Mockito;
//import org.springframework.stereotype.Component;
//
//import jakarta.annotation.Resource;
//
//import java.util.HashMap;
//import java.util.Map;
//
//import static org.mockito.ArgumentMatchers.anyString;
//
//@Component
//public class CustomerMiscBuilderMockImpl implements CustomerMiscBuilderMock {
//    @Resource
//    CustomerMiscBuilder customerMiscBuilder;
//
//    @Override
//    public void getNickName() {
//        Mockito.doAnswer(c -> {
//            Map<String, String> map = new HashMap<>();
//            map.put("nick_name", "TestNickName");
//            return map;
//        }).when(customerMiscBuilder).getNickName(anyString());
//    }
//}
