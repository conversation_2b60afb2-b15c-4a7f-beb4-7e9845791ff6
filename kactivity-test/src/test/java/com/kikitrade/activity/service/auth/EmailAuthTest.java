package com.kikitrade.activity.service.auth;

import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.auth.AuthService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import jakarta.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2025/7/22 19:30
 * @description: Email认证测试
 */
@SpringBootTest
@TestPropertySource(locations = "classpath:application-junit.properties")
public class EmailAuthTest {

    @Resource
    private AuthService authService;

    @Resource
    private RedisService redisService;

    @Test
    public void testEmailAuthSuccess() {
//        // 准备测试数据
//        String saasId = "test-saas";
//        String customerId = "test-customer-123";
//        String email = "<EMAIL>";
//        String verifyCode = "123456";
//
//        // 在redis中设置验证码
//        String redisKey = RedisKeyConst.ACTIVITY_EMAIL_VERIFY_CODE.getKey(customerId);
//        redisService.setIfAbsent(redisKey, verifyCode, 2 * 60);
//
//        // 执行email认证
//        Token token = authService.auth(saasId, "email", verifyCode, email, customerId, null, null, null);
//
//        // 验证结果
//        assertNotNull(token);
//        assertNotNull(token.getAccessToken());
//        assertEquals(30 * 60, token.getExpiresIn());
//
//        // 验证redis中的验证码已被删除
//        String remainingCode = redisService.get(redisKey);
//        assertNull(remainingCode);
//
//        System.out.println("Email认证成功，token: " + token.getAccessToken());
    }

    @Test
    public void testEmailAuthWithInvalidCode() {
        // 准备测试数据
        String saasId = "test-saas";
        String customerId = "test-customer-456";
        String email = "<EMAIL>";
        String correctCode = "123456";
        String wrongCode = "654321";
        
        // 在redis中设置验证码
        String redisKey = RedisKeyConst.ACTIVITY_EMAIL_VERIFY_CODE.getKey(customerId);
        redisService.setIfAbsent(redisKey, correctCode, 2 * 60);
        
        // 使用错误的验证码进行认证
        ActivityException exception = assertThrows(ActivityException.class, () -> {
            authService.auth(saasId, "email", wrongCode, email, customerId, null, null, null);
        });
        
        // 验证异常类型
        assertEquals(ActivityResponseCode.AUTH_CODE_INVALID, exception.getCode());
        
        // 验证redis中的验证码仍然存在
        String remainingCode = redisService.get(redisKey);
        assertEquals(correctCode, remainingCode);
        
        // 清理测试数据
        redisService.del(redisKey);
        
        System.out.println("错误验证码测试通过");
    }

    @Test
    public void testEmailAuthWithExpiredCode() {
        // 准备测试数据
        String saasId = "test-saas";
        String customerId = "test-customer-789";
        String email = "<EMAIL>";
        String verifyCode = "123456";
        
        // 不在redis中设置验证码，模拟过期情况
        
        // 尝试认证
        ActivityException exception = assertThrows(ActivityException.class, () -> {
            authService.auth(saasId, "email", verifyCode, email, customerId, null, null, null);
        });
        
        // 验证异常类型
        assertEquals(ActivityResponseCode.AUTH_CODE_INVALID, exception.getCode());
        
        System.out.println("过期验证码测试通过");
    }

    @Test
    public void testSendVerifyCode() {
        // 测试发送验证码功能
        String saasId = "test-saas";
        String customerId = "test-customer-send";
        String handleName = "test-handleName";
        String channel = "email";
        String receiver = "<EMAIL>";
        
        Boolean result = authService.sendVerifyCode(saasId, customerId, handleName, channel, receiver);
        
        // 验证发送结果
        assertTrue(result);
        
        // 验证redis中是否存储了验证码
        String redisKey = RedisKeyConst.ACTIVITY_EMAIL_VERIFY_CODE.getKey(customerId);
        String storedCode = redisService.get(redisKey);
        assertNotNull(storedCode);
        assertEquals(6, storedCode.length());
        
        // 清理测试数据
        redisService.del(redisKey);
        
        System.out.println("发送验证码测试通过，验证码: " + storedCode);
    }
}
