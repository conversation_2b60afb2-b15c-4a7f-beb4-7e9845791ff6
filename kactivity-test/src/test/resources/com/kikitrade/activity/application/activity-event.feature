## language: zh-CN
#功能: activity-event
#
#  背景:
#    假如activity-event-配置默认activityTaskConfig
#
#  场景大纲: 注册-一次
#    假如activity-event-配置activityTaskConfig<config>
#    假如activity-event-配置用户vip等级<vipLevel>
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | config              | vipLevel | eventMessage                                                                                                | activityCustomRewardList                                                                                                                                                                                                                                                                                                                              | assetTransferInRequestList                                                                                                                                                                               | activityTaskItemList                                                                                                                                                                                                                                                                                                                     | activityTaskDetailList                                                                                                        |
#      | {"event":"SIGN_IN"} | L0       | {"eventCode":"sign_in","customerId":"20220930145600","globalUid":"","eventTime":"","targetId":"","verb":""} | [{"amount":"65.00","batchId":"20230308","businessId":"2023030811260207721","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"SIGN_IN:2023030811260207721","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023030814064007951","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061500360218","completeThreshold":1,"created":"2023-01-06 07:00:36","customerId":"20220930145600","cycle":"20230106","event":"SIGN_IN","expiredTime":"20230106235959","modified":"2023-01-06 07:00:36","progress":1,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:00:35","event":"SIGN_IN","status":"DONE","targetId":"sign_in","taskId":"202301061500360218"}] |
#
#
#  场景大纲: 发帖-一次
#    假如activity-event-配置用户vip等级<vipLevel>
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | vipLevel | eventMessage                                                                                                                              | activityCustomRewardList                                                                                                                                                                                                                                                                                                                           | assetTransferInRequestList                                                                                                                                                                               | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                              |
#      | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | [{"amount":"65.00","batchId":"20230308","businessId":"2023030811260308561","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023030811260308561","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023030811431608111","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061501210964","completeThreshold":1,"created":"2023-01-06 07:01:21","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:01:21","progress":1,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:01:21","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061501210964"}] |
#
#
#  场景大纲: 发帖-一次，remoteAssetOperateService.transferIn异常
#    假如activity-event-配置用户vip等级<vipLevel>
#    假如activity-event-配置remoteAssetOperateService.transferIn异常
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | vipLevel | eventMessage                                                                                                                              | activityCustomRewardList                                                                                                                                                                                                                                                                                                                          | assetTransferInRequestList | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList |
#      | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | [{"amount":"65.00","batchId":"20230308","businessId":"2023030811260400771","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023030811260400771","status":"AWARD_FAILED","userType":"Normal"}] | []                         | [{"activityId":"2022082405582496809807","businessId":"202301061501400760","completeThreshold":1,"created":"2023-01-06 07:01:40","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:01:43","progress":0,"status":"APPENDING","taskId":"2022082405582497101000"}] | []                     |
#
#
#  场景大纲: 发帖-二次，第一次失败，第二次成功
#    假如activity-event-配置用户vip等级<vipLevel>
#    假如activity-event-配置remoteAssetOperateService.transferIn异常
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    假如activity-event-配置remoteAssetOperateService.transferIn正常
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | vipLevel | eventMessage                                                                                                                              | activityCustomRewardList                                                                                                                                                                                                                                                                                                                           | assetTransferInRequestList                                                                                                                                                                               | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                              |
#      | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | [{"amount":"65.00","batchId":"20230308","businessId":"2023030811260402551","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023030811260402551","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023030811431701371","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061501440118","completeThreshold":1,"created":"2023-01-06 07:01:44","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:01:44","progress":1,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:01:44","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061501440118"}] |
#
#
#  场景大纲: 发帖-一次，重复发送事件消息
#    假如activity-event-配置用户vip等级<vipLevel>
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | vipLevel | eventMessage                                                                                                                              | activityCustomRewardList                                                                                                                                                                                                                                                                                                                           | assetTransferInRequestList                                                                                                                                                                               | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                              |
#      | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | [{"amount":"65.00","batchId":"20230308","businessId":"2023030811260404661","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023030811260404661","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023030811431703071","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061501440359","completeThreshold":1,"created":"2023-01-06 07:01:44","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:01:44","progress":1,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:01:44","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061501440359"}] |
#
#
#  场景大纲: 发帖-二次-等于complete_times=2
#    假如activity-event-配置用户vip等级<vipLevel>
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    当activity-event-系统接收到活动事件消息<eventMessage2>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | vipLevel | eventMessage                                                                                                                              | eventMessage2                                                                                                                             | activityCustomRewardList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | assetTransferInRequestList                                                                                                                                                                                                                                                                                                                                                                                      | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                                                                                                                                                                |
#      | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_2","eventTime":"","targetId":"test_target_id_2","verb":""} | [{"amount":"65.00","batchId":"20230106","businessId":"2023010615014502811","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023010615014502811","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"65.00","batchId":"20230106","businessId":"2023010615014502812","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023010615014502812","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023030814064209801","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"},{"amount":65.00,"businessId":"2023030814064209802","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061527530864","completeThreshold":1,"created":"2023-01-06 07:27:53","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:27:53","progress":2,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:27:53","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061527530864"},{"completeTime":"2023-01-06 07:27:53","event":"POST","status":"DONE","targetId":"test_target_id_2","taskId":"202301061527530864"}] |
#
#
#  场景大纲: 发帖-三次-超过complete_times=2，task忽略
#    假如activity-event-配置用户vip等级<vipLevel>
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    当activity-event-系统接收到活动事件消息<eventMessage2>
#    当activity-event-系统接收到活动事件消息<eventMessage3>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | vipLevel | eventMessage                                                                                                                              | eventMessage2                                                                                                                             | eventMessage3                                                                                                                             | activityCustomRewardList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | assetTransferInRequestList                                                                                                                                                                                                                                                                                                                                                                                      | activityTaskItemList                                                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                                                                                                                                                                |
#      | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_2","eventTime":"","targetId":"test_target_id_2","verb":""} | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_3","eventTime":"","targetId":"test_target_id_3","verb":""} | [{"amount":"65.00","batchId":"20230308","businessId":"2023030811260408531","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023030811260408531","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"65.00","batchId":"20230308","businessId":"2023030811260408532","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023030811260408532","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023030811431706361","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"},{"amount":65.00,"businessId":"2023030811431706362","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061527540046","completeThreshold":1,"completeTime":"2023-01-06 07:27:54","created":"2023-01-06 07:27:54","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:27:54","progress":2,"status":"DONE","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:27:53","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061527540046"},{"completeTime":"2023-01-06 07:27:54","event":"POST","status":"DONE","targetId":"test_target_id_2","taskId":"202301061527540046"}] |
#
#  场景大纲: 发帖-一次-配置cycle WEEKLY/MONTHLY
#    假如activity-event-配置activityTaskConfig<config>
#    假如activity-event-配置用户vip等级<vipLevel>
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | config              | vipLevel | eventMessage                                                                                                                              | activityCustomRewardList                                                                                                                                                                                                                                                                                                                           | assetTransferInRequestList                                                                                                                                                                               | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                              |
#      | {"cycle":"WEEKLY"}  | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | [{"amount":"65.00","batchId":"20230306","businessId":"2023030811431708281","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023030811431708281","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023030814064307861","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061527540233","completeThreshold":1,"created":"2023-01-06 07:27:54","customerId":"20220930145600","cycle":"20230102","event":"POST","expiredTime":"20230108235959","modified":"2023-01-06 07:27:54","progress":1,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:27:54","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061527540233"}] |
#      | {"cycle":"MONTHLY"} | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | [{"amount":"65.00","batchId":"20230301","businessId":"2023030811431709601","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023030811431709601","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023030814064400041","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061527540359","completeThreshold":1,"created":"2023-01-06 07:27:54","customerId":"20220930145600","cycle":"20230131","event":"POST","expiredTime":"20230131235959","modified":"2023-01-06 07:27:54","progress":1,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:27:54","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061527540359"}] |
#
#
#  场景大纲: 发帖-config status（1） 状态无效 task忽略
#    假如activity-event-配置activityTaskConfig<config>
#    假如activity-event-配置用户vip等级<vipLevel>
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | config         | vipLevel | eventMessage                                                                                                                              | activityCustomRewardList | assetTransferInRequestList | activityTaskItemList | activityTaskDetailList |
#      | {"status":"1"} | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | []                       | []                         | []                   | []                     |
#
#  场景大纲: 发帖-一次，小于completeThreshold（2）task忽略
#    假如activity-event-配置activityTaskConfig<config>
#    假如activity-event-配置用户vip等级<vipLevel>
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | config                    | vipLevel | eventMessage                                                                                                                              | activityCustomRewardList | assetTransferInRequestList | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                              |
#      | {"completeThreshold":"2"} | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | []                       | []                         | [{"activityId":"2022082405582496809807","businessId":"202301061527540569","completeThreshold":2,"created":"2023-01-06 07:27:54","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:27:54","progress":1,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:27:54","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061527540569"}] |
#
#  场景大纲: 发帖-二次，等于completeThreshold（2）
#    假如activity-event-配置activityTaskConfig<config>
#    假如activity-event-配置用户vip等级<vipLevel>
#    当activity-event-系统接收到活动事件消息<eventMessage>
#    当activity-event-系统接收到活动事件消息<eventMessage2>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | config                    | vipLevel | eventMessage                                                                                                                              | eventMessage2                                                                                                                             | activityCustomRewardList                                                                                                                                                                                                                                                                                                                           | assetTransferInRequestList                                                                                                                                                                               | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                                                                                                                                                                |
#      | {"completeThreshold":"2"} | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_2","eventTime":"","targetId":"test_target_id_2","verb":""} | [{"amount":"65.00","batchId":"20230308","businessId":"2023030811431803151","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023030811431803151","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023030814064405381","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061527540701","completeThreshold":2,"created":"2023-01-06 07:27:54","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:27:54","progress":2,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:27:54","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061527540701"},{"completeTime":"2023-01-06 07:27:54","event":"POST","status":"DONE","targetId":"test_target_id_2","taskId":"202301061527540701"}] |
#
#
##  场景大纲: 发帖-并发-系统收到三次并发的不同事件消息-预期都成功
##    假如activity-event-配置activityTaskConfig<config>
##    假如activity-event-配置用户vip等级<vipLevel>
##    #当activity-event-并发-系统接收到活动事件消息<eventMessage>
##    当activity-event-并发-系统接收到不同活动事件消息<eventMessage>
##    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
##    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
##    那么activity-event-校验activityTaskItemList<activityTaskItemList>
##    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
##    例子:
##      | config                | vipLevel | eventMessage                                                                                                                              | eventMessage                                                                                                                              | activityCustomRewardList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | assetTransferInRequestList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                                                                                                                                                                                                                                                                                                  |
##      | {"completeTimes":"3"} | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_2","eventTime":"","targetId":"test_target_id_2","verb":""} | [{"amount":"65.00","batchId":"20230106","businessId":"2023010615275408691","businessType":"task","customerId":"20220930145600","rewardType":"POINT","seq":"POST:2023010615275408691","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"65.00","batchId":"20230106","businessId":"2023010615275408692","businessType":"task","customerId":"20220930145600","rewardType":"POINT","seq":"POST:2023010615275408692","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"65.00","batchId":"20230106","businessId":"2023010615275408693","businessType":"task","customerId":"20220930145600","rewardType":"POINT","seq":"POST:2023010615275408693","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023010615275408691","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","saasId":"kiki","type":"POINT"},{"amount":65.00,"businessId":"2023010615275408692","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","saasId":"kiki","type":"POINT"},{"amount":65.00,"businessId":"2023010615275408693","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061527540869","completeThreshold":1,"created":"2023-01-06 07:27:54","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:27:54","progress":3,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:27:54","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061527540869"},{"completeTime":"2023-01-06 07:27:54","event":"POST","status":"DONE","targetId":"test_target_id_2","taskId":"202301061527540869"},{"completeTime":"2023-01-06 07:27:54","event":"POST","status":"DONE","targetId":"test_target_id_3","taskId":"202301061527540869"}] |
#
##    # TODO： BUG：activityTaskItemList PROGRASS:3 activityTaskDetailList为3个
##  场景大纲: 发帖-并发-系统收到三次并发的不同事件消息-预期成功两次
##    假如activity-event-配置activityTaskConfig<config>
##    假如activity-event-配置用户vip等级<vipLevel>
##    当activity-event-并发-系统接收到活动事件消息<eventMessage>
##    当activity-event-并发-系统接收到不同活动事件消息<eventMessage>
##    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
##    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
##    那么activity-event-校验activityTaskItemList<activityTaskItemList>
##    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
##    例子:
##      | config                | vipLevel | eventMessage                                                                                                                              | eventMessage                                                                                                                              | activityCustomRewardList                                                                                                                                                                                                                                                                                                                                                                                                                                              | assetTransferInRequestList                                                                                                                                                                                                                                                                                                                | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                                                                                                                                                                                                                                                                                                  |
##      | {"completeTimes":"2"} | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_2","eventTime":"","targetId":"test_target_id_2","verb":""} | [{"amount":"65.00","batchId":"20230106","businessId":"2023010615275500851","businessType":"task","customerId":"20220930145600","rewardType":"POINT","seq":"POST:2023010615275500851","status":"AWARD_SUCCESS","userType":"Normal"},{"amount":"65.00","batchId":"20230106","businessId":"2023010615275500852","businessType":"task","customerId":"20220930145600","rewardType":"POINT","seq":"POST:2023010615275500852","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023010615275500851","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","saasId":"kiki","type":"POINT"},{"amount":65.00,"businessId":"2023010615275500852","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061527550085","completeThreshold":1,"created":"2023-01-06 07:27:55","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:27:55","progress":2,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:27:55","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061527550085"},{"completeTime":"2023-01-06 07:27:55","event":"POST","status":"DONE","targetId":"test_target_id_3","taskId":"202301061527550085"},{"completeTime":"2023-01-06 07:27:55","event":"POST","status":"DONE","targetId":"test_target_id_2","taskId":"202301061527550085"}] |
#
#
#  场景大纲: 发帖-并发-系统收到三次并发的相同事件消息-预期成功一次
#    假如activity-event-配置activityTaskConfig<config>
#    假如activity-event-配置用户vip等级<vipLevel>
#    当activity-event-并发-系统接收到相同活动事件消息<eventMessage>
#    那么activity-event-校验activityCustomRewardList<activityCustomRewardList>
#    那么activity-event-校验assetTransferInRequestList<assetTransferInRequestList>
#    那么activity-event-校验activityTaskItemList<activityTaskItemList>
#    那么activity-event-校验activityTaskDetailList<activityTaskDetailList>
#    例子:
#      | config                | vipLevel | eventMessage                                                                                                                              | activityCustomRewardList                                                                                                                                                                                                                                                                                                                           | assetTransferInRequestList                                                                                                                                                                               | activityTaskItemList                                                                                                                                                                                                                                                                                                                  | activityTaskDetailList                                                                                                              |
#      | {"completeTimes":"3"} | L0       | {"eventCode":"post","customerId":"20220930145600","globalUid":"test_global_uid_1","eventTime":"","targetId":"test_target_id_1","verb":""} | [{"amount":"65.00","batchId":"20230308","businessId":"2023030811431804771","businessType":"task","customerId":"20220930145600","extendParam":"{\"desc\":\"${complete}${L0Post post}\"}","extendParamMap":{"desc":"${complete}${L0Post post}"},"rewardType":"POINT","seq":"POST:2023030811431804771","status":"AWARD_SUCCESS","userType":"Normal"}] | [{"amount":65.00,"businessId":"2023030814064407851","businessType":"ACTIVITY_TASK","category":"NORMAL","customerId":"20220930145600","desc":"${complete}${L0Post post}","saasId":"kiki","type":"POINT"}] | [{"activityId":"2022082405582496809807","businessId":"202301061527550275","completeThreshold":1,"created":"2023-01-06 07:27:55","customerId":"20220930145600","cycle":"20230106","event":"POST","expiredTime":"20230106235959","modified":"2023-01-06 07:27:55","progress":1,"status":"APPENDING","taskId":"2022082405582497101000"}] | [{"completeTime":"2023-01-06 07:27:55","event":"POST","status":"DONE","targetId":"test_target_id_1","taskId":"202301061527550275"}] |
#
#
