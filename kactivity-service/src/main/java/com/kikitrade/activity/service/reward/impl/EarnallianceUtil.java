package com.kikitrade.activity.service.reward.impl;

import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.service.common.config.EarnallianceProperties;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

@Slf4j
public class EarnallianceUtil {

    public static String getSignature(String clientId, String secret, long now, Map<String, Object> map) {
        String message = clientId + now + JSON.toJSONString(map);
        return generateHmacSha256Signature(secret, message);
    }

    private static String generateHmacSha256Signature(String secret, String message){
        try{
            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256Hmac.init(secretKey);
            byte[] hmacBytes = sha256Hmac.doFinal(message.getBytes(StandardCharsets.UTF_8));
            String signature = bytesToHex(hmacBytes);
            return signature;
        }catch (Exception e){
            log.error("generateHmacSha256Signature error", e);
            return null;
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public static EarnallianceProperties getProperties(EarnallianceProperties properties) {
        if(properties == null){
            return new EarnallianceProperties();
        }else{
            return properties;
        }
    }
}
