package com.kikitrade.activity.stepdefs;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import io.cucumber.java.zh_cn.当;
import lombok.SneakyThrows;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.Date;
import java.util.concurrent.CountDownLatch;

public class BugsStepdefs {
    @当("bugs-并发调用TimeUtil.getDataStr")
    public void bugs并发调用TimeUtilGetDataStrDateString() {
        long time = 1683008184000L;
        long time2 = 1680416184000L;
        int len = 200;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch downLatch = new CountDownLatch(1);

        for (int i = 0; i < len; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        endLatch.countDown();
//                        endLatch.wait();
//                        downLatch.await();
                        System.out.println(System.currentTimeMillis());
                        String parse = TimeUtil.getDataStr(TimeUtil.parseUnittime(time), TimeUtil.YYYYMM);
                        System.out.println("parse1: "+ parse);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }


                }
            }).start();
        }
//        endLatch.await();

//        for (int i = 0; i < len/2; i++) {
//            new Thread(new Runnable() {
//                @SneakyThrows
//                @Override
//                public void run() {
//                    try {
//                        downLatch.await();
//                        System.out.println(System.currentTimeMillis());
//                        String parse = TimeUtil.getDataStr(TimeUtil.parseUnittime(time2), TimeUtil.YYYYMM);
//                        System.out.println("parse2: "+ parse);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                    endLatch.countDown();
//
//                }
//            }).start();
//        }

//        downLatch.countDown();
        try {
            endLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @当("bugs-并发调用TimeUtil.getCurrentTime")
    public void bugs并发调用TimeUtilGetCurrentTime() {
        long time = 1683008184000L;
        int len = 10;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch downLatch = new CountDownLatch(1);

        for (int i = 0; i < 5; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        downLatch.await();
                        System.out.println(System.currentTimeMillis());
                        String parse = TimeUtil.getCurrentTime(TimeUtil.YYYYMMDDHHMMSS, 8);
                        System.out.println("parse1: "+ parse);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    endLatch.countDown();

                }
            }).start();
        }

        for (int i = 0; i < 5; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        downLatch.await();
                        System.out.println(System.currentTimeMillis());
                        String parse = TimeUtil.getCurrentTime(TimeUtil.YYYYMMDDHHMMSS, 1);
                        System.out.println("parse2: "+ parse);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    endLatch.countDown();

                }
            }).start();
        }

        downLatch.countDown();
        try {
            endLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @当("bugs-并发调用TimeUtil.getCurrentUtcTime")
    public void bugs并发调用TimeUtilGetCurrentUtcTime() {
        long time = 1683008184000L;
        int len = 10;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch downLatch = new CountDownLatch(1);

        for (int i = 0; i < 5; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        downLatch.await();
                        System.out.println(System.currentTimeMillis());
                        String parse = TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS);
                        System.out.println("parse1: "+ parse);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    endLatch.countDown();

                }
            }).start();
        }

        for (int i = 0; i < 5; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        downLatch.await();
                        System.out.println(System.currentTimeMillis());
                        String parse = TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD_000000);
                        System.out.println("parse2: "+ parse);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    endLatch.countDown();

                }
            }).start();
        }

        downLatch.countDown();
        try {
            endLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @当("bugs-并发调用TimeUtil.getUtcTime")
    public void bugs并发调用TimeUtilGetUtcTime() {
        long time1 = 1683008184000L;
        Date date1 = new Date(time1);
        long time2 = 1680416184000L;
        Date date2 = new Date(time2);
        int len = 10;
        final CountDownLatch endLatch = new CountDownLatch(len);
        final CountDownLatch downLatch = new CountDownLatch(1);

        for (int i = 0; i < 5; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        downLatch.await();
                        System.out.println(System.currentTimeMillis());
                        String parse = TimeUtil.getUtcTime(date1, TimeUtil.YYYYMMDDHHMMSS);
                        System.out.println("parse1: "+ parse);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    endLatch.countDown();

                }
            }).start();
        }
        for (int i = 0; i < 5; i++) {
            new Thread(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        downLatch.await();
                        System.out.println(System.currentTimeMillis());
                        String parse = TimeUtil.getUtcTime(date2, TimeUtil.YYYYMMDDHHMMSS);
                        System.out.println("parse2: "+ parse);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    endLatch.countDown();

                }
            }).start();
        }

        downLatch.countDown();
        try {
            endLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

//        Runnable runnable = () -> {
//            try {
//                Date date = TimeUtil.YYYYMMDDHHMMSS.parse("2023-06-02 08:52:10");
//                String str = TimeUtil.YYYYMMDDHHMMSS.format(date);
//                String utcTime = TimeUtil.getUtcTime(date2, TimeUtil.YYYYMMDDHHMMSS);
//                System.out.println("Thread: " + Thread.currentThread().getName() + ", result: " + str);
//                System.out.println("Thread: " + Thread.currentThread().getName() + ", result: " + utcTime);
//            } catch (ParseException e) {
//                e.printStackTrace();
//            }
//        };
//
//        // 启动多个线程并发执行，访问同一个 SimpleDateFormat 实例进行日期格式化和解析
//        for (int i = 0; i < 10; i++) {
//            new Thread(runnable).start();
//        }

    }
}
