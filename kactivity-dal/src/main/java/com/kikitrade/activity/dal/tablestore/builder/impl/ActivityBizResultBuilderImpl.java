package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBizResultBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBizResult;
import com.kikitrade.framework.common.model.PageResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.*;

/**
 * created by jacky. 2020/2/14 10:21 AM
 */
@Slf4j
@Repository
public class ActivityBizResultBuilderImpl extends WideColumnStoreBuilder<ActivityBizResult> implements ActivityBizResultBuilder {


    private static final String INDEX_FIND_STATUS = "activity_biz_results_index_find_status";

    @PostConstruct
    public void init() {
        init(ActivityBizResult.class);
    }

    @Override
    public boolean insertIfNotExist(ActivityBizResult reward) {
        return putRow(reward);
    }

    @Override
    public PageResult findByCustomer(String customerId, int offset, int limit) {
        return pageSearch(QueryBuilders.term("customer_id", customerId).build(),
                null, offset, limit, INDEX_FIND_STATUS);
    }

    @Override
    public long countByCustomer(String customerId) {
        String aggName = "count_agg_customer_reward_by_customer";
        SearchQuery query = SearchQuery.newBuilder().query(QueryBuilders.term("customer_id", customerId))
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(query, INDEX_FIND_STATUS, aggName);
    }


    @Override
    public PageResult findByCustomerAndActivity(String customerId, Integer activityId, int offset, int limit) {
        BoolQuery q = QueryBuilders.bool()
                .filter(QueryBuilders.term("customer_id", customerId))
                .filter(QueryBuilders.term("activity_id", activityId))
                .build();
        Sort sort = new Sort(Arrays.asList(new FieldSort("created", SortOrder.DESC)));
        return pageSearch(q, sort, offset, limit, INDEX_FIND_STATUS);
    }

    @Override
    public long countByCustomerAndActivity(String customerId, Integer activityId) {
        String aggName = "count_agg_activity_biz_result_" + UUID.randomUUID();

        BoolQuery.Builder filter = QueryBuilders.bool()
                .filter(QueryBuilders.term("customer_id", customerId))
                .filter(QueryBuilders.term("activity_id", activityId));

        SearchQuery query = SearchQuery.newBuilder().query(filter)
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(query, INDEX_FIND_STATUS, aggName);
    }

    @Override
    public PageResult findByCustomerAndActivityAndStatus(String customerId, Integer activityId, String status, int offset, int limit) {
        BoolQuery q = QueryBuilders.bool()
                .filter(QueryBuilders.term("customer_id", customerId))
                .filter(QueryBuilders.term("activity_id", activityId))
                .filter(QueryBuilders.term("status", status))
                .build();

        Sort sort = new Sort(Arrays.asList(new FieldSort("created", SortOrder.DESC)));
        return pageSearch(q, sort, offset, limit, INDEX_FIND_STATUS);
    }

    @Override
    public long countByCustomerAndActivityAndStatus(String customerId, Integer activityId, String status) {
        String aggName = "count_agg_activity_biz_result_" + UUID.randomUUID();
        BoolQuery.Builder filter = QueryBuilders.bool()
                .filter(QueryBuilders.term("customer_id", customerId))
                .filter(QueryBuilders.term("activity_id", activityId))
                .filter(QueryBuilders.term("status", status));

        SearchQuery query = SearchQuery.newBuilder().query(filter)
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(query, INDEX_FIND_STATUS, aggName);
    }


    @Override
    public List<ActivityBizResult> findByCustomerAndActivityDaily(String customerId, Integer activityId, Date date) {
        DateTime start = new DateTime(date).withTimeAtStartOfDay();
        DateTime end = start.plusDays(1);
        BoolQuery q = QueryBuilders.bool()
                .filter(QueryBuilders.term("customer_id", customerId))
                .filter(QueryBuilders.term("activity_id", activityId))
                .filter(QueryBuilders.range("created")
                        .greaterThanOrEqual(start.toDate().getTime())
                        .lessThan(end.toDate().getTime()))
                .build();
        List<ActivityBizResult> all = new ArrayList<>();
        int offset = 0;
        int limit = 10;
        while (true) {
            List<ActivityBizResult> page = search(q, null, offset, limit, INDEX_FIND_STATUS);
            all.addAll(page);
            if (page.size() < limit) {
                break;
            }
            offset = all.size();
        }
        return all;
    }
}
