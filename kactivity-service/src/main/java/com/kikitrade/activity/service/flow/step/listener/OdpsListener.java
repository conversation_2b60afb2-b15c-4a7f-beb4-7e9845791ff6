package com.kikitrade.activity.service.flow.step.listener;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.mysql.model.ActivityEntity;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchRewardRosterStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityBatchStatusStoreBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatch;
import com.kikitrade.activity.dal.tablestore.model.ActivityBatchStatus;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.service.business.ActivityBatchNewService;
import com.kikitrade.activity.service.business.ActivityEntityService;
import com.kikitrade.activity.service.exception.BatchJobException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.*;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-12-12 10:25
 */
@Component
@StepScope
@Slf4j
public class OdpsListener implements StepExecutionListener {

    @Resource
    private ActivityBatchNewService activityBatchNewService;
    @Resource
    private ActivityBatchStatusStoreBuilder activityBatchStatusStoreBuilder;
    @Resource
    private ActivityBatchRewardRosterStoreBuilder activityBatchRewardRosterStoreBuilder;
    @Resource
    private ActivityEntityService activityEntityService;

    @Value("#{jobParameters['batchId']}")
    private String batchId;
    @Value("#{jobParameters['activityId']}")
    private String activityId;

    @Override
    public void beforeStep(StepExecution stepExecution) {
    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        try{
            TimeUnit.SECONDS.sleep(5);
            ActivityBatchStatus batchStatus = activityBatchStatusStoreBuilder.getById(batchId);
            long count = activityBatchRewardRosterStoreBuilder.count(batchId);
            log.info("OdpsListener afterStep:{},{}", count, batchStatus.getNum());
            if(count == batchStatus.getNum()){
                ActivityBatch batch = activityBatchNewService.findByBatchId(batchId);
                if(batch.getRewardStatus().equals(ActivityConstant.BatchRewardStatusEnum.ODPS_COMPLETE.name())){
                    batch.setRewardStatus(ActivityConstant.BatchRewardStatusEnum.NOT_IMPORTED.name());
                    activityBatchNewService.updateBatch(batch);
                    activityBatchStatusStoreBuilder.updateStatusAndNum(batchId, ActivityConstant.BatchRewardStatusEnum.NOT_IMPORTED.name(), null);
                    return ExitStatus.COMPLETED;
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ExitStatus.FAILED;
    }
}
