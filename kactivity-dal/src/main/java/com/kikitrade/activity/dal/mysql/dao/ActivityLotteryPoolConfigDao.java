package com.kikitrade.activity.dal.mysql.dao;

import com.kikitrade.activity.dal.mysql.model.ActivityLotteryDetailConfig;
import com.kikitrade.activity.dal.mysql.model.ActivityLotteryPoolConfig;
import com.kikitrade.framework.mybatis.BaseDao;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ActivityLotteryPoolConfigDao extends BaseDao<ActivityLotteryPoolConfig, String> {

    @Insert("<script>" +
            "insert into activity_lottery_pool_config(`id`,`lottery_id`,`draw_id`,`pool_no`,`remain_num`,`created`,`modified`,`saasId`) values " +
            " <foreach collection='configList' item='item' index='index' separator=','>" +
            "            <trim prefix='(' suffix=')' suffixOverrides=','>" +
            "                    #{item.id,jdbcType=VARCHAR}," +
            "                    #{item.lotteryId,jdbcType=VARCHAR}," +
            "                    #{item.drawId,jdbcType=VARCHAR}," +
            "                    #{item.poolNo,jdbcType=VARCHAR}," +
            "                    #{item.remainNum,jdbcType=INTEGER}," +
            "                    #{item.created,jdbcType=TIMESTAMP}," +
            "                    #{item.modified,jdbcType=TIMESTAMP}," +
            "                    #{item.saasId,jdbcType=VARCHAR}," +
            "            </trim>" +
            "        </foreach>" +
            "</script>")
    boolean batchInsert(@Param("configList") List<ActivityLotteryPoolConfig> configList);

    @Update("update activity_lottery_pool_config set remain_num = remain_num - 1, modified = now() where draw_id = #{drawId} and pool_no = #{storeNo} and remain_num >= 1")
    int decreaseStoreNum(@Param("drawId") String drawId, @Param("storeNo") String storeNo);

    @Update("update activity_lottery_pool_config set remain_num = remain_num + #{num}, modified = now() where draw_id = #{drawId} and pool_no = #{storeNo}")
    int increaseStoreNum(@Param("drawId") String drawId, @Param("storeNo") String storeNo, @Param("num") int num);
}
