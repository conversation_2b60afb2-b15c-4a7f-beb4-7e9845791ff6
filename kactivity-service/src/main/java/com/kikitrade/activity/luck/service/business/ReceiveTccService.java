package com.kikitrade.activity.luck.service.business;

import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneOpenDTO;

/**
 *
 */
public interface ReceiveTccService {

    /**
     * 开启红包
     * @param luckFortuneOpenDTO
     * @return
     */
    LuckFortuneReceiveItem tryOpen(LuckFortuneOpenDTO luckFortuneOpenDTO, LuckFortuneReleaseItem luckFortuneReleaseItem, LuckFortuneReceiveItem luckFortuneReceiveItem) throws Exception;
}
