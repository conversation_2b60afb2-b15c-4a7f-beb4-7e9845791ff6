package com.kikitrade.activity.advice;

import com.kikitrade.activity.op.BusinessOP;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Component
@Aspect
@Slf4j
public class ActivityLotteryItemBuilderAdvice {

    @Pointcut("execution(* com.kikitrade.activity.dal.tablestore.builder.ActivityLotteryItemBuilder.insert(..))")
    public void insertPt() {
    }

    @Around("insertPt()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {

        if("ActivityLotteryItemBuilder.insert.ERR".equals(BusinessOP.pointBlast)){
            log.info("-----定点爆破:ActivityLotteryItemBuilder.insert");
            throw new RuntimeException("-----ActivityLotteryItemBuilder.insert.ERR");
        }
        Object ret = pjp.proceed();
        return ret;
    }
}
