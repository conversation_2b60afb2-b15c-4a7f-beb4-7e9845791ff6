package com.kikitrade.activity.luck.service.business;

import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.service.model.ReceiveItem;
import com.kikitrade.kevent.common.constant.EventConstants;

public interface AirDropEventService {

    /**
     * 领取后事件
     * @param receiveItem
     */
    void sendReceiveEvent(ReceiveItem receiveItem);

    /**
     * 发放空投后事件
     * @param releaseItem
     */
    void sendReleaseEvent(LuckFortuneReleaseItem releaseItem);

    /**
     * 事件上报
     * @param eventName
     * @param data
     * @param <T>
     */
    <T> void reportEvent(EventConstants.EventName eventName, T data);
}
