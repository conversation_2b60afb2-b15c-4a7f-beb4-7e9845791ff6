package com.kikitrade.activity.service.common.strategy.wallet;

import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.service.common.strategy.OpenStrategyAuthRequest;
import com.kikitrade.activity.service.common.strategy.SaasStrategyConstant;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/22 18:35
 * @description: Wallet认证拒绝策略
 */
@Component
@Slf4j
public class WalletAuthRejectStrategy implements WalletSaasStrategyService{

    @Resource
    private RemoteCustomerBindService remoteCustomerBindService;

    @Override
    public String strategy() {
        return SaasStrategyConstant.WalletStrategyEnum.REJECT.name();
    }

    @Override
    public void execute(OpenStrategyAuthRequest request) throws ActivityException {
        log.info("WalletAuthRejectStrategy execute start, request: {}", request);
        
        // 检查是否已经绑定了Wallet账号
        List<TCustomerDTO> customerDTOList = remoteCustomerBindService.findTCustomerBySocial(request.getSaasId(),
                OpenSocialPlatformEnum.wallet.name(), request.getAuthId());
        
        if (CollectionUtils.isNotEmpty(customerDTOList)) {
            log.warn("Wallet账号已被绑定, authId: {}", request.getAuthId());
            throw new ActivityException(ActivityResponseCode.AUTH_REPEAT, ActivityResponseCode.AUTH_REPEAT.getKey());
        }
        
        log.info("WalletAuthRejectStrategy execute end, request: {}", request);
    }
}
