package com.kikitrade.activity.dal.redis;

import lombok.Getter;

@Getter
public enum RedisKeyConst {

    ACTIVITY_KOL_EXTRA_REWARD_KEY("ACTIVITY:KOL_EXTRA_REWARD"),
    ACTIVITY_KEY("ACTIVITY:ACTIVITY_KEY"),
    ACTIVITY_RECORD_KEY("ACTIVITY:ACTIVITY_RECORD_KEY"),
    ACTIVITY_IMPORT_NUM_KEY("ACTIVITY:IMPORT_NUM:"),
    ACTIVITY_VALUE_KEY("ACTIVITY:ACTIVITY_VALUE:"),
    ACTIVITY_BATCH_VALUE_KEY("ACTIVITY:BATCH:BATCH_VALUE:"),
    ACTIVITY_MATERIAL_VALUE_KEY("ACTIVITY:MATER_VALUE:"),
    ACTIVITY_AUTO_BATCH_SEQ_KEY("ACTIVITY:AUTO_BATCH:SEQ:"),
    ACTIVITY_REWARD_SEQ_KEY("ACTIVITY:REWARD:INVITE:SEQ:"),
    //缓存的用户信息
    ACTIVITY_CUSTOMER_KEY("ACTIVITY:CUSTOMER:"),

    //红包状态
    LUCK_FORTUNE_RELEASE_STATUS_KEY("ACTIVITY:LUCK_FORTUNE:RELEASE:STATUS:"),
    //红包详情
    LUCK_FORTUNE_RELEASE_INFO_KEY("ACTIVITY:LUCK_FORTUNE:RELEASE:INFO:"),
    //待领取队列
    LUCK_FORTUNE_RELEASE_NOT_DRAW_KEY("{ACTIVITY:LUCK_FORTUNE:%s}RELEASE:NOT_DRAW"),
    //领取中队列
    LUCK_FORTUNE_RELEASE_DRAWING_KEY("ACTIVITY:LUCK_FORTUNE:RELEASE:DRAWING:"),
    //红包领取记录
    LUCK_FORTUNE_RECEIVE_DETAIL_KEY("{ACTIVITY:LUCK_FORTUNE:%s}RECEIVE:DETAIL:"),
    //待开启的红包
    LUCK_FORTUNE_RECEIVE_NOT_ACTIVE_KEY("ACTIVITY:LUCK_FORTUNE:WAIT:NOT_ACTIVE:DETAIL:"),
    //待开启的红包
    LUCK_FORTUNE_RECEIVE_EXPIRE_KEY("ACTIVITY:LUCK_FORTUNE:RECEIVE:EXPIRE:"),
    //领取完队列
    LUCK_FORTUNE_RELEASE_DREW_KEY("{ACTIVITY:LUCK_FORTUNE:%s}RELEASE:DREW:"),
    //用户领取时锁
    LUCK_FORTUNE_RECEIVE_CUSTOMER_LOCK_KEY("ACTIVITY:LUCK_FORTUNE:RECEIVE:LOCK:"),
    //红包构建锁
    LUCK_FORTUNE_RELEASE_BUILDING_LOCK_KEY("ACTIVITY:LUCK_FORTUNE:RELEASE:BUILDING:LOCK:"),
    //红包规则
    LUCK_FORTUNE_RULE_KEY("ACTIVITY:LUCK_FORTUNE_RULE:"),
    //红包releaseCode
    LUCK_FORTUNE_RELEASE_CODE_KEY("ACTIVITY:LUCK_FORTUNE:RELEASE:RELEASE_CODE:"),
    //任务缓存
    TASK_VALUE_KEY("ACTIVITY:TASK:"),
    //事件缓存
    TASK_EVENT_KEY("ACTIVITY:TASK_EVENT:"),
    //任务等级缓存
    TASK_LEVEL_KEY("ACTIVITY:TASK_LEVEL:"),
    //任务状态
    TASK_STATUS_KEY("ACTIVITY:TASK:STATUS:"),
    //任务状态
    TASK_PROCESS_KEY("ACTIVITY:PROCESS:"),

    //奖池详情
    LOTTERY_POOL_KEY("ACTIVITY:LOTTERY:POOL:"),
    LOTTERY_STORE_LOCK_KEY("ACTIVITY:LOTTERY:LOCK:"),
    LOTTERY_WIN_ITEM("ACTIVITY:LOTTERY:WIN:ITEM:"),

    LOTTERY_WINNER_KEY("ACTIVITY:LOTTERY:WINNER:"),

    ACTIVITY_CAL_LAST_KEY("ACTIVITY:REWARD:LAST"),

    ACTIVITY_REWARD_NOTICE("ACTIVITY:REWARD:NOTICE"),
    ACTIVITY_TWITTER_KOLS("ACTIVITY:TWITTER:KOLS"),
    ACTIVITY_TWITTER_POSITION("ACTIVITY:TWITTER:POSITION"),
    //授权范围发生变更，修改V0
    ACTIVITY_AUTH_TOKEN("ACTIVITY:AUTH:TOKEN:"),
    ACTIVITY_AUTH_SOCIAL_TOKEN("ACTIVITY:AUTH:SOCIAL:TOKEN:"),
    ACTIVITY_MEMBER_RANK("ACTIVITY:MEMBER:RANK"),
    ACTIVITY_BREAD_MEMBER_RANK("ACTIVITY:BREAD:MEMBER:RANK"),
    ACTIVITY_ZEEK_MEMBER_RANK("ACTIVITY:ZEEK:MEMBER:RANK"),
    ACTIVITY_LOTTERY_CYCLE_COUNT("ACTIVITY:LOTTERY:COUNT:CYCLE"),
    ACTIVITY_LOTTERY_CUMULATE_COUNT("ACTIVITY:LOTTERY:COUNT:CUMULATE"),
    TASK_DISTRIBUTE_KEY("ACTIVITY:TASK:DISTRIBUTE"),
    // quests管理端task_whitelist
    TASK_WHITELIST("TASK:WHITELIST:HASH"),

    ACTIVITY_EMAIL_VERIFY_CODE("ACTIVITY:EMAIL:VERIFY:CODE:"),
    ;

    private String prefix;

    RedisKeyConst(String prefix){
        this.prefix = prefix;
    }

    public String getKey(String content){
        return String.format("%s%s", this.prefix, content);
    }

    public static String getKey(String prefix, String content){
        return String.format("%s%s", prefix, content);
    }

    public String getMiddleKey(String content){
        return String.format(this.prefix, content);
    }
}
