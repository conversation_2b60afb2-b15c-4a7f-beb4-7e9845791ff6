package com.kikitrade.activity.mock.impl;

import com.kikitrade.activity.mock.RemoteCustomerBindServiceMock;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

import static org.mockito.ArgumentMatchers.anyString;

@Component
public class RemoteCustomerBindServiceMockImpl implements RemoteCustomerBindServiceMock {
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;


    @Override
    public void findByUid() {
        Mockito.doAnswer(c -> {
            String saasId = c.getArgument(0);
            String uid = c.getArgument(1);

            CustomerBindDTO customerBindDTO = new CustomerBindDTO();
            customerBindDTO.setCid(uid);
            customerBindDTO.setSaasId(saasId);
            return customerBindDTO;

        }).when(remoteCustomerBindService).findByUid(anyString(), anyString());
    }

    @Override
    public void findById() {
        Mockito.doAnswer(c -> {
            String saasId = c.getArgument(0);
            String cid = c.getArgument(1);
            CustomerBindDTO customerBindDTO = new CustomerBindDTO();
            customerBindDTO.setCid(cid);
            customerBindDTO.setSaasId(saasId);
            customerBindDTO.setUid("uid" + cid);
            return customerBindDTO;
        }).when(remoteCustomerBindService).findById(anyString(), anyString());
    }
}
