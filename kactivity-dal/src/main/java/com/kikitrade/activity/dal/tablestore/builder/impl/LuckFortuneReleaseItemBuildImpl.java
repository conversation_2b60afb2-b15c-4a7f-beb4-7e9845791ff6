package com.kikitrade.activity.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.condition.ColumnCondition;
import com.alicloud.openservices.tablestore.model.condition.CompositeColumnValueCondition;
import com.alicloud.openservices.tablestore.model.condition.SingleColumnValueCondition;
import com.alicloud.openservices.tablestore.model.filter.ColumnValueFilter;
import com.alicloud.openservices.tablestore.model.filter.CompositeColumnValueFilter;
import com.alicloud.openservices.tablestore.model.filter.SingleColumnValueFilter;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.AggregationBuilders;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.kikitrade.activity.dal.SeqGeneraterService;
import com.kikitrade.activity.dal.mysql.model.CustomerSeqRuleBuilder;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReleaseItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Time;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward.INDEX_ACTIVITY_CUSTOMER_REWARD_STATUS_INDEX;
import static com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem.IDX_RECEIVE_BY_EXPIRED_DATE;
import static com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem.SEARCH_RECEIVE_BY_ID;
import static com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem.*;

@Service
@Slf4j
public class LuckFortuneReleaseItemBuildImpl extends WideColumnStoreBuilder<LuckFortuneReleaseItem> implements LuckFortuneReleaseItemBuilder {

    @Resource
    private SeqGeneraterService seqGeneraterService;

    @Override
    public String getTableName(){
        return "luck_fortune_release_item";
    }

    @PostConstruct
    public void init() {
        init(LuckFortuneReleaseItem.class);
    }

    /**
     * 插入一个发放的红包
     *
     * @param luckFortuneReleaseItem
     * @return
     */
    @Override
    public boolean insert(LuckFortuneReleaseItem luckFortuneReleaseItem) {
        if(StringUtils.isBlank(luckFortuneReleaseItem.getId())){
            luckFortuneReleaseItem.setId(seqGeneraterService.next(CustomerSeqRuleBuilder.instance(getTableName())));
        }
        return super.putRow(luckFortuneReleaseItem, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    /**
     * 更新发放的红包状态到领取中
     *
     * @param luckFortuneReleaseItem
     * @return
     */
    @Override
    public boolean update(LuckFortuneReleaseItem luckFortuneReleaseItem) {
        luckFortuneReleaseItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        return super.updateRow(luckFortuneReleaseItem, condition);
    }

    /**
     * 更新发放的红包
     *
     * @param luckFortuneReleaseItem
     * @return
     */
    @Override
    public boolean updateStatusToDrawing(LuckFortuneReleaseItem luckFortuneReleaseItem) {
        luckFortuneReleaseItem.setStatus(ActivityConstant.LuckFortuneReleaseStatus.DRAWING.getCode());
        luckFortuneReleaseItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        ColumnCondition columnCondition =
                new CompositeColumnValueCondition(CompositeColumnValueCondition.LogicOperator.OR)
                .addCondition(new SingleColumnValueCondition("status", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(ActivityConstant.LuckFortuneReleaseStatus.BUILDING.getCode())))
                .addCondition(new SingleColumnValueCondition("status", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(ActivityConstant.LuckFortuneReleaseStatus.APPENDING.getCode())));
        condition.setColumnCondition(columnCondition);
        return super.updateRow(luckFortuneReleaseItem, condition);
    }

    /**
     * 更新发放的红包
     *
     * @param luckFortuneReleaseItem
     * @return
     */
    @Override
    public boolean updateStatusToInvalid(LuckFortuneReleaseItem luckFortuneReleaseItem) {
        luckFortuneReleaseItem.setStatus(ActivityConstant.LuckFortuneReleaseStatus.INVALID.getCode());
        luckFortuneReleaseItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        ColumnCondition columnCondition = new SingleColumnValueCondition("status", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(ActivityConstant.LuckFortuneReleaseStatus.APPENDING.getCode()));
        condition.setColumnCondition(columnCondition);
        return super.updateRow(luckFortuneReleaseItem, condition);
    }

    /**
     * 更新发放的红包状态到领取完成
     *
     * @param luckFortuneReleaseItem
     * @return
     */
    @Override
    public boolean updateStatusToDrew(LuckFortuneReleaseItem luckFortuneReleaseItem) {
        luckFortuneReleaseItem.setStatus(ActivityConstant.LuckFortuneReleaseStatus.DREW.getCode());
        luckFortuneReleaseItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        ColumnCondition columnCondition = new SingleColumnValueCondition("status", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(ActivityConstant.LuckFortuneReleaseStatus.DRAWING.getCode()));
        condition.setColumnCondition(columnCondition);
        return super.updateRow(luckFortuneReleaseItem, condition);
    }

    /**
     * 更新发放的红包状态到领取完成
     *
     * @param luckFortuneReleaseItem
     * @return
     */
    @Override
    public boolean updateToRefunding(LuckFortuneReleaseItem luckFortuneReleaseItem) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        CompositeColumnValueCondition columnCondition = new CompositeColumnValueCondition(CompositeColumnValueCondition.LogicOperator.OR);
        columnCondition.addCondition(new SingleColumnValueCondition("status", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(ActivityConstant.LuckFortuneReleaseStatus.DRAWING.getCode())));
        columnCondition.addCondition(new SingleColumnValueCondition("status", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(ActivityConstant.LuckFortuneReleaseStatus.REFUND_FAIL.getCode())));
        columnCondition.addCondition(new SingleColumnValueCondition("status", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(ActivityConstant.LuckFortuneReleaseStatus.EXPIRED.getCode())));
        condition.setColumnCondition(columnCondition);
        return super.updateRow(luckFortuneReleaseItem, condition);
    }

    /**
     * 更新发放的红包状态到领取完成
     *
     * @param luckFortuneReleaseItem
     * @return
     */
    @Override
    public boolean updateStatusToExpire(LuckFortuneReleaseItem luckFortuneReleaseItem) {
        luckFortuneReleaseItem.setStatus(ActivityConstant.LuckFortuneReleaseStatus.EXPIRED.getCode());
        luckFortuneReleaseItem.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);
        ColumnCondition columnCondition = new SingleColumnValueCondition("status", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(ActivityConstant.LuckFortuneReleaseStatus.DRAWING.getCode()));
        condition.setColumnCondition(columnCondition);
        return super.updateRow(luckFortuneReleaseItem, condition);
    }

    /**
     * 根据红包id，查询红包
     *
     * @param id
     * @return
     */
    @Override
    public LuckFortuneReleaseItem findById(String id) {
        List<RangeQueryParameter> query = new ArrayList<>();
        query.add(new RangeQueryParameter("id", PrimaryKeyValue.fromString(id), PrimaryKeyValue.fromString(id)));
        query.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQueryOne(IDX_RELEASE_BY_ID,query);
    }

    /**
     * 根据用户id，查询历史红包
     * @param customerId
     * @return
     */
    @Override
    public Page<LuckFortuneReleaseItem> findByCustomerId(String customerId, String type, String startTime, String endTime, int offset, int limit) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.term("customer_id", customerId));
        if(StringUtils.isNotBlank(type)){
            builder.must(QueryBuilders.term("assign_type", type));
        }
        if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
            builder.must(QueryBuilders.range("release_time").greaterThanOrEqual(startTime).lessThanOrEqual(endTime));
        }else if(StringUtils.isNotBlank(startTime)){
            builder.must(QueryBuilders.range("release_time").greaterThanOrEqual(startTime));
        }else if(StringUtils.isNotBlank(endTime)){
            builder.must(QueryBuilders.range("release_time").greaterThanOrEqual(endTime));
        }
        builder.mustNot(QueryBuilders.term("status", ActivityConstant.LuckFortuneReleaseStatus.INVALID.getCode()));
        Sort sort = new Sort(Arrays.asList(new FieldSort("release_time", SortOrder.DESC)));
        long total = countByCustomerId(customerId, type, startTime, endTime);
        Page page = pageSearchQuery(builder.build(), sort, offset, limit, SEARCH_RELEASE_BY_ID);
        page.setTotalCount(total);
        return page;
    }

    private long countByCustomerId(String customerId, String type, String startTime, String endTime) {
        String aggName = "count_agg_release_by_customer_id_v1";

        BoolQuery.Builder filter = QueryBuilders.bool()
                .filter(QueryBuilders.term("customer_id", customerId));
        if(StringUtils.isNotBlank(type)){
            filter.filter(QueryBuilders.term("assign_type", type));
        }
        if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
            filter.filter(QueryBuilders.range("release_time").greaterThanOrEqual(startTime).lessThanOrEqual(endTime));
        }else if(StringUtils.isNotBlank(startTime)){
            filter.filter(QueryBuilders.range("release_time").greaterThanOrEqual(startTime));
        }else if(StringUtils.isNotBlank(endTime)){
            filter.filter(QueryBuilders.range("release_time").greaterThanOrEqual(endTime));
        }
        filter.mustNot(QueryBuilders.term("status", ActivityConstant.LuckFortuneReleaseStatus.INVALID.getCode()));
        SearchQuery.Builder builder = SearchQuery.newBuilder().query(filter);
        builder.addAggregation(AggregationBuilders.count(aggName, "customer_id"));
        return searchCount(builder.build(), SEARCH_RELEASE_BY_ID, aggName);
    }

    /**
     * 根据用户id，查询红包
     *
     * @param endTime
     * @return
     */
    @Override
    public Page<LuckFortuneReleaseItem> findByExpiredTime(Integer status, String endTime, int offset) {
        String startTime = TimeUtil.getDataStr(TimeUtil.addDay(TimeUtil.parse(endTime), -3), TimeUtil.YYYYMMDDHHMMSS);
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.range("expired_time").greaterThanOrEqual(startTime).lessThanOrEqual(endTime));
        builder.must(QueryBuilders.term("status", status));
        Sort sort = new Sort(Arrays.asList(new FieldSort("id", SortOrder.ASC)));
        return pageSearchQuery(builder.build(), sort, offset, 100, SEARCH_RELEASE_BY_ID);
    }

    /**
     * 发放的红包总数
     *
     * @param customerId
     * @return
     */
    @Override
    public long countRelease(String customerId) {
        String aggName = "count_agg_release_by_customer_id";
        SearchQuery query = SearchQuery.newBuilder().query(QueryBuilders.bool()
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.range("status").greaterThanOrEqual(ActivityConstant.LuckFortuneReleaseStatus.DRAWING.getCode())))
                .addAggregation(AggregationBuilders.count(aggName, "customer_id")).build();

        return searchCount(query, SEARCH_RELEASE_BY_ID, aggName);
    }

    /**
     * 历史发放的空投总价值
     *
     * @param customerId
     * @return
     */
    @Override
    public BigDecimal sumAmountRelease(String customerId) {
        String aggName = "sum_agg_release_by_customer";

        BoolQuery.Builder boolQuery = QueryBuilders.bool().must(QueryBuilders.term("customer_id", customerId))
                .mustNot(QueryBuilders.term("status", ActivityConstant.LuckFortuneReleaseStatus.INVALID.getCode()));

        SearchQuery searchQuery = SearchQuery.newBuilder().query(boolQuery)
                .addAggregation(AggregationBuilders.sum(aggName, "cost")).build();

        return BigDecimal.valueOf(searchSum(searchQuery, SEARCH_RELEASE_BY_ID, aggName));
    }

    /**
     * 根据红包id，查询红包
     *
     * @param releaseCode
     * @return
     */
    @Override
    public LuckFortuneReleaseItem findByReleaseCode(String releaseCode) {
        List<RangeQueryParameter> query = new ArrayList<>();
        query.add(new RangeQueryParameter("release_code", PrimaryKeyValue.fromString(releaseCode), PrimaryKeyValue.fromString(releaseCode)));
        query.add(new RangeQueryParameter("id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        query.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        return rangeQueryOne(IDX_RELEASE_BY_RELEASE_CODE,query);
    }
}
