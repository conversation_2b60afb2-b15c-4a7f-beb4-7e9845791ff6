package com.kikitrade.activity.service.job;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReceiveItemBuilder;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReleaseItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReceiveItem;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.service.business.ReleaseService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.framework.common.model.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class ActivitySignExpiredJob implements SimpleJob {

    @Resource
    @Lazy
    private LuckFortuneReleaseItemBuilder luckFortuneReleaseItemBuilder;
    @Resource
    @Lazy
    private LuckFortuneReceiveItemBuilder luckFortuneReceiveItemBuilder;
    @Resource
    @Lazy
    private RedisService redisService;
    @Resource
    @Lazy
    private ReleaseService releaseService;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("ActivitySignExpiredJob start");
        String endTime = TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS);
        //未领完过期
        for(int i = 0;;i++){
            try{
                Page<LuckFortuneReleaseItem> result = luckFortuneReleaseItemBuilder.findByExpiredTime(ActivityConstant.LuckFortuneReleaseStatus.DRAWING.getCode(), endTime, i * 100);
                log.info("ActivitySignExpiredJob result {}", JSON.toJSONString(result));
                if(result == null){
                    break;
                }
                if(CollectionUtils.isEmpty(result.getRows())){
                    break;
                }
                log.info("ActivitySignExpiredJob num {}", result.getRows().size());
                List<LuckFortuneReleaseItem> refundReleaseList = result.getRows();
                refundReleaseList.forEach(refund -> releaseService.expire(refund));
                if(result.getRows().size() < 100){
                    break;
                }
            }catch (Exception ex){
                log.error("ActivitySignExpiredJob", ex);
            }
        }
    }
}
