package com.kikitrade.activity.luck.service.business.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.dipbit.dtm.context.Compensation;
import com.kikitrade.accounting.api.RemoteAccountingOperateService;
import com.kikitrade.accounting.api.RemoteProductAccountService;
import com.kikitrade.accounting.api.model.account.AccountException;
import com.kikitrade.accounting.api.model.account.common.BusinessType;
import com.kikitrade.accounting.api.model.account.request.AccountFreezeRequest;
import com.kikitrade.accounting.api.model.account.response.AccountOperateResponse;
import com.kikitrade.accounting.api.model.account.response.AccountingResponseCode;
import com.kikitrade.accounting.api.model.productaccount.ProductAccountParam;
import com.kikitrade.activity.dal.mysql.model.LuckFortuneRule;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.LuckFortuneReleaseItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.LuckFortuneReleaseItem;
import com.kikitrade.activity.luck.api.exception.LuckFortuneException;
import com.kikitrade.activity.luck.api.exception.LuckFortuneExceptionType;
import com.kikitrade.activity.luck.api.model.request.ActivityCustomerDTO;
import com.kikitrade.activity.luck.api.model.request.LuckFortuneReleaseDTO;
import com.kikitrade.activity.luck.service.business.AirDropEventService;
import com.kikitrade.activity.luck.service.business.AssignService;
import com.kikitrade.activity.luck.service.business.ReleaseService;
import com.kikitrade.activity.luck.service.business.ReleaseTccService;
import com.kikitrade.activity.luck.service.model.ReceiveItem;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.model.CustomerMiscDTO;
import com.kikitrade.kevent.common.constant.EventConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class ReleaseTccServiceImpl implements ReleaseTccService {

    @Resource
    private RedisService redisService;
    @Resource
    private AssignService assignService;
    @Resource
    private CustomerService customerService;
    @Resource
    private LuckFortuneReleaseItemBuilder luckFortuneReleaseItemBuilder;
    @Resource
    private KactivityProperties kactivityProperties;
    @DubboReference
    private RemoteAccountingOperateService remoteAccountingOperateService;
    @DubboReference
    private RemoteProductAccountService remoteProductAccountService;
    @Resource
    private ReleaseService releaseService;
    @Resource
    private AirDropEventService airDropEventService;

    /**
     * 发放红包
     * @param luckFortuneReleaseDTO
     * @param customer
     * @return
     */
    @Override
    @Compensation(confirmMethod = "confirmRelease", cancelMethod = "cancelRelease")
    public Result<LuckFortuneReleaseItem> tryRelease(LuckFortuneRule rule, LuckFortuneReleaseDTO luckFortuneReleaseDTO, ActivityCustomerDTO customer, String id) throws Exception {
        LuckFortuneReleaseItem releaseItem = build(luckFortuneReleaseDTO, customer, id);
        //计算红包
        List<ReceiveItem> receiveItems = assignService.buildLuckFortuneReceive(rule, luckFortuneReleaseDTO, id);
        releaseItem.setDetail(JSON.toJSONString(receiveItems));
        luckFortuneReleaseItemBuilder.insert(releaseItem);
        //kaccounting, 冻结用户帐
        try{
            AccountOperateResponse accountFreeze = remoteAccountingOperateService.freeze(buildAccountFreezeRequest(releaseItem, customer, id));
            if(AccountingResponseCode.SUCCESS != accountFreeze.getCode()){
                throw new LuckFortuneException(LuckFortuneExceptionType.CUSTOMER_INSUFFICIENT_BALANCE);
            }
        }catch (AccountException ex){
            throw new LuckFortuneException(LuckFortuneExceptionType.CUSTOMER_INSUFFICIENT_BALANCE);
        }
        //kaccounting, 冻结产品帐
        try{
            remoteProductAccountService.freeze(buildProductAccountParam(rule, id, customer.getId(), releaseItem.getCost()));
        }catch (Exception ex){
            throw new LuckFortuneException(LuckFortuneExceptionType.LUCK_FORTUNE_RELEASE_LIMIT_ERROR);
        }
        syncCache(releaseItem, luckFortuneReleaseDTO.getValidTime(), receiveItems);
        return Result.of(Result.ResultCode.SUCCESS, "", releaseItem);
    }

    /**
     * 红包方放成功
     * @param luckFortuneReleaseDTO
     * @param customer
     * @param id
     */
    public void confirmRelease(LuckFortuneRule rule, LuckFortuneReleaseDTO luckFortuneReleaseDTO, ActivityCustomerDTO customer, String id){
        LuckFortuneReleaseItem releaseItem = new LuckFortuneReleaseItem();
        releaseItem.setId(id);
        releaseItem.setCustomerId(customer.getId());
        releaseItem.setValidTime(luckFortuneReleaseDTO.getValidTime());
        luckFortuneReleaseItemBuilder.updateStatusToDrawing(releaseItem);
        if(luckFortuneReleaseDTO.getValidTime() == 0){
            redisService.save(RedisKeyConst.LUCK_FORTUNE_RELEASE_STATUS_KEY.getKey(id), String.valueOf(ActivityConstant.LuckFortuneReleaseStatus.DRAWING.getCode()));
        }else{
            redisService.setIfAbsent(RedisKeyConst.LUCK_FORTUNE_RELEASE_STATUS_KEY.getKey(id), String.valueOf(ActivityConstant.LuckFortuneReleaseStatus.DRAWING.getCode()), TimeUnit.DAYS.toSeconds(luckFortuneReleaseDTO.getValidTime()));
        }
        airDropEventService.sendReleaseEvent(releaseItem);
        // 事件上报
        airDropEventService.reportEvent(EventConstants.EventName.RELEASE_RED_POCKET, luckFortuneReleaseItemBuilder.findById(id));
    }

    /**
     * 红包发放失败
     * @param luckFortuneReleaseDTO
     * @param customer
     * @param id
     */
    public void cancelRelease(LuckFortuneRule rule, LuckFortuneReleaseDTO luckFortuneReleaseDTO, ActivityCustomerDTO customer, String id){
        LuckFortuneReleaseItem releaseItem = new LuckFortuneReleaseItem();
        releaseItem.setId(id);
        releaseItem.setCustomerId(customer.getId());
        luckFortuneReleaseItemBuilder.updateStatusToInvalid(releaseItem);
        redisService.del(RedisKeyConst.LUCK_FORTUNE_RELEASE_STATUS_KEY.getKey(id));
    }

    /**
     * 构建红包
     * @param luckFortuneReleaseDTO
     * @param customer
     * @param id
     * @return
     */
    private LuckFortuneReleaseItem build(LuckFortuneReleaseDTO luckFortuneReleaseDTO, ActivityCustomerDTO customer, String id){
        LuckFortuneReleaseItem item = new LuckFortuneReleaseItem();
        item.setId(id);
        item.setCustomerId(customer.getId());
        CustomerMiscDTO misc = customerService.getMiscByCustomerId(customer.getId());
        item.setAvatar(misc.getAvatar());
        item.setNickName(misc.getNickName());

        item.setAssignType(luckFortuneReleaseDTO.getAssignType());//手气红包、普通红包
        item.setAmount(ActivityConstant.AssignType.FORTUNE.isEqual(luckFortuneReleaseDTO.getAssignType())
                ? luckFortuneReleaseDTO.getAmount().toPlainString()
                : luckFortuneReleaseDTO.getAmount().multiply(new BigDecimal(luckFortuneReleaseDTO.getNum())).toPlainString());
        item.setNum(luckFortuneReleaseDTO.getNum());
        item.setCurrency(luckFortuneReleaseDTO.getCurrency());
        item.setCover(luckFortuneReleaseDTO.getCover());//封面图
        item.setGreeting(luckFortuneReleaseDTO.getGreeting());
        item.setReleaseTime(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        item.setValidTime(luckFortuneReleaseDTO.getValidTime());
        if(luckFortuneReleaseDTO.getValidTime() > 0){
            item.setExpiredTime(TimeUtil.getUtcTime(TimeUtil.addDay(TimeUtil.parse(item.getReleaseTime()), luckFortuneReleaseDTO.getValidTime()), TimeUtil.YYYYMMDDHHMMSS));//过期时间
        }else{
            item.setExpiredTime(TimeUtil.getUtcTime(TimeUtil.addMonth(TimeUtil.parse(item.getReleaseTime()), 120), TimeUtil.YYYYMMDDHHMMSS));//过期时间
        }
        item.setStatus(ActivityConstant.LuckFortuneReleaseStatus.APPENDING.getCode());
        item.setCreated(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        item.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        item.setCost(luckFortuneReleaseDTO.getUsdCost());
        item.setReleaseCode(releaseService.decodeReleaseId(item.getId().substring(14), customer.getId()));
        return item;
    }

    /**
     * 构建冻结账户的请求
     * @param releaseItem
     * @param customer
     * @param id
     * @return
     */
    private AccountFreezeRequest buildAccountFreezeRequest(LuckFortuneReleaseItem releaseItem, ActivityCustomerDTO customer, String id){
        AccountFreezeRequest request = new AccountFreezeRequest();
        request.setBusinessId(id);
        request.setCustomerId(customer.getId());
        request.setCurrency(releaseItem.getCurrency());
        request.setBusinessType(BusinessType.ACTIVITY_AIRDROP);
        request.setAmount(new BigDecimal(releaseItem.getAmount()));
        request.setSaasId(kactivityProperties.getSaasId());
        return request;
    }

    /**
     * 构建产品帐操作
     * @param biz
     * @param customerId
     * @param amount
     * @return
     */
    private ProductAccountParam buildProductAccountParam(LuckFortuneRule rule, String biz, String customerId, BigDecimal amount){
        ProductAccountParam param = new ProductAccountParam();
        param.setBizId(biz);
        param.setCustomerId(customerId);
        param.setAmount(amount);
        param.setProductLimitCode(String.format("%s_%s_%s", ActivityConstant.ProLimitCode.AIRDROP_RELEASE_LIMIT.name(), rule.getKycLevel(), rule.getUserType()));
        return param;
    }


    /**
     * 红包同步到缓存里
     * @param item
     * @param receiveItems
     * @return
     * @throws Exception
     */
    private boolean syncCache(LuckFortuneReleaseItem item, int validTime, List<ReceiveItem> receiveItems) throws Exception {
        String key = RedisKeyConst.LUCK_FORTUNE_RELEASE_NOT_DRAW_KEY.getMiddleKey(item.getId());
        String itemKey = RedisKeyConst.LUCK_FORTUNE_RELEASE_INFO_KEY.getKey(item.getId());
        String releaseCodeKey = RedisKeyConst.LUCK_FORTUNE_RELEASE_CODE_KEY.getKey(item.getReleaseCode());
        String releaseCode1Key = RedisKeyConst.LUCK_FORTUNE_RELEASE_CODE_KEY.getKey(item.getId());
        try{
            //设置默认值， 否则lua 无法复制
            List<String> items = receiveItems.stream().map(JSON::toJSONString).collect(Collectors.toList());
            Long result = redisService.lpushAll(key, items);
            redisService.expire(key, TimeUnit.DAYS.toSeconds(validTime));
            redisService.setIfAbsent(itemKey, JSON.toJSONString(item), TimeUnit.DAYS.toSeconds(validTime));
            redisService.setIfAbsent(releaseCodeKey, item.getId(), TimeUnit.DAYS.toSeconds(validTime + 3));
            redisService.setIfAbsent(releaseCode1Key, item.getReleaseCode(), TimeUnit.DAYS.toSeconds(validTime + 3));
            return result > 0;
        }catch (Exception ex){
            log.error("sync cache exception, id:{}", item, ex);
            throw ex;
        }
    }
}
